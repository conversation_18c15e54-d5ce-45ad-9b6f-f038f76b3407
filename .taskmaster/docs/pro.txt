# 适老化要求与需完成页面总结

## 一、适老化设计要求

### （一）针对不同人群的核心需求
- **老年人**：提供大字体、大图标、高对比度界面，支持一键操作、文本输入提示、拼音提示等功能。
- **视力障碍人士**：兼容读屏软件，提供语音验证码，确保按钮标签和图片信息可识别。
- **听力障碍人士**：信息加配字幕，提高与助听器等设备的兼容性，支持可替代服务方式。
- **肢体障碍人士**：支持自定义手势，简化交互操作。

### （二）界面设计规范
- **页面布局**：采用扁平化结构，视觉层级和信息层级均不超过两级，减少干扰信息。
- **颜色与字体**：
    - 区域颜色通过色彩差异区分，方便老人辨识。
    - 字体大小至少18pt/dp，主要信息加粗，最大字体不小于30pt/dp。
    - 文本/图标对比度不低于4.5:1（字号>18pt/dp时为3:1）。
- **交互细节**：
    - 焦点区域尺寸：适老化界面主要组件≥60×60dp/pt，其他页面≥44×44dp/pt。
    - 行间距：段落内行距≥1.3倍，段落间距≥行距×1.3倍。
    - 文本、语音、颜色兼容：避免仅用颜色提示，如密码错误时需文字/语音提示。

### （三）多媒体与非文本处理
- **语音功能**：引入语音阅读和控制器，各信息区域提供语音告知服务。
- **键盘与手势**：
    - 全键盘操作，常用功能设快捷键（如Ctrl+B跳转搜索）。
    - 移动应用避免3指及以上复杂手势，操作结果提供反馈。
- **验证码优化**：
    - 放大倍数≥2倍，支持语音验证码替代。
    - 时效≤3分钟，提供语音告知和延长设置功能。

### （四）交互与反馈设计
- **错误预防**：可逆提交动作（如法律/财务交易可10分钟内撤销或修改）。
- **内容规范**：避免专业术语和网络新词，确保表述易懂。
- **浮窗与导航**：
    - 浮窗关闭按钮位于左上/右上/中央底部，点击区域≥44×44dp/pt。
    - 界面操作流程和功能标识保持一致性，符合用户常规认知。

### （五）安全性设计
- **广告与诱导限制**：严禁出现广告窗体和诱导下载/付款按键。
- **个人信息保护**：遵循最小必要原则，收集信息符合相关规范（如《常见类型移动互联网应用程序必要个人信息范围规定》）。

## 二、需完成的适老化改造页面

### （一）PC端页面
- 统一身份认证系统
- 校园综合服务平台
- 学院通知公告系统
- 业务流程平台
- 中层干部考核平台
- 一周会议安排系统
- 微信矩阵管理系统
- 校园网后台管理系统

### （二）H5端页面
- 统一身份认证系统
- 校园综合服务平台
- 业务流程平台
- 一周会议安排系统