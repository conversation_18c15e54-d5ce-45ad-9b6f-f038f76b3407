# Task ID: 1
# Title: 建立适老化设计规范和标准
# Status: done
# Dependencies: None
# Priority: high
# Description: 制定统一的适老化界面设计规范，包括字体、颜色、布局、交互等标准
# Details:
根据PRD文档要求，制定适老化设计标准：
- 适应标准模式和适老化模式
- 字体大小至少18pt/dp，主要信息加粗，最大字体不小于30pt/dp
- 文本/图标对比度不低于4.5:1
- 焦点区域尺寸主要组件≥60×60dp/pt
- 行间距≥1.3倍，段落间距≥行距×1.3倍
- 扁平化结构，视觉层级不超过两级

# Test Strategy:
设计规范文档评审，确保符合适老化要求和国家标准

# Subtasks:
## 1. 制定字体和排版规范 [done]
### Dependencies: None
### Description: 制定适老化的字体大小、字重、行间距等排版规范
### Details:
制定详细的字体排版规范：\n- 最小字体18pt/dp，主要信息加粗\n- 最大字体不小于30pt/dp\n- 行间距≥1.3倍\n- 段落间距≥行距×1.3倍\n- 字体选择考虑可读性

## 2. 制定颜色和对比度规范 [done]
### Dependencies: None
### Description: 制定适老化的颜色对比度和色彩使用规范
### Details:
制定颜色和对比度规范：\n- 文本/图标对比度不低于4.5:1\n- 大字体（>18pt/dp）对比度不低于3:1\n- 区域颜色通过色彩差异区分\n- 避免仅用颜色提示，需配合文字/语音\n- 支持高对比度模式

## 3. 制定布局和交互规范 [done]
### Dependencies: None
### Description: 制定适老化的页面布局和交互元素规范
### Details:
制定布局和交互规范：\n- 扁平化结构，视觉层级不超过两级\n- 焦点区域尺寸≥60×60dp/pt（主要组件）\n- 其他组件≥44×44dp/pt\n- 浮窗关闭按钮位置和大小规范\n- 避免复杂手势，支持替代操作

