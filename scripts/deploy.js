/* eslint-disable */
const fs = require('fs');
const EasyDeploy = require('@iboying/easy-deploy');
const chalk = require('chalk');

const localPath = 'dist/';
const TARGET = process.env.npm_lifecycle_event;
const targetOptionsMap = {
    'publish:production': [{
        tag: 'http://os.stiei.edu.cn',
        username: 'web',
        host: 'stiei',
        // port: 2001,
        // host: '**************',
        port: 7890,
        localPath,
        remotePath: '/mnt/home/<USER>/4d/campus-web/dist',
    }, ],
    'publish:hw4': [{
      tag: 'hw4',
      username: 'app',
      host: '************',
      // port: 2001,
      // host: '**************',
      port: 22,
      localPath,
      remotePath: '/home/<USER>/campus-web/dist',
  }, ],
    'publish:campus': [{
        tag: 'http://wchattest.stiei.edu.cn/campus-web',
        username: 'stiei',
        host: '**************',
        port: 2002,
        localPath,
        remotePath: '/opt/shsgzgzjxyjh/stiei/campus-web/dist',
    }, ],
    'publish:tallty': [{
        tag: 'http://os.4dschools.com',
        localPath,
        username: 'web',
        host: '**************',
        remotePath: '/home/<USER>/campus-web/dist',
    }, ],
    'publish:test': [{
        tag: 'http://wchattest.stiei.edu.cn/do_not_use_this_url_or_your_data_will_be_lost',
        username: 'stiei',
        host: '**************',
        port: 2002,
        localPath,
        remotePath: '/opt/shsgzgzjxyjh/stiei/campus-web/dist-test',
    }, ],
};
const targets = targetOptionsMap[TARGET] || [];

async function saveLatestCommitId() {
    const commitId = await EasyDeploy.shell('git rev-parse head');
    fs.writeFileSync(`${localPath}version.json`, commitId, {
        encoding: 'utf-8',
    });
}

async function deployToTargets(option) {
    try {
        const instance = new EasyDeploy(option);
        console.log(chalk.cyan(`开始部署 ${option.tag}`));
        await instance.sync('-aI --delete');
        console.log(chalk.green(`部署 ${option.tag} 成功`));
    } catch (err) {
        console.log(chalk.red(`部署 ${option.tag} 失败`));
        throw err;
    }
}

(async() => {
    await saveLatestCommitId();
    for await (const option of targets) {
        deployToTargets(option);
    }
})();
