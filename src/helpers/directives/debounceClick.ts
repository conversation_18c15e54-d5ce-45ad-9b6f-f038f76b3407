interface IBinding {
  value: any;
  oldValue: any;
  modifiers: object;
  arg: string;
}

export default {
  bind(el: HTMLElement, binding: IBinding) {
    const handler = binding.value;
    const duration = Number(binding.arg) || 1000;
    if (!handler) return;
    let preTime = -1;
    el.onclick = () => {
      const now = Date.now();
      if (preTime + duration <= now) {
        handler();
        preTime = now;
      }
    };
  },
};
