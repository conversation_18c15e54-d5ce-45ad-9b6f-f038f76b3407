/**
 * 语音播报指令
 * 用法：
 * v-speech="'要播报的文本'"
 * v-speech:hover="'鼠标悬停时播报的文本'"
 * v-speech:focus="'获得焦点时播报的文本'"
 * v-speech:click="'点击时播报的文本'"
 */

import Vue from 'vue';
import speechService from '@/utils/speechService';

interface SpeechBinding {
  value: string | (() => string);
  modifiers: {
    hover?: boolean;
    focus?: boolean;
    click?: boolean;
    auto?: boolean; // 自动播报（元素出现时）
  };
  arg?: string; // 事件类型
}

interface SpeechElement extends HTMLElement {
  _speechHandlers?: {
    mouseenter?: EventListener;
    mouseleave?: EventListener;
    focus?: EventListener;
    blur?: EventListener;
    click?: EventListener;
  };
  _speechText?: string;
  _speechObserver?: IntersectionObserver;
}

// 获取元素的语音文本
function getSpeechText(element: SpeechElement, binding: SpeechBinding): string {
  if (typeof binding.value === 'function') {
    return binding.value();
  }
  
  if (typeof binding.value === 'string' && binding.value.trim()) {
    return binding.value;
  }

  // 如果没有指定文本，尝试从元素获取
  return getElementText(element);
}

// 获取元素的可读文本
function getElementText(element: Element): string {
  // 优先获取 aria-label
  const ariaLabel = element.getAttribute('aria-label');
  if (ariaLabel) return ariaLabel;

  // 获取 title 属性
  const title = element.getAttribute('title');
  if (title) return title;

  // 获取 alt 属性（图片）
  const alt = element.getAttribute('alt');
  if (alt) return alt;

  // 获取文本内容
  const textContent = element.textContent?.trim();
  if (textContent) {
    // 限制文本长度，避免过长的内容
    return textContent.length > 100 ? textContent.substring(0, 100) + '...' : textContent;
  }

  // 获取 placeholder（输入框）
  const placeholder = element.getAttribute('placeholder');
  if (placeholder) return `输入框：${placeholder}`;

  // 获取 value（按钮）
  const value = (element as HTMLInputElement).value;
  if (value) return value;

  // 根据元素类型生成描述
  const tagName = element.tagName.toLowerCase();
  switch (tagName) {
    case 'button':
      return '按钮';
    case 'a':
      return '链接';
    case 'input':
      const type = element.getAttribute('type') || 'text';
      return `${type === 'text' ? '文本' : type}输入框`;
    case 'select':
      return '下拉选择框';
    case 'textarea':
      return '文本区域';
    default:
      return '';
  }
}

// 创建事件处理器
function createEventHandlers(element: SpeechElement, binding: SpeechBinding) {
  const handlers: any = {};
  const text = getSpeechText(element, binding);
  
  if (!text) return handlers;

  // 鼠标悬停事件
  if (binding.modifiers.hover || binding.arg === 'hover') {
    handlers.mouseenter = () => {
      speechService.speak(text);
    };
    
    handlers.mouseleave = () => {
      // 可选：鼠标离开时停止播报
      // speechService.stop();
    };
  }

  // 焦点事件
  if (binding.modifiers.focus || binding.arg === 'focus') {
    handlers.focus = () => {
      speechService.speak(text);
    };
  }

  // 点击事件
  if (binding.modifiers.click || binding.arg === 'click') {
    handlers.click = () => {
      speechService.speak(text, { priority: 'high' });
    };
  }

  // 默认行为：根据元素类型自动选择事件
  if (!binding.arg && !Object.keys(binding.modifiers).length) {
    const tagName = element.tagName.toLowerCase();
    
    if (['button', 'a'].includes(tagName)) {
      // 按钮和链接使用悬停
      handlers.mouseenter = () => {
        speechService.speak(text);
      };
    } else if (['input', 'textarea', 'select'].includes(tagName)) {
      // 表单元素使用焦点
      handlers.focus = () => {
        speechService.speak(text);
      };
    } else {
      // 其他元素使用悬停
      handlers.mouseenter = () => {
        speechService.speak(text);
      };
    }
  }

  return handlers;
}

// 绑定事件监听器
function bindEventListeners(element: SpeechElement, handlers: any) {
  Object.keys(handlers).forEach(event => {
    element.addEventListener(event, handlers[event]);
  });
  element._speechHandlers = handlers;
}

// 解绑事件监听器
function unbindEventListeners(element: SpeechElement) {
  if (element._speechHandlers) {
    Object.keys(element._speechHandlers).forEach(event => {
      const handler = element._speechHandlers![event as keyof typeof element._speechHandlers];
      if (handler) {
        element.removeEventListener(event, handler);
      }
    });
    delete element._speechHandlers;
  }
}

// 创建交叉观察器（用于自动播报）
function createIntersectionObserver(element: SpeechElement, text: string) {
  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting && speechService.getSettings().enabled) {
          // 元素进入视口时播报
          speechService.speak(text, { priority: 'low' });
        }
      });
    },
    {
      threshold: 0.5, // 元素50%可见时触发
      rootMargin: '0px'
    }
  );
  
  observer.observe(element);
  return observer;
}

// Vue指令定义
const speechDirective = {
  bind(element: SpeechElement, binding: SpeechBinding) {
    // 只在语音播报启用时处理
    if (!speechService.getSettings().enabled) {
      return;
    }

    const handlers = createEventHandlers(element, binding);
    bindEventListeners(element, handlers);

    // 如果是自动播报模式
    if (binding.modifiers.auto) {
      const text = getSpeechText(element, binding);
      if (text) {
        element._speechObserver = createIntersectionObserver(element, text);
      }
    }

    // 保存文本用于更新
    element._speechText = getSpeechText(element, binding);
  },

  update(element: SpeechElement, binding: SpeechBinding) {
    const newText = getSpeechText(element, binding);
    
    // 如果文本没有变化，不需要更新
    if (element._speechText === newText) {
      return;
    }

    // 解绑旧的事件监听器
    unbindEventListeners(element);
    
    // 重新绑定新的事件监听器
    if (speechService.getSettings().enabled) {
      const handlers = createEventHandlers(element, binding);
      bindEventListeners(element, handlers);
    }

    element._speechText = newText;
  },

  unbind(element: SpeechElement) {
    // 清理事件监听器
    unbindEventListeners(element);
    
    // 清理交叉观察器
    if (element._speechObserver) {
      element._speechObserver.disconnect();
      delete element._speechObserver;
    }
    
    delete element._speechText;
  }
};

// 注册指令
Vue.directive('speech', speechDirective);

export default speechDirective;
