import Vue from 'vue';
import { Spin } from 'ant-design-vue';

const LoadingConstructor = Vue.extend(Spin);

interface MyElement extends HTMLElement {
  setPosition: boolean;
  instance: Vue;
  mask: HTMLElement;
}

interface IBinding {
  value: any;
  oldValue: any;
  modifiers: object;
}

const toggleLoading = (el: MyElement, binding: IBinding) => {
  Vue.nextTick(() => {
    if (binding.value) {
      if (el.style.position !== 'relative') {
        el.style.setProperty('position', 'relative');
        el.setPosition = true;
      }
      el.mask.setAttribute(
        'style',
        `
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
        background: rgba(255,255,255,0.6);
      `,
      );
      el.appendChild(el.mask);
    } else {
      if (el.setPosition) {
        el.style.removeProperty('position');
      }
      el.removeChild(el.mask);
    }
  });
};

export default {
  bind(el: MyElement, binding: IBinding) {
    const size: string = Object.keys(binding.modifiers)[0] || 'default';
    const instance = new LoadingConstructor({
      el: document.createElement('div'),
      propsData: {
        size,
      },
    });
    const mask: HTMLElement = document.createElement('div');
    mask.appendChild(instance.$el);

    el.instance = instance;
    el.mask = mask;

    if (binding.value) {
      toggleLoading(el, binding);
    }
  },
  update(el: MyElement, binding: IBinding) {
    if (binding.oldValue !== binding.value) {
      toggleLoading(el, binding);
    }
  },
  unbind(el: MyElement) {
    el.instance.$destroy();
    if (el.setPosition) {
      el.style.removeProperty('position');
    }
    if (el.contains(el.mask)) {
      el.removeChild(el.mask);
    }
  },
};
