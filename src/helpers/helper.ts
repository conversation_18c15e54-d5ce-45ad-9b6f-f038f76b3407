import FileSaver from 'file-saver';
import utils from '@/utils';
import FileServer, { IFile } from '@/models/file';
import store from '@/store';
import qs from 'qs';

const fileServer = new FileServer({ useCdn: false });

interface IExport {
  url: string;
}

export default {
  fileVerifyMessage() {
    return '文件正在上传中， 请确定上传完成或取消上传再操作!';
  },
  exportExcel(val: IExport = { url: '' }) {
    FileSaver.saveAs(val.url, val.url.split('/').pop());
  },
  fileUrl(fileItem: IFile) {
    return fileServer.getDownloadUrl(fileItem);
  },
};
