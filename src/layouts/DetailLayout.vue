<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component
export default class DetailLayout extends Vue {}
</script>

<template lang="pug">
.detail-layout
  .detail-layout__header
    slot(name="header")
  .detail-layout__content
    slot
</template>

<style lang="stylus" scoped>
.detail-layout
  display flex
  flex-direction column
  width 100%
  height 100%
  background #f5f5f5
  .detail-layout__header
    flex-shrink 0
    width 100%
  .detail-layout__content
    position relative
    flex-grow 1
    overflow hidden
    padding 16px 60px 0
</style>
