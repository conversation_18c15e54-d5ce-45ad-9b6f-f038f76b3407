<template lang="pug">
#app(:class="{ debug: isDebugRelease }")
  a-config-provider(:locale="locale")
    .app-loading-box(v-if="loading")
      a-spin.spin(tip="加载中...")
    .app-content(v-else)
      keep-alive(:include='KeepAliveLayout')
        component(:is='layout')
          keep-alive(:exclude='$route.meta.keepAlive ? [] : /.*/')
            router-view
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
import zhCN from 'ant-design-vue/es/locale-provider/zh_CN';
import session from './models/session';
import store from '@/store';
import infoStore from './store/modules/info.store';
import { IRoute } from '@/interfaces/route.interface';
import MenuLayout from '@/layouts/MenuLayout.vue';
import AutoMenuLayout from '@/layouts/AutoMenuLayout.vue';
import DefaultLayout from '@/layouts/DefaultLayout.vue';
import HeaderLayout from '@/layouts/HeaderLayout.vue';
import speechService from '@/utils/speechService';

@Component({
  components: {
    MenuLayout,
    AutoMenuLayout,
    DefaultLayout,
    HeaderLayout,
  },
})
export default class App extends Vue {
  isDebugRelease: boolean = process.env.VUE_APP_DEBUG_RELEASE === 'true';
  locale: any = zhCN;
  loading: boolean = true;
  socket: WebSocket | null = null;

  KeepAliveLayout: string[] = ['MenuLayout', 'AutoMenuLayout'];

  get layout() {
    return `${this.$route.meta.layout || 'default'}-layout`;
  }

  get authRole() {
    return store.state.authRole || 'teacher';
  }

  @Watch('$route')
  public onRouteChanged() {
    document.title = this.$route.meta.title;
    // 延迟播报页面标题，等待页面内容加载
    setTimeout(() => {
      if (speechService.getSettings().enabled) {
        const title = document.title || this.$route.meta?.title || '新页面';
        speechService.speak(`已进入：${title}`, { priority: 'high' });
      }
    }, 500);
  }

  created() {
    infoStore.setRole(this.authRole);
    this.checkAuth();
    // this.initSocket();
  }
  initSocket() {
    this.socket = new WebSocket('ws://sidekiq.rxqiang.cn/cable');
    this.socket.addEventListener('open', this.onSocketOpen);
    this.socket.addEventListener('message', this.onSocketReceiveMessage);
    this.socket.addEventListener('close', this.onSocketClose);
  }
  onSocketOpen() {
    const channel = { command: 'subscribe', identifier: { channel: 'NoticeChannel', id: 1 } };
    this.socket!.send(JSON.stringify(channel));
  }
  onSocketClose() {
    this.socket = new WebSocket('ws://sidekiq.rxqiang.cn/cable/notice_1');
  }
  onSocketReceiveMessage(event: any) {
    try {
      /* eslint-disable-next-line */
    } catch (error) {
      /* eslint-disable-next-line */
      this.socket!.close();
    }
  }
  async checkAuth() {
    const publicPath: string = process.env.VUE_APP_PUBLIC_PATH as string;
    const realPath = window.location.pathname.substring(
      window.location.pathname.indexOf(publicPath) + publicPath.length,
    );
    const route: IRoute = (this.$router as any).match(realPath);
    if (route.path.includes('/login')) {
      this.loading = false;
      return;
    }
    if (route.path.includes('/home/<USER>')) {
      this.loading = false;
      return;
    }
    if (route.meta && route.meta.requireAuth === false) {
      this.loading = false;
      return;
    }
    try {
      this.loading = true;
      await session.checkToken();
      await infoStore.findAndSetRolePermits();
      this.loading = false;
    } catch (error) {
      window.setTimeout(() => {
        this.loading = false;
      }, 100);
    }
  }
}
</script>

<style lang="stylus">
@media (max-width: 768px) {
  #app {
    min-width auto !important;
  }
}
#app
  min-width 1080px
  width 100%
  height 100%
  color #383838
  font-weight 400
  font-family -apple-system, BlinkMacSystemFont, Helvetica Neue, PingFang SC, Microsoft YaHei, sans-serif
  -webkit-font-smoothing antialiased
  -moz-osx-font-smoothing grayscale
  .app-content
    height 100%
    width 100%
  .app-loading-box
    position relative
    width 100%
    height 100%
    .spin
      position absolute
      top 50%
      left 50%
      margin-top -15px
      margin-left -27px
.debug
  border 0px solid red
@media print
  #app
    height auto
    font-family SimSun
</style>
