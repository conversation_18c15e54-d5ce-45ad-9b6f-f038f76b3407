import { ActiveStore } from '../lib/ActiveStore';
type AttrType = 'string' | 'number' | 'integer' | 'boolean' | 'datetime' | 'date' | 'time' | 'array' | 'object';

export interface IFormTemplateItem {
  key?: string;
  name?: string;
  map_key?: string; // 对应后台模型字段，使用 workflow.meta.workflow_attributes 保存的映射表
  accessibility?: 'read_and_write' | 'readonly' | 'hidden';
  layout: {
    component: TemplateFormComponents;
    options?: Array<{ label: string; value?: string | number | null; group?: string }>;
    placeholder?: string;
    required?: boolean;
    disabled?: boolean;
    multiple?: boolean;
    accept?: string;
    span?: number;
    rowspan?: number;
    textAlign?: 'left' | 'center' | 'right';
    min?: number;
    max?: number;
    unit?: string; // a-input-number 添加单位
    fields?: IFormTemplateItem[];
    attrs?: Array<{ key: string; name: string; value?: string; show?: boolean }>; // TODO: 统一使用 fields
    wechat?: IWechatApp;
    defaultValue?: any;
    type?: string; // 废弃，为了兼容之前的配置
    notEditable?: boolean; //
    template?: IFormTemplateItem[];
    templateIndexAry?: number[];
    conditionKey?: string;
    conditionValue?: string;
  };
  model: {
    attr_type: AttrType;
    store?: ActiveStore;
    storeConfig?: StoreConfig;
  };
}

export interface StoreConfig {
  initParams?: IObject;
  tagKey?: string; // 显示在标签上的值所对应的 key，默认为 name
  tableColumns?: {
    title: string;
    dataIndex: string;
    type?: string;
    search?: boolean;
  }[];
}

export type TemplateFormComponents =
  | 'input'
  | 'currency'
  | 'textarea'
  | 'date'
  | 'time'
  | 'datetime'
  | 'radio'
  | 'checkbox'
  | 'select'
  | 'file'
  | 'switch'
  | 'image'
  | 'table'
  | 'rich_text'
  | 'contacts'
  | 'record'
  | 'department'
  | 'hr'
  | 'label'
  | 'tag'
  | 'wechat_articles_editor' // 公众号文章创建新的
  | 'wechat_articles' // 公众文章选择已有的
  | 'list_container'
  | 'if_container'
  | 'container_end'
  | 'store_field'
  | 'form_designer'
  | 'major'
  | 'level_two_college'
  | 'teacher'
  | 'student'
  | 'course_dir'
  | 'department_new'
  | 'department_new_nested'
  | 'course_set'
  | 'time_range_switch_input'
  | 'budget_lock_amount'
  | 'budget_lock_create'
  | 'budget_adjust'
  | 'budget_adjust_negative'
  | 'budget_create'
  | 'finance_project'
  | 'finance_project_admin_all'
  | 'finance_project_execute'
  | 'finance_project_own';

export interface IWechatApp {
  name: string;
  desc: string;
  appid: string;
}

export interface IFormItemArticle {
  appId: string;
  mediaId: string;
  count?: number;
  headline?: string;
}
