import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';
import { ResDepartment } from '@/types/model';

export class ResAdminDepartmentsApi extends ActiveModel<ResDepartment> {
  constructor(config?: IModelConfig) {
    super({
      namespace: '/res/admin',
      name: 'department',
      actions: [
        { name: 'export', method: 'post', on: 'collection' },
        { name: 'upload_excel', method: 'post', on: 'collection' },
        { name: 'excel', method: 'post', on: 'collection' },
        { name: 'import_headers', method: 'post', on: 'collection' },
        { name: 'export_headers', method: 'post', on: 'collection' },
        { name: 'import', method: 'post', on: 'collection' },
      ],
      ...config,
    });
  }
}
