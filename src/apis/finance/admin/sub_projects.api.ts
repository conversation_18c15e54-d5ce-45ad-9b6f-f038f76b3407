import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';
import { FinanceSubProject } from '@/types/model';

export class FinanceAdminSubProjectsApi extends ActiveModel<FinanceSubProject> {
  constructor(config?: IModelConfig) {
    super({
      namespace: '/finance/admin',
      name: 'sub_project',
      actions: [
        { name: 'export', method: 'post', on: 'collection' },
        { name: 'upload_excel', method: 'post', on: 'collection' },
        { name: 'excel', method: 'post', on: 'collection' },
        { name: 'import_headers', method: 'post', on: 'collection' },
        { name: 'export_headers', method: 'post', on: 'collection' },
        { name: 'import', method: 'post', on: 'collection' },
      ],
      ...config,
    });
  }
}
