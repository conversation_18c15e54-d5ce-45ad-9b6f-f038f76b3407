import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';
import { FinanceBudgetLockGrp } from '@/types/model';

export class FinanceAdminBudgetLockGrpApi extends ActiveModel<FinanceBudgetLockGrp> {
  constructor(config?: IModelConfig) {
    super({
      namespace: '/finance/admin',
      name: 'budget_lock_grp',
      dataIndexKey: 'records',
      actions: [
        { name: 'export', method: 'post', on: 'collection' },
        { name: 'export_headers', method: 'post', on: 'collection' },
      ],
      ...config,
    });
  }
}
