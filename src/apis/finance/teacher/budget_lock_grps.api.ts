import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';
import { FinanceBudgetLockGrp } from '@/types/model';

export class FinanceTeacherBudgetLockGrpsApi extends ActiveModel<FinanceBudgetLockGrp> {
  constructor(config?: IModelConfig) {
    super({
      namespace: '/finance/teacher',
      name: 'budget_lock_grp',
      dataIndexKey: 'records',
      actions: [
        { name: 'done', method: 'post', on: 'member' },
        { name: 'statistics', method: 'post', on: 'collection' },
      ],
      ...config,
    });
  }
}
