// 1. Make sure to import 'vue' before declaring augmented types
import Vue from 'vue';
import { PermissionModule } from '@/store';
import moment from 'moment';

type func = (item: any) => string;

// 2. Specify a file with the types you want to augment
//    Vue has the constructor type in types/vue.d.ts
declare module 'vue/types/vue' {
  // 3. Declare augmentation for Vue
  interface Vue {
    $models: any;
    $moment: typeof moment;
    $tools: any;
    $utils: {
      only(obj: IObject, keys: string | string[]): IObject;
      except(obj: IObject, keys: string | string[]): IObject;
      toCurrency: (price: number | string, decimalCount?: number, suffix?: string) => string;
      stringToArray: (string: string, char: string, shouldRemoveEmptyItem?: boolean) => string[];
      open: (path: string, target?: string) => void;
      promiseSerial: (funcs: Promise<any>[]) => void;
      groupBy: (array: any[], func: any) => IObject;
      objectify: (ary: any[], key: string | func, valueKey?: string | number) => IObject;
      isIsoDate: (dateString: string) => boolean;
      deepCopy: (obj: any) => any;
      debounce: (fn: (...args: any) => void, wait?: number, immediate?: boolean) => void;
      hasPermission: (module: PermissionModule, powers: string | string[], mode?: 'every' | 'some' | 'not') => boolean;
      weekDay: (index: number) => string;
      parseStringToArray: (string: string) => string[];
      stringSplice: (string: string | number, start: number, delCount: number, newSubStr: string) => string;
      parseSeconds: (seconds: number) => { hour: string; minute: string; second: string; toString: () => string };
      isMine: (userId: any, userType: any) => boolean;
      digitUppercase: (digit: number) => string;
      getLocation: () => Promise<Coordinates>;
    };
    $helper: any;
    $speech: {
      speak: (text: string, options?: any) => void;
      setEnabled: (enabled: boolean) => void;
      getSettings: () => any;
      updateSettings: (settings: any) => void;
      pause: () => void;
      resume: () => void;
      stop: () => void;
      speakSuccess: (message?: string) => void;
      speakError: (message?: string) => void;
      speakWarning: (message?: string) => void;
      speakPageTitle: () => void;
      isSupported: () => boolean;
    };
    // 语音播报方法（如果使用了混入）
    $speak?: (text: string, options?: any) => void;
    $speakSuccess?: (message?: string) => void;
    $speakError?: (message?: string) => void;
    $speakWarning?: (message?: string) => void;
    $speakPageTitle?: () => void;
  }
}
