<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { IQuestionCatalog } from '@/service/question_catalog';
import { IQuestionSet } from '@/service/question_set';
import { UserViewQuestionService, IQuestion, QuestionTypes } from '@/service/question';
import { AnswerSetService, IAnswerSet } from '@/service/answer_set';
import { IAnswer, AnswerService, UserOwnAnswer } from '@/service/answer';

@Component({
  components: {},
})
export default class StudentAnswerSetDetail extends Vue {
  @Prop({ type: Number }) questionSetId!: number;
  @Prop({ type: Number }) answerSetId!: number;
  @Prop({ type: Boolean }) isOwn!: boolean;

  answerSet: IAnswerSet = { state: 'todo' };
  answers: IAnswer[] = [];
  loading: boolean = false;

  get QuestionTypes() {
    return QuestionTypes;
  }
  get typeMap() {
    return {
      [QuestionTypes.single]: { text: '单选题', value: QuestionTypes.single },
      [QuestionTypes.multiple]: { text: '多选题', value: QuestionTypes.multiple },
      [QuestionTypes.fill]: { text: '填空题', value: QuestionTypes.fill },
    };
  }

  @Watch('answerSetId', { immediate: true })
  onSetChange() {
    if (this.answerSetId) {
      if (this.isOwn) {
        this.fetchOwnAnswerSet();
      } else {
        this.fetchTeacherAnswerSet();
      }
    }
  }

  async fetchOwnAnswerSet() {
    const { data } = await AnswerSetService.UserOwn.find(this.answerSetId);
    this.answerSet = data;
    this.answers = (this.answerSet.answers || []).map(AnswerService.UserOwn.getProcessedAnswer);
  }
  async fetchTeacherAnswerSet() {
    const { data } = await AnswerSetService.UserView.find({
      questionSetId: this.questionSetId,
      id: this.answerSetId,
    });
    this.answerSet = data;
    this.answers = (this.answerSet.answers || []).map(AnswerService.UserOwn.getProcessedAnswer);
  }
  getOptionIndexKey(index: number) {
    return 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.charAt(index);
  }
}
</script>

<template lang="pug">
.question-container
  .module
    Empty.empty(
      type="table"
      desc="暂无试卷信息"
      v-if="answers.length === 0 && !loading")

    transition-group(name="questions" tag="div" class="questions" v-loading="loading")
      .question(
        v-for="(answer, index) in answers"
        :key="answer.id")
        .row-cell
          //- 题目名称
          .title {{ answer.question.position }}. {{ typeMap[answer.question.type].text }}
          .ck-content.input.content(v-html="answer.question.title")
        //- 单选题
        .options(v-if="answer.question.type === QuestionTypes.single")
          .option(v-for="(option, optionIndex) in answer.question.choices.options" :key="option.key")
            .row-cell
              .title 选项 {{ getOptionIndexKey(optionIndex) }}
              .input.content {{ option.value }}
          .option.answers(v-if="answer.value")
            .row-cell
              .title.answer-title(:class="{ 'text-danger': answer.isError }")
                | 提交答案
              .answer {{ answer.myValue }}
          .option.answers(v-if="answer.answer_meta")
            .row-cell
              .title.text-success 正确答案
              .answer {{ answer.rightValue }}
        //- 多选题
        .options(v-else-if="answer.question.type === QuestionTypes.multiple")
          .option(v-for="(option, optionIndex) in answer.question.choices.options" :key="option.key")
            .row-cell
              .title 选项 {{ getOptionIndexKey(optionIndex) }}
              .input.content {{ option.value }}
          .option.answers(v-if="answer.value")
            .row-cell
              .title.answer-title(:class="{ 'text-danger': answer.isError }")
                | 提交答案
              .answers {{ answer.myValue }}
          .option.answers(v-if="answer.answer_meta")
            .row-cell
              .title.text-success 正确答案
              .answer {{ answer.rightValue }}
        //- 填空题
        .options(v-else-if="answer.question.type === QuestionTypes.fill")
          .option.answers(v-if="answer.value")
            .row-cell
              .title.answer-title(:class="{ 'text-danger': answer.isError }")
                | 提交答案
              .answer {{ answer.value }}
          .option.answers(v-if="answer.answer_meta")
            .row-cell
              .title.text-success 正确答案
              .answer {{ answer.rightValue }}
</template>

<style lang="stylus" scoped>
.question-container
  .module
    padding 16px 0
  .questions
    min-height 100px
    .input
      outline none
      border none
      background transparent
      color rgba(56, 56, 56, 1)
      font-weight 400
      font-size 14px
      line-height 20px
    .row-cell
      display flex
      align-items flex-start
      padding 14px 0
      .title
        flex-shrink 0
        width 120px
        color #808080
      .content
        width 100%
        color #383838
        padding 0 16px
      .my-check
        font-size 20px
        color #75C940
      .answers
        .answer
          margin-right 10px
      .answer-title
        color #3DA8F5
    .question
      margin-bottom 20px
      padding 2px 16px 2px
      border-radius 4px
      background rgba(250, 250, 250, 1)
      position relative
      .option
        margin-bottom 12px
        padding 0 16px
        border 1px solid rgba(232, 232, 232, 1)
        border-radius 4px
        background rgba(255, 255, 255, 1)
        background #fff
        position relative
        .content
          padding-left 0px
</style>
