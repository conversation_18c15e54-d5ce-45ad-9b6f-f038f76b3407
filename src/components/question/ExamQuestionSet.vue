<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { IQuestionSet, IExamCatalog } from '@/service/question_set';
import { IQuestion, QuestionTypes } from '@/service/question';
import ObjectBlock from '../ObjectBlock.vue';
import QuestionItem from './QuestionItem.vue';

@Component({
  components: {
    ObjectBlock,
    QuestionItem,
  },
})
export default class ExamQuestionSet extends Vue {
  @Prop({ type: Object, default: () => ({}) }) questionSet!: IQuestionSet;
  @Prop({ type: Array, default: () => [] }) questions!: IQuestion[];

  activeCatalog: IExamCatalog | null = null;

  get QuestionTypes() {
    return QuestionTypes;
  }
  get questionMap() {
    return this.$utils.objectify(this.questions || [], 'id');
  }
  get catalogs() {
    return this.questionSet.meta ? this.questionSet.meta.catalogs || [] : [];
  }
  get activeQuestions() {
    const questions = (this.activeCatalog && this.activeCatalog.questions) || [];
    return questions.map((o: any) => this.questionMap[o.id]);
  }

  @Watch('questionSet.id', { immediate: true })
  onSetChange() {
    this.activeCatalog = this.catalogs[0];
  }
}
</script>

<template lang="pug">
.exam-question-set-detail
  Empty(desc="无试卷信息" v-if="!(questionSet && questionSet.id)")
  template(v-else)
    Panel.catalogs-side(title="题型")
      .panel-content
        ObjectBlock(
          v-for="(c, i) in catalogs"
          :key="i"
          :showActions="false"
          :title="c.name"
          :desc="`总分：${c.total_score}分，共 ${c.questions.length} 题`"
          :active="activeCatalog === c"
          @click="activeCatalog = c")
    .catalog-detail
      Panel.catalog-panel(
        :title="`${activeCatalog.name} · 总分 ${activeCatalog.total_score} 分 · 共 ${activeCatalog.questions.length} 题`")
        .body
          .ck-content(v-html="activeCatalog.body")
          Attachments(:attachments="activeCatalog.attachments.files")
      Panel.full-height(:title="`题目列表 · ${activeQuestions.length}`")
        .actions(slot="actions")
          slot(name="actions")
        .panel-content
          Empty(v-if="activeQuestions.length === 0" desc="未添加题目")
          QuestionItem(
            v-for="(q, i) in activeQuestions"
            :key="i"
            :question="q"
            :index="i")
</template>

<style lang="stylus" scoped>
.exam-question-set-detail
  height 100%
  position relative
  padding-left 300px
  .full-height
    height 100%
  .catalogs-side
    width 280px
    position absolute
    top 0
    left 0
    height 100%
  .catalog-detail
    height 100%
    overflow auto
    .catalog-panel
      margin-bottom 20px
      .body
        padding 16px 0
        .ck-content
          margin-bottom 16px
    .panel-content
      padding 16px 0
      .body
        margin-bottom 20px
        padding 16px
        background #f4f4f4
</style>
