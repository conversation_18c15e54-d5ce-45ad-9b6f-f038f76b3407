<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { IQuestionSet } from '@/service/question_set';
import { UserViewQuestionService, IQuestion, QuestionTypes, IQuestionAna, QuestionTypeMap } from '@/service/question';
import <PERSON><PERSON><PERSON> from '@/components/statistic/CheckChart.vue';

@Component({
  components: {
    CheckChart,
  },
})
export default class QuestionSetDetail extends Vue {
  @Prop({ type: Object, default: () => ({}) }) questionSet!: IQuestionSet;
  @Prop({ type: Boolean, default: false }) showAnswer!: boolean;

  questions: IQuestion[] = [];
  // 统计
  loading: boolean = false;
  anaData: IQuestionAna = { stat_info: [], type: QuestionTypes.single };
  chartData: any[] = [];
  chartVisible: boolean = false;

  get isTeacher() {
    return this.$store.state.authRole === 'teacher';
  }
  get QuestionTypes() {
    return QuestionTypes;
  }
  get typeMap() {
    return QuestionTypeMap;
  }

  @Watch('questionSet.id', { immediate: true })
  onSetChange() {
    if (this.questionSet.id) {
      this.fetchQuestions();
    }
  }

  // 获取所有题目
  async fetchQuestions() {
    this.loading = true;
    try {
      const { data } = await UserViewQuestionService.fetch(this.questionSet.id!, {
        page: 1,
        per_page: 1000,
        q: {
          s: ['position asc'],
        },
      });
      this.questions = data.questions;
      this.loading = false;
    } catch (error) {
      this.loading = false;
    }
  }

  getOptionIndexKey(index: number) {
    return 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.charAt(index);
  }
  getAnswerKey(answer: string, question: IQuestion) {
    const index = (question.choices!.options || []).findIndex(o => o.key === (answer || '').toString());
    return this.getOptionIndexKey(index);
  }
  showQuestionAna(question: IQuestion) {
    UserViewQuestionService.findAna(this.questionSet.id!, question.id!).then(({ data }) => {
      this.anaData = data;
      if (data.type === QuestionTypes.fill) {
        const formatedData = Object.entries(data.stat_info).map(o => ({
          key: o[0],
          value: o[0],
          count: o[1],
        }));
        this.chartData = this.getAnaChartData(formatedData);
      } else {
        this.chartData = this.getAnaChartData(data.stat_info);
      }
      this.chartVisible = true;
    });
  }
  getAnaChartData(statInfo: any) {
    if (statInfo.length) {
      const total = statInfo.reduce((sum: number, o: any) => sum + o.count, 0);
      return statInfo.map((o: any) => ({
        name: o.value,
        count: o.count,
        percent: Math.round((o.count / total) * 100),
      }));
    }
    return [];
  }
  getOptionAnswerKeys(question: IQuestion) {
    const { answer_meta } = question;
    const value = answer_meta ? answer_meta.value || [] : [];
    if (typeof value === 'string') return value.split(',');
    if (value instanceof Array) return value;
    return [];
  }
}
</script>

<template lang="pug">
.question-container
  .module
    Empty.empty(
      type="table"
      desc="暂无试卷信息"
      v-if="questions.length === 0 && !loading")

    transition-group(name="questions" tag="div" class="questions" v-loading="loading")
      .question(
        v-for="(question, index) in questions"
        :key="question.id")
        .row-cell
          //- 题目名称
          .title {{ question.position }}. {{ typeMap[question.type].text }}
          .input.ck-content(v-html="question.title")
          .actions(v-if="isTeacher")
            TextButton(icon="pie-chart" @click="showQuestionAna(question)")
              | 答题统计
        //- 题目选项
        .question-content(v-if="question.type === QuestionTypes.fill && showAnswer")
          .option
            .row-cell
              .title.text-success 正确答案
              .input {{ question.answer_meta.value }}

        .question-content(v-if="question.type === QuestionTypes.essay && showAnswer")
          .option
            .row-cell
              .title.text-success 正确答案
              .input {{ question.answer_meta.value }}

        .question-content(v-else-if="question.type === QuestionTypes.single")
          .option(v-for="(option, optionIndex) in question.choices.options" :key="option.key")
            .row-cell
              .title 选项 {{ getOptionIndexKey(optionIndex) }}
              .input.content {{ option.value }}
          .option(v-if="(question.choices.options || []).length > 0 && showAnswer")
            .row-cell
              .title.text-success 正确答案
              .answers
                span.answer
                  | {{ getAnswerKey(question.answer_meta.value, question) }}

        .question-content(v-else-if="question.type === QuestionTypes.multiple")
          .option(v-for="(option, optionIndex) in question.choices.options" :key="option.key")
            .row-cell
              .title 选项 {{ getOptionIndexKey(optionIndex) }}
              .input.content {{ option.value }}
          .option(v-if="(question.choices.options || []).length > 0 && showAnswer")
            .row-cell
              .title.text-success 正确答案
              .answers
                span.answer(v-for="answer in getOptionAnswerKeys(question)" :key="answer")
                  | {{ getAnswerKey(answer, question) }}

  a-modal(
    v-model="chartVisible"
    :footer="null")
    .question-chart
      .title.ck-content(v-html="anaData.title")
      CheckChart(:data="chartData" unit="人" v-if="chartData.length")
      Empty(v-else desc="暂无统计数据")
</template>

<style lang="stylus" scoped>
.question-container
  .module
    padding 16px 0
  .questions
    min-height 100px
    .input
      outline none
      border none
      background transparent
      color rgba(56, 56, 56, 1)
      font-weight 400
      font-size 14px
      line-height 20px
    .row-cell
      display flex
      align-items flex-start
      padding 14px 0
      .actions
        flex-shrink 0
      .title
        flex-shrink 0
        width 120px
        color #808080
      .content
        width 100%
        color #383838
        padding 0 16px
      .answers
        .answer
          margin-left 16px
    .question
      margin-bottom 20px
      padding 2px 16px 2px
      border-radius 4px
      background rgba(250, 250, 250, 1)
      position relative
      .option
        margin-bottom 12px
        padding 0 16px
        border 1px solid rgba(232, 232, 232, 1)
        border-radius 4px
        background rgba(255, 255, 255, 1)
        background #fff
        position relative
</style>
