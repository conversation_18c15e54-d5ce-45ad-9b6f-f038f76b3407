<script lang="ts">
/**
 * 试卷答题
 * answer.value 为 string 类型， 多个答案，是要数组 toString
 */
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { AnswerSetService, IAnswerSet } from '@/service/answer_set';
import { QuestionTypes } from '@/service/question';
import { IAnswer, AnswerService } from '@/service/answer';

@Component({
  components: {},
})
export default class SubmitAnswerSet extends Vue {
  @Prop({ type: Object, default: () => ({ answers: [] }) }) answerSet!: IAnswerSet;
  @Prop({ type: Boolean, default: false }) loading!: boolean;
  @Prop({ type: String, default: '' }) title!: string;

  answers: IAnswer[] = [];

  @Watch('answerSet', { deep: true, immediate: true })
  onSetChange() {
    if (this.answerSet.id) {
      this.answers = (this.answerSet.answers || []).map(AnswerService.UserOwn.getProcessedAnswer);
    }
  }

  get QuestionTypes() {
    return QuestionTypes;
  }
  get disabled() {
    return this.answers.some(o => !o.value);
  }

  async submit() {
    if (this.disabled || this.loading) return;
    if (this.answers.every(o => !!o.value)) {
      this.$emit('publish', {
        id: this.answerSet.id,
        state: 'done',
        answers_attributes: this.answers.map(a => ({ id: Number(a.id), value: a.value.toString()!, meta: {} })),
      });
    } else {
      this.$message.warning('请完成试卷后提交');
    }
  }
}
</script>

<template lang="pug">
Panel.submit-answer-set(:title="title" :bordered="true")
  .questions
    .type-question(v-for="(answer, index) in answers" :key="answer.id")
      .question-content
        .title {{ index + 1 }}.
        .ck-content(v-html="answer.question.title")
      .question-submit(v-if="answer.question.type === QuestionTypes.single")
        a-radio-group.answers(v-model="answer.value")
          a-radio.radio-item(
            v-for="option in answer.question.choices.options"
            :key="option.key"
            :value="option.key")
            | {{ option.value }}
      .question-submit(v-if="answer.question.type === QuestionTypes.multiple")
        a-checkbox-group.answers(v-model="answer.value")
          a-checkbox.radio-item(
            v-for="option in answer.question.choices.options"
            :key="option.key"
            :value="option.key")
            | {{ option.value }}
      .question-submit(v-if="answer.question.type === QuestionTypes.fill")
        a-input(type="textarea" v-model.trim="answer.value" placeholder="请输入你的答案")
      .question-submit(v-if="answer.question.type === QuestionTypes.essay")
        a-input(type="textarea" v-model.trim="answer.value" placeholder="请输入你的答案")
  template(#footer)
    .footer-actions
      a-button(
        type="primary"
        size="large"
        :disabled="disabled"
        :loading="loading"
        @click="submit")
        | 提交
</template>

<style lang="stylus" scoped>
.submit-answer-set
  height 100%
  .panel-header
    padding 0
  .questions
    width 800px
    margin 16px auto
    .question-content
      display flex
      align-items flex-start
      font-size 14px
      font-weight 500
      color rgba(38,38,38,0.85)
      line-height 20px
      margin-bottom 18px
      .title
        flex-shrink 0
        display inline-block
    .question-submit
      border-bottom 1px solid #E8E8E8
      padding-bottom 20px
      margin-bottom 20px
      &:last-child
        border-bottom none
      .answers
        border 1px solid #E8E8E8
        border-bottom none
        border-radius 4px
        width 100%
        .radio-item
          display block
          line-height 20px
          padding 14px
          border-bottom 1px solid #E8E8E8
          margin-left 0
  .footer-actions
    text-align right
    width 100%
</style>
