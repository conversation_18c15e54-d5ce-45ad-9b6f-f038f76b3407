<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { IQuestion, QuestionTypes } from '@/service/question';

@Component({
  components: {},
})
export default class Question extends Vue {
  @Prop({ type: Object, default: () => ({}) }) question!: IQuestion;
  @Prop({ type: Number, default: 0 }) index?: number;

  get QuestionTypes() {
    return QuestionTypes;
  }
  get innnerQuestion() {
    return Object.assign(this.question, { answer_meta: this.question.answer_meta || { value: '' } });
  }

  getOptionIndexKey(index: number) {
    return 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.charAt(index);
  }
  getAnswerKey(answer: string, question: IQuestion) {
    const index = (question.choices!.options || []).findIndex(o => o.key === (answer || '').toString());
    return this.getOptionIndexKey(index);
  }
  getOptionAnswerKeys(question: IQuestion) {
    const value = question.answer_meta ? question.answer_meta.value || [] : [];
    return String(value).split(',');
  }
}
</script>

<template lang="pug">
.question
  .title
    .index {{ index + 1 }}.
    .ck-content(v-html="innnerQuestion.title")
  //- 填空题
  .content(v-if="innnerQuestion.type === QuestionTypes.fill")
    .row-cell
      .label.text-success 正确答案
      .answer {{ innnerQuestion.answer_meta.value }}
  //- 简答题
  .content(v-if="innnerQuestion.type === QuestionTypes.essay")
    .row-cell
      .label.text-success 正确答案
      .answer {{ innnerQuestion.answer_meta.value }}
    Attachments(:attachments="innnerQuestion.attachments ? innnerQuestion.attachments.files : []")
  //- 单选题
  .content(v-else-if="innnerQuestion.type === QuestionTypes.single")
    .row-cell(v-for="(option, optionIndex) in (innnerQuestion.choices.options || [])" :key="option.key")
      .label 选项 {{ getOptionIndexKey(optionIndex) }}
      .answer {{ option.value }}
    .row-cell(v-if="(innnerQuestion.choices.options || []).length > 0")
      .label.text-success 正确答案
      .answer {{ getAnswerKey(innnerQuestion.answer_meta.value, innnerQuestion) }}
  //- 多选题
  .content(v-else-if="innnerQuestion.type === QuestionTypes.multiple")
    .row-cell(v-for="(option, optionIndex) in innnerQuestion.choices.options || []" :key="option.key")
      .label 选项 {{ getOptionIndexKey(optionIndex) }}
      .answer {{ option.value }}
    .row-cell(v-if="(innnerQuestion.choices.options || []).length > 0")
      .label.text-success 正确答案
      .answer
        span(v-for="answer in getOptionAnswerKeys(innnerQuestion)" :key="answer")
          | {{ getAnswerKey(answer, innnerQuestion) }}
</template>

<style lang="stylus" scoped>
.question
  .title
    margin-bottom 10px
    display flex
    align-items flex-start
    line-height 24px
    font-size 16px
    .index
      margin-right 10px
  .content
    margin-bottom 20px
    .row-cell
      display flex
      align-items flex-start
      margin-bottom 6px
      padding 8px 10px
      border 1px solid #eee
      border-radius 4px
      .label
        flex-shrink 0
        width 120px
        color #808080
      .answer
        width 100%
        color #383838
        span
          margin-right 8px
</style>
