<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import Comments from './Comments.vue';
import CommentTree from './CommentTree.vue';
import CommentSender from './CommentSender.vue';
import commentStore from '@/store/modules/comm/comment.store';

@Component({
  components: {
    CommentSender,
    Comments,
    CommentTree,
  },
})
export default class CommentPanel extends Vue {
  @Prop({ type: Number }) commentableId!: number;
  @Prop({ type: String }) commentableType!: string;
  @Prop({ type: String, default: 'List' }) commentType!: string; // List, Tree
  @Prop({ type: Boolean, default: true }) showHeader!: boolean;
  @Prop({ type: Boolean, default: false }) disabled!: boolean;
  // tree
  @Prop({ type: String, default: '' }) private indexPath?: string; //如： /teaching/user/topics/1
  @Prop({ type: String, default: '' }) private deletePath?: string; //如： /teaching/user
  @Prop({ type: Number, default: 20 }) private perPage?: number;
  private commentStore: any = {
    records: [],
    totalCount: 0,
    currentPage: 1,
    totalPages: 1,
  };
  load(...args: any) {
    this.$emit('load', ...args);
  }
  // tree
  public mounted() {
    if (this.commentType === 'Tree') {
      this.fetchData();
    }
  }

  public async fetchData(page: number = 1) {
    const params = {
      page,
      per_page: this.perPage,
      parentPath: `${this.indexPath}/${this.commentableId}`,
    };
    const { data } = await commentStore.fetchByParent(params);
    this.commentStore = {
      ...data,
      records: data.comments,
      totalCount: data.total_count,
      currentPage: data.current_page,
      totalPages: data.total_pages,
    };
  }

  public async updateComment(val: any) {
    try {
      await commentStore.update(val);
      this.fetchData();
      this.$message.success('操作成功！');
    } catch (error) {
      this.$message.error('操作失败！');
    }
  }

  public async deleteComment(id: number) {
    try {
      const params = {
        id,
        parentPath: this.deletePath,
      };
      await commentStore.deleteByParent(params);
      this.fetchData();
      this.$emit('refresh');
      this.$message.success('操作成功！');
    } catch (error) {
      this.$message.success('操作成功！');
    }
  }

  // 回复评论
  public async onReply(val: any) {
    try {
      const obj = {
        ...val,
        commentable_id: this.commentableId,
        commentable_type: this.commentableType,
      };
      await commentStore.create(obj);
      this.fetchData();
      this.$message.success('操作成功！');
    } catch (error) {
      this.$message.error('操作失败！');
    }
  }
  // 创建评论刷新数据
  public onRefresh() {
    if (this.commentType === 'Tree') {
      this.fetchData();
      this.$emit('refresh');
    }
  }
}
</script>

<template lang="pug">
.comment-container
  .comment-content(id="commentPanelContent")
    template(v-if="commentType === 'List'")
      Comments(
        :commentableId="commentableId"
        :commentableType="commentableType"
        :showHeader="showHeader"
        :disabled="disabled"
        @load="load")
    template(v-else)
      .comment-label(v-if="showHeader") 全部评论 · {{ commentStore.totalCount }}
      CommentTree(
        :comments.sync="commentStore.records"
        :disabled="disabled"
        @edit="updateComment"
        @reply="onReply"
        @destroy="deleteComment")
      .pagination(v-if="commentStore.totalPages > 1")
        a-pagination(
          showQuickJumper
          size="large"
          :current="commentStore.currentPage"
          :defaultPageSize="perPage"
          :total="commentStore.totalCount"
          @change="fetchData")
  .comment-footer(v-if="!disabled")
    CommentSender(
      :commentableId="commentableId"
      :commentableType="commentableType"
      commentsContainerId="commentPanelContent"
      @add="onRefresh()")
</template>

<style lang="stylus" scoped>
.comment-container
  position relative
  display flex
  flex-direction column
  width 100%
  height 100%
  .comment-content
    overflow auto
    height 100%
    overflow-anchor auto
    .comment-label
      margin-bottom 8px
      color rgba(51, 51, 51, 1)
      font-weight 500
      font-size 16px
      line-height 20px
    .pagination
      padding 20px 0px
      text-align right
  .comment-footer
    flex-shrink 0
    border-top 1px solid #E6E7EB
</style>
