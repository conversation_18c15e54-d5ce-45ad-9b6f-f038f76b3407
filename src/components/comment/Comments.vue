<template lang="pug">
.comments-container(ref="container")
  a-list(
    class="comments"
    :header="showHeader ? `评论·${store.totalCount}` : null"
    itemLayout="horizontal"
    :dataSource="store.records"
    :emptyText="{ emptyText: '暂无评论' }")
    .load-more(slot="loadMore" v-if="store.currentPage < store.totalPages")
      a-button(
        @click="fetchComments(store.currentPage + 1)"
        :loading="store.loading"
        :disabled="store.loading")
        | {{ store.loading ? '加载中...' : '加载更多' }}
    a-list-item.list-item(slot="renderItem" slot-scope="item, index")
      a-comment.comment
        a-avatar.avatar(:size="28" slot="avatar")
          | {{ item.user_name }}
        .title-action(slot="author" v-if="!disabled")
          .name {{ item.user_name }}
          .actions(v-if="deleteable && item.user_id === $store.state.currentUser.id")
            .action(@click="deleteConfirm(item, index)") 删除
        .datetime(slot="datetime")
          a-tooltip(:title="item.created_at | format('YYYY-MM-DD HH:mm:ss')")
            span {{ $moment(item.created_at).fromNow() }}
        .content(slot="content")
          .text-pre {{ item.body }}
          Attachments(:attachments="(item.attachments || {}).attachments || []")
          Attachments(:attachments="(item.attachments || {}).files || []")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { IComment } from '../../models/comment';
import commentStore from '@/store/modules/comment.store';

@Component
export default class Comments extends Vue {
  @Prop({ type: Number }) commentableId!: number;
  @Prop({ type: String }) commentableType!: string;
  @Prop({ type: Boolean, default: true }) showHeader!: boolean;
  @Prop({ type: Boolean, default: true }) deleteable!: boolean;
  @Prop({ type: Boolean, default: false }) disabled!: boolean;

  get store() {
    return commentStore;
  }

  @Watch('commentableId', { immediate: true })
  onTargetChange() {
    this.fetchComments(1);
  }

  async fetchComments(page: number = 1) {
    if (this.commentableId) {
      const { data } = await this.store.fetchByParent({
        parentId: this.commentableId,
        shouldAppend: true,
        page,
        per_page: 40,
        q: {
          s: ['id dasc'],
        },
      });
      this.$emit('load', data);
      this.scrollToBottom();
    }
  }
  deleteConfirm(record: IComment, index: number) {
    this.$confirm({
      title: '删除动态',
      content: '确定希望永久删除这条动态吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        await this.store.delete(record.id!);
      },
    });
  }
  scrollToBottom() {
    this.$nextTick(() => {
      const container = this.$refs.container as any;
      container.scrollTop = container.scrollHeight;
    });
  }
}
</script>

<style lang="stylus" scoped>
.load-more
  padding 10px
  text-align center

.comments-container
  padding 0px 16px
  height 100%
  overflow auto
  overflow-anchor auto

.list-item
  padding 0

.comment
  position relative
  padding-top 4px
  width 100%
  color rgba(128, 128, 128, 1)
  font-weight 400
  font-size 14px
  line-height 20px
  &:hover
    .actions
      .action
        display inline-block
  .name
    display inline-block
  .actions
    display inline-block
    .action
      display none
      margin-left 14px
      color rgba(128, 128, 128, 0.9)
      font-size 13px
      cursor pointer
      &:hover
        color #3DA8F5
  .title-action
    height 28px
    color rgba(56, 56, 56, 1)
    font-weight 500
    font-size 14px
    line-height 28px
  .avatar
    border-radius 50%
    background-color #58A8EF
  .content
    margin 8px 0
    font-size 14px
  .datetime
    position absolute
    top 0
    right 0
    color rgba(128, 128, 128, 1)
    font-weight 400
    font-size 14px
    line-height 28px
</style>
