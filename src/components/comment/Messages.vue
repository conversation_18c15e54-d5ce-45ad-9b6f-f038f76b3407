<script lang="ts">
/**
 * 评论 - 即时通信样式组件
 */
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { IComment } from '@/service/comment';
import AttachmentFile from '@/components/global/AttachmentFile.vue';
import { debounce } from 'lodash';

@Component({
  components: {
    AttachmentFile,
  },
})
export default class CommentMessages extends Vue {
  @Prop({ type: Array, default: () => [], required: true }) comments!: IComment[];
  @Prop({ type: Boolean, default: false }) topLoading!: boolean;
  @Prop({ type: Boolean, default: false }) readonly!: boolean;

  cachePreScrollHeight: number = 0; // 向上滚动，记录数据刷新之前的滚动高度
  debounceScroll: (e: any) => void = () => {};

  get me() {
    return this.$store.state.currentUser;
  }

  @Watch('comments.length')
  onCommentsChange() {
    this.$nextTick(() => {
      const dom: any = this.$refs.messagesContainer;
      if (dom) {
        dom.scrollTop = dom.scrollHeight - this.cachePreScrollHeight;
      }
    });
  }

  mounted() {
    this.debounceScroll = debounce(this.onMessagesScroll, 1000, {
      trailing: true,
    });
  }
  reply(comment: IComment) {
    this.$emit('reply', comment);
  }
  onMessagesScroll(e: any) {
    this.$emit('scroll', e);
    this.cachePreScrollHeight = e.target.scrollHeight;
    if (e.target.scrollTop < 100) {
      this.onTop(e.target);
    } else if (e.target.scrollHeight - e.target.clientHeight - e.target.scrollTop < 100) {
      this.onBottom(e.target);
    }
  }
  onTop(element: any) {
    this.$emit('top', element);
  }
  onBottom(element: any) {
    this.$emit('bottom', element);
  }
  scrollToTop() {
    const dom: any = this.$refs.messagesContainer;
    if (dom) {
      dom.scrollTop = 0;
    }
  }
  scrollToBottom() {
    const dom: any = this.$refs.messagesContainer;
    if (dom) {
      dom.scrollTop = dom.scrollHeight;
    }
  }
}
</script>

<template lang="pug">
.im-comments-container(@scroll="debounceScroll" ref="messagesContainer")
  .top-loading(v-if="topLoading")
    a-spin(size="small")
  .message(
    v-for="comment in comments"
    :key="comment.id"
    :class="{ 'my-message': comment.user_id === me.id }")
    .message-avatar
      a-avatar.avatar(:size="28")
        | {{ comment.user_name.charAt(0) }}
    .message-content
      .message-date {{ comment.created_at | format('M月D日 HH:mm') }}
      .message-sender
        span {{ comment.user_name  }}
        span.meta(v-if="comment.user_type === 'Teacher'")
          | (教师)
        span.meta {{ comment.created_at | format('M月D日 HH:mm') }}
      .message-body
        .reference-box(v-if="comment.parent_id")
          .reference-message
            div {{ comment.reply_user_name }}
            pre.pre-text(v-if="comment.reply_body")
              | {{ comment.reply_body }}
        .body-box(v-if="comment.body")
          pre.pre-text {{ comment.body }}
        .file-box(v-if="comment.files && comment.files.length")
          AttachmentFile.file-item(
            v-for="file in comment.files"
            :key="file.fileKey"
            :attachment="file"
            :display="true"
            :imageMaxHeight="200")
        .reply-box(v-if="comment.user_id !== me.id && !readonly" @click="reply(comment)")
          | 回复
</template>

<style lang="stylus" scoped>
$messageMaxWidth = 600px
$messageMinWidth = 400px

.im-comments-container
  position relative
  padding 10px 20px
  height 100%
  overflow auto
  .top-loading
    text-align center
  .message
    margin-bottom 20px
    display flex
    align-items flex-start
    &:last-child
      margin-bottom 0
    .message-avatar
      order 0
      flex-shrink 0
      .avatar
        background-color #3DA8F5
    .message-content
      order 1
      padding 0 8px
      max-width $messageMaxWidth
      min-width $messageMinWidth
      .message-sender
        height 20px
        line-height 20px
        font-size 14px
        font-weight 500
        color #383838
        margin-bottom 8px
        .meta
          color #A6A6A6
          margin-left 4px
      .message-date
        color #A6A6A6
        font-size 12px
        text-align right
        display none
      .message-body
        background #FFFFFF
        border-radius 0px 4px 4px 4px
        border 1px solid #F5F5F5
        pre.pre-text
          white-space pre-wrap
          margin 0
        .body-box
          font-size 14px
          font-weight 400
          color #383838
          line-height 20px
          padding 10px 12px
        .file-box
          padding 10px 12px
          border-top 1px solid #E8E8E8
          box-sizing border-box
          &:first-child
            border-top none
          .file-item
            cursor pointer
        .reference-box
          font-size 14px
          font-weight 400
          color #808080
          line-height 20px
          padding 10px 12px
          border-bottom 1px solid #E8E8E8
          .reference-message
            border-left 2px solid rgba(0,0,0,0.1)
            padding-left 6px
        .reply-box
          padding 8px 12px
          text-align center
          cursor pointer
          border-top 1px solid #E8E8E8
          font-size 14px
          color #383838
          &:hover
            color #3DA8F5
          &:active
            background #f5f5f5

  .my-message
    display flex
    justify-content flex-end
    .message-avatar
      order 1
    .message-content
      order 0
      .message-sender
        display none
      .message-date
        display block
      .message-body
        background rgba(237,247,255,1)
        border-radius 4px 0px 4px 4px
        border 1px solid rgba(61,168,245,0.2)
</style>
