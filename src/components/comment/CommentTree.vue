<template lang="pug">
a-list(itemLayout="horizontal" :dataSource="comments")
  a-list-item(slot="renderItem" slot-scope="item, index")
    a-list-item-meta
      template(slot="title")
        strong(v-if="item.user_name") {{ item.user_name }}
        span.name-type(v-if="isMyComment(item)") (我)
      template(slot="description")
        .content
          template(v-if="item.reply_user_name")
            span.reply 回复
            span.nickname {{ item.reply_user_name }}
            span.nickname(v-if="isMyComment(item)") (我)
          .text-pre.comment-body {{ item.body }}
          template(v-if="item.files && item.files.length > 0")
            Attachments(
              :attachments="item.files"
              style="margin-top: 10px")
        .operation-box
          a-tooltip(:title="$moment(item.created_at).format('YYYY/MM/DD HH:mm:ss')")
            span {{ $moment(item.created_at).fromNow() }}
          template(v-if="!disabled")
            a-divider(type="vertical")
            a-button(@click="() => { comment.parent_id = ''}" v-if="comment.parent_id === item.id") 取消回复
            a-button(@click="onReply(item.id)" v-else) 回复
          //- a-divider(type="vertical")
          //- a-button(@click="changeLike(item)") {{ item.like ? '已点赞' : '点赞' }}
          .undo(v-if="!disabled && item.operations && item.operations.length > 0")
            template(v-if="item.operations.includes('update')")
              a-button(@click="onEdit(item)") 修改
              a-divider(type="vertical")
            template(v-if="item.operations.includes('destroy')")
              a-button(@click="onDelete(item.id)") 删除
        template(v-if="comment.parent_id === item.id || comment.id === item.id")
          Attachments(
            :attachments="comment.files"
            style="margin-top: 12px"
            @change="onSuccess")
          .submit-box(v-if="!disabled")
            .attachment
              FileUploader(
                ref="uploader"
                :value="comment.files"
                :showList="false"
                @change="onSuccess")
                a-button.trigger(shape="circle" size="large" icon="paper-clip")
            a-input(v-model="comment.body" size="large" placeholder="说点什么吧" auto-focus)
            a-button.submit-btn(
              type="primary"
              size="large"
              :disabled="!comment.body && comment.files.length === 0"
              @click="onSubmit(comment)")
              | {{ comment.id ? '保存' : '提交回复' }}
        .reply-list(v-if="item.comments && item.comments.length")
          ReplyList(
            :comments="item.comments"
            :disabled="disabled"
            @edit="onSubmit"
            @reply="reply"
            @destroy="destroy")
      template(slot="avatar")
        a-avatar.avatar(:size="32")
          | {{ item.user_name && item.user_name.charAt(0) }}
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import commentStore from '@/store/modules/comm/comment.store';
// import actionStore from '@/store/modules/action.store';
import store from '@/store';
import { IComment } from '../../models/comment';

@Component({
  components: {
    ReplyList: () => import('./CommentTree.vue'),
  },
})
export default class CommentTree extends Vue {
  @Prop({ type: Array, default: () => [] }) private comments?: any;
  @Prop({ type: Boolean, default: false }) private disabled?: boolean;

  private uploadState: boolean = true;
  private comment: any = {
    body: '',
    attachments: {},
    files: [],
  };
  get userInfo() {
    return store.state.currentUser || {};
  }

  public changeLike(val: any) {
    const obj: any = {
      action_type: 'like',
      target_type: 'Comment',
      target_id: val.id,
    };
    if (val.like) {
      this.offLike(obj);
    } else {
      this.onLike(obj);
    }
  }

  public onEdit(val: any) {
    this.comment = {
      ...val,
    };
  }

  public async onSubmit(val: any) {
    if (!this.uploadState) {
      this.$message.warning(this.$helper.fileVerifyMessage());
      return;
    }
    if (val.id) {
      await this.$emit('edit', val);
    } else {
      await this.$emit('reply', val);
    }
    this.comment = {};
  }

  public onDelete(id: number) {
    this.$confirm({
      title: '确定要删除此评论吗？',
      okText: '确定',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        this.$emit('destroy', id);
      },
      onCancel: () => {},
    });
  }

  // public async delete(id: number) {
  //   try {
  //     await commentStore.delete(id);
  //     this.$emit('refresh');
  //     this.$message.success('操作成功！');
  //   } catch (error) {
  //     this.$message.success('操作成功！');
  //   }
  // }

  public onReply(id: number) {
    this.comment = {
      parent_id: id,
      body: '',
      attachments: {},
      files: [],
    };
  }

  // 递归组件事件
  public reply(val: any) {
    this.$emit('reply', val);
  }

  public destroy(id: any) {
    this.$emit('destroy', id);
  }

  public onRefresh() {
    this.$emit('refresh');
  }

  // 点赞
  public async onLike(val: any) {
    try {
      // await actionStore.create(val);
      this.onRefresh();
      this.$message.success('操作成功！');
    } catch (error) {
      this.$message.error('操作失败！');
    }
  }

  // 取消点赞
  public async offLike(val: any) {
    try {
      // await actionStore.cancel(val);
      this.onRefresh();
      this.$message.success('操作成功！');
    } catch (error) {
      this.$message.error('操作失败！');
    }
  }
  // 附件
  public onSuccess(fileItems: any[], statusFiles: any, allSettled: boolean) {
    Object.assign(this.comment, {
      files: fileItems,
      attachments: {
        files: statusFiles.done || [],
      },
    });
    this.uploadState = allSettled;
  }
  isMyComment(item: IComment) {
    const { user_id, user_type, reply_user_id, reply_user_type } = item;
    return this.$utils.isMine(user_id, user_type) || this.$utils.isMine(reply_user_id, reply_user_type);
  }
}
</script>

<style lang="stylus" scoped>
.ant-list-item
  padding 20px 0px
  strong
    font-size 16px
    line-height 20px
  .name-type
    margin-left 4px
    color #999999
    font-size 16px
    line-height 20px
  .content
    color #383838
    font-weight 400
    font-size 14px
    line-height 20px
    .comment-body
      margin-top 10px
    .reply
      color #b2b2b2
    .nickname
      margin-left 4px
      color #333
      font-weight 500
  .operation-box
    margin-top 10px
    color #B2B2B2
    font-weight 500
    font-size 14px
    line-height 20px
    button
      padding 0px
      height 20px
      border none
      color #B2B2B2
      font-weight 500
      font-size 14px
      line-height 20px
    .undo
      float right
  .reply-list
    margin 20px 0px -20px
    border-top 1px #e6e6e6 solid
  .attachments
    width 100%
    background #fff
  .submit-box
    display flex
    align-items center
    padding 12px 0px 0px
    width 100%
    .ant-input
      margin 0px 12px
    .trigger
      flex-shrink 0
    .submit-btn
      width 100px

.avatar
  background-color #58A8EF
</style>
