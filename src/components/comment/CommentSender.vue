<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import commentStore from '@/store/modules/comment.store';
import emojiData from './emoji';
import { IFile } from '@/models/file';

@Component
export default class CommentSender extends Vue {
  private content: string = '';
  private attachments: any[] = [];
  private emojiVisible: boolean = false;
  private isFileAllSettled: boolean = true;

  @Prop({ type: Number }) commentableId!: number;
  @Prop({ type: String }) commentableType!: string;
  @Prop({ type: Number }) parentId!: number;
  @Prop({ type: String }) size!: string;
  @Prop({ type: String, default: '添加评论' }) placeholder!: string;
  @Prop({ type: String }) commentsContainerId!: string;
  @Prop({ type: Boolean, default: false }) useCdn!: boolean;

  get store() {
    return commentStore;
  }
  get emojis() {
    return emojiData.frequently;
  }

  isCommentable() {
    return (
      this.commentableId && this.commentableType && (this.content || (this.attachments.length && this.isFileAllSettled))
    );
  }
  handleKeypress(e: KeyboardEvent) {
    if (e.ctrlKey && e.key === 'Enter') {
      this.insertChar('\n');
      return true;
    }
    if (e.key === 'Enter') {
      e.preventDefault();
      this.send();
      return false;
    }
    return true;
  }
  async send() {
    if (!this.isFileAllSettled) {
      this.$message.warning('存在没有上传完成的附件');
      return;
    }
    if (this.isCommentable()) {
      this.$emit('beforeSend');
      const { data } = await commentStore.create({
        title: '评论',
        body: this.content,
        subject: '',
        parent_id: this.parentId,
        commentable_id: this.commentableId,
        commentable_type: this.commentableType,
        attachments: {
          files: this.attachments.filter((file: IFile) => file.status === 'done'),
        },
      });
      this.content = '';
      this.attachments = [];
      this.$emit('add', data);
      this.afterAddComment();
    }
  }
  restartUpload(fileItem: any) {
    (this.$refs.uploader as any).start(fileItem);
  }
  insertChar(char: string, callback?: () => any) {
    const textarea = (this.$refs.commentInput as any) as HTMLTextAreaElement;
    const start = textarea.selectionStart;
    this.content = this.content.slice(0, start) + char + this.content.slice(start);
    this.$nextTick(() => {
      textarea.selectionEnd = start + char.length;
      textarea.selectionStart = start + char.length;
    });
    if (callback) {
      callback();
    }
  }
  handlePaste(e: ClipboardEvent) {
    const { items } = e.clipboardData as DataTransfer;
    if (items.length) {
      for (let index = 0; index < items.length; index += 1) {
        const item = items[index];
        if (item.type.includes('image')) {
          const file = item.getAsFile();
          (this.$refs.uploader as any).addFile(file);
        }
      }
    }
  }
  afterAddComment() {
    if (this.commentsContainerId) {
      this.$nextTick(() => {
        const parentDom = document.getElementById(this.commentsContainerId);
        if (parentDom) {
          parentDom.scrollTop = parentDom.scrollHeight;
        }
      });
    }
  }
  focus() {
    (this.$refs.commentInput as any).focus();
  }
}
</script>

<template lang="pug">
.comment-sender
  .comment__wrap
    .comment__input
      .faker {{ content }}
      textarea.textarea(
        ref="commentInput"
        v-model.trip="content"
        :placeholder="placeholder"
        @paste="handlePaste"
        @keypress="handleKeypress")
    Attachments(
      v-model="attachments"
      :showActions="true"
      @restart="restartUpload")
  .comment__toolbar
    .comment__actions
      FileUploader(
        ref="uploader"
        v-model="attachments"
        :useCdn="useCdn"
        :isAllSettled.sync="isFileAllSettled"
        :showList="false")
        button.action
          a-icon(type="paper-clip")
      Popover(v-model="emojiVisible" placement="topLeft" :width="320")
        template(#main)
          .emoji-box
            span.emoji(
              v-for="(emoji, key) in emojis"
              :key="key"
              @click="insertChar(emoji, () => emojiVisible = false)")
              | {{ emoji }}
        .action(@click="emojiVisible = !emojiVisible")
          a-icon(type="smile")
    .comment__trigger
      .tips
        | Enter 发送，Ctrl + Enter 换行
      a-button.trigger(
        :size="size"
        type="primary"
        @click="send"
        :loading="store.loading")
        | 发送
</template>

<style lang="stylus" scoped>
.comment-sender
  padding 4px 16px 8px
  width 100%
  .comment__wrap
    padding 12px 0
    .comment__input
      position relative
      margin 4px 0
      .faker
        visibility hidden
        margin 0
        padding 0
        min-height 20px
        width 100%
        white-space pre-wrap
        word-break break-word
        overflow hidden
      .textarea
        position absolute
        top 0
        left 0
        margin 0
        padding 0
        width 100%
        height 100%
        outline 0 none
        border none
        border-radius 0
        white-space pre-wrap
        word-break break-word
        line-height 20px
        resize none
  .comment__toolbar
    display flex
    justify-content space-between
    align-items center
    .comment__actions
      .action
        display inline-block
        margin-right 2px
        padding 4px
        width 28px
        height 28px
        border-radius 4px
        color #808080
        font-size 20px
        line-height 1
        cursor pointer
        border none
        &:hover
          background-color #e8f5fd
          color #1b9aee
    .comment__trigger
      display flex
      align-items center
      .tips
        color rgba(204, 204, 204, 1)
        font-size 12px
        line-height 20px
      .trigger
        margin-left 12px
        padding-right 20px
        padding-left 20px

.emoji-box
  .emoji
    display inline-block
    padding 6px
    width 36px
    height 36px
    border-radius 4px
    font-size 24px
    line-height 1
    cursor pointer
    &:hover
      background-color #e8f5fd
</style>
