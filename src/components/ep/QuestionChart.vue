<template lang="pug">
.page
  a-card(
    v-for="(item, index) in registerQuestions"
    :key="index"
    size="small"
    style="width: 100%; margin-bottom: 20px;")
    template(slot="title")
      span {{ index + 1 }}、{{ item.title }}
    template(slot="extra" v-if="pageType === 'Index' ")
      a-button(style="padding: 0px; border: 0px; color: #808080;" @click="onShow(item)")
        span 详情数据
        a-icon(type="right")
    template(v-if="item.basePie && item.basePie.length")
      a-row(:gutter="16")
        a-col(:span="12")
          .count-progress
            TargetProgress(
              v-for="ob in item.basePie"
              :key="ob.state"
              :name="ob.state"
              :count="ob.amount"
              unit="人")
        a-col(:span="12")
          G2Pie(
            :charData="item.basePie"
            :id="`Q${index + 1}`"
            :padding="[0, 0, 40, 0]"
            :unit="pageType === 'Index' ? '人' : '次'"
            v-if="item.basePie.length")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import G2Pie from '@/components/ep/G2Pie.vue';
import TargetProgress from '../statistic/TargetProgress.vue';

@Component({
  components: {
    G2Pie,
    TargetProgress,
  },
})
export default class QuestionChart extends Vue {
  @Prop({ type: Object, default: () => {} }) private question?: any;
  @Prop({ type: Array, default: () => [] }) private questions?: any;
  @Prop({ type: String, default: 'Index' }) private pageType?: string; // Index, Show
  get registerQuestions() {
    return Object.keys(this.question).length
      ? (this.questions || []).map((item: any) => ({
          ...item,
          basePie: this.initChartData(item),
        }))
      : [];
  }

  public initChartData(val: any) {
    return (val.options || []).map((item: any) => ({
      state: item.value,
      amount: this.question[val.key] && this.question[val.key][item.key] && this.question[val.key][item.key].number,
      percent: this.question[val.key] && this.question[val.key][item.key] && this.question[val.key][item.key].rate,
    }));
  }

  public onShow(val: any) {
    this.$emit('detail', val);
  }
}
</script>

<style lang="stylus" scoped>
.page
  width 100%
  height 100%
  .count-progress
    margin-top 80px
    display flex
    justify-content flex-end
    padding-left 80px
    margin-right -80px
</style>
