<template lang="pug">
Panel.pannel(:title='title')
  template(slot='actions')
    TextButton(icon='setting', theme='filled', style='margin-left: 16px', @click='visibleModal = true') 设置教师
  AdminTable(:data='teachers', :perPage='1000', :showHeader='true')
    a-table-column(dataIndex='name', title='姓名')
    a-table-column(dataIndex='code', title='职工号')
    a-table-column(dataIndex='department_name', title='所属部门')
    a-table-column(dataIndex='college_name', title='所属学院')

  MainModal(v-model='visibleModal', title='管理教师', :width='1200')
    .modal-row(slot='footer')
      .count
        span 已选择
        span.text-primary(style='margin: 0px 4px') {{ selectedTeacherIds.length || 0 }}
        span 人
      a-button(
        type='primary',
        size='large',
        :loading='activityStore.loading || userSelectorLoading',
        :disabled='activityStore.loading || userSelectorLoading',
        @click='configTeachers'
      ) 确定
    UserSelector(
      ref='memberSelector',
      :userIds='selectedTeacherIds',
      @change='changeSelect',
      @loadingChange='handleUserSelectorLoadingChange'
    )
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import UserSelector from '@/components/hr/UserSelector.vue';
import { adminActivityStore } from '@/store/modules/ep/activity.store';

@Component({
  components: {
    UserSelector,
  },
})
export default class SelectTeacher extends Vue {
  @Prop({ type: String, required: true }) elements!: string; // 需要设置的 teachers 列表属性
  @Prop({ type: String, required: true }) element_ids!: string; // 需要设置的 teacher ids 列表属性
  @Prop({ type: String, required: true }) title!: string; // 标题

  private teachers: any[] = [];
  private selectedTeacherIds: number[] = [];
  private visibleModal: boolean = false;
  userSelectorLoading = false;
  get activityStore() {
    return adminActivityStore || {};
  }

  public mounted() {
    this.fetchData();
  }

  public async fetchData(page: number = 1) {
    const { data } = await adminActivityStore.find(this.$route.params.id);
    this.teachers = data[this.elements] || [];
    this.selectedTeacherIds = data[this.element_ids] || [];
  }

  public async configTeachers() {
    try {
      const obj: any = {
        id: this.$route.params.id,
        [`${this.element_ids}`]: this.selectedTeacherIds,
      };
      await adminActivityStore.update(obj);
      this.fetchData();
      this.visibleModal = false;
      this.$message.success('操作成功');
    } catch (error) {
      this.$message.error('操作失败！');
    }
  }

  public changeSelect(ids: number[]) {
    this.selectedTeacherIds = ids;
  }

  handleUserSelectorLoadingChange(loading: boolean) {
    this.userSelectorLoading = loading;
  }
}
</script>

<style lang="stylus" scoped>
.pannel
  width 100%
  height 100%
</style>
