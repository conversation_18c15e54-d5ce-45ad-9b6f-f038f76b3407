<template lang="pug">
.g2(:id="id")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import G2 from '@antv/g2';

@Component({
  components: {},
})
export default class G2Pie extends Vue {
  @Prop({ type: Array, default: () => [] }) private charData!: any;
  @Prop({ type: String, default: '' }) private id?: string;
  @Prop({ type: Number, default: 600 }) private width?: number;
  @Prop({ type: Number, default: 230 }) private boxHeight?: number;
  @Prop({ type: Boolean, default: true }) private forceFit?: boolean;
  @Prop({ type: Boolean, default: false }) private animate?: boolean;
  @Prop({ type: Number, default: 0.75 }) private radius?: number;
  @Prop({ type: Number, default: 0.6 }) private innerRadius?: number;
  @Prop({ type: Boolean, default: false }) private showTitle?: boolean;
  @Prop({ type: [Number, Array], default: 0 }) private padding?: number;
  @Prop({ type: String, default: '人' }) private unit?: string;
  @Prop({
    type: Array,
    default: () => ['#1FA1FF', '#4CB4FC', '#79C6FE', '#A4DCFE', '#D1ECFC', '#E1E0E3'],
  })
  private colors?: string[];
  // data
  private chart: any = null;
  @Watch('charData')
  public watchChange() {
    this.chart.destroy();
    this.drawChart();
  }

  public mounted() {
    this.drawChart();
  }

  public drawChart() {
    this.chart = new G2.Chart({
      container: this.id,
      width: this.width,
      height: this.boxHeight,
      forceFit: this.forceFit,
      animate: this.animate,
      padding: this.padding,
    });
    this.chart.source(this.charData);
    this.chart.scale('percent', {
      formatter: function formatter(val: number): string {
        return `${val}%`;
      },
    });
    this.chart.coord('theta', {
      radius: this.radius,
      innerRadius: this.innerRadius,
    });
    this.chart.tooltip({
      showTitle: this.showTitle,
      itemTpl: '<li><span style="background-color:{color};" class="g2-tooltip-marker"></span>{name}: {value}</li>',
    });
    const interval = this.chart
      .intervalStack()
      .position('percent')
      .color('state', this.colors)
      .label('state', {
        formatter: (_: any, item: any): string => {
          return `${item.point.state} ${item.point.amount}${this.unit}`;
        },
      })
      .tooltip('state*amount*percent', (state: any, amount: any, percent: any) => {
        const val = `${amount}${this.unit} (${percent}%)`;
        return {
          name: state,
          value: val,
        };
      })
      .style({
        lineWidth: 0,
        stroke: '#fff',
      });
    this.chart.render();
  }
}
</script>

<style lang="stylus" scoped>
::-webkit-scrollbar
  display none

html, body
  overflow hidden
  margin 0
  height 100%

.g2
  width 100%
  height 100%
</style>
