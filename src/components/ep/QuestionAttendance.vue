<template lang="pug">
.page
  .header
    StepToolbar(
      v-model="tabIndex"
      :steps="tabs"
      mode="tabs")
  .main
    template
      .title {{ activeOption.key }}.{{ activeOption.value }}
      label.text-gray 共{{ store.total_count }}人
    Empty(v-if="store.users.length === 0")
    .users
      a-tag(v-for="(item, index) in store.users" :key="index" size="large" color="green")
        span {{ item.name }}
    .pagination(v-if="store.total_pages > 1")
      a-pagination(
        showQuickJumper
        size="small"
        :current="store.current_page"
        :defaultPageSize="80"
        :total="store.total_count"
        @change="onPagination")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import { Activity } from '@/models/ep/activity';
import { EpQuestion } from '@/models/ep/question';

@Component({
  components: {},
})
export default class QuestionAttendance extends Vue {
  @Prop({ type: Object, default: () => ({}) }) private question?: any;
  @Prop({ type: String, default: '' }) private date?: string;
  @Prop({ type: String }) private userRole?: 'Teacher' | 'Student';

  private tabIndex: string = 'A';
  private store: any = {
    current_page: 1,
    total_pages: 1,
    total_count: 0,
    users: [],
  };
  private activeOption: any = {};
  get tabs() {
    return (this.question.options || {}).map((item: any) => ({
      title: item.value,
      key: item.key,
    }));
  }
  get userType() {
    if (this.userRole) return this.userRole;
    return ['ep_activity_teacher_register_index', 'ep_teacher_inspect_activity_teacher_register_index'].includes(
      this.$route.name || '',
    )
      ? 'Teacher'
      : 'Student';
  }
  get roleType() {
    return (this.$route.name || '').includes('inspect_activity') ? 'inspect' : 'admin';
  }

  @Watch('tabIndex')
  public watchChange() {
    this.fetchData();
    this.activeOption = (this.question.options || {}).find((e: any) => e.key === this.tabIndex) || {};
  }

  public mounted() {
    this.fetchData();
    this.activeOption = (this.question.options || {}).find((e: any) => e.key === this.tabIndex) || {};
  }

  public async fetchData(page: number = 1) {
    const params = {
      page,
      per_page: 80,
      parentId: this.$route.params.id,
      q: {
        user_type_eq: this.userType,
        date_eq: this.$moment(this.date).format('YYYY-MM-DD'),
        find_users_by_question: [this.question.key, this.tabIndex],
      },
    };
    const activityModel = new Activity(this.roleType);
    let res = { data: {} };
    if (params.parentId) {
      res =
        this.roleType === 'inspect'
          ? await activityModel.inspectQuestions(params)
          : await activityModel.questions(params);
    } else {
      Object.assign(params.q, { type_eq: 'Register::Ep' });
      res = await new EpQuestion('admin').index(params);
    }
    this.store = res.data;
  }

  public onPagination(page: number) {
    this.fetchData(page);
  }
}
</script>

<style lang="stylus" scoped>
.page
  padding 10px 16px 30px
  width 100%
  height 100%
  background #fff
  .header
    border-bottom 1px #e8e8e8 solid
  .main
    margin-top 16px
    border 1px #e8e8e8 solid
    border-radius 3px
    .title
      margin-bottom 14px
      padding 14px 20px
      border-bottom 1px #e8e8e8 solid
      color #383838
      line-height 20px
      font--size 14px
    label
      margin-left 20px
    .users
      display flex
      flex-wrap wrap
      padding 4px 12px 20px
      width 100%
      .ant-tag
        margin 8px
        padding 0px
        width 77px
        height 40px
        border none
        background #F0F9F2
        color #6DC37D
        text-align center
        font-weight 500
        line-height 40px
    .pagination
      padding 20px
      width 100%
      text-align right
</style>
