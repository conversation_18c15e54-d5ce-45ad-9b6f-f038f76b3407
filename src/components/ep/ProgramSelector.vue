<script lang="ts">
/**
 * 人才培养方案选择器
 * 事件： change(ids, programs)
 * v-model: programIds [培养方案 ids]
 * props: schoolId, required: true [所属学校的 id]
 *
 * functions:
 * reset    调用：$refs.component.reset()     用来重置组件的筛选参数
 */
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import { Department } from '@/models/department';
import { adminProgramStore } from '@/store/modules/teaching/program.store';
import { teacherMajorStore, adminMajorStore } from '@/store/modules/res/major.store';

interface IProgram {
  id: number;
  [key: string]: any;
}

@Component
export default class ProgramSelector extends Vue {
  currentPage: number = 1;
  totalPages: number = 1;
  totalCount: number = 1;
  perPage: number = 15;
  programs: IProgram[] = [];
  loading: boolean = false;
  scrollY: number | null = null;
  // filter
  timer: any = null;
  keyword: string = '';
  query: IObject = {
    name_or_major_name_or_department_name_cont_any: '',
    s: [],
  };
  // 高级筛选
  advancedFilterVisible: boolean = false;
  activeMajorId: number | null = null; // 已选的专业Id
  // 已选列表
  listCurrentPage: number = 1;
  listTotalPages: number = 1;
  listTotalCount: number = 1;
  selectedPrograms: IProgram[] = [];
  selectedListLoading: boolean = false;
  // config
  resizeTimer: any = null;
  syncTimer: any = null;

  @Model('change', { type: Array }) readonly programIds!: number[]; // program ids
  @Prop({ type: Number, required: true }) readonly schoolId!: number[]; // school id
  @Prop({ type: Boolean, default: true }) readonly multiple!: boolean;

  // 是否设置了高级筛选，用于高级搜索窗口关闭后的提醒文案
  get isSetAdvancedFilter() {
    return this.activeMajorId;
  }
  // 表格的多选配置
  get rowSelection() {
    return this.multiple
      ? {
          selectedRowKeys: this.selectionKeys,
          onChange: this.selectionChange,
        }
      : null;
  }
  get selectionKeys() {
    return this.selectedPrograms.map(o => o.id);
  }
  get majorStore() {
    return adminMajorStore || {};
  }
  get activeMajor() {
    return this.activeMajorId ? this.majorStore.records.find((e: any) => e.id === this.activeMajorId) : {};
  }

  @Watch('programIds')
  onValueChange() {
    // 如果 programIds 等于 组件内已选择的方案列表 ids, 无须再重复获取数据
    const listIds = this.selectedPrograms.map(o => o.id).sort();
    const programIds = this.programIds.concat().sort();
    if (programIds.toString() !== listIds.toString()) {
      this.fetchSelectedPrograms(1);
    }
  }

  async mounted() {
    this.fetchPrograms(1); // 所有方案
    this.fetchSelectedPrograms(1); // 已选列表
    this.fetchMajors(); // 高级筛选
    this.$nextTick(this.initTableSize);
    window.addEventListener('resize', this.initTableSize);
  }

  beforeDestroy() {
    window.removeEventListener('resize', this.initTableSize);
  }

  /**
   * 获取表格方案数据
   */
  async fetchPrograms(page: number = this.currentPage, tableQuery: object = {}, pageSize: number = this.perPage) {
    try {
      this.loading = true;
      const params = {
        page,
        per_page: pageSize,
        q: {
          major_id_eq: this.activeMajorId || '',
          activate: true,
          ...this.query,
          ...tableQuery,
        },
      };
      const { data } = await adminProgramStore.fetch(params);
      this.currentPage = data.current_page;
      this.totalPages = data.total_pages;
      this.totalCount = data.total_count;
      this.programs = data.programs;
      this.loading = false;
    } catch (error) {
      this.loading = false;
    }
  }

  /**
   * 获取已选择的方案记录
   */
  async fetchSelectedPrograms(page: number = this.listCurrentPage) {
    if (this.selectedListLoading) return;
    if (this.programIds instanceof Array && this.programIds.length) {
      // programIds 存在
      try {
        this.selectedListLoading = true;
        const params = {
          page,
          per_page: 1000,
          q: {
            id_in: this.programIds,
          },
        };
        const { data } = await adminProgramStore.fetch(params);
        this.listCurrentPage = data.current_page;
        this.listTotalPages = data.total_pages;
        this.listTotalCount = data.total_count;
        let sortedSelectedPrograms: IProgram[] = [];
        if (page === 1) {
          sortedSelectedPrograms = data.programs;
        } else {
          sortedSelectedPrograms = sortedSelectedPrograms.concat(data.programs);
        }
        // 按照 programIds 数组对方案列表进行排序
        sortedSelectedPrograms.sort((a: IProgram, b: IProgram) => {
          return this.programIds.indexOf(a.id) - this.programIds.indexOf(b.id);
        });
        this.selectedPrograms = sortedSelectedPrograms;
        this.selectedListLoading = false;
      } catch (error) {
        this.selectedListLoading = false;
      }
    } else {
      // programIds 为空
      this.selectedPrograms = [];
    }
  }

  /**
   * 获取专业数据，用于高级搜索
   */
  fetchMajors() {
    const params = {
      page: 1,
      per_page: 1000,
    };
    this.majorStore.fetch(params);
  }

  /**
   * 重置组件筛选数据
   */
  reset() {
    this.keyword = '';
    this.query.name_or_major_name_or_department_name_cont_any = '';
    this.activeMajorId = null;
    this.advancedFilterVisible = false;
    this.fetchPrograms(1);
  }

  /**
   * 输入框搜索
   */
  onSearch(e: any) {
    window.clearTimeout(this.timer);
    const keywordAry = this.$utils.parseStringToArray(e.target.value);
    this.query.name_or_major_name_or_department_name_cont_any = keywordAry;
    this.timer = setTimeout(() => {
      this.fetchPrograms(1);
    }, 600);
  }

  /**
   * 表格多选
   */
  selectionChange(ids: number[], programs: any[]) {
    // 去重
    const allProgramsMap: { [key: string]: IProgram } = this.selectedPrograms.concat(programs).reduce(
      (obj, item) => ({
        ...obj,
        [`${item.id}`]: item,
      }),
      {},
    );
    const selectedPrograms = Object.values(allProgramsMap).filter(o => ids.includes(o.id));
    this.syncPrograms(selectedPrograms);
  }

  /**
   * 删除方案
   */
  removeProgram(index: number) {
    this.selectedPrograms.splice(index, 1);
    this.syncPrograms(this.selectedPrograms);
  }

  /**
   * 加载更多方案
   */
  loadMoreSelectedPrograms() {
    if (!this.selectedListLoading) {
      this.fetchSelectedPrograms(this.listCurrentPage + 1);
    }
  }

  /**
   * v-model，同步本地方案状态，发送事件
   */
  syncPrograms(programs: IProgram[]) {
    clearTimeout(this.syncTimer);
    this.selectedPrograms = programs.concat();
    this.syncTimer = setTimeout(() => {
      const programIds = programs.map(o => o.id);
      this.$emit('change', programIds, this.selectedPrograms);
    }, 600);
  }

  /**
   * 单选
   */
  chooseProgram(program: IObject) {
    this.$emit('change', [program.id], [program]);
  }

  /**
   * 重置高级筛选
   */
  resetAdvancedFilter() {
    this.activeMajorId = null;
  }

  /**
   * 清空高级搜索条件
   */
  clearFilter() {
    this.resetAdvancedFilter();
    this.fetchPrograms(1);
  }

  /**
   * 按照专业获取数据
   */
  onMajorChange(value: any) {
    this.activeMajorId = value;
    this.fetchPrograms(1);
  }

  /**
   * 清空已选方案列表
   */
  resetSelectedList() {
    this.syncPrograms([]);
  }

  /**
   * 计算表格高度
   */
  initTableSize() {
    clearTimeout(this.resizeTimer);
    this.resizeTimer = setTimeout(() => {
      const el = this.$refs.tableContent as any;
      this.scrollY = el.clientHeight - 42 - 45 - 52;
    }, 300);
  }
}
</script>

<template lang="pug">
.program-selector
  //- =============== 方案列表 ===============
  .panel-box(:class="{ 'has-title': multiple }")
    .title(v-if="multiple")
      | 选择
    .content-box.table-box(ref="tableContent" :class="{ 'filter-open': advancedFilterVisible }")
      .programs-box
        .filter-header
          a-input-search(
            v-model="keyword"
            placeholder="搜索名称、专业"
            @change="onSearch")
          .filter-menu(@click="advancedFilterVisible = !advancedFilterVisible" v-if="!advancedFilterVisible")
            span.text-primary(v-if="activeMajor.id")
              | {{ activeMajor.name }}
            span(v-else)
              | 高级筛选&nbsp;
            a-icon(type="down")
        .programs-table
          AdminTable(
            :data="programs"
            :currentPage="currentPage"
            :totalPages="totalPages"
            :perPage="perPage"
            :totalCount="totalCount"
            :showSizeChanger="true"
            :rowSelection="rowSelection"
            :scroll="{ y: scrollY }"
            :loading="loading"
            paginationSize="small"
            rowKey="id"
            @change="fetchPrograms")
            a-table-column(title="名称" dataIndex="name" key="name" sorter="custom" :width="200")
            a-table-column(title="专业" key="major_name" dataIndex="major_name" :width="200")
            a-table-column(title="年级" dataIndex="grade" key="grade" sorter="custom" :width="100")
              template(slot-scope="grade")
                span {{ grade ? $moment(new Date(grade)).format('YYYY 级') : '-' }}
            a-table-column(title="年限" dataIndex="duration" key="duration" sorter="custom" :width="100")
              template(slot-scope="duration")
                span {{ duration }} 年制
            a-table-column(dataIndex="students_count" title="学生数" :width="80" align="center")
            a-table-column(title="选择" :width="80" v-if="!multiple")
              template(slot-scope="text, record, index")
                a-button(type="primary" size="small" @click.stop="chooseProgram(record, index)")
                  | 选择
      .advanced-filter-box
        .advanced-filter-header
          span 高级筛选
          a-icon(type="close" @click="advancedFilterVisible = false")
        .advanced-filter-content
          .label.flex-between
            span 筛选
            a.text-primary(v-if="isSetAdvancedFilter" @click="clearFilter") 清空筛选
          .tab
            a-select.form-item(
              :value="activeMajorId ? activeMajorId : undefined"
              placeholder="请选择专业"
              allowClear
              @change="onMajorChange")
              a-select-option(
                v-for="major in majorStore.records"
                :key="major.id"
                :value="major.id")
                span {{ major.name }}
  //- =============== 已选 ===============
  .panel-box.program-list(v-if="multiple" :class="{ 'has-title': multiple }")
    .title.flex-between
      span 已选（{{ selectedPrograms.length }}）
      PopoverConfirm(
        v-if="selectedPrograms.length"
        title="清空"
        content="您确认要清空已选列表吗？"
        placement="bottomRight"
        @confirm="resetSelectedList")
        IconTooltip(icon="delete" tips="清空")
    .content-box(v-loading="selectedListLoading")
      .selected-programs
        .program(v-for="(item, index) in selectedPrograms" :key="item.id")
          .info
            | {{ item.name }}
          a-icon.close(type="close-circle" @click="removeProgram(index)")
        .load-more(
          @click="loadMoreSelectedPrograms"
          v-loading="selectedListLoading"
          v-if="listCurrentPage < listTotalPages")
          | 点击加载更多
</template>

<style lang="stylus" scoped>
.program-selector
  display flex
  width 100%
  height 100%
  .has-title
    padding-top 44px !important
  .panel-box
    position relative
    flex 1 1 auto
    padding 0 0 20px
    height 100%
    background #fff
    .title
      position absolute
      top 0
      left 0
      padding 12px 0
      width 100%
      color rgba(128, 128, 128, 1)
      font-size 14px
      line-height 20px
    .filter-open
      padding-right 240px !important
      .advanced-filter-box
        transform translateX(0) !important
    .table-box
      position relative
      padding-right 0px
      transition all 0.3s ease
      .programs-box
        .filter-header
          display flex
          justify-content space-between
          align-items center
          padding 10px 10px 0
          .filter-menu
            flex-shrink 0
            width 106px
            color rgba(56, 56, 56, 1)
            text-align center
            font-size 14px
            line-height 20px
            cursor pointer
            &:hover
              color #3DA8F5
        .programs-table
          overflow hidden
          padding 0 10px 0px
          height 100%
          .avatar
            background #3DA8F5
          .checkbox-group
            width 100%
          .teacher
            margin-left 0
            padding 8px
            width 100%
            cursor pointer
            &:hover
              background #eee
      .advanced-filter-box
        position absolute
        top 0
        right 0
        bottom 0
        z-index 1000
        overflow-x hidden
        overflow-y auto
        padding-top 54px
        width 240px
        border-left 1px solid #E8E8E8
        background #fff
        transition transform 0.3s ease
        transform translateX(100%)
        .advanced-filter-header
          position absolute
          top 0
          left 0
          display flex
          justify-content space-between
          align-items center
          padding 16px
          width 100%
          border-bottom 1px solid #E8E8E8
          color rgba(56, 56, 56, 1)
          font-weight 400
          font-size 14px
          i:hover
            color #58A8EF
            cursor pointer
        .advanced-filter-content
          overflow auto
          padding 16px
          height 100%
          .label
            margin-bottom 12px
            color rgba(128, 128, 128, 1)
            font-weight 500
            font-size 14px
            line-height 20px
          .form-item
            margin 16px 0
            width 100%
    .content-box
      position relative
      overflow hidden
      height 100%
      border 1px solid rgba(232, 232, 232, 1)
      border-radius 3px
      background rgba(255, 255, 255, 1)
      .selected-programs
        overflow auto
        padding 10px
        height 100%
        .program, .load-more
          display flex
          justify-content space-between
          align-items center
          margin-bottom 4px
          padding 6px
          border-radius 4px
          background #fff
          color #888
          line-height 24px
          cursor pointer
          &:hover
            background #F5F5F5
          .close
            cursor pointer
            &:hover
              color red
        .load-more
          justify-content center
          color #3DA8F5
  .program-list
    flex 0 0 240px
    margin-left 20px
</style>
