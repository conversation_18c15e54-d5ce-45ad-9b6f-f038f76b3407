<template lang="pug">
.container
  .header
    .top
      h1 每日健康调查
      p 疫情时期守护学生健康
    .middle
      .flex
        a-icon(type="check-circle" style="font-size: 18px")
        span 师生隐私保护
      .flex
        a-icon(type="check-circle" style="font-size: 18px")
        span 数据实时统计
    .bottom
      .name {{ register.user && register.user.name }}
      p 打卡时间：{{ register.state !== 'undo' ? $moment(register.created_at).format('YYYY.MM.DD HH:mm') : '未打卡' }}
      p 打卡地址：{{ register.address || location.address|| '未打卡' }}
  .main
    .question
      .title
        | 1. 今日居住地址：
      .input-box
        template(v-if="pageType === 'Register' && !disabled")
          .label.label1 省/市/区（县、乡）：
          v-distpicker(
            :province='registerAddress.province',
            :city='registerAddress.city',
            :area='registerAddress.district',
            @selected='onAreaChange'
          )
          .label 详细地址（到门牌号）：
          a-input.input(v-model='registerAddress.address')
        template(v-else)
          .line
            .label.label1 省/市/区（县、乡）：
            .value {{ registerAddress.province }} {{ registerAddress.city }} {{ registerAddress.district }}
          .line
            .label 详细地址（到门牌号）：
            .value {{ registerAddress.address }}

    .question(v-for="(question, index) in questions" :key="index")
      .title {{ index + 2 }}. {{ question.title }}
      .cell-box
        template(v-if="pageType !== 'Register' || disabled")
          .cell(v-for="(option, key) in question.options" :key="key")
            a-icon(
              type="check-circle"
              theme="filled"
              style="font-size: 20px;"
              :style="{color: option.key === registerMeta[question.key] ? '#3da8f5' : '#ccc'}")
            span {{ option.value }}
        template(v-else)
          a-radio-group(v-model="registerMeta[question.key]")
            .cell(v-for="(option, key) in question.options" :key="key")
              a-radio(:value="option.key" style="width: 100%") {{ option.value }}
    template(v-if="pageType === 'Register' && !disabled")
      a-button(
        type="primary"
        size="large"
        style="width: 100%"
        @click="onSubmit") 提交
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import { adminRegisterStore, userRegisterStore, inspectRegisterStore } from '@/store/modules/ep/register.store';
import { Register } from '@/models/ep/register';
import VDistpicker from 'v-distpicker';
const RegisterModel = new Register('user');

@Component({
  components: {
    VDistpicker,
  },
})
export default class RegisterShow extends Vue {
  @Prop({ type: Number, default: () => ({}) }) private registerId!: number;
  @Prop({ type: Array, default: () => [] }) private questions?: any;
  @Prop({ type: String, default: 'View' }) private pageType?: string; // View, Register
  private register: any = {};
  private location: any = {};
  private registerMeta: any = {};

  registerAddress: { province: string; city: string; district: string; address: string } = {
    province: '',
    city: '',
    district: '',
    address: '',
  };

  get pageRole() {
    const pathname = this.$route.name || '';
    if (pathname.includes('ep_activity')) return 'admin';
    if (pathname.includes('ep_teacher_inspect')) return 'inspect';
    return 'user';
  }
  get disabled() {
    const failure = this.register.created_at
      ? new Date().getTime() > new Date(this.register.created_at).setHours(23, 59, 59, 999)
      : false;
    return failure || this.register.state === 'done';
  }
  get registerStore() {
    return ({
      admin: adminRegisterStore,
      user: userRegisterStore,
      inspect: inspectRegisterStore,
    } as any)[this.pageRole];
  }

  @Watch('registerId')
  public watchChange() {
    this.initData();
  }

  public created() {
    this.initData();
  }

  public initData() {
    // if (this.pageType === 'Register') {
    //   this.getAddress();
    // }
    this.fetchData();
  }

  // public async getAddress() {
  //   const ip = this.$tools.getIpInfo().cip;
  //   const { data } = await RegisterModel.getAddress({ ip });
  //   this.location = {
  //     address: `${data.province} ${data.city} ${data.district}`,
  //     province: data.province,
  //     city: data.city,
  //   };
  // }

  public async fetchData() {
    const { data } = await this.registerStore.find(this.registerId);
    this.register = data;
    this.registerMeta = (data.questions || []).reduce((res: any, item: any) => {
      res[item.key] = data.meta && data.meta[item.key] ? data.meta[item.key] : '';
      return res;
    }, {});

    this.registerAddress = {
      province: data.province,
      city: data.city,
      district: data.district,
      address: data.address,
    };
  }

  public async onSubmit() {
    const finish = [...Object.values(this.registerMeta), ...Object.values(this.registerAddress)].every((e: any) => !!e);
    if (!finish) {
      this.$message.warning('请完成答题！');
      return;
    }
    this.updateRegister();
  }

  public async updateRegister() {
    try {
      const obj: any = {
        // ...this.location,
        id: this.registerId,
        noCompare: true,
        meta: this.registerMeta,
        state: 'done',
        created_at: new Date(),
        ...this.registerAddress,
      };
      await this.registerStore.update(obj);
      this.$emit('refresh');
    } catch (error) {
      this.$message.error('提交失败！');
    }
  }

  onAreaChange(data: { [key: string]: { code: string; value: string } }) {
    this.registerAddress.province = data.province.value;
    this.registerAddress.city = data.city.value;
    this.registerAddress.district = data.area.value;
  }
}
</script>

<style lang="stylus" scoped>
.container
  overflow auto
  width 100%
  height 100%
  .header
    width 100%
    background linear-gradient(180deg, rgba(49, 170, 255, 1) 0%, rgba(38, 124, 231, 1) 58%, rgba(255, 255, 255, 0) 100%)
    color #fff
    .top
      padding 70px 24px 50px
      width 100%
      h1
        color #fff
        letter-spacing 2px
        font-size 30px
        line-height 30px
      p
        margin-top 12px
        letter-spacing 4px
        font-size 14px
        line-height 20px
    .middle
      display flex
      justify-content space-between
      align-items center
      margin 0px auto
      max-width 240px
      span
        margin-left 4px
        font-weight 500
        font-size 14px
        line-height 20px
    .bottom
      margin 14px 20px 4px
      padding 16px
      border-radius 3px
      background #fff
      box-shadow 0px 0px 4px 0px rgba(0, 0, 0, 0.1)
      .name
        color #383838
        font-weight 500
        font-size 15px
        line-height 24px
      p
        margin-top 6px
        color #808080
        font-size 15px
        line-height 24px
  .main
    padding 0px 20px 40px
    width 100%
    background linear-gradient(-180deg, #fefefe, #ffffff)
    .question
      padding-top 20px
      width 100%
      border-top 1px #E9E9E9 solid
      &:first-child
        border none
      .title
        color #262626
        font-weight 500
        font-size 15px
        line-height 24px
      .input-box
        margin 18px 0px 24px
        width 100%
        .line
          display flex
          .value
            margin 10px 0
        .label1
          margin-bottom 10px
        .label
          margin-top 10px
        .input
          margin-top 10px
      .cell-box
        margin 18px 0px 24px
        width 100%
        border 1px solid rgba(232, 232, 232, 1)
        border-radius 3px
        .cell
          display flex
          align-items center
          padding 14px
          width 100%
          border-top 1px solid rgba(232, 232, 232, 1)
          &:first-child
            border 0px
          span
            margin-left 20px
            color rgba(38, 38, 38, 0.65)
            font-size 14px
            line-height 20px

.ant-radio-group
  width 100%
</style>
