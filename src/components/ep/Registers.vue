<template lang="pug">
.pannel
  .pannel-top
    .count-box
      .count-item(v-for="(item, index) in registerInfos" :key="index")
        .key {{ item.title }}
        .value
          strong.count {{ item.count }}
          strong.unit {{ pageType === 'Index' ? '人' : '次' }}
    .ring-box(v-if="register.total > 0")
      G2Pie(
        :charData.sync="basePie"
        :colors="['#1FA0FF', '#CDCBCE']"
        id="A1"
        :unit="pageType === 'Index' ? '人' : '次'"
        v-if="basePie.length")
  .pannel-middle
    QuestionChart(
      :question="question"
      :questions="questions"
      :pageType="pageType"
      @detail="onDetail")
  .pannel-bottom
    .module-title.flex-between
      span 打卡详情
      .actions
        Searcher(
          v-if="userType"
          :variables="[`user_of_${userType}_type_name`]"
          v-model="query"
          @change="fetchData(1)")
        a-radio-group(v-model="activeState" @change="filterByState")
          a-radio-button(value="done") 已打卡
          a-radio-button(value="undo") 未打卡
    AdminTable(
      :data="registers"
      :totalCount="store.totalCount"
      :currentPage="store.currentPage"
      :totalPages="store.totalPages"
      :perPage="perPage"
      :showHeader="true"
      :showSizeChanger="true"
      :hideOnSinglePage="store.totalCount < 10"
      :bordered="true"
      rowClassName="click-row"
      @rowClick="onShow"
      @change="fetchData")
      a-table-column(dataIndex="user_name" title="姓名" align="center" width="80px")
      template(v-if="role === 'student'")
        a-table-column(dataIndex="user_code" title="学号" align="center")
        a-table-column(dataIndex="user_major_name" title="所属专业" align="center")
        a-table-column(dataIndex="user_college_name" title="所属部门" align="center")
      template(v-else)
        a-table-column(dataIndex="user_code" title="职工号" align="center")
        a-table-column(dataIndex="user_college_name" title="所属部门" align="center")
      a-table-column(title="打卡日期" align="center")
        template(slot-scope="scope")
          span {{ $moment(scope.created_at).format('YYYY.MM.DD') }}
      a-table-column(title="打卡时间" align="center")
        template(slot-scope="scope")
          span {{ scope.state === 'undo' ? '-' : $moment(scope.created_at).format('HH:mm') }}
      a-table-column(title="打卡省市" align="center")
        template(slot-scope="scope")
          span {{ scope.state === 'undo' ? '-' : scope.province}}
      a-table-column(title="打卡地点" align="center")
        template(slot-scope="scope")
          span {{ scope.state === 'undo' ? '-' : scope.address}}
      a-table-column(title="打卡状态" align="center")
        template(slot-scope="scope")
          span(:class="scope.state === 'undo' ? 'text-gray' : 'text-success'")
            | {{ scope.state === 'undo' ? '未打卡' : '已打卡'}}
      a-table-column(title="操作" align="center" v-if="cancelable")
        template(slot-scope="record")
          PopoverConfirm(
            title="提示"
            content="确定取消此打卡记录吗？"
            @confirm="cancelRegister(record)")
            IconTooltip(icon="undo" tipe="取消")

  MainModal(
    v-model="visibleInfo"
    width="600px"
    title="每日健康打卡"
    :footer="false"
    :maskClosable="false")
    Register(:registerId="info.id" :questions="questions" v-if="visibleInfo")

  MainModal(
    v-model="visibleQuestion"
    width="800px"
    :footer="false"
    :maskClosable="false")
    template(slot="title")
      strong {{ registerQuestion.position }}. {{ registerQuestion.title }}
    QuestionAttendance(:question="registerQuestion" :date="date" v-if="visibleQuestion")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import G2Pie from '@/components/ep/G2Pie.vue';
import QuestionChart from '@/components/ep/QuestionChart.vue';
import QuestionAttendance from '@/components/ep/QuestionAttendance.vue';
import Register from '@/components/ep/Register.vue';
import { adminActivityStore } from '@/store/modules/ep/activity.store';

@Component({
  components: {
    G2Pie,
    QuestionChart,
    QuestionAttendance,
    Register,
  },
})
export default class Registers extends Vue {
  @Prop({ type: Object, default: () => ({}) }) private store?: any;
  @Prop({ type: Array, default: () => [] }) private records?: any;
  @Prop({ type: Array, default: () => [] }) private questions?: any;
  @Prop({ type: String, default: 'Index' }) private pageType?: string; // Index, Show
  @Prop({ type: String, default: '' }) private date?: string;
  @Prop({ type: Boolean, default: false }) private cancelable?: boolean;
  @Prop({ type: String }) private userType?: 'Teacher' | 'Student';

  query: IObject = {};
  activeState: string = 'done';
  private currentDate: string = this.$moment().format();
  private perPage: number = 10;
  private info: any = {};
  private visibleInfo: boolean = false;
  // 题目统计
  private registerQuestion: any = {};
  private visibleQuestion: boolean = false;

  get role() {
    return this.$tools.getRole();
  }
  get registers() {
    return this.records.length > 0 ? this.records : this.store.records;
  }
  get registerInfos() {
    return [
      {
        title: '应打卡',
        key: 'total',
      },
      {
        title: '已打卡',
        key: 'done',
      },
      {
        title: '未打卡',
        key: 'undo',
      },
    ].map((item: any) => ({
      ...item,
      count: this.register[item.key],
    }));
  }
  get basePie() {
    return [
      {
        label: '已打卡',
        key: 'done',
      },
      {
        label: '未打卡',
        key: 'undo',
      },
    ].map((item: any) => ({
      state: item.label,
      amount: this.register[item.key],
      percent: +((this.register[item.key] / this.register.total) * 100).toFixed(2),
    }));
  }
  get register() {
    return this.store.info && this.store.info.register ? this.store.info.register : {};
  }
  get question() {
    return this.store.info && this.store.info.question ? this.store.info.question : {};
  }

  @Watch('$route')
  onRouteChange() {
    this.query = {};
  }

  public onShow(val: any) {
    if (val.state === 'undo') {
      this.$message.warning('暂无打卡信息！');
      return;
    }
    this.info = val;
    this.visibleInfo = true;
  }

  public async onDetail(val: any = {}) {
    this.registerQuestion = val;
    this.visibleQuestion = true;
  }

  public fetchData(page: number, query: any, perPage: number) {
    this.perPage = perPage;
    this.$emit('change', page, Object.assign(this.query, query), perPage);
  }

  cancelRegister(record: IObject) {
    this.$emit('cancel', record);
  }

  filterByState() {
    this.fetchData(1, { state_eq: this.activeState }, this.perPage);
  }
}
</script>

<style lang="stylus" scoped>
.pannel
  width 100%
  height 100%
  overflow auto
  padding 16px 0
  .pannel-top
    display flex
    justify-content center
    align-items center
    padding 30px 0px
    width 100%
    height 300px
    .ring-box
      width 100%
    .count-box
      display flex
      justify-content space-around
      align-items center
      padding-left 100px
      width 100%
      .count-item
        min-width 60px
        .key
          margin-bottom 8px
          color #A6A6A6
          font-weight 500
          font-size 12px
          line-height 14px
        .count
          color #383838
          font-weight 500
          font-size 30px
          font-family DINCond-Medium, DINCond
          line-height 30px
        .unit
          margin-left 4px
          color #808080
          font-weight 400
          font-size 12px
          line-height 18px

.module-title
  margin-bottom 16px
  color rgba(56, 56, 56, 1)
  font-weight 500
  font-size 16px
  line-height 24px
</style>
