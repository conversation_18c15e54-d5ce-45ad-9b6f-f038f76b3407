<template lang="pug">
.cell-page
  .cell-title {{ info.title}}
  .cell-content(v-if="cellType === 'News'")
    .cover-image(:style="{ backgroundImage: 'url(' + info.cover_image + ')'}")
    .info
      .content
        span(v-html="info.body")
        //- a-button 查看全部
          a-icon(type="right")
      .info-bottom
        span
          a-tooltip(:title="$moment(info.created_at).format('YYYY/MM/DD HH:mm:ss')")
            span {{ $moment(info.created_at).fromNow() }}
          //- a-divider(type="vertical")
          //- span {{ info.likes_count }}
          //- a-divider(type="vertical")
          //- span {{ info.stars_count }} {{ $t('common.star') }}
          //- a-divider(type="vertical")
          //- span {{ info.views_count }} {{ $t('common.view') }}
        span {{ info.created_at | format }}
  .cell-bottom(v-else)
    .creator-info
      span 发布者 {{ info.user && info.user.name }}
      a-divider(type="vertical")
      span(v-if="info.comment && info.comment.id")
        | @{{ info.comment.user_name }} 最后回复于{{ $moment(info.comment.created_at).fromNow() }}
      span(v-else) 发布于{{ $moment(info.created_at).fromNow() }}
    .statistical-info
      .count-box
        a-icon(type="eye" style="font-size: 16px; margin-right: 4px")
        span {{ info.view_count }}
      .count-box
        a-icon(type="message" style="font-size: 16px; margin-right: 4px")
        span {{ info.comment_count }}
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import store from '@/store';

@Component({
  components: {},
})
export default class InfoCell extends Vue {
  @Prop({ type: Object, default: () => [] }) private info?: any;
  @Prop({ type: String, default: 'News' }) private cellType?: string;
}
</script>

<style lang="stylus" scoped>
.cell-page
  padding 20px 0px
  width 100%
  cursor pointer
  .cell-title
    width 100%
    color #333333
    font-weight 500
    font-size 16px
    line-height 24px
  .cell-content
    display flex
    margin-top 10px
    width 100%
    .cover-image
      margin-right 12px
      min-width 150px
      width 150px
      height 88px
      border-radius 2px
      background #eee
      background-position center
      background-size cover
      background-repeat no-repeat
    .info
      max-height 88px
      width 100%
      color rgba(102, 102, 102, 1)
      font-size 14px
      line-height 22px
      .content
        display -webkit-box
        overflow hidden
        width 100%
        -webkit-box-orient vertical
        -webkit-line-clamp 3
      .line-four
        -webkit-line-clamp 4
      button
        border none
        color rgba(201, 101, 101, 1)
        font-size 14px
        cursor pointer
        .anticon
          vertical-align -0.1em !important
      .info-bottom
        display flex
        justify-content space-between
        align-items center
        margin-top 8px
        color #B2B2B2
        font-weight 500
        font-size 14px
        line-height 14px
  .cell-bottom
    display flex
    justify-content space-between
    align-items center
    margin-top 6px
    color #B2B2B2
    font-weight 500
    font-size 14px
    line-height 20px
    .count-box
      display inline
      margin-left 22px
</style>
