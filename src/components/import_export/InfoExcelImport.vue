<template lang="pug">
.page
  .header
    a-steps(:current="currentStep")
      a-step(v-for="(title, index) in steps" :key="index" :title="title")
  .main
    .loading-box(v-if="loading")
      a-tooltip
        template(slot="title")
          p 总数：{{ notifyInfo.total_count || 0 }}
          p 已完成数：{{ notifyInfo.finished_count || 0 }}
          p 成功数：{{ notifyInfo.successed_count || 0 }}
          p 失败数：{{ notifyInfo.failed_count || 0 }}
        a-progress(:percent="finishPercent" :successPercent="successPercent")
      a-spin(:tip="loadingTitle")
    a-form(labelAlign="left")
      a-form-item(
        label="选择参考字段"
        :label-col="{ span: 3 }"
        :wrapper-col="{ span: 18 }")
        a-select(
          v-model="validKeys"
          mode="multiple"
          placeholder="请选择参考字段"
          style="width: 460px")
          a-select-option(
            v-for="(item, ind) in primaryKeys"
            :key="ind"
            :value="item.key")
            | {{ item.name }}
    AdminTable(
      :data="store.records"
      :totalCount="store.totalCount"
      :currentPage="store.currentPage"
      :totalPages="store.totalPages"
      :perPage="perPage"
      :showSizeChanger="true"
      :showHeader="true"
      @change="onChange")
      a-table-column(title="校验状态" :width="100" align="center" v-if="currentStep === 2")
        template(slot-scope="scope")
          template(v-if="scope.errors_text")
            a-tooltip(:title="scope.errors_text")
              a-icon(type="info-circle" theme="filled" style="font-size: 20px;").text-warning
          template(v-else)
            a-icon(type="check-circle" theme="filled" style="font-size: 20px").text-primary
      a-table-column(
        v-for="(dataTitle, index) in dataKeys"
        :key="index"
        :dataIndex="dataTitle"
        :width="120")
        span(slot="title")
          a-select(
            v-model="tableTitles[index]"
            placeholder="请选择表头"
            allowClear
            style="width: 100%; min-width: 100px")
            a-select-option(
              v-for="(item, ind) in importsKeys"
              :key="ind"
              :value="item.key")
              | {{ item.label }}
          .table-title {{ dataTitle }}
  .footer
    a-button(size="large" @click="onCancel") 关闭
    template(v-if="currentStep === 1")
      a-button(
        type="primary"
        size="large"
        :disabled="loading"
        @click="onModal") 确定校验
    template(v-else-if="currentStep === 2")
      a-button(
        type="primary"
        size="large"
        :disabled="loading"
        @click="onConfirm") 确定上传
    template(v-if="currentStep !== 2")
      a-button(
        type="primary"
        size="large"
        :disabled="loading"
        @click="directConfirm") 直接上传
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import { ImportModel } from '@/service/import';

const importModel = new ImportModel();

@Component({
  components: {},
})
export default class InfoExcelImport extends Vue {
  @Prop({ type: String, default: () => '' }) uid!: string;
  @Prop({ type: String, default: () => '' }) source_type!: 'finance';
  @Prop({ type: String, default: () => '' }) targets!: 'project';
  @Prop({ type: String, default: () => '' }) type!: 'finance_activity_project';
  @Prop({ type: Array, default: () => [] }) validTitles!: any;
  @Prop({ type: Object, default: () => ({}) }) private params?: any;
  store: any = {};
  perPage: number = 20;
  dataKeys: string[] = [];
  currentStep: number = 1;
  steps: string[] = ['选择文件', '信息确认', '信息校验', '信息上传', '上传完成'];
  importsKeys: any[] = [];
  primaryKeys: string[] = [];
  tableTitles: string[] = [];
  validKeys: string[] = ['mobile'];
  // 处理
  timer: any = null;
  loading: boolean = false;
  finishPercent: number = 0;
  successPercent: number = 0;
  loadingTitle: string = '信息校验中';
  tooltipTitle: string = '';
  notifyInfo: any = {};

  @Watch('uid', { immediate: true, deep: true })
  async watchChange() {
    await this.fetchData();
    this.fetchTabledKeys();
  }

  @Watch('validTitles', { immediate: true, deep: true })
  async watchValidTitles() {
    this.validKeys = this.validTitles;
  }

  beforeDestroy() {
    this.loading = false;
    this.timer = null;
  }

  async fetchData(page: number = 1) {
    const params = {
      page,
      per_page: this.perPage,
      uid: this.uid,
      row: this.currentStep === 2 ? 'valid_failed_rows' : '',
    };
    const { data } = await importModel.getExcelInfo(params);
    const results = data.data;
    this.dataKeys = results.titles || [];
    this.store = {
      currentPage: results.current_page,
      totalPages: results.total_pages,
      totalCount: results.total_count,
      records: (results.datas || []).map((item: any, index: any) => ({
        id: index + 1, // table rowKey
        ...(item.row || []).reduce((res: any, value: string, index: any) => {
          res[this.dataKeys[index]] = value;
          return res;
        }, {}),
        errors_text: (Object.keys(item.errors) || []).map((key: string) => item.errors[key]).join('，'),
      })),
    };
  }

  async fetchTabledKeys() {
    const { data } = await importModel.tableKeys({ type: this.type });
    const keys = [{ name: '暂无', key: '' }].concat(data.data || []);
    this.importsKeys = keys.map((e: any) => ({
      label: e.name,
      key: e.key,
    }));
    this.primaryKeys = (data.data || []).filter((e: any) => !!e.primary);
    this.tableTitles = (this.dataKeys || []).map(
      (title: string) => ((this.importsKeys || []).find((e: any) => e.label === title) || { key: '' }).key,
    );
  }

  async submitExcelInfo(params: any) {
    try {
      const { data } = await importModel.submitExcelInfo(params);
      if (data.status === 'successed') {
        this.onNotify();
      }
      this.$message.success('提交成功！');
    } catch (error) {
      this.$message.error('操作失败！');
    }
  }

  onChange(page: number, query: any, perSize: number) {
    this.perPage = perSize;
    this.fetchData(page);
  }

  onModal() {
    const checkTitle = this.tableTitles.some((e: string) => !e);
    if (checkTitle) {
      this.$confirm({
        title: '有未匹配成功的表头信息，是否继续操作？',
        okText: '确定',
        okType: 'danger',
        cancelText: '取消',
        onOk: async () => {
          this.onValid();
        },
        onCancel: () => {},
      });
    } else {
      this.onValid();
    }
  }

  async onValid() {
    this.loading = true;
    const params = {
      uid: this.uid,
      titles: this.tableTitles,
      primary_keys: this.validKeys,
      source_type: this.source_type,
      targets: this.targets,
    };
    const { data } = await importModel.onValid(params);
    if (data.status === 'successed') {
      this.notifyInfo = {};
      this.onNotify();
    }
  }

  async onNotify() {
    const params = {
      uid: this.uid,
    };
    const { data } = await importModel.onNotify(params);
    this.initProgress(data.data);
    if (data.data && data.data.state === 'valid' && this.currentStep === 1) {
      if (this.timer) {
        clearTimeout(this.timer);
      }
      this.currentStep = 2;
      this.fetchData();
      this.loading = false;
    } else if (data.data && data.data.state === 'completed') {
      if (this.timer) {
        clearTimeout(this.timer);
      }
      this.currentStep = 4;
      this.loading = false;
      this.$emit('finish');
    } else if (this.loading) {
      this.onTimer();
    }
  }

  onTimer() {
    if (this.timer) {
      clearTimeout(this.timer);
    }
    this.timer = setTimeout(() => {
      this.onNotify();
    }, 5000);
  }

  directConfirm() {
    this.currentStep = 3;
    this.loading = true;
    const params = {
      uid: this.uid,
      titles: this.tableTitles,
      primary_keys: this.validKeys,
      ...this.params,
    };
    this.$emit('confirm', params);
    this.submitExcelInfo(params);
    this.notifyInfo = {};
  }

  onConfirm() {
    this.loading = true;
    const params = {
      uid: this.uid,
      titles: this.tableTitles,
      primary_keys: this.validKeys,
      ...this.params,
    };
    this.$emit('confirm', params);
    this.submitExcelInfo(params);
    this.notifyInfo = {};
  }

  onCancel() {
    this.$emit('cancel');
  }

  //extends
  initProgress(notifyInfo: any) {
    const {
      valid_successed_rows_count: valS,
      valid_failed_rows_count: valF,
      successed_rows_count: uplS,
      failed_rows_count: uplF,
      total_rows_count: total,
    } = notifyInfo;
    const validFinish = Number(valS) + Number(valF);
    const uploadFinish = Number(uplS) + Number(uplF);
    if (this.currentStep === 1) {
      this.loadingTitle = '信息校验中';
      this.successPercent = Math.floor((Number(valS) / Number(total)) * 100);
      this.finishPercent = Math.floor((Number(validFinish) / Number(total)) * 100);
      this.notifyInfo = {
        total_count: total,
        finished_count: validFinish,
        successed_count: valS,
        failed_count: valF,
      };
    } else {
      this.loadingTitle = '信息上传中';
      this.successPercent = Math.floor((Number(uplS) / Number(total)) * 100);
      this.finishPercent = Math.floor((Number(uploadFinish) / Number(total)) * 100);
      this.notifyInfo = {
        total_count: total,
        finished_count: uploadFinish,
        successed_count: uplS,
        failed_count: uplF,
      };
    }
  }
}
</script>

<style lang="stylus" scoped>
.page
  position relative
  overflow hidden
  padding 71px 0px 61px
  width 100%
  height 100%
  .header
    position absolute
    top 0px
    left 0px
    z-index 1
    padding 14px 120px
    width 100%
    width 100%
    border-bottom 1px #e8e8e8 solid
    background #eee
  .main
    overflow auto
    padding 0px 20px 20px
    width 100%
    height 100%
    .loading-box
      padding 10px 0px
      text-align center
    .table-title
      padding 10px 10px 0px
      color #808080
  .footer
    position absolute
    bottom 0px
    left 0px
    z-index 1
    display flex
    justify-content flex-end
    padding 10px
    width 100%
    border-top 1px #e8e8e8 solid
    background #fff
    button
      min-width 80px
</style>
