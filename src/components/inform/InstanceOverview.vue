<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class InstanceOverview extends Vue {
  @Prop({ type: Object, default: '' }) flowable!: IObject;

  get info() {
    return this.flowable || {};
  }

  richTextFormat(value: string) {
    value = value.replace(/<\/?.+?>/g, '');
    value = value.replace(/\s+/g, '');
    value = value.replace(/&nbsp/g, '');
    if (value.length > 30) {
      return value.slice(0, 30) + '...';
    }
    return value;
  }
}
</script>

<template lang="pug">
.instance-overview
  a-timeline.advise-overview(v-if="info.type === 'Inform::Advise'")
    a-timeline-item
      router-link.advise(:to="`/portal/advises/${info.id}`")
        .title.one-line {{ info.title }}
        .footer.flex-between
          span {{ (info.tag_list || []).map(tag=> `#${tag}`).join(' ') }}
          span {{ $moment(info.publish_time).format('YYYY-MM-DD') }}
  .news-overview(v-else)
    router-link.headline(:to="`/portal/news/${info.id}`")
      .new-part1
        img(:src="info.cover_image" height="60" width="60")
        .title.two-line {{ info.title }}
        .tag.text-gray {{ (info.tag_list || []).map(tag=> `#${tag}`).join(' ') }}
      .content {{ richTextFormat(info.content || '') }}
      .time.text-gray {{ $moment(info.publish_time).format('YYYY-MM-DD') }}
  .dialog-content
    .title
      h3 {{ info.title }}
    .info
      span {{ `发布时间：${$moment(info.publish_time).format('YYYY-MM-DD')}` }}
      span {{ `标签：${(info.tag_list || []).join(' ') || '无'}` }}
    .content(v-html="info.content")
</template>

<style lang="stylus" scoped>
.dialog-content
  .title
    margin-top 49px
    text-align center
    font-size 18px
  .info
    display flex
    justify-content center
    margin 14px 0 22px
    text-align center
    span
      margin 0 6px

.instance-overview
  border-bottom 1px solid #E8E8E8
  .advise-overview
    padding-top 10px
    .advise
      display block
      margin-top 0px
      &:hover
        .title
          color rgba(16, 130, 204, 1)
          cursor pointer
      .title
        margin-bottom 6px
        color rgba(56, 56, 56, 1)
        font-weight 500
        font-size 14px
        line-height 20px
      .footer
        margin-bottom 4px
        color rgba(166, 166, 166, 1)
        font-weight 400
        font-size 12px
        line-height 12px
  .news-overview
    .headline
      display block
      padding 18px 0
      border-bottom 1px solid #E8E8E8
      &:last-child
        border-bottom 0px solid white
      .new-part1
        position relative
        display block
        margin-bottom 10px
        padding-left 70px
        height 60px
        &:hover
          cursor pointer
          .title
            color rgba(16, 130, 204, 1)
            text-decoration underline
        img
          position absolute
          top 0
          left 0
        .title
          margin-bottom 10px
          min-height 40px
          color black
          font-weight 500
          font-size 16px
          line-height 20px
        .tag
          min-height 12px
          font-size 12px
          line-height 12px
      .content
        margin-bottom 10px
        color #5F5F5F
        font-weight 400
        font-size 12px
        line-height 16px
      .time
        min-height 12px
        font-size 12px
        line-height 12px
</style>
