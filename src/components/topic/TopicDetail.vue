<script lang="ts">
/**
 * topic
 * 论坛帖子列表 - 单项条目
 */
import { Component, Vue, Prop } from 'vue-property-decorator';
import { ITopic } from '@/service/topic';

@Component({
  components: {},
})
export default class TopicDetail extends Vue {
  @Prop({ type: Object, default: () => ({ user: {} }) }) topic!: ITopic;

  get currentUser() {
    return this.$store.state.currentUser || {};
  }
  get files() {
    return this.topic.attachments ? this.topic.attachments.files || [] : [];
  }

  isMine() {
    return this.$utils.isMine(this.topic.user_id, this.topic.user_type);
  }
}
</script>

<template lang="pug">
.topic-detail
  .title
    .title-tags
      TaTag.title-tag(size="small" color="#3DA8F5" v-if="topic.suggest")
        | 置顶
      TaTag.title-tag(size="small" color="#FF4F3E" v-if="topic.is_new")
        | 新
    .title-text
      | {{ topic.title }}
  .extra
    .author-info
      a-avatar.avatar(:size="20" slot="avatar")
        | {{ topic.user && topic.user.name && topic.user.name.charAt(0) }}
      span {{ topic.user && topic.user.name }}
      span(v-if="isMine()") (我)
      span(v-else-if="topic.user_type === 'Teacher'") (教师)
      span.time  {{ topic.created_at | format }}
    .statistic
      a-icon.icon(type="message")
      span.count {{ topic.comment_count }}
      a-icon.icon(type="eye")
      span.count {{ topic.view_count }}
      a-icon.icon(type="star")
      span.count {{ topic.star_count }}
      a-icon.icon(type="like")
      span.count {{ topic.like_count }}
  .detail.ck-content(v-html="topic.body")
  .attachments
    Attachments(:attachments="files")
</template>

<style lang="stylus" scoped>
.topic-detail
  padding 20px 0
  .title
    margin-bottom 20px
    color #333333
    font-weight 500
    font-size 16px
    line-height 0
    .title-text
      display inline-block
      margin-right 8px
      vertical-align middle
      line-height 24px
    .title-tags
      display inline-block
      padding 1px 0
      vertical-align middle
      .title-tag
        margin-right 4px
        vertical-align middle
  .extra
    display flex
    justify-content space-between
    margin-bottom 27px
    font-weight 500
    font-size 14px
    line-height 20px
    .author-info
      display flex
      align-items center
      color #333333
      .avatar
        margin-right 6px
        background-color #58A8EF
      .time
        margin-left 6px
        color #B2B2B2
    .statistic
      color rgba(178, 178, 178, 1)
      .count
        margin-left 4px
      .icon
        margin-left 20px
        font-size 16px
  .attachments
    margin-top 30px
</style>
