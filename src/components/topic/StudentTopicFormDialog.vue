<script lang="ts">
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import { ITopic, TopicService, TopicType } from '@/service/topic';
import MainModal from '../global/MainModal.vue';
import StepToolbar from '../global/StepToolbar.vue';

@Component({
  components: {
    MainModal,
    StepToolbar,
  },
})
export default class StudentTopicFormDialog extends Vue {
  @Model('input', { type: Boolean, default: false }) private value!: boolean;
  @Prop({ type: Object, default: () => ({}), required: true }) private topic!: ITopic;
  @Prop({ type: Boolean, default: false }) private loading!: boolean;

  formData: ITopic = {
    attachments: {
      files: [],
    },
  };
  fileAllSettled: boolean = true;

  get visible() {
    return this.value;
  }
  set visible(visible: boolean) {
    this.$emit('input', visible);
  }

  get TopicType() {
    return TopicType;
  }
  get topicOption() {
    return TopicService.teaching.topicUiOptions[this.topic.type || TopicType.Forum];
  }
  get title() {
    const typeName = this.topicOption.label;
    return this.topic.id ? `编辑${typeName}` : `新建${typeName}`;
  }
  get canPublish() {
    return this.formData.title && this.formData.body && this.fileAllSettled;
  }

  @Watch('value')
  onDialogOpen() {
    if (this.value) {
      this.initData();
    }
  }

  initData() {
    const { type, attachments } = this.topic;
    this.formData = {
      ...this.topic,
      type: type || TopicType.Forum,
      attachments: attachments || {
        files: [],
      },
    };
  }

  publish() {
    if (this.canPublish) {
      this.$emit('publish', {
        ...this.formData,
        state: 'published',
      } as ITopic);
    }
  }
}
</script>

<template lang="pug">
MainModal(v-model="visible")
  .topic-form-header(slot="title")
    StepToolbar(
      :title="title"
      :bordered="false"
      size="large")
  .topic-form-content
    .content
      input.title-input(placeholder="请输入标题" v-model="formData.title")
      FileUploader.uploader(v-model="formData.attachments.files" :isAllSettled.sync="fileAllSettled" :useCdn="true")
      a-textarea.body-editor(placeholder="请输入内容" v-model="formData.body" :autoSize="{ minRows: 6 }")
  .topic-form-footer(slot="footer")
    a-button(
      key="publish"
      :disabled="!canPublish"
      type="primary" size="large" v-debounce-click="publish" :loading="loading")
      | 发布
</template>

<style lang="stylus" scoped>
.topic-form-header
  margin -16px -24px
.topic-form-content
  padding 20px
  .content
    .title-input
      font-size 24px
      font-weight 500
      color #383838
      line-height 34px
      width 100%
      margin-bottom 20px
      border none
      outline none
    .uploader
      width 100%
      margin-bottom 20px
    .body-editor
      width 100%
      margin-bottom 20px
      font-size 16px
      padding 12px
.topic-form-footer
  width 100%
  text-align right
</style>
