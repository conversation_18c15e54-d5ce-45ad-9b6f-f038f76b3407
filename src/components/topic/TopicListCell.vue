<script lang="ts">
/**
 * topic
 * 论坛帖子列表 - 单项条目
 */
import { Component, Vue, Prop } from 'vue-property-decorator';
import { ITopic, TopicType } from '@/service/topic';
import TopicOperations from './TopicOperations.vue';

@Component({
  components: {
    TopicOperations,
  },
})
export default class TopicListCell extends Vue {
  @Prop({ type: Object, default: () => ({ user: {} }) }) topic!: ITopic;
  @Prop({ type: String }) to!: string;
  @Prop({ type: Boolean, default: true }) canEdit!: boolean;

  get TopicType() {
    return TopicType;
  }
  get currentUser() {
    return this.$store.state.currentUser || {};
  }
  get operationMap() {
    return (this.topic.operations || []).reduce(
      (power: IObject, key: string) => ({
        ...power,
        [key]: true,
      }),
      {},
    );
  }

  onClick(...args: any) {
    if (this.to) {
      this.$router.push(this.to);
    } else {
      this.$emit('click', ...args);
    }
  }
  moveToTop() {
    this.$emit('toTop', this.topic);
  }
  moveToBottom() {
    this.$emit('toBottom', this.topic);
  }
  onEdit() {
    this.$emit('edit', this.topic);
  }
  onDelete() {
    this.$emit('delete', this.topic);
  }
  isMine() {
    return this.$utils.isMine(this.topic.user_id, this.topic.user_type);
  }
}
</script>

<template lang="pug">
.topic-cell(@click="onClick")
  .type
    TaTag(size="small" type="warning" v-if="topic.type === TopicType.Notice")
      a-icon(type="notification" theme="filled")
      |  通知
    TaTag(size="small" type="success" v-else-if="topic.type === TopicType.Discuss")
      a-icon(type="wechat" theme="filled")
      |  讨论
    TaTag(size="small" type="primary" v-else-if="topic.type === TopicType.Forum")
      a-icon(type="message" theme="filled")
      |  论坛
    TaTag(size="small" type="danger" v-else-if="topic.type === TopicType.Question")
      a-icon(type="copy")
      |  试卷
  .content
    .header
      .title
        .title-text {{ topic.title }}
        .title-tags
          TaTag.title-tag(size="small" color="#3DA8F5" v-if="topic.suggest")
            | 置顶
          TaTag.title-tag(size="small" color="#FF4F3E" v-if="topic.is_new")
            | 新
          TaTag.title-tag(
            size="small"
            color="#FA8C15"
            v-if="topic.comment_expire_at")
            | 评论截止{{ topic.comment_expire_at | format('M月D日 HH:mm') }}
      TopicOperations.actions(
        :topic="topic"
        :canEdit="canEdit"
        @edit="onEdit"
        @delete="onDelete"
        @toTop="moveToTop"
        @toBottom="moveToBottom"
      )
    .extra
      .author-info
        span
          span 发布者 {{ topic.user && topic.user.name }}
          span(v-if="isMine()") (我)
          span(v-else-if="topic.user_type === 'Teacher'") (教师)
          a-divider(type="vertical")
          span(v-if="topic.comment && topic.comment.id")
            | @{{ topic.comment.user_name }} 最后回复于{{ $moment(topic.comment.created_at).fromNow() }}
          span(v-else) 发布于{{ $moment(topic.created_at).fromNow() }}
      .statistic
        a-icon.icon(type="message")
        span.count {{ topic.comment_count }}
        a-icon.icon(type="eye")
        span.count {{ topic.view_count }}
        a-icon.icon(type="star")
        span.count {{ topic.star_count }}
        a-icon.icon(type="like")
        span.count {{ topic.like_count }}
</template>

<style lang="stylus" scoped>
.topic-cell
  padding 22px 0
  border-bottom 1px solid rgba(0,0,0,0.1)
  cursor pointer
  display flex
  &:last-child
    border-bottom none
  &:hover
    .content .header
      .title
        color #3DA8F5
      .actions
        display block
  .type
    flex-shrink 0
    line-height 0
    padding 1px 0
  .content
    padding 0 8px
    flex-grow 1
    .header
      display flex
      margin-bottom 6px
      align-items flex-start
      .title
        flex-grow 1
        font-size 16px
        font-weight 500
        color #333333
        padding-right 16px
        line-height 0
        .title-text
          margin-right 8px
          vertical-align middle
          line-height 24px
          display inline-block
        .title-tags
          padding 1px 0
          display inline-block
          vertical-align middle
          .title-tag
            vertical-align middle
            margin-right 4px
      .actions
        flex-shrink 0
        padding 1px 0
        display none
    .extra
      display flex
      justify-content space-between
      font-size 14px
      font-weight 500
      line-height 20px
      .author-info
        color rgba(166,166,166,1)
      .statistic
        color rgba(178,178,178,1)
        .count
          margin-left 4px
        .icon
          margin-left 20px
          font-size 16px
</style>
