<script lang="ts">
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import { ITopic, TopicService, TopicType } from '@/service/topic';
import MainModal from '../global/MainModal.vue';
import StepToolbar from '../global/StepToolbar.vue';
import QuestionSetSelector from '../teaching/QuestionSetSelector.vue';
import { QuestionSetService } from '../../service/question_set';

@Component({
  components: {
    MainModal,
    StepToolbar,
    QuestionSetSelector,
  },
})
export default class QuestionTopicFormDialog extends Vue {
  @Model('input', { type: Boolean, default: false }) private value!: boolean;
  @Prop({ type: Object, default: () => ({}), required: true }) private topic!: ITopic;
  @Prop({ type: Number }) private courseId!: number;
  @Prop({ type: Boolean, default: false }) private loading!: boolean;

  editorVisible: boolean = false;
  currentStep: string = 'content';
  formData: ITopic = {};
  publishButtonActivated: boolean = false;
  setting: IObject = {
    has_expire_at: false,
  };
  // question set
  originQuestionSets: IObject[] = [];
  questionSets: IObject[] = [];
  questionSetVisible: boolean = false;

  get visible() {
    return this.value;
  }
  set visible(visible: boolean) {
    this.$emit('input', visible);
  }

  get TopicType() {
    return TopicType;
  }
  get topicOption() {
    return TopicService.teaching.topicUiOptions[this.topic.type || TopicType.Forum];
  }
  get title() {
    const typeName = this.topicOption.label;
    return this.topic.id ? `编辑${typeName}` : `新建${typeName}`;
  }
  get steps() {
    const typeName = this.topicOption.label;
    return [
      { key: 'content', title: `${typeName}内容`, icon: 'info-circle' },
      { key: 'setting', title: `${typeName}设置`, icon: 'setting' },
    ];
  }
  get canPublish() {
    return this.questionSets.length && this.formData.title && this.formData.body;
  }

  @Watch('value', { immediate: true })
  onDialogOpen() {
    if (this.value) {
      this.initData();
      // 弹出框嵌入富文本，会导致富文本无法探测宽度，造成工具条收窄，延迟显示富文本
      this.editorVisible = false;
      setTimeout(() => {
        this.editorVisible = true;
      }, 200);
    }
  }

  initData() {
    const { id, type, suggest, comment_expire_at } = this.topic;
    this.currentStep = 'content';
    this.setting.has_expire_at = !!this.topic.comment_expire_at;
    this.formData = {
      ...this.topic,
      suggest: !!suggest,
      comment_expire_at: comment_expire_at && this.$moment(comment_expire_at).toISOString(),
    };
    // 编辑时获取列表
    if (id) {
      this.fetchQuestionSets();
    }
  }

  async fetchQuestionSets() {
    const { data } = await QuestionSetService.Teaching.topicQuestionSets({
      courseId: this.topic.source_id!,
      topicId: this.topic.id!,
    });
    this.originQuestionSets = data.question_sets.concat();
    this.questionSets = data.question_sets.concat();
  }

  publish() {
    if (this.canPublish) {
      const originIds = this.originQuestionSets.map(o => o.id);
      const newQuestionSets = this.questionSets.filter(o => !originIds.includes(o.id));
      this.$emit(
        'publish',
        {
          ...this.formData,
          body: this.formData.body || ' ',
          state: 'published',
        },
        newQuestionSets,
      );
    }
  }

  toSettingTab() {
    this.publishButtonActivated = false;
    this.currentStep = 'setting';
    setTimeout(() => {
      this.publishButtonActivated = true;
    }, 1000);
  }
  // question set
  onSelectQuestionSet(questionSet: IObject, questionCatalog: IObject) {
    if (this.questionSets.find(o => o.id === questionSet.id)) {
      this.$message.warning('试卷已存在');
      return;
    }
    this.questionSets.push({
      ...questionSet,
      question_catalog_id: questionCatalog.id,
    });
  }
  deleteConfirm(set: IObject, index: number) {
    this.$confirm({
      title: '提醒',
      content: '确定要删除该试卷你吗？',
      onOk: () => {
        this.questionSets.splice(index, 1);
        QuestionSetService.UserOwn.delete(set.id);
      },
    });
  }
}
</script>

<template lang="pug">
MainModal(v-model="visible")
  .topic-form-header(slot="title")
    StepToolbar(
      v-model="currentStep"
      :title="title"
      :bordered="false"
      :steps="steps"
      mode="steps"
      size="large"
      @change="publishButtonActivated = true")
  .topic-form-content
    .content(v-if="currentStep === 'content'")
      input.title-input(placeholder="请输入标题" v-model="formData.title")
      .question-set
        TaTag.tag(type="primary" v-for="(set, index) in questionSets" :key="set.id")
          span {{ set.name }}&nbsp;
          a-icon.close-btn(type="close-circle" @click="deleteConfirm(set, index)")
        a-button.tag(@click="questionSetVisible = true" icon="plus")
          | 关联试卷
      RichEditor.body-editor(
        v-if="editorVisible"
        placeholder="相关描述"
        v-model="formData.body")
    .setting(v-if="currentStep === 'setting'")
      a-form
        a-form-item(label="试卷截止时间")
          a-radio-group(
            v-model="setting.has_expire_at"
            :options="[{ label: '无截止时间', value: false }, { label: '设置截止时间', value: true }]")
          .picker-box
            a-date-picker.picker(
              v-if="setting.has_expire_at"
              v-model="formData.comment_expire_at"
              placeholder="设置试卷截止时间"
              format="YYYY-MM-DD HH:mm"
              :showTime="true")
        a-form-item(label="是否置顶")
          a-radio-group(
            v-model="formData.suggest"
            :options="[{ label: '不置顶', value: false }, { label: '置顶', value: true }]")
  .topic-form-footer(slot="footer")
    a-button(
      v-if="currentStep === 'content'"
      key="next"
      type="primary" size="large" @click.prevent="toSettingTab")
      | 下一步
    template(v-if="currentStep === 'setting'")
      TaTag.tag(type="warning" v-if="!canPublish")
        | 请先完善上一步的表单内容
      a-button(
        v-if="currentStep === 'setting'"
        key="publish"
        :disabled="!canPublish"
        type="primary" size="large" v-debounce-click="publish" :loading="loading")
        | 发布

  QuestionSetSelector(
    v-model="questionSetVisible"
    @select="onSelectQuestionSet")
</template>

<style lang="stylus" scoped>
.topic-form-header
  margin -16px -24px

.topic-form-content
  padding 20px
  .content
    .title-input
      margin-bottom 10px
      padding 0 10px
      width 100%
      outline none
      border none
      color #383838
      font-weight 500
      font-size 24px
      line-height 34px
    .body-editor
      min-height 300px
      width 100%
    .question-set
      margin 16px 0
      .tag
        margin-right 10px
        margin-bottom 10px
        vertical-align middle
        .close-btn
          cursor pointer
          font-size 16px
          margin-left 10px
          &:hover
            font-weight bold
            transform scale(1.1)
  .setting
    .picker-box
      .picker
        width 100% !important

.topic-form-footer
  width 100%
  text-align right
  .tag
    margin-right 16px
</style>
