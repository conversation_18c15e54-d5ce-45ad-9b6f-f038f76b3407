<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { ITopic } from '@/service/topic';

@Component({
  components: {},
})
export default class TopicOperations extends Vue {
  @Prop({ type: Object, default: () => ({ user: {} }) }) topic!: ITopic;
  @Prop({ type: Boolean, default: true }) canEdit!: boolean;

  get isStudent() {
    return this.$store.getters.currentRole === 'student';
  }
  get operationMap() {
    return (this.topic.operations || []).reduce(
      (power: IObject, key: string) => ({
        ...power,
        [key]: true,
      }),
      {},
    );
  }

  moveToTop() {
    this.$emit('toTop', this.topic);
  }
  moveToBottom() {
    this.$emit('toBottom', this.topic);
  }
  onEdit() {
    this.$emit('edit', this.topic);
  }
  onDelete() {
    this.$emit('delete', this.topic);
  }
}
</script>

<template lang="pug">
.topic-operations
  template(v-if="canEdit && operationMap.update")
    template(v-if="!isStudent")
      TextButton(icon="to-top" @click.stop="moveToTop" v-if="!topic.suggest")
        | 置顶
      TextButton(icon="close-circle" @click.stop="moveToBottom" v-if="topic.suggest")
        | 取消置顶
    TextButton(icon="edit" @click.stop="onEdit")
      | 编辑
  template(v-if="canEdit && operationMap.destroy")
    PopoverConfirm(
      title="删除"
      placement="bottomRight"
      type="danger"
      content="确定要删除该数据吗？"
      @confirm="onDelete")
      TextButton(icon="delete" @click.stop="")
        | 删除
</template>

<style lang="stylus" scoped>
.topic-operations
  display inline-block
  button
    margin-left 22px
    color #B2B2B2
    font-weight 500
    &:hover
      color #3DA8F5
</style>
