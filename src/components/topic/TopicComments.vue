<template lang="pug">
.topic-comments
  .comment-container(v-if="canEdit")
    FileUploader.uploader-box(
      :useCdn="true"
      :isAllSettled="fileAllSettled"
      v-model="files")
      .comment-form
        a-button.trigger(shape="circle" size="large" icon="paper-clip")
        a-input.comment-input(
          v-model="comment"
          size="large"
          placeholder="说点什么吧 ...")
        a-button(
          type="primary"
          size="large"
          :disabled="!(comment || fileAllSettled)"
          :loading="commentLoading"
          @click.stop="createComment") 发表评论
  .comments
    .comment-meta-info
      .title 全部评论 · {{ commentResponse.total_count }}
      .meta
        template(v-if="topic.comment_expire_at")
          span.text-warning(v-if="isExpired") 学生评论于 {{ topic.comment_expire_at | format('M月D日HH:mm') }} 截止
          span(v-else) 学生评论截止{{ topic.comment_expire_at | format('M月D日HH:mm') }}
          a-divider(type="vertical")
        span {{ topic.visible_conf === 'visible' ? '互相可见' : '互不可见' }}
        span.filter
          span.filter-item(
            :class="{ 'filter-item-active': authorType === '' }"
            @click="onAuthorTypeChange('')")
            | 全部评论
          a-divider(type="vertical")
          span.filter-item(
            :class="{ 'filter-item-active': authorType === 'Teacher' }"
            @click="onAuthorTypeChange('Teacher')")
            | 仅老师
          a-divider(type="vertical")
          span.filter-item(
            :class="{ 'filter-item-active': authorType === 'Student' }"
            @click="onAuthorTypeChange('Student')")
            | 仅学生
        span.filter
          span.filter-item(@click="fetchComments(1)")
            | 刷新
    template(v-if="commentResponse.comments.length > 0")
      CommentTree(
        v-loading="commentLoading"
        :comments.sync="commentResponse.comments"
        :disabled="!canEdit"
        @edit="updateComment"
        @reply="createComment"
        @destroy="deleteComment")
    .pagination(v-if="commentResponse.total_pages > 1")
      a-pagination(
        showQuickJumper
        size="large"
        :current="commentResponse.current_page"
        :defaultPageSize="20"
        :total="commentResponse.total_count"
        @change="fetchComments")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import CommentTree from '@/components/comment/CommentTree.vue';
import { CommentService, IComment } from '@/service/comment';
import { ITopic } from '@/service/topic';
import FileUploader from '../global/FileUploader.vue';

@Component({
  components: {
    CommentTree,
    FileUploader,
  },
})
export default class TopicComments extends Vue {
  @Prop({ type: Object }) topic!: ITopic;

  comment: string = '';
  files: object[] = [];
  fileAllSettled: boolean = true;
  commentLoading: boolean = false;
  commentResponse: any = {
    comments: [],
    total_count: 0,
    current_page: 1,
    total_pages: 1,
  };
  isExpired: boolean = false; // 是否过期
  timer: any = null;
  // filter
  authorType: string = '';

  get courseId() {
    return +this.topic.source_id!;
  }
  get role() {
    return this.$store.getters.currentRole;
  }
  get canEdit() {
    if (this.role === 'student') {
      return this.topic.can_reply && !this.isExpired;
    }
    // 教师端，不受 评论到期时间 影响
    return this.topic.teach_permit === 'teaching';
  }

  @Watch('topic.id', { immediate: true })
  onTopicChange() {
    if (this.topic.id) {
      this.fetchComments(1);
      // 过期检查
      clearInterval(this.timer);
      this.timer = setInterval(this.onExpiredCheck, 10000);
      this.onExpiredCheck();
    }
  }

  beforeDestroy() {
    clearInterval(this.timer);
  }

  // 评论
  async fetchComments(page: number = this.commentResponse.current_page) {
    if (!this.topic.id) return;
    this.commentLoading = true;
    const { data } = await CommentService.teaching
      .fetchTopicComments({
        courseId: this.courseId,
        topicId: this.topic.id,
        isDiscuss: false,
        params: {
          page,
          per_page: 20,
          q: {
            user_type_eq: this.authorType,
          },
        },
      })
      .finally(() => {
        this.commentLoading = false;
      });
    this.commentResponse = data;
  }

  onExpiredCheck() {
    this.isExpired = this.topic.comment_expire_at ? new Date(this.topic.comment_expire_at) < new Date() : false;
  }

  // 创建 & 回复
  async createComment(comment: IComment) {
    try {
      this.commentLoading = true;
      const { files, body, parent_id } = comment;
      await CommentService.teaching.create({
        parent_id,
        commentable_type: 'Topic',
        commentable_id: this.topic.id!,
        body: body || this.comment,
        attachments: {
          files: files || this.files,
        },
      });
      this.comment = '';
      this.files = [];
      this.fileAllSettled = true;
      this.fetchComments();
      this.commentLoading = false;
    } catch (error) {
      this.commentLoading = false;
      this.$message.error('操作失败！');
    }
  }

  async updateComment(comment: IComment) {
    try {
      this.commentLoading = true;
      await CommentService.teaching.update(comment);
      this.fetchComments();
      this.commentLoading = false;
    } catch (error) {
      this.commentLoading = false;
      this.$message.error('操作失败！');
    }
  }

  async deleteComment(id: number) {
    try {
      this.commentLoading = true;
      await CommentService.teaching.deleteTopicComment({
        courseId: this.courseId,
        topicId: this.topic.id!,
        commentId: id,
      });
      this.fetchComments();
      this.commentLoading = false;
    } catch (error) {
      this.commentLoading = false;
      this.$message.error('操作失败！');
    }
  }

  onAuthorTypeChange(authorType: string) {
    this.authorType = authorType;
    this.fetchComments(1);
  }
}
</script>

<style lang="stylus" scoped>
.topic-comments
  margin-top 10px
  .comment-container
    padding 30px 0
    border-top 1px rgba(229, 229, 229, 1) solid
    border-bottom 1px rgba(229, 229, 229, 1) solid
    .uploader-box
      width 100%
    .comment-form
      display flex
      align-items center
      width 100%
      .trigger
        flex-shrink 0
      .comment-input
        margin 0 12px
        width 100%
  .comments
    padding-top 30px
    width 100%
    .comment-meta-info
      display flex
      justify-content space-between
      align-items center
      .title
        margin-bottom 8px
        color rgba(51, 51, 51, 1)
        font-weight 500
        font-size 16px
        line-height 20px
      .meta
        color #A6A6A6
        font-weight 500
        font-size 14px
        line-height 20px
        .filter
          margin-left 20px
          .filter-item
            color #808080
            cursor pointer
          .filter-item-active
            color #3DA8F5
            cursor default
    .pagination
      padding 20px 0px
      text-align right
</style>
