<script lang="ts">
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import { ITopic, TopicService, TopicType } from '@/service/topic';
import { StudentService, IStudent } from '@/service/student';
import MainModal from '../global/MainModal.vue';
import StepToolbar from '../global/StepToolbar.vue';

@Component({
  components: {
    MainModal,
    StepToolbar,
  },
})
export default class TopicFormDialog extends Vue {
  @Model('input', { type: Boolean, default: false }) private value!: boolean;
  @Prop({ type: Object, default: () => ({}), required: true }) private topic!: ITopic;
  @Prop({ type: Number }) private courseId!: number;
  @Prop({ type: Boolean, default: false }) private loading!: boolean;

  students: IStudent[] = [];
  editorVisible: boolean = false;
  currentStep: string = 'content';
  formData: ITopic = {
    view_permit: 'all',
    view_student_ids: [],
    view_students: [],
    reply_student_ids: [],
    reply_students: [],
    attachments: {
      files: [],
    },
  };
  fileAllSettled: boolean = true;
  publishButtonActivated: boolean = false;
  setting: IObject = {
    has_expire_at: false,
  };

  get visible() {
    return this.value;
  }
  set visible(visible: boolean) {
    this.$emit('input', visible);
  }

  get TopicType() {
    return TopicType;
  }
  get topicOption() {
    return TopicService.teaching.topicUiOptions[this.topic.type || TopicType.Forum];
  }
  get title() {
    const typeName = this.topicOption.label;
    return this.topic.id ? `编辑${typeName}` : `新建${typeName}`;
  }
  get steps() {
    const typeName = this.topicOption.label;
    return [
      { key: 'content', title: `${typeName}内容`, icon: 'info-circle' },
      { key: 'setting', title: `${typeName}设置`, icon: 'setting' },
    ];
  }
  get canPublish() {
    return this.formData.title && this.formData.body && this.publishButtonActivated;
  }

  @Watch('value', { immediate: true })
  onDialogOpen() {
    if (this.value) {
      this.initData();
      this.fetchStudents();
      // 弹出框嵌入富文本，会导致富文本无法探测宽度，造成工具条收窄，延迟显示富文本
      this.editorVisible = false;
      setTimeout(() => {
        this.editorVisible = true;
      }, 200);
    }
  }

  initData() {
    const {
      type,
      is_new,
      suggest,
      view_permit = 'all',
      reply_permit = 'all',
      view_student_ids,
      view_students,
      reply_student_ids,
      reply_students,
      visible_conf,
      comment_expire_at,
      attachments,
    } = this.topic;

    this.currentStep = 'content';
    this.setting.has_expire_at = !!this.topic.comment_expire_at;
    this.formData = {
      ...this.topic,
      type: type || TopicType.Forum,
      suggest: !!suggest,
      is_new: !!is_new,
      view_permit,
      reply_permit,
      visible_conf: visible_conf || 'visible',
      view_student_ids: view_student_ids || [],
      view_students: view_students || [],
      reply_student_ids: reply_student_ids || [],
      reply_students: reply_students || [],
      comment_expire_at: comment_expire_at && (this.$moment(comment_expire_at) as any),
      attachments: attachments || {
        files: [],
      },
    };
  }

  async fetchStudents() {
    if (!this.courseId) {
      this.students = [];
      return;
    }
    const { data } = await StudentService.teaching.fetchByCourse({
      courseId: this.courseId,
      params: { page: 1, per_page: 1000 },
    });
    this.students = data.students;
  }

  publish() {
    if (this.canPublish) {
      this.$emit('publish', {
        ...this.formData,
        comment_expire_at: this.setting.has_expire_at ? this.formData.comment_expire_at : null,
        state: 'published',
      } as ITopic);
    }
  }

  onViewPermitChange(v: any) {
    if (v === 'part') {
      return;
    } else {
      this.formData.view_students = [];
      this.formData.view_student_ids = [];
    }
  }
  onSelectViewStudents(ids: number[], students: any) {
    this.setting.view_students = students;
    this.formData.view_student_ids = ids;
  }
  onReplyPermitChange(v: any) {
    if (v === 'part') {
      return;
    } else {
      this.formData.reply_students = [];
      this.formData.reply_student_ids = [];
    }
  }
  onSelectReplyStudents(ids: number[], students: any) {
    this.setting.reply_students = students;
    this.formData.reply_student_ids = ids;
  }
  toSettingTab() {
    this.publishButtonActivated = false;
    this.currentStep = 'setting';
    setTimeout(() => {
      this.publishButtonActivated = true;
    }, 1000);
  }
}
</script>

<template lang="pug">
MainModal(v-model="visible")
  .topic-form-header(slot="title")
    StepToolbar(
      v-model="currentStep"
      :title="title"
      :bordered="false"
      :steps="steps"
      mode="steps"
      size="large"
      @change="publishButtonActivated = true")
  .topic-form-content
    .content(v-if="currentStep === 'content'")
      input.title-input(placeholder="请输入标题" v-model="formData.title")
      template(v-if="editorVisible")
        FileUploader.uploader(v-model="formData.attachments.files" :isAllSettled.sync="fileAllSettled" :useCdn="true")
        RichEditor.body-editor(placeholder="请输入内容" v-model="formData.body")
    .setting(v-if="currentStep === 'setting'")
      a-form
        template(v-if="formData.type === TopicType.Forum")
          a-form-item(label="谁可以看")
            a-radio-group(
              v-model="formData.view_permit"
              :options="[{ label: '全部学生', value: 'all' }, { label: '部分学生', value: 'part' }]"
              @change="onViewPermitChange")
            a-select(
              v-if="formData.view_permit === 'part'"
              v-model="formData.view_student_ids"
              mode="multiple"
              showSearch
              optionFilterProp="label"
              placeholder="选择谁可以看此论坛，不选表示公开")
              a-select-option(v-for="student in students" :value="student.id" :key="student.id" :label="student.name")
                | {{ student.name }}
          a-form-item(label="评论是否相互可见")
            a-radio-group(
              v-model="formData.visible_conf"
              :options="[{ label: '相互可见', value: 'visible' }, { label: '互不可见', value: 'invisible' }]")
          a-form-item(label="评论截止时间")
            a-radio-group(
              v-model="setting.has_expire_at"
              :options="[{ label: '无截止时间', value: false }, { label: '设置截止时间', value: true }]")
            .picker-box
              a-date-picker.picker(
                v-if="setting.has_expire_at"
                v-model="formData.comment_expire_at"
                placeholder="设置学生评论截止时间"
                format="YYYY-MM-DD HH:mm"
                :showTime="true")
        template(v-if="formData.type === TopicType.Discuss")
          a-form-item(label="谁可以看")
            a-radio-group(
              v-model="formData.reply_permit"
              :options="[{ label: '全部学生', value: 'all' }, { label: '部分学生', value: 'part' }]"
              @change="onReplyPermitChange")
            a-select(
              v-if="formData.reply_permit === 'part'"
              v-model="formData.reply_student_ids"
              mode="multiple"
              showSearch
              optionFilterProp="label"
              placeholder="选择谁可以参与此讨论")
              a-select-option(v-for="student in students" :value="student.id" :key="student.id" :label="student.name")
                | {{ student.name }}
          a-form-item(label="评论是否相互可见")
            a-radio-group(
              v-model="formData.visible_conf"
              :options="[{ label: '相互可见', value: 'visible' }, { label: '互不可见', value: 'invisible' }]")
          a-form-item(label="评论截止时间")
            a-radio-group(
              v-model="setting.has_expire_at"
              :options="[{ label: '无截止时间', value: false }, { label: '设置截止时间', value: true }]")
            .picker-box
              a-date-picker.picker(
                v-if="setting.has_expire_at"
                v-model="formData.comment_expire_at"
                placeholder="设置评论截止时间"
                format="YYYY-MM-DD HH:mm"
                :showTime="true")
        a-form-item(label="是否置顶")
          a-radio-group(
            v-model="formData.suggest"
            :options="[{ label: '不置顶', value: false }, { label: '置顶', value: true }]")
  .topic-form-footer(slot="footer")
    a-button(
      v-if="currentStep === 'content'"
      key="next"
      :disabled="!fileAllSettled"
      type="primary" size="large" @click.prevent="toSettingTab")
      | 下一步
    template(v-if="currentStep === 'setting'")
      TaTag.tag(type="warning" v-if="!canPublish")
        | 请先完善上一步的表单内容
      a-button(
        v-if="currentStep === 'setting'"
        key="publish"
        :disabled="!canPublish"
        type="primary" size="large" v-debounce-click="publish" :loading="loading")
        | 发布
</template>

<style lang="stylus" scoped>
.topic-form-header
  margin -16px -24px

.topic-form-content
  padding 20px
  .content
    .title-input
      margin-bottom 10px
      padding 0 10px
      width 100%
      outline none
      border none
      color #383838
      font-weight 500
      font-size 24px
      line-height 34px
    .uploader
      padding 10px
      width 100%
    .body-editor
      min-height 300px
      width 100%
  .setting
    .users
      padding 12px
      border-radius 4px
      background rgba(245, 245, 245, 1)
      .users-count
        margin-bottom 2px
        color rgba(166, 166, 166, 1)
        font-weight 400
        font-size 14px
        line-height 20px
      .users-name
        color rgba(128, 128, 128, 1)
        font-weight 400
        font-size 14px
        line-height 20px
    .picker-box
      .picker
        width 100% !important

.topic-form-footer
  width 100%
  text-align right
  .tag
    margin-right 16px
</style>
