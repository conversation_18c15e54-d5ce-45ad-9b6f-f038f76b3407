<template lang="pug">
.hr-teachers-contact-index
  .header
    TaTitleHeader(title="通讯录")
      Searcher(
        v-model="queryObject"
        :variables="['name', 'code']"
        tips="检索教师"
        placeholder="检索教师")
  .main
    .teacher-list()
      Empty(v-if="groups.length === 0")
      .scroll-body(
         v-infinite-scroll="loadMore"
        infinite-scroll-disabled="hrTeacherStore.loading"
        infinite-scroll-distance="10"
      )
        .module(v-for="(group, index) in groups" :key="index")
          .module-top
            .flex-between
              strong {{ group.label }}
              span {{ group.teachers.length }}
          .module-middle
            .user-list-item(
              v-for="(item,index) in group.teachers"
              :key="index"
              @click="onShow(item,index)")
              TaAvatar.avatar(
                :url="item.avatar"
                :name="item.name || item.code"
                fontSize="20px"
                :sex="item.sex"
                :short="true"
                )
              .user-name {{ item.name||item.code }}
              a-icon(
                :class="{'star-active':item.star}"
                type="star"
                :theme="item.star?'filled':'outlined'")
      .more
        span(v-if="hrTeacherStore.currentPage === hrTeacherStore.totalPages && hrTeacherStore.currentPage !== 1")
          a-divider 加载完成
        a-spin(tip="数据加载中..." v-if="hrTeacherStore.loading")
    .teacher-info
      .flex
        TaAvatar(
          :url="teacher.avatar"
          :name="teacher.name || teacher.code"
          fontSize="20px"
          :sex="teacher.sex"
          :short="true")
        .title {{ teacher.name || teacher.code }}
        a-icon(
          :class="{'star-active':teacher.star}"
          @click="star(teacher.id,!teacher.star)"
          type="star"
          :theme="teacher.star?'filled':'outlined'"
          style="cursor:pointer;")
      .module
        IconCell(icon="bold" :required="true" label="职工号" :value="teacher.code")
      .module
        IconCell(icon="man" label="姓名拼音" :value="teacher.name_pinyin")
        IconCell(icon="man" label="性别" :value="teacher.sex")
        IconCell(icon="mail" label="邮箱" :value="teacher.email")
      .module
        IconCell(icon="apartment" label="所属部门" :value="teacher.department_name")
        IconCell(icon="deployment-unit" label="所属学院" :value="teacher.college_name")
        IconCell(icon="trophy" label="职务" )
          template(slot="scope")
            a-tag(
              v-for="(item, index) in teacher.duties"
              :key="index"
              color="blue"
              style="margin: 3px; border: 0px;")
              | {{ item.name }}
</template>

/* 通讯录组件 */
<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import { log } from 'util';

@Component({
  components: {},
})
export default class HrTeachersContactIndex extends Vue {
  @Prop({ type: Object, default: () => ({}) }) hrTeacherStore!: IObject;

  private queryObject: IObject = {};
  private groups: any[] = [];
  private teacher: any = {};
  private currentIndex = 0;

  @Watch('queryObject')
  public watchQuery() {
    this.fetchdate();
  }

  public mounted() {
    this.fetchdate();
  }

  public async fetchdate(page: number = 1) {
    const params = {
      page,
      per_page: 20,
      q: {
        s: ['name_pinyin asc'],
        ...this.queryObject,
      },
    };
    const { data } = await this.hrTeacherStore.index(params);

    this.teacher = this.hrTeacherStore.records[this.currentIndex];
    const groups: any[] = data.current_page > 1 ? this.groups : [];
    (this.hrTeacherStore.records || []).forEach((item: any) => {
      const index = groups.findIndex((e: any) => e.label === item.first_letter);
      if (index >= 0) {
        groups[index].teachers.push(item);
      } else {
        groups.push({
          label: item.first_letter,
          teachers: [item],
        });
      }
    });
    this.groups = groups;
  }

  public onShow(val: any, index: number) {
    this.teacher = val;
    this.currentIndex = index;
  }

  public loadMore() {
    if (this.hrTeacherStore.currentPage < this.hrTeacherStore.totalPages) {
      this.fetchdate(this.hrTeacherStore.currentPage + 1);
    }
  }

  // 收藏
  async star(id: number, type: boolean) {
    await this.hrTeacherStore.sendMemberAction({
      action: type ? 'star' : 'unstar',
      id,
    });
    this.fetchdate();
  }
}
</script>

<style lang="stylus" scoped>
.hr-teachers-contact-index
  overflow hidden
  padding-top 58px
  width 100%
  height 100%
  .header
    // float left
    margin-top -58px
    width 100%
  .main
    display flex
    width 100%
    height 100%
    border 1px #e8e8e8 solid
    .teacher-list
      overflow scroll
      min-width 300px
      width 300px
      height 100%
      border-right 1px #e8e8e8 solid
      &::-webkit-scrollbar
        display none
      .module
        width 100%
        .module-top
          position sticky
          top 0
          z-index 99
          padding 10px 14px
          border-bottom 1px #eee solid
          background #f4f5f5
          box-shadow 0px 1px 4px 0px #eee
          strong
            color #383838
            font-size 20px
            line-height 20px
          span
            color #888
            font-size 14px
        .module-middle
          padding 10px
          width 100%
          .department
            width 120px
            text-align right
            font-weight 500
          .user-list-item
            display flex
            justify-content space-between
            align-items center
            margin-bottom 10px
            padding 0 5px 0 0
            height 60px
            border-width 0 0 1px 0
            border-style solid
            border-color #ededed
            .avatar
              flex 0 0 36px
              margin-right 10px
              cursor pointer
            .user-name
              flex 1
      .more
        padding 14px
        width 100%
        text-align center
    .teacher-info
      overflow auto
      padding 14px 24px 30px
      width 100%
      height 100%
      &::-webkit-scrollbar
        display none
      .title
        margin-right 10px
        margin-left 10px
        font-weight 500
        font-size 20px
        line-height 28px
      .module
        padding-top 12px
        border-bottom 1px #e6e6e6 solid
        .row
          display flex
          margin-bottom 24px
          width 100%
          img
            width 44px
            height 44px

.star-active
  color #FDE45F
  cursor pointer
</style>
