<template lang="pug">
.table-content
  table.table
    thead
      tr
        th.top-left-cell(align="left")
          span.column-desc 等级
          .line
          span.row-desc 类别
        th(v-for="(level, index) in levels", :key="index", align="left") {{ level.label }}
    tbody
      tr(v-if="categories.length === 0")
        td(:colspan="levels.length + 1" align="center") 暂无数据
      tr(v-for="(category, rowIndex) in categories" :key="rowIndex")
        th {{ category.name }}
        td(v-for="(level, colIndex) in category.levels" :key="colIndex"
          style="padding: 0px;")
          .cell(
            v-for="(item, index) in level.titles"
            :key="index"
            :class="{'active': filterActive(item.id)}"
            @click="onSelect(item)") {{ item.name }}
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import titleCategoryStore from '@/store/modules/res/title_category.store';

@Component({
  components: {},
})
export default class SelectTitle extends Vue {
  @Prop({ type: Array, default: () => [] }) private selects!: any[];
  @Prop({ type: String, default: 'Form' }) private pageType?: string;
  private names: any[] = [];
  private groups: any[] = [];
  private categories: any[] = [];
  private selectTitles: any[] = [];
  private titlesMap: object = {};
  get titleCategoryStore() {
    return titleCategoryStore || {};
  }
  get levels() {
    return [
      {
        label: '初级',
        value: 'primary',
      },
      {
        label: '中级',
        value: 'middle',
      },
      {
        label: '副高',
        value: 'vice',
      },
      {
        label: '高级',
        value: 'high',
      },
    ];
  }

  @Watch('selects', { immediate: true, deep: true })
  public watchChange() {
    this.selectTitles = this.selects;
  }

  public mounted() {
    titleCategoryStore.changeNamespace('teacher');
    this.fetchData();
  }

  public async fetchData() {
    const params = {
      page: 1,
      per_page: 1000,
    };
    const { data } = await titleCategoryStore.fetch(params);
    this.categories = data.title_categories.map((item: any) => ({
      ...item,
      levels: this.levels.map((level: any) => ({
        ...level,
        titles: item.titles.filter((e: any) => e.level === level.value),
      })),
    }));
  }

  public filterActive(id: number): boolean {
    return this.selectTitles.some((e: any) => e.id === id);
  }

  public onSelect(val: any) {
    if (this.pageType === 'View') return;
    const isExist: boolean = this.selectTitles.some((e: any) => e.id === val.id);
    if (isExist) {
      this.$confirm({
        title: '确定要继续执行此操作吗?',
        okText: '确定',
        okType: 'danger',
        cancelText: '取消',
        onOk: async () => {
          this.selectTitles = this.selectTitles.filter((e: any) => e.id !== val.id);
        },
        onCancel: () => {},
      });
    } else {
      this.selectTitles.push(val);
    }
    this.$emit('select', this.selectTitles);
  }
}
</script>

<style lang="stylus" scoped>
.table-content
  display flex
  height 100%
  .table
    width 100%
    border 1px solid #e6e6e6
    border-collapse collapse
    border-radius 3px
    thead, tbody
      tr
        td, th
          padding 12px 14px
          border 1px solid #e6e6e6
          color #606266
          font-weight 600
        td
          width 20%
        .cell
          padding 8px 14px
          width 100%
          height 100%
          border-top 1px solid #e6e6e6
          font-weight 400
          line-height 20px
          cursor pointer
          &:first-child
            border none
        .active
          background #3DA8F5
          color #fff
    .top-left-cell
      position relative
      overflow hidden
      min-width 170px
      max-width 170px
      width 170px
      font-size 14px
      .column-desc
        position absolute
        top 5px
        right 10px
      .line
        position absolute
        top 49%
        left -30px
        width 230px
        height 1px
        background #ebeef5
        transform rotateZ(14deg)
      .row-desc
        position absolute
        bottom 5px
        left 10px
</style>
