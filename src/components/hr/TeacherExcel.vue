<template lang="pug">
.page
  .header
    a-steps(:current="currentStep")
      a-step(v-for="(title, index) in steps" :key="index" :title="title")
  .main
    a-form(labelAlign="left")
      a-form-item(
        label="选择参考字段"
        :label-col="{ span: 3 }"
        :wrapper-col="{ span: 18 }")
        a-select(
          v-model="validKeys"
          mode="multiple"
          placeholder="请选择参考字段"
          style="width: 460px")
          a-select-option(
            v-for="(item, ind) in primaryKeys"
            :key="ind"
            :value="item.key")
            | {{ item.name }}
    AdminTable(
      :data="store.teachers"
      :totalCount="store.totalCount"
      :currentPage="store.currentPage"
      :totalPages="store.totalPages"
      :perPage="perPage"
      :showSizeChanger="true"
      :showHeader="true"
      @change="onChange")
      a-table-column(title="校验状态" :width="100" align="center" v-if="currentStep === 2")
        template(slot-scope="scope")
          template(v-if="scope.errors_text")
            a-tooltip(:title="scope.errors_text")
              a-icon(type="info-circle" theme="filled" style="font-size: 20px;").text-warning
          template(v-else)
            a-icon(type="check-circle" theme="filled" style="font-size: 20px").text-primary
      a-table-column(
        v-for="(dataTitle, index) in dataKeys"
        :key="index"
        :dataIndex="dataTitle"
        :width="120")
        span(slot="title")
          a-select(
            v-model="tableTitles[index]"
            placeholder="请选择表头"
            style="width: 100%; min-width: 100px")
            a-select-option(
              v-for="(item, ind) in teacherKeys"
              :key="ind"
              :value="item.key")
              | {{ item.label }}
          .table-title {{ dataTitle }}
  .footer
    a-button(size="large" @click="onCancel") 取消
    template(v-if="currentStep === 1")
      a-button(
        type="primary"
        size="large"
        @click="onModal") 确定校验
    template(v-else)
      a-button(
        type="primary"
        size="large"
        @click="onConfirm") 确定上传
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import teacherStore from '@/store/modules/hr/teacher.store';

@Component({
  components: {},
})
export default class TeacherExcel extends Vue {
  @Prop({ type: String, default: () => '' }) uid!: string;

  store: any = {};
  perPage: number = 20;
  dataKeys: string[] = [];
  currentStep: number = 1;
  steps: string[] = ['选择文件', '信息确认', '信息校验', '确定上传'];
  teacherKeys: any[] = [];
  primaryKeys: string[] = [];
  tableTitles: string[] = [];
  validKeys: string[] = ['code'];
  validInfo: any = {};
  timer: any = null;

  @Watch('uid', { immediate: true, deep: true })
  async watchChange() {
    await this.fetchData();
    this.fetchTeacherKeys();
  }

  async fetchData(page: number = 1) {
    const params = {
      page,
      per_page: this.perPage,
      uid: this.uid,
      row: this.currentStep === 2 ? 'valid_failed_rows' : '',
    };
    const { data } = await teacherStore.getExcelInfo(params);
    const results = data.data;
    this.dataKeys = results.titles || [];
    this.store = {
      currentPage: results.current_page,
      totalPages: results.total_pages,
      totalCount: results.total_count,
      teachers: (results.datas || []).map((item: any, index: any) => ({
        id: index + 1, // table rowKey
        ...(item.row || []).reduce((res: any, value: string, index: any) => {
          res[this.dataKeys[index]] = value;
          return res;
        }, {}),
        errors_text: (Object.keys(item.errors) || []).map((key: string) => item.errors[key]).join('，'),
      })),
    };
  }

  async fetchTeacherKeys() {
    const { data } = await teacherStore.teacherKeys({ type: 'hr_teacher' });
    const keys = [{ name: '暂无', key: '' }].concat(data.datas || []);
    this.teacherKeys = keys.map((e: any) => ({
      label: e.name,
      key: e.key,
    }));
    this.primaryKeys = (data.datas || []).filter((e: any) => !!e.primary);
    this.tableTitles = (this.dataKeys || []).map(
      (title: string) => ((this.teacherKeys || []).find((e: any) => e.label === title) || { key: '' }).key,
    );
  }

  onChange(page: number, query: any, perSize: number) {
    this.perPage = perSize;
    this.fetchData(page);
  }

  onModal() {
    const checkTitle = this.tableTitles.some((e: string) => !e);
    if (checkTitle) {
      this.$confirm({
        title: '有未匹配成功的表头信息，是否继续操作？',
        okText: '确定',
        okType: 'danger',
        cancelText: '取消',
        onOk: async () => {
          this.onValid();
        },
        onCancel: () => {},
      });
    } else {
      this.onValid();
    }
  }

  async onValid() {
    const params = {
      uid: this.uid,
      titles: this.tableTitles,
      primary_keys: this.validKeys,
      source_type: 'hr',
      targets: 'teachers',
    };
    const { data } = await teacherStore.onValid(params);
    if (data.status === 'successed') {
      this.onNotify();
    }
  }

  async onNotify() {
    const params = {
      uid: this.uid,
    };
    const { data } = await teacherStore.onNotify(params);
    if (data.data && data.data.state === 'valid') {
      if (this.timer) {
        clearTimeout(this.timer);
      }
      this.currentStep = 2;
      this.fetchData();
    } else {
      this.onTimer();
    }
  }

  onTimer() {
    if (this.timer) {
      clearTimeout(this.timer);
    }
    this.timer = setTimeout(() => {
      this.onNotify();
    }, 5000);
  }

  onConfirm() {
    const params = {
      uid: this.uid,
      titles: this.tableTitles,
      primary_keys: this.validKeys,
    };
    this.$emit('confirm', params);
  }

  onCancel() {
    this.$emit('cancel');
  }
}
</script>

<style lang="stylus" scoped>
.page
  position relative
  overflow hidden
  padding 71px 0px 61px
  width 100%
  height 100%
  .header
    position absolute
    top 0px
    left 0px
    z-index 1
    padding 14px 120px
    width 100%
    width 100%
    border-bottom 1px #e8e8e8 solid
    background #eee
  .main
    overflow auto
    padding 0px 20px 20px
    width 100%
    height 100%
    .table-title
      padding 10px 10px 0px
      color #808080
  .footer
    position absolute
    bottom 0px
    left 0px
    z-index 1
    display flex
    justify-content flex-end
    padding 10px
    width 100%
    border-top 1px #e8e8e8 solid
    background #fff
    button
      width 80px
</style>
