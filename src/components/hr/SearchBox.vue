<template lang="pug">
.search-box
  a-input.search-input(
    v-model="keyword"
    :size="size"
    :placeholder="placeholder"
    @change="onChange")
    a-icon(
      slot="suffix"
      type="close"
      @click="resetSearch"
      v-show="keyword")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class SearchBox extends Vue {
  // props
  @Prop({ type: String, default: () => '请输入关键词' }) private placeholder!: string;
  @Prop({ type: String, default: () => 'large' }) private size!: string;
  // data
  private keyword: any = '';
  private timer: any = null;
  public onSearch() {
    this.$emit('change', this.keyword.trim());
  }
  public resetSearch() {
    this.keyword = '';
    this.onSearch();
  }
  public onChange() {
    if (this.timer) {
      clearTimeout(this.timer);
    }
    this.timer = setTimeout(() => {
      this.onSearch();
    }, 500);
  }
}
</script>

<style lang="stylus" scoped>
.search-box
  width 100%
  .search-input
    width 100%
    color #383838
    font-size 14px
    line-height 20px
</style>
