<template lang="pug">
.select-tags
  .header
    SearchBox(placeholder="检索标签" @change="onSearch")
  .main
    .cell(v-for="item in labelStore.records" :key="item.id" @click="onSelect(item)")
      .name {{ item.name }}
      a-icon(type="check" v-if="filterSelect(item.name)")
  .footer
    a-button(type="primary" size="large" @click="onOk") 确定
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import SearchBox from '@/components/hr/SearchBox.vue';
import labelStore from '@/store/modules/res/label.store';

@Component({
  components: {
    SearchBox,
  },
})
export default class SelectTag extends Vue {
  @Prop({ type: Array, default: () => [] }) private selects?: any;
  private selectTags: any[] = [];
  get labelStore() {
    return labelStore || {};
  }
  @Emit('ok')
  public onOk(): object[] {
    return this.selectTags;
  }

  public mounted() {
    this.selectTags = this.selects.concat();
    this.fetchData();
  }

  public async fetchData(val: string = '') {
    const params = {
      page: 1,
      per_page: 200,
      q: {
        name_cont: val,
      },
    };
    labelStore.fetch(params);
  }

  public onSelect(val: any) {
    const isExist: boolean = this.selectTags.some(e => e.id === val.id);
    if (isExist) {
      this.selectTags = this.selectTags.filter(e => e.id !== val.id);
    } else {
      this.selectTags.push(val);
    }
  }

  public filterSelect(val: string): boolean {
    return this.selectTags.some(e => e.name === val);
  }

  public onSearch(val: string) {
    this.fetchData(val);
  }
}
</script>

<style lang="stylus" scoped>
.select-tags
  .header
    padding 16px
  .main
    overflow auto
    max-height 260px
    .cell
      display flex
      justify-content space-between
      align-items center
      padding 0px 16px
      width 100%
      line-height 40px
      &:hover
        background #f8f8f8
        cursor pointer
  .footer
    padding 12px 16px
    border-top 1px #E5E5E5 solid
    button
      width 100%
</style>
