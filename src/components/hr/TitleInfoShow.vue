<template lang="pug">
.panel
  .content(v-if="teacherTitles.length > 0")
    a-timeline(mode="left")
      a-timeline-item(v-for="(record, index) in teacherTitles" :key="index")
        .time {{ record.max_auth_at }}
        .title-desc
          TeacherTitleInfo.title-info(:titleInfo="record" :cardClass="'timeline'")
          .title-button
            TextButton(icon="edit" theme="filled" @click="onEdit(record)" v-if="record.state === 'active'")
  Empty.empty(v-else)
  a-modal.modal(:visible="visibleAdd" :footer="null" :width="660" @cancel="handleCancel") 
    .headers
      span.desc 添加职称
      span.steps
        a-icon(type="icategoryIdnfo-circle" theme="filled")
        span.remark 编辑职称详情
      span.white
        span.de —————
        a-icon(type="link")
        span.remark 编辑职称详细信息
    .tags
      a-row(:gutter="24")
        a-col(:span="8" v-for="(record, index) in Category" :key="index")
          a-button.title(@click="handleCurrentTitle(record)" :class="{active: record.id === currentCategoryId }")
            | {{ record.name }}
    .radio
      a-radio-group(v-model="flag" @change="onChange")
        a-radio(:value="'update'" :disabled="teacher.titles && teacher.titles.length == 0")
          | 沿用之前职称
        a-radio(:value="'create'")
          | 新增职称
    .button
      a-button(type="primary" :disabled="!currentCategoryId" @click="onDisplay")
        | 下一步
  a-modal.modal(:visible="visibleAddFrom" :footer="null" :width="660" @cancel="handleCancelForm") 
    .headers
      span.desc 添加职称
      span.steps
        a-icon(type="info-circle" theme="filled")
        span.remark 编辑职称详情
      span.white
        span.de —————
        a-icon(type="link")
        span.remark 编辑职称聘用详情信息
    .card
      .text 高等学校教师
      a-row(:gutter="24")
        a-col(:span="8")
          .form
            .place
              span.circle.circle-blue 评
            a-form-model-item(label="评定职称")
              a-select(
                v-model="formData.country_title_id"
                style="width: 100%;"
                size="large"
                placeholder="请选择"
                allowClear)
                a-select-option(
                  v-for="(title, index) in currentTitle"
                  :key="index"
                  :value="title.value") {{ title.label }}
            a-form-model-item(label="评定等级")
              a-select(
                v-model="formData.country_level"
                style="width: 100%;"
                size="large"
                placeholder="请选择"
                allowClear)
                a-select-option(
                  v-for="(level, index) in levels"
                  :key="index"
                  :value="level") {{ level }}
            a-form-model-item(label="评定时间")
              a-date-picker(
                :value="$tools.formatDefaultDate(formData.country_auth_at)"
                format="YYYY-MM-DD"
                size="large"
                style="width: 100%"
                @change="(date, dataString) => { formData.country_auth_at = dataString }")
        a-col(:span="8")
          .form
            .place
              span.circle.circle-red 聘
            a-form-model-item(label="评定职称")
              a-select(
                v-model="formData.school_title_id"
                style="width: 100%;"
                size="large"
                placeholder="请选择"
                allowClear)
                a-select-option(
                  v-for="(title, index) in currentTitle"
                  :key="index"
                  :value="title.value") {{ title.label }}
            a-form-model-item(label="评定等级")
              a-select(
                v-model="formData.school_level"
                style="width: 100%;"
                size="large"
                placeholder="请选择"
                allowClear)
                a-select-option(
                  v-for="(level, index) in levels"
                  :key="index"
                  :value="level") {{ level }}
            a-form-model-item(label="评定时间")
              a-date-picker(
                :value="$tools.formatDefaultDate(formData.school_auth_at)"
                format="YYYY-MM-DD"
                size="large"
                style="width: 100%"
                @change="(date, dataString) => { formData.school_auth_at = dataString }")
        a-col(:span="8")
          .form
            .place
              span.circle.circle-green 内聘
            a-form-model-item(label="评定职称")
              a-select(
                v-model="formData.teacher_title_id"
                style="width: 100%;"
                size="large"
                placeholder="请选择"
                allowClear)
                a-select-option(
                  v-for="(title, index) in currentTitle"
                  :key="index"
                  :value="title.value") {{ title.label }}
            a-form-model-item(label="评定等级")
              a-select(
                v-model="formData.teacher_level"
                style="width: 100%;"
                size="large"
                placeholder="请选择"
                allowClear)
                a-select-option(
                  v-for="(level, index) in levels"
                  :key="index"
                  :value="level") {{ level }}
            a-form-model-item(label="评定时间")
              a-date-picker(
                :value="$tools.formatDefaultDate(formData.teacher_auth_at)"
                format="YYYY-MM-DD"
                size="large"
                @change="(date, dataString) => { formData.teacher_auth_at = dataString }"
                style="width: 100%")
    .button.flex-between
      a-button(type="primary" @click="onDisplayForm" v-if="visibleButton")
        | 上一步
      a-button(type="primary" @click="onSubmit")
        | 确认添加
</template>
<script lang="ts">
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import titleCategoryStore from '@/store/modules/res/title_category.store';
import TeacherTitleInfo from '@/components/hr/TitleInfo.vue';
import { type } from 'os';

@Component({
  components: {
    TeacherTitleInfo,
  },
})
export default class TitleInfo extends Vue {
  @Model('change', { type: Object, default: () => ({}) }) readonly teacher!: any;
  @Prop({ type: Array, default: () => [] }) readonly teacherTitles!: IObject[];
  @Prop({ type: Boolean, default: false }) readonly visibleAdd!: boolean;
  @Prop({ type: Boolean, default: false }) readonly visibleAddFrom!: boolean;
  // @Prop({ type: Array, default: () => [] }) readonly dataInfo!: IObject[];

  formData: IObject = {};
  flag: string = 'create';
  selectTitles: IObject = {};
  currentTitle: IObject[] = [];
  visibleButton: boolean = true;
  currentCategoryId: number = 0;
  categoryId: number | string = '';
  levels: string[] = ['1级', '2级', '3级', '4级', '5级'];
  // yang chang checkboxStatus
  // get records() {
  //   return titleCategoryStore.records;
  // }

  get Category() {
    return titleCategoryStore.records;
  }

  get sorts() {
    return [{ id: '', name: '全部' }].concat(this.teacher.title_categories || []);
  }

  get initFormData() {
    return {
      country_title_id: '',
      country_level: '',
      country_auth_at: '',
      school_title_id: '',
      school_level: '',
      school_auth_at: '',
      teacher_title_id: '',
      teacher_level: '',
      teacher_auth_at: '',
    };
  }

  @Watch('teacher', { immediate: true, deep: true })
  watchTeacherChange() {
    if (this.teacher.id) {
      this.fetchTitles();
    }
  }

  mounted() {
    this.fetchRecords();
    this.Category.forEach((record: any) => {
      this.selectTitles[record.id] = record.titles.map((title: any) => ({ label: title.name, value: title.id }));
    });
  }

  async fetchTitles() {
    const params = {
      page: 1,
      per_page: 100,
      q: {
        teacher_id_eq: this.teacher.id,
        s: ['max_auth_at desc', 'id desc'],
        title_category_id_eq: this.categoryId,
      },
    };
  }

  async fetchRecords() {
    const params = {
      page: 1,
      per_page: 100,
    };
    await titleCategoryStore.fetch(params);
    this.Category.forEach((record: any) => {
      this.selectTitles[record.id] = record.titles.map((title: any) => ({ label: title.name, value: title.id }));
    });
  }

  onChange() {
    this.currentCategoryId = this.flag === 'update' ? this.teacher.titles[0].title_category_id : 0;
  }

  onDisplay() {
    if (this.flag === 'update') {
      const { id, ...data } = this.teacher.titles[0];
      this.formData = data;
    } else {
      this.formData = this.initFormData;
    }
    this.$emit('visibleForm');
  }

  onDisplayForm() {
    this.$emit('visibleFormBack');
  }

  onSubmit() {
    this.$emit('onSubmit', {
      ...this.teacher,
      teacher_titles_attributes: [{ ...this.formData, title_category_id: this.currentCategoryId }],
    });
    this.currentCategoryId = 0;
    this.visibleButton = true;
    // this.fetchTitles();
    this.$emit('visibleFormHied');
  }

  onCancel() {
    this.$emit('displayTitleInfo', false);
  }

  onEdit(payload: any) {
    this.currentCategoryId = payload.title_category_id;
    this.currentTitle = this.selectTitles[payload.title_category_id];
    this.formData = payload;
    this.visibleButton = false;
    this.$emit('visibleForm', 'updata');
  }

  handleCancel() {
    this.currentCategoryId = 0;
    this.$emit('visibleAddHied');
  }

  handleCancelForm() {
    this.currentCategoryId = 0;
    this.visibleButton = true;
    this.$emit('visibleFormHied');
  }

  handleOk() {}

  handleCurrentTitle(record: IObject) {
    this.currentCategoryId = record.id;
    this.currentTitle = this.selectTitles[record.id];
  }
}
</script>

<style lang="stylus" scoped>
.panel
  .filter-text
    margin 0px 10px
    color #808080
    cursor pointer
    .filter-icon
      margin 0px 5px
  .empty
    height 100%
  .content
    margin-top 24px
    padding-top 50px
    padding-left 100px
    height 100%
    .time
      margin-left -120px
      color #3da8f5
    .title-desc
      display flex
      .title-button
        margin-top -25px
        min-width 50px
      .title-info
        width 100%

.ant-modal-body
  width 800px
  .headers
    .desc
      color #383838
      font-weight 500
    .steps
      margin-left 32px
      color #3da8f5
    .white
      color #A6A6A6
      .de
        padding 0 2px
        color #D8D8D8
    .remark
      padding 0 2px
  .tags
    margin-top 12px
  .title
    margin 6px 0
    width 100%
    height 50px
  .radio
    margin-top 12px
  .button
    margin-top 12px
    .active
      border-color #2984cf
      color #2984cf
  .card
    margin-top 12px
    .text
      margin-bottom 12px
      color #383838
      font-weight 500
    .form
      padding 0 6px
      border 1px solid #E5E5E5
      border-radius 6px
      background #FAFAFA
      .ant-form-item
        margin-top -12px
        margin-bottom 12px
      .place
        margin 12px 0
      .circle
        margin-right 6px
        padding 0 6px
        width 20px
        height 20px
        border-radius 50%
        text-align center
      .circle-blue
        border 1px solid #E5F4FF
        background #E5F4FF
        color #3DA8F5
      .circle-green
        border 1px solid #D7FFEA
        background #D7FFEA
        color #22BE6E
      .circle-red
        border 1px solid #F97239
        background #FFE9E0
        color #F97239
</style>
