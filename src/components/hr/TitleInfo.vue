<template lang="pug">
.card(:class="cardClass")
  .info.flex-between
    .description
      span.black {{ titleInfo.title_category_name || '' }}
      span.blue {{ titleInfo.max_level }}
    .remark(v-if="titleInfo.state === 'active'")
      span 合同期限
      a-icon.blue(type="clock-circle")
      span.blue 36个月
    .remark(v-else)
      a-icon.white(type="clock-circle")
      span.white 已结束
  .flex-bettween
    a-row.row(:gutter="24")
      a-col(:span="8")
        .card-info(v-if="titleInfo.country_title_id")
          .title-info.flex-between
            span
              span.circle.circle-blue 评
              span {{ titleInfo.country_title_name }}
            span {{ titleInfo.country_level }}
          .title-date
            span 评定时间: {{ titleInfo.country_auth_at }}
        .card-info(v-else)
          .title-info
            span.circle.circle-blue 评
            span 无职称信息
      a-col(:span="8")
        .card-info(v-if="titleInfo.school_title_id")
          .title-info.flex-between
            span
              span.circle.circle-red 聘
              span {{ titleInfo.school_title_name }}
            span {{ titleInfo.school_level}}
          .title-date
            span 聘用时间: {{ titleInfo.school_auth_at }}
        .card-info(v-else)
          .title-info
            span.circle.circle-red 聘
            span 无职称信息
      a-col(:span="8")
        .card-info(v-if="titleInfo.teacher_title_id")
          .title-info.flex-between
            span
              span.circle.circle-green 内聘
              span {{ titleInfo.teacher_title_name }}
            span {{ titleInfo.teacher_level}}
          .title-date
            span 聘用时间: {{ titleInfo.teacher_auth_at }}
        .card-info(v-else)
          .title-info
            span.circle.circle-green 内聘
            span 无职称信息
    slot(name="actions")
</template>

<script lang="ts">
import { Component, Vue, Prop, Emit, Model } from 'vue-property-decorator';

@Component
export default class TitleInfoShow extends Vue {
  @Prop({ type: Object, default: () => ({}) }) private titleInfo!: any;
  @Prop({ type: String, default: 'normal' }) private cardClass?: any;
}
</script>

<style lang="stylus" scoped>
.timeline
  margin -75px 0 50px 36px

.normal
  margin 12px 0

.card
  padding 12px
  border 1px solid #E5E5E5
  border-radius 5px
  background #FAFAFA
  .remark
    line-height 22px
  .row
    margin-top 6px
    border none
  .title-date
    margin-top 6px
  .card-info
    padding 12px
    height 80px
    border 1px solid #fafafa
    border-radius 5px
    background #FFFFFF
  .black
    color #383838
    font-weight 500
  .blue
    padding-left 6px
    color #3da8f5
  .white
    padding-left 6px
    color #EAEAEA
  .gray
    padding-left 6px
    color #EAEAEA
  .circle
    margin-right 6px
    padding 0 6px
    width 20px
    height 20px
    border-radius 50%
  .circle-blue
    border 1px solid #E5F4FF
    background #E5F4FF
    color #3DA8F5
  .circle-green
    border 1px solid #D7FFEA
    background #D7FFEA
    color #22BE6E
  .circle-red
    border 1px solid #F97239
    background #FFE9E0
    color #F97239
</style>
