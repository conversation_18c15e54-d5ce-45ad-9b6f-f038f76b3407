<template lang="pug">
.page
  template(v-if="tabKey !== 73668888")
    .extension-info
      TemplateForm(
        :formData="value.dynamic_attrs"
        :template="templates"
        :showActions="false"
        @submit="onSubmit")
        .submit-box
          a-button(size="large" @click="$router.go(-1)") 取消
          slot(name="pre")
          a-button(
            slot="actions"
            type="primary"
            size="large"
            :disabled="teacherStore.loading"
            :loading="teacherStore.loading"
            htmlType="submit") {{ submitTitle }}
  template(v-else)
    .basic-info
      a-form(layout="vertical")
        a-form-item(label="姓名")
          a-input(
            v-model="value.name"
            size="large"
            placeholder="请输入姓名")
        a-form-item(label="职工号")
          a-input(
            v-model="value.code"
            size="large"
            disabled
            placeholder="请输入职工号")
        a-form-item(label="手机号")
          a-input(
            v-model="value.phone"
            size="large"
            placeholder="请输入手机号")
        a-form-item(label="联系电话")
          a-input(
            v-model="value.tel"
            size="large"
            placeholder="请输入联系电话")
        a-form-item(label="邮箱")
          a-input(
            v-model="value.email"
            size="large"
            placeholder="请输入邮箱")
        //- a-form-item(label="地址")
          a-textarea(
            v-model="value.addr"
            size="large"
            placeholder="请输入地址")
        a-form-item(label="性别")
          a-radio-group(
            v-model="value.sex"
            size="large"
            style="width: 100%;")
            a-radio(value="男") 男
            a-radio(value="女") 女
        a-form-item(label="出生日期")
          a-date-picker(
            style="width: 100%;"
            size="large"
            :value="$tools.formatDefaultDate(value.birthday)"
            @change="(data, dateString) => { value.birthday = dateString}")
        a-form-item(label="证件类型")
          a-select(
            style="width: 100%;"
            size="large"
            placeholder="请选择证件类型"
            allowClear
            v-model="value.identity_type")
            a-select-option(
              v-for="(type, index) in ['身份证', '护照', '其他']"
              :key="index"
              :value="type") {{ type }}
        a-form-item(label="证件号")
          a-input(
            v-model="value.identity_id"
            size="large"
            placeholder="请输入证件号")
        a-form-item(label="办公室")
          a-input(
            v-model="value.office"
            size="large"
            placeholder="请输入办公室门牌号")
        a-form-item(label="进校时间")
          a-date-picker(
            style="width: 100%;"
            size="large"
            :value="$tools.formatDefaultDate(value.to_school_at)"
            @change="(data, dateString) => { value.to_school_at = dateString}")
        a-form-item(label="离校时间")
          a-date-picker(
            style="width: 100%;"
            size="large"
            :value="$tools.formatDefaultDate(value.leave_school_at)"
            @change="(data, dateString) => { value.leave_school_at = dateString}")
        a-form-item(label="用人方式")
          a-input(
            v-model="value.work_way"
            size="large"
            placeholder="请输用人方式")
        a-form-item(label="学位")
          a-select(
            style="width: 100%;"
            size="large"
            placeholder="请选择学位"
            allowClear
            v-model="value.degree")
            a-select-option(
              v-for="(degree, index) in degrees"
              :key="index"
              :value="degree") {{ degree }}
        a-form-item(label="学历")
          a-select(
            style="width: 100%;"
            size="large"
            placeholder="请选择学历"
            allowClear
            v-model="value.education")
            a-select-option(
              v-for="(education, index) in educations"
              :key="index"
              :value="education") {{ education }}
    .parent-box
      .parent-top
        strong 部门职务
        .row(style="align-items: flex-start")
          .key
            a-icon(type="cluster")
            span 部门
          .value
            .department(v-for="(department, index) in value.departments" :key="index")
              span
                span(v-if="department.path && department.path.length")
                  | {{ department.path.join(' > ') }} >&nbsp;
                span {{ department.name }}
              span.clear-icon(@click="clearDepartment(index)")
                a-icon(type="close-circle" theme="filled")
            IconTooltip(
              icon="plus-circle"
              tips="添加部门"
              theme="filled"
              style="color: #ccc; margin: 0px" @click="onModal")
        .row
          .key
            a-icon(type="audit")
            span 职务
          .value
            .selects(v-if="value.duties && value.duties.length")
              .select-item(v-for="(item, index) in value.duties" :key="index" @click="clearDuty(index)")
                span {{ item.name }}
                span.clear-icon
                  a-icon(type="close-circle" theme="filled")
            Popover(v-model="visibleDuty")
              template(slot='main')
                SelectDuty(:selects.sync="value.duties" @ok="configDuty" v-if="visibleDuty")
              IconTooltip(
                icon="plus-circle"
                tips="设置职务"
                theme="filled"
                style="color: #ccc; margin: 0px"
                @click="visibleDuty=!visibleDuty")
        .row
          .key
            a-icon(type="tags")
            span 标签
          .value
            .selects(v-if="value.tags && value.tags.length")
              .select-item(v-for="(item, index) in value.tags" :key="index" @click="clearTag(index)")
                span {{ item.name }}
                span.clear-icon
                  a-icon(type="close-circle" theme="filled")
            Popover(v-model="visibleTag")
              template(slot='main')
                SelectTag(:selects.sync="value.tags" @ok="configTag" v-if="visibleTag")
              IconTooltip(
                icon="plus-circle"
                tips="设置标签"
                theme="filled"
                style="color: #ccc; margin: 0px"
                @click="visibleTag=!visibleTag")
      .parent-middle
        strong 职称
        .selects(v-if="value.titles && value.titles.length" style="margin-top: 4px")
          .select-item(v-for="(item, index) in value.titles" :key="index" @click="clearTitle(index)")
            span {{ item.name }}
            span.clear-icon
              a-icon(type="close-circle" theme="filled")
        .title-hint(v-else) 在下方表格选择对应职称
        .title-box
          SelectTitle(:selects="value.titles" @select="configTitle")

  MainModal(
    v-model="visibleModal"
    :width="700")
    .modal-row(slot="title")
      .title 部门
      .search
        Searcher(v-model="queryObject" :variables="['name', 'name']" tips="检索部门" placeholder="检索部门")
    .modal-row(slot="footer")
      .count
        span 已选择
        span.text-primary(style="margin: 0px 4px")
          | {{ selectDepartment.name }}
      a-button(type="primary" size="large" style="width: 100px" @click="addDepartment") 确定
    SelectDepartment(:departments.sync="departments" @ok="configDepartment")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import SelectDepartment from '@/components/hr/SelectDepartment.vue';
import SelectDuty from '@/components/hr/SelectDuty.vue';
import SelectTag from '@/components/hr/SelectTag.vue';
import SelectTitle from '@/components/hr/SelectTitle.vue';
import TemplateForm from '@/components/form/TemplateForm.vue';
import teacherStore from '@/store/modules/hr/teacher.store';
import department from '@/models/res/department';

@Component({
  components: {
    SelectDepartment,
    SelectDuty,
    SelectTag,
    SelectTitle,
    TemplateForm,
  },
})
export default class TeacherForm extends Vue {
  @Prop({ type: Number, default: 73668888 }) private tabKey?: number;
  @Prop({ type: Array, default: () => [] }) private templates?: any;
  @Prop({ type: Object, default: () => ({}) }) private value?: any;
  @Prop({ type: String, default: '下一步' }) private submitTitle?: string;
  private selectDepartment: any = {};
  private teacher: any = {};
  private queryObject: any = {};
  private departments: any[] = [];
  private visibleModal: boolean = false;
  private visibleDuty: boolean = false;
  private visibleTag: boolean = false;
  private degrees: any[] = ['学士', '硕士', '博士', '博士后', '其他'];
  private educations: any[] = ['本科', '其他'];
  get teacherStore() {
    return teacherStore || {};
  }
  @Watch('queryObject')
  public watchQuery() {
    this.fetchTree();
  }

  public mounted() {}

  public async fetchTree() {
    const params = {
      q: {
        ...this.queryObject,
      },
    };
    const { data } = await department.tree(params);
    this.departments = data.departments;
  }

  public async onModal() {
    await this.fetchTree();
    this.visibleModal = true;
  }

  // 动态表单
  public onSubmit(val: any) {
    const obj = {
      dynamic_attrs: {
        ...this.value.dynamic_attrs,
        ...val,
      },
    };
    this.$emit('submit', obj);
  }

  // duty or department or title
  public addDepartment() {
    if (this.selectDepartment.id) {
      this.value.departments.push(this.selectDepartment);
      this.value.department_ids = this.value.departments.map((e: any) => e.id);
    }
    this.visibleModal = false;
  }

  public configDuty(val: object[]) {
    this.value.duty_ids = (val || []).map((e: any) => e.id);
    this.value.duties = val;
    this.visibleDuty = false;
  }

  public configTag(val: object[]) {
    this.value.tag_list = (val || []).map((e: any) => e.name).toString();
    this.value.tags = val;
    this.visibleTag = false;
  }

  public configTitle(val: object[]) {
    this.value.title_ids = (val || []).map((e: any) => e.id);
    this.value.titles = val;
  }

  public configDepartment(val: any) {
    this.selectDepartment = val;
  }

  public clearDuty(index: number) {
    this.$confirm({
      title: '确定要继续执行此操作吗?',
      okText: '确定',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        this.value.duties.splice(index, 1);
        this.value.duty_ids = this.value.duties.map((e: any) => e.id);
      },
      onCancel: () => {},
    });
  }

  public clearTag(index: number) {
    this.$confirm({
      title: '确定要继续执行此操作吗?',
      okText: '确定',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        this.value.tags.splice(index, 1);
        this.value.tag_list = (this.value.tags || []).map((e: any) => e.name).toString();
      },
      onCancel: () => {},
    });
  }

  public clearTitle(index: number) {
    this.$confirm({
      title: '确定要继续执行此操作吗?',
      okText: '确定',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        this.value.titles.splice(index, 1);
        this.value.title_ids = this.value.titles.map((e: any) => e.id);
      },
      onCancel: () => {},
    });
  }

  public clearDepartment(index: number) {
    this.$confirm({
      title: '确定要继续执行此操作吗?',
      okText: '确定',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        this.value.departments.splice(index, 1);
        this.value.department_ids = this.value.departments.map((e: any) => e.id);
      },
      onCancel: () => {},
    });
  }
}
</script>

<style lang="stylus" scoped>
.page
  display flex
  width 100%
  height 100%
  .extension-info
    overflow-x hidden
    overflow-y auto
    padding 22px 0px
    width 100%
    height 100%
    .submit-box
      display flex
      justify-content flex-end
      margin-top 20px
      width 100%
      button
        width 100px
  .basic-info
    overflow auto
    padding 24px 20px 24px 0px
    min-width 460px
    width 460px
    height 100%
    border-right 1px #e6e6e6 solid
    .title
      margin 22px 8px 4px
      font-weight 500
      font-size 20px
      line-height 28px
    .module
      padding-top 12px
      border-bottom 1px #e6e6e6 solid
      .row
        display flex
        margin-bottom 24px
        width 100%
        img
          width 44px
          height 44px
  .parent-box
    overflow auto
    padding 20px 0px 20px 20px
    width 100%
    height 100%
    strong
      font-size 14px
    button
      width 20px
      height 20px
      border none
      background none
    .parent-top
      width 100%
      .row
        display flex
        align-items center
      .department
        margin-bottom 8px
        width 100%
        font-size 14px
        line-height 20px
        cursor pointer
        &:hover
          color #3DA8F5
          .clear-icon
            display inline
        .clear-icon
          display none
          margin-left 4px
          color rgba(0, 0, 0, 0.3)
    .parent-middle
      margin 28px 0px 24px
      padding-bottom 24px
      .title-hint
        margin 8px 0px 4px
        color #A6A6A6
      .title-box
        margin-top 10px

.selects
  display flex
  flex-wrap wrap
  align-items center
  .select-item
    margin 4px 8px 4px 0px
    padding 0px 12px
    border 1px rgba(61, 168, 245, 0.12) solid
    border-radius 20px
    background rgba(61, 168, 245, 0.1)
    color #3DA8F5
    font-size 14px
    line-height 28px
    cursor pointer
    &:hover
      .clear-icon
        display inline
    .clear-icon
      display none
      margin-left 4px

.row
  display flex
  align-items center
  margin 20px 0px 4px
  .key
    width 152px
    height 20px
    label
      padding 0px 1px
    span
      margin-left 12px
      color #808080
      font-size 14
      line-height 20px
  .value
    display flex
    flex-wrap wrap
    align-items center
    width 100%
</style>
