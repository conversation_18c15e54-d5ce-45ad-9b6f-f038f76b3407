ModificationInfo<template lang="pug">
.page
  .page-top
    StepToolbar(v-model="tabIndex" mode="tabs" :steps="tabs")
  .page-middle
    template(v-if="tabIndex === '修改项'")
      table
        tr
          th 名称
          th 修改前
          th 修改后
        tr(v-for="(item, index) in changeItems", :key="index")
          td {{ item.label }}
          td
            span(v-if="$tools.getDynamicType(item.key) === 'date'")
              | {{ item.before | format('YYYY-MM-DD') }}
            span(v-else-if="$tools.getDynamicType(item.key) === 'time'")
              | {{ item.before | format('HH:mm:ss') }}
            span(v-else-if="$tools.getDynamicType(item.key) === 'datetime'")
              | {{ item.before | format('YYYY-MM-DD HH:mm:ss') }}
            span(v-else) {{ item.before }}
          td
            span(v-if="$tools.getDynamicType(item.key) === 'date'")
              | {{ item.after | format('YYYY-MM-DD') }}
            span(v-else-if="$tools.getDynamicType(item.key) === 'time'")
              | {{ item.after | format('HH:mm:ss') }}
            span(v-else-if="$tools.getDynamicType(item.key) === 'datetime'")
              | {{ item.after | format('YYYY-MM-DD HH:mm:ss') }}
            span(v-else) {{ item.after }}
    .change(v-else-if="tabIndex === '修改后'")
      a-row(:gutter="20")
        a-col(:span="12" v-for="item in changeAfter.static", :key="item.key")
          .column
            .key {{ item.label }}
            .avatar(v-if="item.key === 'avatar'")
              img(:src="teacher.avatar")
            .value(v-else) {{ item.value }}
        a-col(:span="12" v-for="item in changeAfter.dynamic", :key="item.key")
          .column
            .key {{ item.label }}
            .value
              span(v-if="$tools.getDynamicType(item.key) === 'date'")
                | {{ item.value | format('YYYY-MM-DD') }}
              span(v-else-if="$tools.getDynamicType(item.key) === 'time'")
                | {{ item.value | format('HH:mm:ss') }}
              span(v-else-if="$tools.getDynamicType(item.key) === 'datetime'")
                | {{ item.value | format('YYYY-MM-DD HH:mm:ss') }}
              span(v-else) {{ item.value }}
    .change(v-else-if="tabIndex === '修改前'")
      a-row(:gutter="20")
        a-col(:span="12" v-for="item in changeBefore.static", :key="item.key")
          .column
            .key {{item.label}}
            .avatar(v-if="item.key === 'avatar'")
              img(:src="teacher.avatar")
            .value(v-else) {{ item.value }}
        a-col(:span="12" v-for="item in changeBefore.dynamic", :key="item.key")
          .column
            .key {{ item.label }}
            .value
              span(v-if="$tools.getDynamicType(item.key) === 'date'")
                | {{ item.value | format('YYYY-MM-DD') }}
              span(v-else-if="$tools.getDynamicType(item.key) === 'time'")
                | {{ item.value | format('HH:mm:ss') }}
              span(v-else-if="$tools.getDynamicType(item.key) === 'datetime'")
                | {{ item.value | format('YYYY-MM-DD HH:mm:ss') }}
              span(v-else) {{ item.value }}
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class ModificationInfo extends Vue {
  @Prop({ type: Object, default: () => ({}) }) private value?: any;
  @Prop({ type: Number, default: 73668888 }) private typeIndex?: string;
  private tabIndex: string = '修改项';
  private tabs: any[] = [
    {
      title: '修改项',
      key: '修改项',
    },
    {
      title: '修改后',
      key: '修改后',
    },
    {
      title: '修改前',
      key: '修改前',
    },
  ];
  get changeItems() {
    return this.value.changeItems || [];
  }
  get changeBefore() {
    return this.value.changeBefore || {};
  }
  get changeAfter() {
    return this.value.changeAfter || {};
  }
}
</script>

<style lang="stylus" scoped>
.page
  overflow auto
  width 100%
  height 100%
  .page-top
    position sticky
    top 0px
    left 0px
    padding 0px 16px
    width 100%
    border-bottom 1px #e8e8e8 solid
    background #fff
  .page-middle
    padding 20px
    table
      width 100%
      th
        flex 1
        padding 10px 16px
        border 1px #DCDFE6 solid
        background #F0F2F5
      td
        flex 1
        padding 14px 16px
        border 1px rgba(33, 34, 35, 0.1) solid
    .change
      padding-bottom 46px
      width 100%
      .column
        display flex
        font-size 14px
        line-height 40px
        .key
          width 80px
          color #888
        .value
          color #424344
      .avatar
        overflow hidden
        padding 6px
        width 120px
        height 160px
        border 1px rgba(33, 34, 35, 0.1) solid
        img
          display inline
          width 100%
</style>
