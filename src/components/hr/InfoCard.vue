<template lang="pug">
Panel.info-card
  .card-header
    TaAvatar(
      :name="teacher.name"
      :sex="teacher.sex"
      :short="true"
      fontSize="24px"
      boxSize="60px")
    .info
      .name {{ teacher.name }}
      .code 职工号 {{ teacher.code }}
  .card-middle
    .menu-item(
      v-for="(menu, index) in menus" 
      :key="index"
      :class="{'menu-item-active': value === menu.key}"
      @click="onMenu(menu)")
      | {{ menu.name }}
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit, Model } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class InfoCard extends Vue {
  @Prop({ type: String, default: 'base' }) private value!: string;
  @Prop({ type: Array, default: () => [{ key: 'base', name: '基本信息' }] }) private menus?: any;
  @Prop({ type: Object, default: () => ({}) }) private teacher?: any;
  onMenu(val: any) {
    if (this.value !== val.key) {
      this.$emit('input', val.key);
    }
  }
}
</script>

<style lang="stylus" scoped>
.info-card
  padding-bottom 8px
  width 100%
  background #fff
  .card-header
    display flex
    align-items center
    padding 24px 20px
    .info
      margin-left 12px
      .name
        color #383838
        font-weight 500
        font-size 16px
        line-height 24px
      .code
        margin-top 4px
        color #808080
        font-size 14px
        line-height 20px
  .card-middle
    width 100%
    .menu-item
      padding 14px 20px
      color #808080
      font-weight 500
      font-size 14px
      cursor pointer
    .menu-item-active
      position relative
      color #383838
      &:before
        position absolute
        top 0px
        left 0px
        width 4px
        height 48px
        background #3DA8F5
        content ''
</style>
