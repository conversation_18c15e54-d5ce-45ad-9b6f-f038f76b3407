<template lang="pug">
.member-page
  AdminTable(
    :data="data"
    :totalCount="totalCount"
    :currentPage="currentPage"
    :perPage="perPage"
    :hideOnSinglePage="hideOnSinglePage"
    :rowSelection="selectedRowKeys ? {selectedRowKeys: selectedRowKeys, onChange: onSelectChange} : null"
    :scroll="scroll"
    :showSizeChanger="showSizeChanger"
    @change="onChange"
    @tableChange="tableChange")
    a-table-column(dataIndex="code" title="职工号")
    a-table-column(title="姓名")
      template(slot-scope="scope")
        span.title {{scope.name}}
    a-table-column(title="所属部门")
      template(slot-scope="scope")
        span.department {{ scope.college_name || scope.department_name }}
    a-table-column(:width="100" align="center" v-if="isSlot")
      template(slot-scope="scope")
        .actions
          //- PopoverConfirm(
          //-   title="删除"
          //-   content="您确认删除该成员吗？"
          //-   placement="bottomRight"
          //-   @confirm="onClear(scope.id)")
          //-   IconTooltip(icon="delete" tips="删除")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class MemberTable extends Vue {
  // props
  @Prop({ type: Array, default: [] }) private data?: object[];
  @Prop({ type: Number, default: 0 }) private totalCount?: number;
  @Prop({ type: Number, default: 0 }) private currentPage?: number;
  @Prop({ type: Number, default: 20 }) private perPage?: number;
  @Prop({ type: [Array, Boolean], default: () => [] }) private selectedRowKeys?: number[];
  @Prop({ type: Boolean, default: true }) private showHeader?: number;
  @Prop({ type: Boolean, default: false }) private isSlot?: number;
  @Prop({ type: Boolean, default: false }) private hideOnSinglePage?: boolean;
  @Prop({ type: Boolean, default: true }) private showSizeChanger?: boolean;
  @Prop({ type: Object, default: () => ({}) }) private scroll!: IObject;

  public onClear(id: number) {
    this.$emit('clear', id);
  }

  @Emit('change')
  public onSelectChange(keys: number[]): number[] {
    return keys;
  }

  public tableChange(pagination: object, filters: object, sorter: object) {
    this.$emit('tableChange', pagination, filters, sorter);
    this.$emit('tableChange', pagination, filters, sorter);
  }

  public onChange(page: number, query: any, pageSize: number) {
    this.$emit('paginate', page, query, pageSize);
  }
}
</script>

<style lang="stylus" scoped>
.member-page
  overflow auto
  width 100%
  height 100%
  tr:hover
    .title
      color #3DA8F5
      font-size 14px
    .department
      cursor pointer
    .actions
      display inline
      width 60px
  .actions
    display none
</style>
