<template lang="pug">
.page
  .extension-info(v-if="tabKey !== 73668888")
    TemplateView(
      :formData="value.dynamic_attrs"
      :template="templates"
      :colSpan="colSpan"
      layout="horizontal"
      style="padding: 0px")
  .basic-info(v-else)
    .title {{ value.name }}
    .module
      IconCell(icon="bold" :required="true" label="职工号" :value="value.code")
    .module
      IconCell(icon="mobile" label="手机号" :value="value.phone")
      IconCell(icon="phone" label="联系电话" :value="value.tel")
      IconCell(icon="mail" label="邮箱" :value="value.email")
      IconCell(icon="environment" label="地址" :value="value.addr")
    .module
      IconCell(icon="man" label="性别" :value="value.sex")
      IconCell(icon="calendar" label="出生日期" :value="value.birthday")
      IconCell(icon="file-search" label="证件类型" :value="value.identity_type")
      IconCell(icon="solution" label="证件号" :value="value.identity_id")
    .module
      IconCell(icon="bold" label="办公室" :value="value.office")
      IconCell(icon="schedule" label="进校时间" :value="value.to_school_at_zh")
      IconCell(icon="calendar" label="离校时间" :value="value.leave_school_at_zh")
      IconCell(icon="compass" label="用人方式" :value="value.work_way")
      IconCell(icon="trophy" label="学位" :value="value.degree")
      IconCell(icon="bulb" label="学历" :value="value.educations")
  .parent-box
    .parent-top
      strong 部门职务
      .row
        .key
          a-icon(type="cluster")
          span 部门
        .value
          .department(v-for="(department, index) in value.departments" :key="index")
            span(v-if="department.path && department.path.length")
              | {{ department.path.join(' > ') }} >&nbsp;
            span {{ department.name }}
      .row
        .key
          a-icon(type="audit")
          span 职务
        .value
          .selects(v-if="value.duties && value.duties.length")
            .select-item(v-for="(item, index) in value.duties" :key="index")
              span {{ item.name }}
      .row
        .key
          a-icon(type="tags")
          span 标签
        .value
          .selects(v-if="value.tags && value.tags.length")
            .select-item(v-for="(item, index) in value.tags" :key="index")
              span {{ item.name }}
    .parent-middle
      strong 职称
      .selects(v-if="value.titles && value.titles.length" style="margin-top: 4px")
        .select-item(v-for="(item, index) in value.titles" :key="index")
          span {{ item.name }}
      .title-box
        SelectTitle(:selects="value.titles" pageType="View")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import SelectTitle from '@/components/hr/SelectTitle.vue';
import teacherStore from '@/store/modules/hr/teacher.store';
import TemplateView from './TemplateView.vue';

@Component({
  components: {
    TemplateView,
    SelectTitle,
  },
})
export default class TeacherView extends Vue {
  @Prop({ type: Number, default: 73668888 }) private tabKey?: number;
  @Prop({ type: Array, default: () => [] }) private templates?: any;
  @Prop({ type: Object, default: () => ({}) }) private value?: any;
  @Prop({ type: Number, default: 0 }) private colSpan?: number; // 0~24
}
</script>

<style lang="stylus" scoped>
.page
  display flex
  width 100%
  height 100%
  .extension-info
    overflow auto
    padding 12px 0px 30px
    min-width 460px
    width 460px
    height 100%
    border-right 1px #e6e6e6 solid
  .basic-info
    overflow auto
    padding 0px 20px 30px 0px
    min-width 460px
    width 460px
    border-right 1px #e6e6e6 solid
    .title
      margin 22px 8px 4px
      font-weight 500
      font-size 20px
      line-height 28px
    .module
      padding 12px 0px
      border-bottom 1px #e6e6e6 solid
  .parent-box
    overflow auto
    padding 20px 0px 20px 20px
    width 100%
    height 100%
    strong
      font-size 14px
    button
      width 20px
      height 20px
      border none
      background none
    .parent-top
      width 100%
      .row
        display flex
        align-items flex-start
      .department
        margin-bottom 8px
        width 100%
        font-size 14px
        line-height 20px
    .parent-middle
      margin 28px 0px 24px
      padding-bottom 24px
      .title-box
        margin-top 10px

.selects
  display flex
  flex-wrap wrap
  align-items center
  .select-item
    margin 4px 8px 4px 0px
    padding 0px 12px
    border 1px rgba(61, 168, 245, 0.12) solid
    border-radius 20px
    background rgba(61, 168, 245, 0.1)
    color #3DA8F5
    font-size 14px
    line-height 20px

.row
  display flex
  align-items center
  margin 20px 0px 4px
  .key
    width 152px
    height 20px
    span
      margin-left 12px
      color #808080
      font-size 14
      line-height 20px
  .value
    display flex
    flex-wrap wrap
    align-items center
    width 100%
</style>
