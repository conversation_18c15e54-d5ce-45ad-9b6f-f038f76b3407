<script lang="ts">
import { Component, Vue, Prop, Model } from 'vue-property-decorator';
import { IFormTemplateItem } from '../../interfaces/formTemplate.interface';
import UserSelector from './UserSelector.vue';

interface IMember {
  id: number;
  [key: string]: any;
}

@Component({
  components: {
    UserSelector,
  },
})
export default class UserSelectorDialog extends Vue {
  @Model('input', { type: Boolean, required: true }) visible!: boolean;
  @Prop({ type: String, required: true }) readonly title!: string;
  @Prop({ type: Array, default: () => [] }) readonly userIds!: number[];
  @Prop({ type: Array, default: () => [] }) readonly users!: IMember[];
  @Prop({ type: Boolean, default: false }) readonly loading!: boolean;
  @Prop({ type: Boolean, default: false }) readonly multiple!: boolean;
  @Prop({ type: Boolean, default: false }) readonly hasRoleType!: boolean; // 用户角色筛选

  args: any = {};
  selectorLoading = false;

  get _userIds() {
    return Array.isArray(this.users) && !!this.users.length ? this.users.map(o => o.id) : this.userIds;
  }

  handleCancel() {
    this.args = {};
    this.$emit('input', false);
  }
  handleOk() {
    if (this.loading) return;
    const { userIds = this._userIds, users = [], ransack = {} } = this.args;
    this.$emit('change', userIds, users, ransack);
    this.$emit('selectUsers', users, userIds);
    this.handleCancel();
  }
  selectUsers(userIds: number[], users: object[], ransack: object) {
    this.args = {
      userIds,
      users,
      ransack,
    };
    if (!this.multiple) {
      this.handleOk();
    }
  }

  handleLoadingChange(val: boolean) {
    this.selectorLoading = val;
  }
}
</script>

<template lang="pug">
a-modal(
  :title='title',
  :visible='visible',
  :footer='multiple ? undefined : null',
  :destroyOnClose='true',
  width='75%',
  @cancel='handleCancel'
  :zIndex='9999'
)
  .member-selector-container
    UserSelector(
      v-if='visible',
      :userIds='_userIds',
      :users='users',
      :multiple='multiple',
      :hasRoleType='hasRoleType',
      @change='selectUsers',
      @loadingChange='handleLoadingChange'
    )
  template(slot='footer')
    a-button(key='back', @click='handleCancel', size='large')
      | 取消
    a-button(
      key='submit',
      type='primary',
      :loading='loading || selectorLoading',
      @click='handleOk',
      size='large',
      :disabled='loading || selectorLoading'
    )
      | 确认
</template>

<style lang="stylus" scoped>
.member-selector-container
  margin -24px
  padding 0px 16px 0px
  height 600px
</style>
