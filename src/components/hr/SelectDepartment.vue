<template lang="pug">
.select-page
  Empty(v-if="departmentTrees.length === 0")
  .module(v-else)
    .cell(
      v-for="(item, index) in departmentTrees"
      :key="index"
      :class="{'cell-active': selects[0] && selects[0].id === item.id }"
      @click="onSelect(item)")
      .type(
        :class="{'college': formatType(item.type) === '学院', 'major': formatType(item.type) === '部门'}")
        | {{ formatType(item.type) }}
      .name {{ item.name }}
      span {{ item.children_count }}
  .module(
    v-for="(department, index) in selects"
    :key="index"
    v-if="department.children_count > 0")
    .cell(
      v-for="(item, key) in department.children"
      :key="key"
      :class="{'cell-active': selects[index + 1] && item.id === selects[index + 1].id }"
      @click="onSelect(item)")
      .type(
        :class="{'college': formatType(item.type) === '学院', 'major': formatType(item.type) === '部门'}")
        | {{ formatType(item.type) }}
      .name {{ item.name }}
      span {{ item.children_count }}
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import departmentStore from '@/store/modules/res/department.store';
import SearchBox from '@/components/hr/SearchBox.vue';

@Component({
  components: {
    SearchBox,
  },
})
export default class SelectDepartment extends Vue {
  @Prop({ type: Object, default: () => ({}) }) select?: object;
  @Prop({ type: Object, default: () => ({}) }) query?: object;
  selects: IObject[] = [];
  get departmentTrees() {
    return departmentStore.tree || [];
  }

  @Watch('query', { deep: true })
  watchQuery() {
    this.fetchData();
  }

  mounted() {
    this.fetchData();
  }

  async fetchData() {
    const params = {
      q: {
        ...this.query,
      },
    };
    await departmentStore.fetchTree(params);
    const firstTree = (this.departmentTrees || [])[0] || {};
    this.selects = firstTree.id ? [firstTree] : [];
  }

  onSelect(val: any) {
    const depth = val.depth || 0;
    const { length } = this.selects;
    if (depth === 0) {
      this.selects = [val];
    } else if (length === depth) {
      this.selects.push({ ...val, children: val.children || [] });
    } else {
      this.selects.splice(depth, length, { ...val, children: val.children || [] });
    }
    const path = (this.selects.slice(0, this.selects.length - 1) || []).map((e: any) => e.name);
    this.$emit('ok', { ...val, path });
  }

  formatType(type: string) {
    return (
      ({
        'Department::College': '学院',
        'Department::Major': '部门',
      } as any)[type] || '科室'
    );
  }
}
</script>

<style lang="stylus" scoped>
.select-page
  display flex
  width 100%
  height 100%
  overflow-y hidden
  overflow-x auto
  .module
    overflow-y auto
    overflow-x hidden
    padding 8px 0px
    min-width 220px
    width 100%
    height 70vh
    border-left 1px #e6e6e6 solid
    &::-webkit-scrollbar
      display none
    .cell
      display flex
      justify-content space-between
      align-items center
      padding 0px 16px 0px 48px
      width 100%
      color #383838
      line-height 40px
      position relative
      &:hover
        background #f8f8f8
        cursor pointer
      .type
        width 34px
        text-align center
        font-size 12px
        line-height 20px
        color #fff
        border-radius 4px
        background #FA8C15
        transform scale(0.9)
        position absolute
        left 8px
        bottom 10px
      .college
        background #6DC37D
      .major
        background #3DA8F5
      .name
        white-space nowrap
    .cell-active
      background #f6f6f6
      color #3da8f5
</style>
