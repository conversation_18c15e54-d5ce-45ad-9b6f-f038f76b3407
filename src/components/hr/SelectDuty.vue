<template lang="pug">
.select-duties
  .header
    SearchBox(placeholder="检索职务" @change="onSearch")
  .main
    .cell(v-for="item in dutyStore.records" :key="item.id" @click="onSelect(item)")
      .name {{item.name}}
      .type {{ item.is_manager ? '领导' : '员工' }}
      a-icon(type="check" style="color: #A6A6A6;" v-if="filterSelect(item.id)")
  .footer
    a-button(type="primary" size="large" @click="onOk") 确定
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import SearchBox from '@/components/hr/SearchBox.vue';
import dutyStore from '@/store/modules/res/duty.store';

@Component({
  components: {
    SearchBox,
  },
})
export default class SelectDuty extends Vue {
  @Prop({ type: Array, default: () => [] }) private selects?: any;
  private selectDuties: any[] = [];
  get dutyStore() {
    return dutyStore || {};
  }
  @Emit('ok')
  public onOk(): object[] {
    return this.selectDuties;
  }

  public mounted() {
    this.selectDuties = this.selects.concat();
    this.fetchData();
  }

  public async fetchData(val: string = '') {
    const params = {
      page: 1,
      per_page: 200,
      q: {
        name_cont: val,
      },
    };
    dutyStore.fetch(params);
  }

  public onSelect(val: any) {
    const isExist: boolean = this.selectDuties.some(e => e.id === val.id);
    if (isExist) {
      this.selectDuties = this.selectDuties.filter(e => e.id !== val.id);
    } else {
      this.selectDuties.push(val);
    }
  }

  public filterSelect(id: number): boolean {
    return this.selectDuties.some(e => e.id === id);
  }

  public onSearch(val: string) {
    this.fetchData(val);
  }
}
</script>

<style lang="stylus" scoped>
.select-duties
  .header
    padding 16px
  .main
    overflow auto
    max-height 260px
    .cell
      position relative
      display flex
      justify-content space-between
      align-items center
      padding 0px 16px
      width 100%
      line-height 40px
      &:hover
        background #f8f8f8
        cursor pointer
      .type
        position absolute
        top 8px
        right 36px
        width 44px
        border 1px solid #E5E5E5
        border-radius 11px
        background #fff
        color #808080
        text-align center
        font-size 10px
        line-height 22px
  .footer
    padding 12px 16px
    border-top 1px #E5E5E5 solid
    button
      width 100%
</style>
