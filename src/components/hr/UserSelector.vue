<script lang="ts">
/**
 * 人事选择器
 * 事件： change(ids, members)
 * v-model: userIds [人员 ids]
 * props: schoolId, required: true [所属学校的 id]
 *
 * functions:
 * reset    调用：$refs.component.reset()     用来重置组件的筛选参数
 */
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import { Teacher } from '@/models/teacher';
import { Expert } from '@/models/hr/expert';
import { Department } from '@/models/department';
import { isEqual, intersection, sortedUniq, difference } from 'lodash';

interface IMember {
  id: number;
  key: string;
  [key: string]: any;
}

@Component
export default class UserSelector extends Vue {
  currentPage: number = 1;
  totalPages: number = 1;
  totalCount: number = 1;
  perPage: number = 15;
  members: IMember[] = [];
  loading: boolean = false;
  scrollY: number | null = null;
  // filter
  query: IObject = {
    s: [],
  };
  // 人员类型
  selectUserType: 'Teacher' | 'Expert' = 'Teacher';
  userTypeOptions: IObject[] = [
    { text: '教师', value: 'Teacher' },
    { text: '专家', value: 'Expert' },
  ];
  userTypeConfig: IObject = {
    Teacher: { text: '教师', type: 'primary' },
    Expert: { text: '专家', type: 'success' },
  };
  // 高级筛选
  advancedFilterVisible: boolean = false;
  filterType: 'manager' | 'department' = 'manager';
  levels: number[] = [1, 2, 3, 4, 5];
  activeLevel: number | null = null; // 已选的学院等级
  departmentTreeData: any[] = [];
  activeDepartmentIds: number[] = []; // 已选的部门 ids
  // 已选列表
  listCurrentPage: number = 1;
  listTotalPages: number = 1;
  listPerPage: number = 50;
  selectedUsers: IMember[] = [];
  selectedListLoading: boolean = false;
  // config
  resizeTimer: any = null;
  syncTimer: any = null;

  @Model('change', { type: Array }) readonly userIds!: number[]; // member ids
  @Prop({ type: Array, default: () => [] }) readonly users!: IMember[]; // selected users
  @Prop({ type: Number, default: 1 }) readonly schoolId!: number[]; // school id
  @Prop({ type: Boolean, default: true }) readonly multiple!: boolean;
  @Prop({ type: Boolean, default: false }) readonly hasRoleType!: boolean;

  get storeSchoolId() {
    return this.$store.state.currentUser.school_id;
  }
  get role() {
    return this.$store.state.authRole;
  }
  // 是否设置了高级筛选，用于高级搜索窗口关闭后的提醒文案
  get isSetAdvancedFilter() {
    // yangyi
    return this.activeLevel || (this.activeDepartmentIds && this.activeDepartmentIds.length) || 0;
  }
  // 高级搜索的 ransack 条件
  get ransack() {
    const query: IObject = {};
    if (this.activeLevel) {
      const schoolId = this.schoolId || this.storeSchoolId;
      query.department_level_manager = [schoolId, this.activeLevel];
    }
    if (this.activeDepartmentIds && this.activeDepartmentIds.length) {
      query.department_manager = this.activeDepartmentIds;
    }
    return query;
  }
  // 表格的多选配置
  get selectionKeys() {
    return this.selectedUsers.map(user => user.key);
  }
  get rowSelection() {
    return this.multiple
      ? {
          selectedRowKeys: this.selectionKeys,
          onChange: this.selectionChange,
        }
      : null;
  }

  get allLoading() {
    return this.loading || this.selectedListLoading;
  }

  get UserIdsLength(): number {
    return (this.userIds && this.userIds.length) || 0;
  }

  @Watch('userIds')
  onValueChange() {
    if (this.userIds.length && this.selectedUsers.length === 0) {
      this.fetchSelectedUsers(1);
    } else {
      const selectedIds = this.selectedUsers.map(o => o.id).sort();
      const intersectionIds = intersection(this.userIds, selectedIds).sort();
      if (!isEqual(selectedIds, intersectionIds)) {
        this.fetchSelectedUsers(1);
      }
    }
  }

  @Watch('allLoading')
  handleAllLoading() {
    this.$emit('loadingChange', this.allLoading);
  }

  async mounted() {
    this.fetchTableUsers(1);
    if (this.multiple) {
      this.fetchSelectedUsers(1);
    }
    // 高级筛选
    if (this.role === 'teacher') {
      this.fetchDepartments();
    }
    this.$nextTick(this.initTableSize);
    window.addEventListener('resize', this.initTableSize);
  }

  beforeDestroy() {
    window.removeEventListener('resize', this.initTableSize);
  }

  /**
   * 根据 selectUserType 调用不同人员模型
   * expert 专家
   * teacher 教师
   */
  async fetchTypeMembers(params: object) {
    let responseData = {} as any;
    if (this.selectUserType === 'Expert') {
      const { data } = await new Expert(this.role).index(params);
      responseData = this.$utils.except({ ...data, members: data.experts }, 'experts');
    } else {
      const { data } = await new Teacher(this.role).index(params);
      responseData = this.$utils.except({ ...data, members: data.teachers }, 'teachers');
    }
    return { data: responseData };
  }

  /**
   * 获取表格人员数据
   */
  async fetchTableUsers(page: number = this.currentPage, tableQuery: object = {}, pageSize: number = this.perPage) {
    try {
      this.loading = true;
      const { data } = await this.fetchTypeMembers({
        page,
        per_page: pageSize,
        q: {
          ...this.query,
          ...tableQuery,
          ...this.ransack,
        },
      });
      this.currentPage = data.current_page;
      this.totalPages = data.total_pages;
      this.totalCount = data.total_count;
      this.members = this.getUniqueUsers(data.members);
      this.loading = false;
    } catch (error) {
      this.loading = false;
    }
  }

  /**
   * 获取已选择的人员记录
   */
  async fetchSelectedUsers(page: number = this.listCurrentPage) {
    if (this.selectedListLoading) return;
    if (Array.isArray(this.users) && this.users && this.users.length > 0) {
      // 外部提供用户列表，直接使用
      this.selectedUsers = this.getUniqueUsers(this.users);
      this.syncMembers(this.selectedUsers);
    } else if (!this.hasRoleType && Array.isArray(this.userIds) && this.userIds && this.userIds.length) {
      // 外部只有 ids, 获取接口数据
      try {
        this.selectedListLoading = true;
        this.listCurrentPage = page;
        this.listTotalPages = Math.ceil(this.userIds.length / this.listPerPage);
        const startIndex = (page - 1) * this.listPerPage;
        const endIndex = page * this.listPerPage;
        const { data } = await this.fetchTypeMembers({
          page: 1,
          per_page: this.listPerPage,
          q: {
            id_in: this.userIds.sort().slice(startIndex, endIndex),
            s: ['id asc'],
          },
        });
        const users = page === 1 ? data.members : this.selectedUsers.concat(data.members);
        this.selectedUsers = this.getUniqueUsers(users);
        this.selectedListLoading = false;
      } catch (error) {
        this.selectedListLoading = false;
      }
    } else {
      this.selectedUsers = [];
    }
  }

  /**
   * 设置类型用户的唯一 key
   * type_id
   */
  getUniqueUsers(users: IMember[]) {
    return users.concat().map(o => Object.assign(o, { key: `${o.type}_${o.id}` }));
  }

  /**
   * 获取部门数据，用于高级搜索
   */
  async fetchDepartments() {
    if (this.role === 'teacher') {
      const department = new Department();
      const { data } = await department.tree();
      this.departmentTreeData = Department.convertTreeData(data.departments);
    }
  }

  /**
   * 重置组件筛选数据
   */
  reset() {
    this.query.name_or_code_cont_any = '';
    this.activeLevel = null;
    this.activeDepartmentIds = [];
    this.advancedFilterVisible = false;
    this.fetchTableUsers(1);
  }

  /**
   * 切换人员类型
   */
  onMemberTypeChange() {
    this.reset();
  }

  /**
   * 表格多选
   */
  selectionChange(keys: string[], members: any[]) {
    // 去重
    const usersMap: { [key: string]: IMember } = members.concat(this.selectedUsers).reduce(
      (obj, item) => ({
        ...obj,
        [item.key]: item,
      }),
      {},
    );
    const selectedUsers = Object.values(usersMap).filter(o => keys.includes(o.key));
    this.syncMembers(selectedUsers);
  }

  /**
   * 删除成员
   */
  removeMember(index: number) {
    this.selectedUsers.splice(index, 1);
    this.syncMembers(this.selectedUsers);
  }

  /**
   * 加载更多成员
   */
  loadMoreSelectedMembers() {
    if (!this.selectedListLoading) {
      this.fetchSelectedUsers(this.listCurrentPage + 1);
    }
  }

  /**
   * v-model，同步本地成员状态，发送事件
   */
  syncMembers(members: IMember[]) {
    clearTimeout(this.syncTimer);
    this.selectedUsers = members.concat();
    this.syncTimer = setTimeout(() => {
      const currentIds = members.map(o => o.id);
      const unloadedIds = this.userIds.slice(this.listCurrentPage * this.listPerPage);
      const allIds = [...new Set(unloadedIds.concat(currentIds))].sort();
      this.$emit('change', allIds, this.selectedUsers, this.ransack);
    }, 600);
  }

  /**
   * 单选
   */
  chooseMember(member: IObject) {
    this.$emit('change', [member.id], [member], this.ransack);
  }

  /**
   * 重置高级筛选
   */
  resetAdvancedFilter() {
    this.activeLevel = null;
    this.activeDepartmentIds = [];
  }

  /**
   * 清空高级搜索条件
   */
  clearFilter() {
    this.resetAdvancedFilter();
    this.fetchTableUsers(1);
  }

  /**
   * 切换高级搜索类型
   */
  onFilterTypeChange() {
    this.resetAdvancedFilter();
  }

  /**
   * 按照学院等级获取数据
   */
  onDepartmentLevelChange(value: any) {
    this.activeLevel = value;
    this.fetchTableUsers(1);
  }

  /**
   * 按照部门获取数据
   */
  onDepartmentIdsChange() {
    this.fetchTableUsers(1);
  }

  /**
   * 清空已选人员列表
   */
  resetSelectedList() {
    this.syncMembers([]);
  }

  /**
   * 计算表格高度
   */
  initTableSize() {
    clearTimeout(this.resizeTimer);
    this.resizeTimer = setTimeout(() => {
      const el = this.$refs.tableContent as any;
      this.scrollY = el.clientHeight - 42 - 45 - 52;
    }, 300);
  }
}
</script>

<template lang="pug">
.member-selector
  //- =============== 人员列表 ===============
  .panel-box(:class='{ "has-title": multiple }')
    .title(v-if='multiple')
      | 选择
    .content-box.table-box(ref='tableContent', :class='{ "filter-open": advancedFilterVisible }')
      .members-box
        .filter-header
          Searcher.search-bar(
            v-if='selectUserType === "Teacher"',
            v-model='query',
            :variables='["name", "code"]',
            :focus='true',
            placeholder='职工号、姓名',
            @change='fetchTableUsers(1)'
          )
          Searcher.search-bar(
            v-if='selectUserType === "Expert"',
            v-model='query',
            :variables='["name", "company", "duty", "phone"]',
            :focus='true',
            placeholder='姓名、电话、职务、单位',
            @change='fetchTableUsers(1)'
          )
          .member-type(v-if='hasRoleType')
            a-radio-group(v-model='selectUserType', @change='onMemberTypeChange')
              a-radio(v-for='typeOpt in userTypeOptions', :key='typeOpt.value', :value='typeOpt.value')
                | {{ typeOpt.text }}
          .filter-menu(
            @click='advancedFilterVisible = !advancedFilterVisible',
            v-if='!advancedFilterVisible && role !== "student" && selectUserType === "Teacher"'
          )
            span.text-primary(v-if='activeLevel')
              | {{ activeLevel }} 级学院
            span.text-primary(v-else-if='activeDepartmentIds.length')
              | 已设置部门
            span(v-else)
              | 高级筛选&nbsp;
            a-icon(type='down')
        .members-table
          AdminTable(
            :data='members',
            :currentPage='currentPage',
            :totalPages='totalPages',
            :perPage='perPage',
            :totalCount='totalCount',
            :showSizeChanger='true',
            :rowSelection='rowSelection',
            :scroll='{ y: scrollY }',
            :loading='loading',
            paginationSize='small',
            rowKey='key',
            @change='fetchTableUsers'
          )
            a-table-column(title='头像', key='avatar', :width='60')
              template(slot-scope='text, record, index')
                a-avatar.avatar(:src='record.avatar', :size='28')
                  | {{ record.name }}
            a-table-column(title='姓名', dataIndex='name', key='name', :width='120')
            //- 教师
            template(v-if='selectUserType === "Teacher"')
              a-table-column(title='工号/学号', dataIndex='code', key='code', sorter='custom', :width='120')
              a-table-column(title='院部/班级', key='department_name', dataIndex='department_name')
                template(slot-scope='text, record, index')
                  | {{ (record.department_path || []).concat(record.department_name).join(" / ") }}
            template(v-if='selectUserType === "Expert"')
              a-table-column(title='性别', dataIndex='sex')
              a-table-column(title='电话', dataIndex='phone')
              a-table-column(title='单位', dataIndex='company')
              a-table-column(title='职务', dataIndex='duty')
            //- 选择
            a-table-column(title='选择', :width='80', v-if='!multiple')
              template(slot-scope='text, record, index')
                a-button(type='primary', size='small', @click.stop='chooseMember(record, index)')
                  | 选择
      .advanced-filter-box
        .advanced-filter-header
          span 高级筛选
          a-icon(type='close', @click='advancedFilterVisible = false')
        .advanced-filter-content
          .label.flex-between
            span 筛选
            a.text-primary(v-if='isSetAdvancedFilter', @click='clearFilter') 清空筛选
          a-radio-group(v-model='filterType', @change='onFilterTypeChange')
            a-radio(value='manager') 学院主管
            a-radio(value='department') 部门主管
          .tab(v-if='filterType === "manager"')
            a-select.form-item(
              :value='activeLevel ? activeLevel : undefined',
              placeholder='请选择学院级别',
              @change='onDepartmentLevelChange'
            )
              a-select-option(v-for='level in levels', :key='level', :value='level')
                | 第 {{ level }} 级学院
          .tab(v-if='filterType === "department"')
            a-tree-select.form-item(
              style='width: 100%',
              :dropdownStyle='{ maxHeight: "400px", overflow: "auto" }',
              :treeData='departmentTreeData',
              showSearch,
              allowClear,
              multiple,
              placeholder='请选择部门',
              treeNodeFilterProp='title',
              :treeDefaultExpandAll='false',
              v-model='activeDepartmentIds',
              @change='onDepartmentIdsChange'
            )
  //- =============== 已选 ===============
  .panel-box.member-list(v-if='multiple', :class='{ "has-title": multiple }')
    .title.flex-between
      span 已选（{{ userIds && userIds.length }}）
      PopoverConfirm(
        v-if='selectedUsers && selectedUsers.length ',
        title='清空',
        content='您确认要清空已选列表吗？',
        placement='bottomRight',
        @confirm='resetSelectedList'
      )
        IconTooltip(icon='delete', tips='清空')
    .content-box(v-loading='selectedListLoading')
      .selected-members
        .member(v-for='(user, index) in selectedUsers', :key='user.key')
          .info
            TaTag(:type='userTypeConfig[user.type].type', size='small', v-if='user.type')
              | {{ userTypeConfig[user.type].text }}
            span {{ user.name }}
          a-icon.close(type='close-circle', @click='removeMember(index)')
        .load-more(
          @click='loadMoreSelectedMembers',
          v-loading='selectedListLoading',
          v-if='listCurrentPage < listTotalPages'
        )
          | 点击加载更多
</template>

<style lang="stylus" scoped>
.member-selector
  display flex
  width 100%
  height 100%
  .has-title
    padding-top 44px !important
  .panel-box
    position relative
    flex 1 1 auto
    padding 0 0 20px
    height 100%
    background #fff
    .title
      position absolute
      top 0
      left 0
      padding 12px 0
      width 100%
      color rgba(128, 128, 128, 1)
      font-size 14px
      line-height 20px
    .filter-open
      padding-right 240px !important
      .advanced-filter-box
        transform translateX(0) !important
    .table-box
      position relative
      padding-right 0px
      transition all 0.3s ease
      .members-box
        .filter-header
          display flex
          justify-content space-between
          align-items center
          padding 5px 10px
          border-bottom 1px solid #eeeeee
          .search-bar
            flex-grow 1
            padding-left 18px
          .member-type
            flex 0
            padding-left 20px
            white-space nowrap
          .filter-menu
            flex-shrink 0
            width 106px
            color rgba(56, 56, 56, 1)
            text-align center
            font-size 14px
            line-height 20px
            cursor pointer
            &:hover
              color #3DA8F5
        .members-table
          overflow hidden
          padding 0 10px 0px
          height 100%
          .avatar
            background #3DA8F5
          .checkbox-group
            width 100%
          .teacher
            margin-left 0
            padding 8px
            width 100%
            cursor pointer
            &:hover
              background #eee
      .advanced-filter-box
        position absolute
        top 0
        right 0
        bottom 0
        z-index 1000
        overflow-x hidden
        overflow-y auto
        padding-top 54px
        width 240px
        border-left 1px solid #E8E8E8
        background #fff
        transition transform 0.3s ease
        transform translateX(100%)
        .advanced-filter-header
          position absolute
          top 0
          left 0
          display flex
          justify-content space-between
          align-items center
          padding 16px
          width 100%
          border-bottom 1px solid #E8E8E8
          color rgba(56, 56, 56, 1)
          font-weight 400
          font-size 14px
          i:hover
            color #58A8EF
            cursor pointer
        .advanced-filter-content
          overflow auto
          padding 16px
          height 100%
          .label
            margin-bottom 12px
            color rgba(128, 128, 128, 1)
            font-weight 500
            font-size 14px
            line-height 20px
          .form-item
            margin 16px 0
            width 100%
    .content-box
      position relative
      overflow hidden
      height 100%
      border 1px solid rgba(232, 232, 232, 1)
      border-radius 3px
      background rgba(255, 255, 255, 1)
      .selected-members
        overflow auto
        padding 10px
        height 100%
        .member, .load-more
          display flex
          justify-content space-between
          align-items center
          margin-bottom 4px
          padding 6px
          border-radius 4px
          background #fff
          color #888
          line-height 24px
          cursor pointer
          &:hover
            background #F5F5F5
          .close
            cursor pointer
            &:hover
              color red
          .info
            span
              vertical-align middle
        .load-more
          justify-content center
          color #3DA8F5
  .member-list
    flex 0 0 240px
    margin-left 20px
</style>
