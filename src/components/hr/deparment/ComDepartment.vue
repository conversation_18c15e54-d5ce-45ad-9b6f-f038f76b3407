<template lang="pug">
.container
  .header
    TaTitleHeader(title='组织结构')
      Searcher(v-model='queryObject', :variables='["name"]', tips='检索部门', placeholder='检索部门')
      TaExport(
        v-if='this.$utils.hasPermission("hr", "admin")',
        :store='adminDepartmentStore', 
        :temporaryQuery='queryObject')
      a-dropdown
        a-button(style='border: none')
          span {{ layoutType === "Tree" ? "树形视图" : "列表视图" }}
          a-icon(type='down')
        a-menu(slot='overlay')
          a-menu-item(
            v-for='(item, index) in layoutTypes',
            :key='index',
            style='padding: 12px 14px',
            @click='layoutType = item.key'
          )
            a-icon(:type='item.icon')
            span(style='margin: 0px 67px 0px 14px') {{ item.label }}
            a-icon(type='check', style='color: #ccc', v-if='layoutType === item.key')
  .main
    template(v-if='layoutType === "Tree"')
      .card(style='max-width: 310px; border-right: 0px')
        .card-header
          .title 结构
          PopoverForm(
            :value='visible === "create" || visible === "child"',
            trigger='click',
            placement='top',
            :form.sync='department',
            :template.sync='template',
            :width='248',
            :title='department.id ? "编辑部门" : "新建部门"',
            :submitTitle='department.id ? "提交" : "创建"',
            @change='(val) => { visible = val; }',
            @submit='submit'
          )
            IconTooltip(
              icon='plus-circle',
              theme='filled',
              v-if='role !== "teacher"',
              tips='新建一级部门',
              style='color: #3da8f5',
              @click='onCreate'
            )
        .card-main
          a-tree(
            :treeData='departmentTrees',
            :defaultExpandedKeys='expandedKeys',
            :expandedKeys='expandedKeys',
            :autoExpandParent='autoExpandParent',
            @expand='onExpand',
            :show-line='false',
            draggable
          )
            .tree-node-column(
              slot='name',
              slot-scope='scope',
              @click='onTreeNode(scope.id)',
              :style='{ paddingLeft: scope.type_text ? "40px" : "0px" }'
            )
              .type(v-if='scope.type_text', :class='[getTypeClass(scope.type)]')
                | {{ getTypeText(scope.type) }}
              .name
                template(v-if='scope.name.indexOf(keywork) > -1')
                  span {{ scope.name.substr(0, scope.name.indexOf(keywork)) }}
                  strong(style='color: #1890ff') {{ keywork }}
                  span {{ scope.name.substr(scope.name.indexOf(keywork) + keywork.length) }}
                template(v-else) {{ scope.name }}
              .children-count
                a-icon(type='bars', style='padding: 0px 4px; color: #a6a6a6')
                span(style='width: 20px') {{ scope.children_count }}
              .actions
                PopoverForm(
                  :value='visible === "create" + scope.id',
                  trigger='click',
                  placement='top',
                  :form.sync='department',
                  :template.sync='template',
                  :width='248',
                  :title='department.id ? "编辑部门" : "新建子部门"',
                  :submitTitle='department.id ? "提交" : "创建"',
                  @change='(val) => { visible = ""; }',
                  @submit='submit'
                )
                  IconTooltip(
                    icon='plus-circle',
                    tips='新建子部门',
                    v-if='role !== "teacher"',
                    @click='onCreateChild(scope.id)'
                  )
                IconTooltip(icon='form', tips='编辑部门', v-if='role !== "teacher"', @click='onEdit(scope)')
                //- TODO: 亟待优化，By TangMingChao
                PopoverConfirm(
                  :value='visible === scope.id',
                  @change='(val) => { visible = ""; }',
                  @confirm='onDelete(scope.id)'
                )
                  IconTooltip(icon='delete', tips='删除部门', v-if='role !== "teacher"', @click='visible = scope.id')
      .card
        .card-header
          a-breadcrumb(separator='>')
            a-breadcrumb-item(href='', v-if='departmentTree.path && departmentTree.path.length')
              | {{ departmentTree.path.join(" > ") }}
            a-breadcrumb-item {{ departmentTree.name }}
          Popover(
            :value='visible === "exchange"',
            placement='bottomRight',
            :width='160',
            @change='(val) => { visible = val; }'
          )
            .popover(slot='main')
              .popover-item(v-for='(name, index) in infoTypes', :key='index', @click='onChangType(name)')
                span {{ name }}
                a-icon(type='check', v-if='infoType === name')
            a-button.exchange(shape='circle', icon='block', size='small', @click='visible = "exchange"')
        .card-main
          Show(:department='departmentTree', :dutyStore='dutiesStore', v-if='infoType === "部门信息"')
          //- Logs(:departmentId.sync="departmentTree.id" v-else)
    template(v-else)
      AdminTable.table(
        :data='departmentStore.departments',
        :store='departmentStore',
        :showSizeChanger='true',
        :hideOnSinglePage='true',
        :perPage='perPage',
        @rowClick='onShow',
        @change='onChange'
      )
        a-table-column(dataIndex='name', title='部门全称')
        a-table-column(title='所属')
          template(slot-scope='scope')
            span.text-gray {{ scope.level_type_text }}
        a-table-column(dataIndex='code', title='部门代码')
        a-table-column(title='上级部门')
          template(slot-scope='scope')
            span {{ scope.path_text }}
        a-table-column(title='子部门', align='center')
          template(slot-scope='scope')
            span.text-primary(v-if='scope.children_count')
              | 包含{{ scope.children_count }}个
            span.text-none(v-else) 无
        a-table-column(title='岗位领导', align='center')
          template(slot-scope='scope')
            span.text-primary(v-if='scope.manager_duty_count')
              | 关联{{ scope.manager_duty_count }}个岗位
            span.text-none(v-else) 无
        a-table-column(title='岗位员工', align='center')
          template(slot-scope='scope')
            span.text-primary(v-if='scope.employee_duty_count')
              | 关联{{ scope.employee_duty_count }}个岗位
            span.text-none(v-else) 无
        a-table-column(width='80px', v-if='this.$utils.hasPermission("hr", "admin")')
          template(slot-scope='scope')
            .actions
              PopoverForm(
                :value='visible === "child"',
                trigger='click',
                placement='top',
                :form.sync='department',
                :template.sync='template',
                :width='248',
                title='编辑部门',
                submitTitle='提交',
                @change='(val) => { visible = ""; }',
                @submit='submit'
              )
                IconTooltip(icon='edit', tips='编辑', @click='onEdit(scope)')
              PopoverConfirm(
                :value='visible === scope.id',
                @change='(val) => { visible = ""; }',
                @confirm='onDelete(scope.id)'
              )
                IconTooltip(icon='delete', tips='删除', @click='visible = scope.id')
</template>

<script lang="ts">
import { Component, Vue, Watch, Prop } from 'vue-property-decorator';
import departmentStore from '@/store/modules/res/department.store';
import { resAdminDepartmentsStore } from '@/store/modules/res/admin/departments.store';
import departmentModel from '@/models/res/department';

import Show from '@/views/hr/department/Show.vue';

@Component({
  components: {
    Show,
  },
})
export default class ComDepartment extends Vue {
  @Prop({ type: Object, default: () => ({}) }) dutiesStore!: IObject;

  private keywork: string = '';
  private queryObject: any = {};
  private totalCount: number = 0;
  private department: any = {};
  private expandedKeys: number[] = [];
  private autoExpandParent: boolean = false;
  private infoType: string = '部门信息';
  private infoTypes: string[] = ['部门信息', '操作日志'];
  private layoutType: string = 'Tree';
  private visible: string | number = '';
  private perPage: number = 20;
  private role: string = '';
  private exportHeaders: IObject[] = [];
  private layoutTypes: any[] = [
    {
      label: '树形视图',
      key: 'Tree',
      icon: 'apartment',
    },
    {
      label: '列表视图',
      key: 'Table',
      icon: 'bars',
    },
  ];

  get template() {
    return [
      {
        key: 'name',
        label: '部门全称',
        widgetType: 'text',
        widget: 'input',
        placeholder: '请输入部门全称',
      },
      {
        key: 'code',
        label: '部门代码',
        widgetType: 'text',
        widget: 'input',
        placeholder: '请输入部门代码',
      },
      {
        key: 'type',
        label: '所属类型',
        widgetType: 'text',
        widget: 'select',
        placeholder: '请选择类型',
        options: [
          {
            label: '学院',
            value: 'Department::College',
          },
          {
            label: '部门',
            value: 'Department::Division',
          },
          {
            label: '科室',
            value: 'Department::Office',
          },
          {
            label: '专业',
            value: 'Department::Major',
          },
          {
            label: '系',
            value: 'Department::Line',
          },
        ],
      },
      {
        key: 'short_name',
        label: '部门简称',
        widgetType: 'text',
        widget: 'input',
        placeholder: '请输入部门简称',
      },
    ];
  }
  get departmentStore() {
    return departmentStore;
  }
  get adminDepartmentStore() {
    return resAdminDepartmentsStore;
  }

  get departmentTrees() {
    return departmentStore.tree || {};
  }
  get departmentTree() {
    return departmentStore.record || {};
  }

  @Watch('layoutType')
  watchDartment() {
    if (this.layoutType == 'tree') {
      this.fetchData();
    } else {
      this.fetchRecords();
    }
  }

  @Watch('queryObject')
  watchQuery() {
    this.fetchData();
    this.keywork = this.queryObject.name_cont_any && this.queryObject.name_cont_any[0];
    if (this.keywork) {
      this.expandedKeys = departmentModel.getDepartmentKeys(this.departmentTrees, this.keywork);
      this.autoExpandParent = true;
    } else {
      this.expandedKeys = [];
    }
  }

  mounted() {
    const role: string = this.$utils.hasPermission('hr', 'admin') ? 'admin' : 'teacher';
    departmentStore.changeNamespace(role);
    this.fetchData();
    this.role = role;
    this.adminDepartmentStore.init();
  }

  getTypeClass(val: string) {
    switch (val) {
      case 'Department::College':
        return { college: true };
        // eslint-disable-next-line no-unreachable
        break;
      case 'Department::Major':
        return { major: true };
        // eslint-disable-next-line no-unreachable
        break;
      case 'Department::Line':
        return {
          line: true,
        };
      case 'Department::Division':
        return { Division: true };
    }
  }

  getTypeText(type: string) {
    switch (type) {
      case 'Department::College':
        return '学院';
        // eslint-disable-next-line no-unreachable
        break;
      case 'Department::Division':
        return '部门';
        // eslint-disable-next-line no-unreachable
        break;
      case 'Department::Major':
        return '专业';
        // eslint-disable-next-line no-unreachable
        break;
      case 'Department::Line':
        return '系';
        // eslint-disable-next-line no-unreachable
        break;
      default:
        return '科室';
    }
  }

  async fetchData(refresh = false) {
    const params = {
      q: {
        ...this.queryObject,
      },
    };
    departmentStore.fetch(params);
    const data = await departmentStore.fetchTree(params);
    const firstTree = (data.departments || [])[0] || {};
    if (firstTree.id && !refresh) {
      this.expandedKeys = [firstTree.id];
      this.onTreeNode(firstTree.id);
    }
  }

  async fetchRecords(page: number = 1, perPage: number = 20) {
    const params = {
      page,
      per_page: this.perPage,
      q: { ...this.queryObject },
    };
    await departmentStore.fetch(params);
  }

  async onTreeNode(id: number) {
    departmentStore.find(id);
  }

  onCreate() {
    this.department = {
      code: '',
      name: '',
      short_name: '',
      type: '',
    };
    this.visible = 'create';
  }

  onCreateChild(id: number) {
    this.visible = `create${id}`;
    this.department = {
      parent_id: id,
      code: '',
      name: '',
      short_name: '',
      type: '',
    };
  }

  onEdit(val: IObject) {
    this.department = val;
    this.visible = 'child';
  }

  submit(val: IObject) {
    departmentStore.changeNamespace('admin');
    if (this.department.id) {
      this.patch(val);
    } else {
      this.create(val);
    }
  }

  async create(val: any) {
    try {
      await departmentStore.create(val);
      await this.fetchData(true);
      this.$message.success('创建成功');
    } catch (error) {
      this.$message.error('创建失败！');
    }
  }

  async patch(val: any) {
    try {
      const obj: any = {
        id: val.id,
        code: val.code,
        name: val.name,
        short_name: val.short_name,
        type: val.type,
      };
      await departmentStore.update(obj);
      await this.fetchData(true);
      this.$message.success('更新成功');
    } catch (error) {
      this.$message.error('更新失败！');
    }
  }

  async onDelete(id: number) {
    departmentStore.changeNamespace('admin');
    try {
      await departmentStore.delete(id);
      await this.fetchData(true);
      this.$message.success('删除成功');
    } catch (error) {
      this.$message.error('删除失败！');
    }
  }

  onExpand(expandedKeys: number[]) {
    this.expandedKeys = expandedKeys;
    this.autoExpandParent = false;
  }

  onShow() {}

  onChange(page: number, query: any, perPage: number) {
    Object.assign(this.queryObject, query);
    this.perPage = perPage;
    this.fetchRecords(page, perPage);
  }

  // extends
  onChangType(val: string) {
    if (this.infoType !== val) {
      this.infoType = val;
      this.visible = '';
    }
  }
}
</script>

<style lang="stylus" scoped>
.container
  overflow hidden
  padding-top 50px
  width 100%
  height 100%
  .header
    float left
    margin-top -50px
    width 100%
  .main
    display flex
    width 100%
    height 100%
    tr:hover
      .actions
        display flex
        justify-content space-around
        align-items center
    .actions
      display none
    .card
      position relative
      padding-top 54px
      width 100%
      height 100%
      border 1px solid #E8E8E8
      .card-header
        position absolute
        top 0px
        left 0px
        display flex
        justify-content space-between
        align-items center
        padding 0px 14px 0px 20px
        width 100%
        height 54px
        border-bottom 1px solid #E8E8E8
        .title
          color rgba(56, 56, 56, 1)
          font-weight 500
          font-size 14px
          line-height 14px
        span
          color rgba(56, 56, 56, 1)
          font-weight 400
          font-size 14px
          line-height 14px
        .exchange
          padding 3px
          border 1px #E5E5E5 solid
          border-radius 3px
          color #A6A6A6
          &:hover
            border 1px #3DA8F5 solid
            color #3DA8F5
        .create
          width 20px
          height 20px
          border none
          color #3da8f5
      .card-main
        overflow auto
        max-height 100%
        width 100%
        height 100%
        .tree-node-column
          position relative
          display flex
          justify-content space-between
          align-items center
          padding 0px
          height 20px
          &:hover
            .children-count
              display none
            .actions
              display inline
              background #f0fbff
              text-align center
          .type
            position absolute
            bottom 0px
            left 0px
            width 34px
            border-radius 4px
            background #FA8C15
            // background #3da8f5
            color #fff
            text-align center
            font-size 11px
            line-height 20px
          .college
            background #6DC37D
          .major
            background #FF4F3E
          .line
            background #3DA8F5
          .Division
            background #3DA8F5
          .profession
            background FF4F3E
          .name
            overflow hidden
            width 100%
            text-overflow ellipsis
            white-space nowrap
          .children-count
            width 50px
            color #A6A6A6
          .actions
            display none
            width 120px
            height 100%
    .table
      overflow auto
.text-none
  color #ccc
  font-size 14px
</style>
