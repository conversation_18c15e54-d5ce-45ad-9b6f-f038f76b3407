<template lang="pug">
.tabs
  a-tabs(defaultActiveKey="1")
    a-tab-pane.panel(tab="审批流程" key="1")
      Timeline(
        :instance="instance"
        :tokenFormResponse="tokenFormResponse"
        @change="emitChange"
        @flowableEdit="flowableEdit"
        @print="print")
    a-tab-pane.panel(:tab="commentsCount" key="2" forceRender)
      CommentPanel(
        :commentableId="instance.id"
        commentableType="Instance"
        :showHeader="false")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import Timeline from '@/components/bpm/Timeline.vue';
import { CommentPanel } from '@/components/comment';
import commentStore from '@/store/modules/comment.store';
import { userWorkflow, ITokenFormResponse } from '@/models/bpm/workflow';
import instanceModel, { IInstance } from '@/models/bpm/instance';
import { IToken } from '@/models/bpm/token';

@Component({
  components: {
    Timeline,
    CommentPanel,
  },
})
export default class ModificationInstance extends Vue {
  @Prop({ type: Number }) readonly instanceId!: number;

  instanceLoading: boolean = false;
  instance: IInstance = {
    payload: {},
  };
  // 表单相关
  tokenFormResponse: ITokenFormResponse = {
    workflow: { id: undefined },
    token: {},
    formTemplate: [],
    formEditable: false,
  };
  formData: IObject = {};
  formTemplate: object[] = [];
  get commentsCount() {
    return commentStore.totalCount ? `动态(${commentStore.totalCount})` : '动态';
  }
  @Watch('instanceId', { immediate: true })
  onInstanceChange() {
    if (this.instanceId) {
      this.fetchInstance();
    } else {
      this.resetData();
    }
  }

  created() {
    instanceModel.setRole('user');
  }
  resetData() {
    this.instance = { payload: {} };
  }
  async fetchInstance() {
    try {
      this.instanceLoading = true;
      const { data } = await instanceModel.find(this.instanceId);
      this.instance = data;
      this.formData = { ...this.instance.payload };
      await this.findTokenFormAndWorkflow(data.workflow_id!, data.current_token!.id as number);
      this.instanceLoading = false;
    } catch (error) {
      this.instanceLoading = false;
      this.$message.error('获取审批详情异常');
    }
  }
  // 获取【当前流程节点】的 workflow 和 加权表单模板
  async findTokenFormAndWorkflow(workflowId: number, tokenId: number) {
    try {
      if (!workflowId) return;
      const tokenFormResponse = await userWorkflow.findTokenForm(workflowId, tokenId);
      this.tokenFormResponse = tokenFormResponse;
      this.formTemplate = tokenFormResponse.formTemplate;
    } catch (error) {
      this.$message.error('获取动态表单模板异常');
    }
  }
  emitChange() {
    this.$emit('change', this.instance);
    this.fetchInstance();
  }
  print() {
    this.$emit('print', this.instance);
  }
  flowableEdit() {
    this.$emit('flowableEdit', this.instance);
  }
}
</script>

<style lang="stylus">
.tabs
  width 100%
  height 100%
  .ant-tabs-nav .ant-tabs-tab-active
    height 50px
  .panel
    height 100%
    width1 100%
</style>
