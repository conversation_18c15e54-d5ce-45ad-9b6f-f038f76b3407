<template lang="pug">
.page
  a-checkbox(
    :indeterminate="indeterminate"
    @change="onCheckAllChange"
    :checked="checkAll")
    | 全选
  a-checkbox-group(v-model="checkedList" @change="onChange")
    a-row(:gutter="20")
      a-col(
        :span="6"
        v-for="(item, index) in templates"
        :key="index")
        a-checkbox(:value="item.key") {{ item.label }}
  .footer
    a-button(size="large" @click="onCancel") 取消
    a-button(
      type="primary"
      size="large"
      :disabled="checkedList.length === 0"
      @click="onConfirm"
    ) 确定
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class TeacherKeys extends Vue {
  @Prop({ type: Array, default: () => [] }) templates!: any[];
  checkedList: any[] = [];
  indeterminate: boolean = true;
  checkAll: boolean = false;

  onConfirm() {
    const resObj = this.checkedList.reduce((res: any, key: string) => {
      res[key] = this.templates.find((e: any) => e.key === key).label;
      return res;
    }, {});
    this.$emit('confirm', resObj);
  }

  onCancel() {
    this.$emit('cancel');
  }

  onChange(checkedList: string[]) {
    this.indeterminate = !!checkedList.length && checkedList.length < this.templates.length;
    this.checkAll = checkedList.length === this.templates.length;
  }

  onCheckAllChange(e: any) {
    Object.assign(this, {
      checkedList: e.target.checked ? this.templates.map((e: any) => e.key) : [],
      indeterminate: false,
      checkAll: e.target.checked,
    });
  }
}
</script>

<style lang="stylus" scoped>
.page
  position relative
  padding 20px
  width 100%
  height 100%
  .ant-checkbox-group
    margin-top 14px
    padding 10px 0px
    width 100%
    border-top 1px #e8e8e8 solid
    .ant-col-6
      margin 5px 0px

.footer
  position absolute
  bottom 0px
  left 0px
  display flex
  justify-content flex-end
  padding 10px
  width 100%
  border-top 1px #e8e8e8 solid
  button
    width 80px
</style>
