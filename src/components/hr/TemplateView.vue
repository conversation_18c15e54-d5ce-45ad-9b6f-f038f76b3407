<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import Attachments from '../global/Attachments.vue';

@Component({
  components: {
    Attachments,
  },
})
export default class TemplateView extends Vue {
  @Prop({ type: Array, default: () => [] }) template!: any[];
  @Prop({ type: Object, default: () => ({}) }) formData!: IObject;
  @Prop({ type: String, default: 'vertical' }) private layout?: string; // vertical, horizontal
  @Prop({ type: Number, default: 0 }) private colSpan?: number;

  getContacts(value: any[]) {
    const members = value instanceof Array ? value : [];
    return members.map((o: any) => o.name).join('、');
  }
}
</script>

<template lang="pug">
.template-form-payload
  a-row(:gutter="16")
    a-col(
      v-for="item in template"
      :span="colSpan || item.layout.span || 24"
      :key="item.key")
      .payload-item(:class="{'horizontal': layout === 'horizontal'}")
        .title
          span(
            style="color: #e50114; padding: 0px 1px; vertical-align: middle;"
            v-if="item.layout && item.layout.required") *
          span {{ item.name }}
        .value-container
          .value(v-if="item.layout.component === 'date'")
            | {{ formData[item.key] | format('YYYY-MM-DD') }}
          .value(v-else-if="item.layout.component === 'time'")
            | {{ formData[item.key] | format('HH:mm:ss') }}
          .value(v-else-if="item.layout.component === 'datetime'")
            | {{ formData[item.key] | format('YYYY-MM-DD HH:mm:ss') }}
          .value(v-else-if="item.layout.component === 'contacts'")
            | {{ getContacts(formData[item.key]) }}
          .value(v-else-if="item.layout.component === 'checkbox'")
            ul.options
              li.option(v-for="(val, index) in (formData[item.key] || [])")
                | {{ val }}
          .value(v-else-if="item.layout.component === 'file'")
            Attachments(:attachments="formData[item.key] || []")
          .value(v-else)
            | {{ formData[item.key] }}
          //- 空内容
          span.text-gray(v-if="!formData[item.key]")
            | 空
</template>

<style lang="stylus" scoped>
.template-form-payload
  padding 0 16px 10px
  .payload-item
    padding 20px 0 10px
    border-bottom 1px solid #E6E7EB
    .title
      margin-bottom 8px
      color #000
      font-weight bold
    .value-container
      color rgba(0, 0, 0, 0.65)
      line-height 24px
      .options
        margin 0
        padding-left 20px
  .horizontal
    display flex
    padding 12px 0px
    border 0px
    .title
      margin 0px
      min-width 100px
      color #808080
      font-weight 400
      line-height 20px
    .value-container
      margin-left 20px
      color #383838
      line-height 20px
</style>
