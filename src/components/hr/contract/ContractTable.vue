<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class contractTable extends Vue {
  @Prop({ type: String, default: '' }) prop!: string;

  state: string = '';

  mounted() {
    this.fetchData();
  }

  fetchData() {}
}
</script>

<template lang="pug">
.container
  | 表格
</template>

<style lang="stylus" scoped></style>
