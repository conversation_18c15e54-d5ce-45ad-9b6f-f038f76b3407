<script lang="ts">
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import StoreField from './StoreField.vue';
import { hrTeacherTeacherStore } from '@/store/modules/hr/teacher/teacher.store';
import { TaIndexViewConfigInterface, TaIndexImportHeader } from '../global/TaIndex';
import { ITeacher } from '@/models/teacher';
import { ILabel } from '@/models/res/label';
import { resTeacherTeacherStore } from '@/store/modules/res/teacher/teacher.store';
import { commTeacherLabelStore } from '../../store/modules/comm/teacher/label.store';
import TaImport from '../global/TaImport.vue';

@Component({
  components: {
    StoreField,
  },
})
export default class TeacherField extends Vue {
  @Model('change') value!: number | number[];

  @Prop({ type: Boolean, default: false }) disabled!: boolean;
  @Prop({ type: <PERSON><PERSON><PERSON>, default: false }) multiple!: boolean;
  @Prop({ type: Boolean, default: false }) hideTags!: boolean;

  selectedLabels: ILabel[] = [];
  visible = false;

  get store() {
    return hrTeacherTeacherStore;
  }

  get labelTeacherStore() {
    return resTeacherTeacherStore;
  }

  get config() {
    return {
      initParams: {
        params: { q: {} },
      },
      tableColumns: [
        { title: '姓名', dataIndex: 'name', type: 'string', search: true },
        { title: '工号', dataIndex: 'code', type: 'string', search: true },
        { title: '学院', dataIndex: 'college_name', type: 'string' },
        { title: '部门', dataIndex: 'department_name', type: 'string' },
      ],
    };
  }

  get localValue() {
    return this.value;
  }

  set localValue(val: number | number[]) {
    this.$emit('change', val);
  }

  get labelStore() {
    return commTeacherLabelStore;
  }

  get taIndexViewConfig(): TaIndexViewConfigInterface {
    return {
      recordName: '标签',
      store: this.labelStore,
      showSelectionByDefault: true,
      mode: 'table',
      tableConfig: {
        scroll: { y: '60vh' },
      },
    };
  }

  @Watch('visible')
  handleVisibleChange() {
    if (this.visible) {
      this.$nextTick(() => {
        this.labelStore.init();
      });
    }
  }

  onSelect(newValue: ILabel[], oldValue: ILabel[]) {
    // 单选
    this.selectedLabels = newValue.filter(item => !oldValue.includes(item));
  }

  onOpenModal() {
    this.visible = true;
  }

  async handleOk() {
    const comp = this.$refs.storeField as StoreField<Partial<ILabel> & Partial<ITeacher>>;
    this.labelTeacherStore.init({
      parents: [
        {
          type: 'labels',
          id: this.selectedLabels[0].id,
        },
      ],
    });
    await this.labelTeacherStore.index({ per_page: 999999 });
    comp.selectedRecords = this.labelTeacherStore.records;
    this.visible = false;
  }

  handleCancel() {
    this.visible = false;
  }

  onOk() {
    this.$emit('ok');
  }

  onCancel() {
    this.$emit('cancel');
  }

  onOpen() {
    this.$emit('open');
  }

  open() {
    const comp = this.$refs.storeField as StoreField<Partial<ILabel> & Partial<ITeacher>>;
    comp.visible = true;
  }

  get importHeaders() {
    return [
      {
        name: '工号',
        key: 'code',
        primary_key: true,
      },
    ];
  }

  onImport() {
    (this.$refs.importComp as TaImport).onClickFileInput();
  }

  onImportConfirm(options: { resultHeaders: TaIndexImportHeader[]; uid: string; resetImport: () => void }) {
    this.store
      .sendCollectionAction({
        action: 'find_by_file',
        config: {
          data: {
            headers: options.resultHeaders,
            uid: options.uid,
          },
        },
      })
      .then(({ data }) => {
        this.localValue = data.teachers.map((record: IObject) => record.id);
        options.resetImport();
      });
  }
}
</script>

<template lang="pug">
.duty-field
  StoreField(
    ref='storeField'
    v-model='localValue',
    recordName='教师',
    :store='store',
    :storeConfig='config',
    :disabled='disabled',
    :multiple='multiple',
    @ok='onOk',
    @cancel='onCancel',
    @open='onOpen',
  )
    template(#modal-header)
      .modal-header
        .title 选择
        TextButton.button(icon='import', @click='onOpenModal') 导入
        TextButton.button(icon='import', @click='onImport') Excel 导入
    template(#tags, v-if='hideTags')
      .empty
    template(#tag-text='{ record }')
      span {{ record.name }}（{{ record.code }}）
  TaImport.hidden(
    ref='importComp'
    :store='store',
    :headers='importHeaders',
    :onConfirmFunc='onImportConfirm',
  )
  a-modal(v-model='visible', title='从标签导入', width='90%', @ok='handleOk', @cancel='handleCancel')
    .modal
      TaIndexView.ta-index-view(
        v-model='selectedLabels',
        :config='taIndexViewConfig',
        @onSelect='onSelect'
      )
        template(#table)
          a-table-column(dataIndex='name', title='名称')
          a-table-column(dataIndex='tag_count', title='教师人数')
</template>

<style lang="stylus" scoped>
.modal-header
  display flex
  .title
    padding 10px 30px 10px 10px
.hidden
  display none
</style>
