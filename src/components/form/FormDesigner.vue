<script lang="ts">
import { Component, Vue, Prop, Watch, Emit, Model } from 'vue-property-decorator';
import { cloneDeep } from 'lodash/fp';
import draggable from 'vuedraggable';
import widgets from './widgets';
import TemplateForm from '@/components/form/TemplateForm.vue';
import FormItemEditCell from './FormItemEditCell.vue';
import FormTemplateSelector from './FormTemplateSelector.vue';
import { IFormTemplateItem } from '@/interfaces/formTemplate.interface';
import wechatApp, { IApp } from '@/models/wechat/app';
import { FormTemplate, IFormTemplate } from '@/models/forms/form_template';

@Component({
  components: {
    draggable,
    FormTemplateSelector,
    FormItemEditCell,
    TemplateForm: () => import('./TemplateForm.vue'),
  },
})
export default class FormDesigner extends Vue {
  @Model('change', { type: Array, default: () => [] }) value!: IObject[];
  @Prop({ type: Boolean, default: true }) hasTemplate!: boolean;

  mode: string = 'mobile'; // mobile, pc
  activeKey: string = '';
  templates: IFormTemplateItem[] = widgets;
  formFields: IObject[] = [];
  formatFormField: IObject[] = [];
  formItem: IObject = {};
  // 编辑默认值
  defaultPayloadEditorVisible: boolean = false;
  // form-template
  templateSelectorVisible: boolean = false;
  saveTemplateName: string = '';
  // wechat
  wechatApps: IApp[] = [];
  wechatAppsOptions: any[] = [];
  // other
  gridVisible: boolean = false;
  gridCols: number[] = Array(24)
    .fill(0)
    .map((o, i) => i + 1);

  @Watch('value')
  watchValue() {
    this.formFields = this.formatFormFields(this.value || []);
  }
  @Watch('formItem')
  watchFormItem() {
    this.$emit('change', this.formFields);
  }

  get conditionOptions() {
    return (
      this.formFields
        ?.filter(item => !this.isContainerItem(item))
        ?.map(item => ({
          label: item.name,
          value: item.key,
        })) || []
    );
  }

  formatFormFields(raw: IObject[]) {
    const keyToIdRange: IObject = {};
    raw.forEach((item, index) => {
      if (this.isContainerItem(item)) {
        keyToIdRange[item.key] = keyToIdRange[item.key] || [];
        keyToIdRange[item.key].push(index);
      }
    });

    Object.keys(keyToIdRange).forEach(key => {
      const startIndex = keyToIdRange[key][0];
      const endIndex = keyToIdRange[key][1];
      raw[startIndex].layout.template = [];
      raw[startIndex].layout.templateIndexAry = [];
      for (let i = startIndex + 1; i < endIndex; i++) {
        raw[startIndex].layout.template.push(this.formFields[i]);
        raw[startIndex].layout.templateIndexAry.push(i);
      }
    });
    return raw;
  }

  mounted() {
    this.formFields = this.value || [];
    this.fetchWechatApps();
  }
  async fetchWechatApps() {
    if (this.wechatApps.length === 0) {
      const { data } = await wechatApp.index({ page: 1, per_page: 1000 });
      this.wechatApps = data.wechat_apps;
      this.wechatAppsOptions = data.wechat_apps.map((o: IApp) => ({ label: o.name, value: o.appid }));
    }
  }

  isContainerItem(formItem: IObject) {
    return (formItem.layout.component || '').includes('container');
  }

  selectWidget(value: IObject) {
    const key = `${value.layout.component}_${Date.now()}`;
    const item = cloneDeep(value);
    this.formItem = {
      key,
      ...item,
    };

    if (this.isContainerItem(this.formItem)) {
      // 同时推入两块，开始与结束
      this.formFields.push(this.formItem);
      this.formFields.push({
        key: key,
        accessibility: 'hidden',
        layout: {
          component: 'container_end',
        },
        name: '块结束',
      });
    } else {
      this.formFields.push(this.formItem);
    }
    this.activeKey = key;
    this.$emit('change', this.formFields);
  }

  onFormItemClick(formItem: IObject) {
    this.formItem = formItem;
    this.activeKey = formItem.key;
  }

  onRemoveFormItem(index: number) {
    if (this.formFields[index].key === this.activeKey) {
      this.activeKey = '';
      this.formItem = {};
    }
    const key = this.formFields[index].key;
    this.formFields.splice(index, 1);
    const anotherIndex = this.formFields.findIndex(item => key === item.key);

    if (anotherIndex !== -1) {
      this.formFields.splice(anotherIndex, 1);
    }
    this.$emit('change', this.formFields);
  }

  onCopyFormItem(formItem: IFormTemplateItem, index: number) {
    if (this.isContainerItem(formItem)) {
      return;
    }
    const item = cloneDeep(formItem);
    this.formItem = {
      ...item,
      key: `${item.layout.component}_${Date.now()}`,
    };
    this.activeKey = this.formItem.key;
    this.formFields.splice(index + 1, 0, this.formItem);
    this.$emit('change', this.formFields);
  }

  // 编辑form项
  scrollSettingPanelToBottom() {
    this.$nextTick(() => {
      const ref = this.$refs.settingPanel as any;
      ref.scrollTop = ref.scrollHeight;
    });
  }
  addOption(arr: IObject[]) {
    const obj = { label: '' };
    this.formItem.layout.options.push(obj);
    this.scrollSettingPanelToBottom();
  }

  deleteOption(index: number) {
    this.formItem.layout.options.splice(index, 1);
  }

  copyOption(val: IObject, index: number) {
    this.formItem.layout.options.splice(index + 1, 0, { ...val });
  }

  clone(val: IObject) {
    const key = `${val.layout.component}_${Date.now()}`;
    return {
      ...val,
      key,
    };
  }

  onGroupItem(val: number) {
    this.formItem.layout.span = val;
  }
  onSetAlign(a: string) {
    this.formItem.layout.textAlign = a;
  }

  formatFromType(val: IObject) {
    const formItem: IObject = (this.templates || []).find((e: IObject) => e.layout.component === val.component) || {};
    return formItem.name || '未知类型';
  }

  onDragUpdate() {
    this.$emit('change', this.formFields);
  }

  onSelectTemplate(o: IFormTemplate) {
    const fields = o.form ? o.form.fields || [] : [];
    this.formFields.push(...fields);
  }

  async saveTemplate() {
    const role = this.$utils.hasPermission('forms', 'admin') ? 'admin' : 'teacher';
    await new FormTemplate(role).create({
      name: this.saveTemplateName,
      form: {
        fields: this.formFields,
      },
    });
    this.$message.success('保存成功');
  }

  // 编辑表格
  addAttrsItem() {
    this.formItem.layout.attrs.push({
      key: `col_${Date.now()}`,
      name: ` 列${this.formItem.layout.attrs.length + 1}`,
      value: '',
      show: true,
    });
    this.scrollSettingPanelToBottom();
  }
  deleteAttrsItem(index: number) {
    this.formItem.layout.attrs.splice(index, 1);
  }

  // wechat articles
  onWechatChange(formItem: IFormTemplateItem, appid: string) {
    const wechat = this.wechatApps.find((o: IApp) => o.appid === appid);
    Object.assign(formItem.layout, { wechat });
  }
  // set default value
  onSetDefaultValue(payload: IObject) {
    this.formFields.forEach(f => {
      (f as IFormTemplateItem).layout.defaultValue = payload[f.key];
    });
    this.defaultPayloadEditorVisible = false;
  }
  isValueComponent(item: IFormTemplateItem) {
    return item.layout.component !== 'hr' && item.layout.component !== 'label';
  }

  // container 块，禁止移动
  onChoose(event: CustomEvent) {
    const choseDivId = (event as any).item.id;
    if (choseDivId.includes('container')) {
      const doc = document.getElementById(choseDivId);
      (doc as any).draggable = false;
    }
  }
}
</script>

<template lang="pug">
.form-designer
  //- 表单模块
  .layout-sider
    .sider-header
      span 表单控件
    .sider-main
      draggable.widgets(
        :list="templates"
        :group="{ name: 'people', pull: 'clone', put: false }"
        ghost-class="ghost"
        :sort="false"
        :clone="clone"
        :move="() => {return true}")
        .column(
          v-for="(widget, index) in templates"
          :key="index"
          @click="selectWidget(widget)")
          .form-item
            span {{ widget.name }}
  //- 表单内容
  .layout-content
    .card(:style='{ width: mode === "mobile" ? "320px" : "720px", height: mode === "mobile" ? "520px" : "100%"}')
      .card-header
        a-radio-group.mode(v-model="mode" size="small" buttonStyle="solid")
          a-radio-button(value="mobile")
            | 手机端
          a-radio-button(value="pc")
            | 电脑端
        .menu(v-if="hasTemplate")
          IconTooltip(icon="plus-circle" tips="从模板中导入" @click="templateSelectorVisible = true")
          PopoverConfirm(
            title="完善信息"
            type="primary"
            :disabled="!saveTemplateName"
            @confirm="saveTemplate")
            a-input(placeholder="请输入表单模板名称" v-model="saveTemplateName" slot="content")
            IconTooltip(icon="save" tips="保存到模板库")
      .form
        draggable.row(v-model="formFields" @update="onDragUpdate" @choose="onChoose")
          a-col(
            v-for="(item, index) in formFields"
            :id="`${item.layout.component}-${index}`"
            :key="index"
            :span="mode === 'mobile' ? 24 : item.layout.span || 24")
            FormItemEditCell(
              :formItem="item"
              :active="activeKey === item.key"
              @click="onFormItemClick(item, index)"
              @remove="onRemoveFormItem(index)"
              @copy="onCopyFormItem(item, index)")
  //- 表单设置
  .layout-config(v-if="formFields.length > 0 && activeKey" ref="settingPanel")
    .config-header
      span 表单设置 - {{ formatFromType(formItem.layout) }}
    .config-middle
      .form
        .form-item
          .form-item-label 标题
          a-textarea(v-model="formItem.name" :autoSize="{ minRows: 1 }" placeholder="请输入表单标题")

          template(v-if="isValueComponent(formItem)")
            .form-item(v-if="formItem.layout.component !== 'tag' && formItem.layout.component !== 'table'")
              .form-item-label 提示
              input(v-model="formItem.layout.placeholder", placeholder="请输入表单提示")

            .form-item(v-if="isValueComponent(formItem)")
              .form-item-label 默认值（可选）
              a-button(type="primary" @click="defaultPayloadEditorVisible = true" v-if="!!formItem.layout.defaultValue")
                | 更新默认值
              a-button(@click="defaultPayloadEditorVisible = true" v-else)
                | 创建默认值

        .form-item(v-if="formItem.layout.defaultValue")
          .form-item-label 组件可见性
          a-checkbox(v-model="formItem.layout.notEditable") 仅显示
        .form-item
          .form-item-label 表单关键字（不可重复）
          a-textarea(v-model="formItem.key" :autoSize="{ minRows: 1 }" placeholder="表单关键字（不可重复）")
        .form-item
          .form-item-label 存储关键字（可选）
          a-textarea(v-model="formItem.map_key" :autoSize="{ minRows: 1 }" placeholder="可用于storage的存储关键字，英文字母")
        .form-item
          .form-item-label Antd 图标
          a-textarea(v-model="formItem.icon" :autoSize="{ minRows: 1 }" placeholder="Antd 图标 type")
        .form-item
          a-checkbox(v-model="formItem.layout.summary") 显示在列表页
        //- 交互属性
        .form-item(v-if="formItem.layout.multiple !== undefined")
          .form-item-label 多选
          a-checkbox(v-model="formItem.layout.multiple") 多选
        .form-item(v-if="formItem.layout.required !== undefined")
          .form-item-label 验证
          a-checkbox.box(
            :checked="formItem.layout.required"
            @change="(e) => {formItem.layout.required = e.target.checked}") 必选项

        template(v-if="formItem.layout.component === 'label'")
          //- 文本对齐
          .form-item
            .form-item-label.flex-between
              span 对齐方式
            .group
              .group-item(
                v-for="a in ['left', 'center', 'right']"
                :key="a"
                :class="{'group-item-active': formItem.layout.textAlign === a }",
                @click="onSetAlign(a)")
                | {{ a }}
          //- 行数
          .form-item
            .form-item-label 标签行数
            a-input-number.block(v-model="formItem.layout.rowspan" :min="1" :step="1" placeholder="所占行数")
        //- 宽度
        .form-item(v-if="formItem.layout.component !== 'table'")
          .form-item-label.flex-between
            span 表单占比宽度 ...%
            span.link.text-primary(@click="gridVisible = !gridVisible")
              | {{ gridVisible ? '隐藏' : '显示更多' }}
          .group
            .group-item(v-for="num in [6, 8, 12, 24]", :key="num",
            :class="{'group-item-active': formItem.layout.span === num }",
            @click="onGroupItem(num)") {{ (num * 25/6).toFixed() }}

        //- 更多宽度配置
        .form-item(v-if="formItem.layout.component !== 'table' && gridVisible")
          .form-item-label 表单占列数
          a-row(:gutter="10")
            a-col(v-for="num in gridCols" :key="num" :span="4")
              .grid-item(
                :class="{'grid-item-active': formItem.layout.span === num }",
                @click="onGroupItem(num)")
                | {{ num }}

        //- 多选项
        .form-item(v-if="formItem.layout.options")
          .form-item-label 选项内容 (注：拖动可排序)
          draggable(v-model="formItem.layout.options")
            .option-box(v-for="(item, index) in formItem.layout.options", :key="index")
              .option-item
                .option-item-label 选项
                input(v-model="item.label" placeholder="请输入选项" autofocus)
              .option-item
                .option-item-label 值
                input(v-model="item.value" placeholder="请输入值" autofocus)
              .option-icon.delete(@click="deleteOption(index)")
                a-icon(type="delete")
              .option-icon.copy(@click="copyOption(item, index)")
                a-icon(type="copy")
          .add-action(icon="plus", @click="addOption(formItem.layout.options)")
            a-icon(type="plus")
            span(style="margin-left: 4px") 添加新选项
        //- 表格组件
        .form-item(v-if="formItem.layout.component === 'table'")
          .form-item-label 配置表格列
          draggable(v-model="formItem.layout.attrs")
            .option-box(v-for="(item, index) in formItem.layout.attrs", :key="index")
              .option-item
                .option-item-label 字段名
                input(v-model="item.key", placeholder="请字段名称")
              .option-item
                .option-item-label 列名称
                input(v-model="item.name", placeholder="请输入列名称")
              .option-item(style="margin-top: 10px")
                .option-item-label 默认值
                input(v-model="item.value", placeholder="默认值（选填）")
              .option-item(style="margin-top: 10px")
                .option-item-label 可见性
                a-switch(v-model="item.show")
              .option-icon.delete(@click="deleteAttrsItem(index)")
                a-icon(type="delete")
          .add-action(icon="plus", @click="addAttrsItem")
            a-icon(type="plus")
            span(style="margin-left: 4px") 添加列
        .form-item(v-if="formItem.layout.component === 'if_container'")
          .form-item-label 关联条件
          a-select.condition-selector(
            v-model="formItem.layout.conditionKey"
            :options="conditionOptions"
            placeholder="请关联条件选项")
          .form-item-label 为真时条件值
          input(v-model="formItem.layout.conditionValue", placeholder="请输入条件为真的选项值")
        //- 关联微信控件
        .form-item(v-if="formItem.layout.component.includes('wechat_articles')")
          .form-item-label 关联微信应用
          a-select.wechat-selector(
            v-model="formItem.layout.wechat.appid"
            :options="wechatAppsOptions"
            placeholder="请关联微信应用"
            @change="(appid) => { onWechatChange(formItem, appid) }")

  FormTemplateSelector(v-model="templateSelectorVisible" @select="onSelectTemplate")

  MainModal(v-model="defaultPayloadEditorVisible" title="管理表单默认值")
    .default-form
      TemplateForm(
        :formData="{ id: -1 }"
        :template="formFields"
        :required="false"
        :showActions="true"
        @submit="onSetDefaultValue"
        @cancel="defaultPayloadEditorVisible = false"
      )
</template>

<style lang="stylus" scoped>
.form-designer
  position relative
  overflow hidden
  width 100%
  height 100%
  .layout-sider
    position absolute
    top 0px
    left 0px
    overflow auto
    padding 0px 14px 14px
    width 300px
    height 100%
    background #fff
    .sider-header
      padding 15px 6px 5px
      font-weight 400
      font-size 14px
      line-height 26px
    .sider-main
      padding 6px 0px
      width 100%
      font-size 14px
      .widgets
        display flex
        flex-wrap wrap
        .column
          width 50%
          .form-item
            margin 6px
            padding 6px
            border 1px #e6e6e6 solid
            border-radius 4px
            background #fff
            color #808080
            text-align center
            line-height 20px
            cursor move
            &:hover
              border 1px #20a0ff dashed
              color #20a0ff
  .layout-content
    display flex
    align-items center
    overflow auto
    padding 20px 0px
    width 100%
    height 100%
    background #f6f6f6
    .card
      position relative
      margin 0px auto
      padding 40px 10px
      width 700px
      height 500px
      border-radius 10px
      background #fff
      box-shadow 0 2px 12px 0 rgba(0, 0, 0, 0.1)
      .card-header
        position absolute
        top 0
        left 0
        display flex
        justify-content space-between
        align-items center
        padding 0 10px
        width 100%
        height 40px
        .menu
          display inline-block
      .card-footer
        position absolute
        bottom 0
        left 0
        display flex
        justify-content center
        align-items center
        padding 0 10px
        width 100%
        height 40px
      .form
        overflow-y auto
        padding 5px
        width 100%
        height 100%
        border 1px #f5f5f5 solid
        background #f5f5f5
        .row
          margin 0 -5px
        .table-item
          position relative
          margin-bottom 10px
          padding 8px 5px
          border 1px #fff solid
          background #fff
          .table-title
            font-weight bold
        .form-item
          position relative
          display flex
          justify-content space-between
          align-items center
          margin-bottom 10px
          padding 14px 5px
          border 1px #fff solid
          background #fff
          .pre-label
            color #000
            white-space pre-wrap
          .placeholder
            flex-shrink 0
            margin-left 8px
            color #888
          &:hover
            border 1px #20A0FF dashed
            cursor pointer
            .form-item-action
              display block
          .form-item-action
            z-index 99
            display none
            padding 3px
            border-radius 0px
            background #20A0FF
            color #fff
            font-size 10px
            line-height 10px
            &:hover
              opacity 0.8
        .group
          display flex
          flex-wrap nowrap
          overflow-x auto
          width 100%
        .editor
          width 100%
          height 180px
        .box
          width 100%
        img
          padding-top 5px
          width 20px
          .grid-item-fade
            position absolute
            top 0px
            left 0px
            z-index 9
            width 100%
            height 100%
        .form-item-active
          border 1px #20A0FF solid
          &:hover
            border 1px #20A0FF solid
  .layout-config
    position absolute
    top 0px
    right 0px
    overflow auto
    padding 15px 20px 20px
    width 320px
    height 100%
    background #fff
    transition all 1s cubic-bezier(0.645, 0.045, 0.355, 1)
    .config-header
      font-weight 400
      font-size 14px
      line-height 25px
    .config-middle
      width 100%
      .form
        width 100%
        .form-item
          margin-top 20px
          width 100%
          .form-item-label
            margin-bottom 8px
            color #808080
            font-weight 400
            font-size 14px
            line-height 20px
          .wechat-selector
            width 100%
          .condition-selector
            width 100%
            margin-bottom 20px
          .group
            display flex
            border 1px rgba(33, 34, 35, 0.1) solid
            border-radius 3px
            .group-item
              flex 1
              border-left 1px rgba(33, 34, 35, 0.1) solid
              text-align center
              line-height 32px
              cursor pointer
              &:first-child
                border none
            .group-item-active
              background #20a0ff
              color #fff
          .grid-item
            margin-bottom 10px
            height 32px
            border 1px rgba(33, 34, 35, 0.1) solid
            text-align center
            line-height 32px
            cursor pointer
          .grid-item-active
            background #20a0ff
            color #fff
          input
            padding 8px 10px
            width 100%
            outline-style none
            border 1px rgba(33, 34, 35, 0.1) solid
            border-radius 3px
            line-height 22px
            &:focus
              border 1px #20a0ff solid
          .option-box
            position relative
            margin-bottom 10px
            padding 10px
            border 1px solid #bfcbd9
            border-radius 3px
            background #fff
            &:hover
              border 1px solid #f9ca48
              cursor pointer
              .option-icon
                display inline
            .option-item
              display flex
              align-items center
              .option-item-label
                width 64px
                color #808080
          .option-icon
            display none
            padding 2px
            width 20px
            height 20px
            border-radius 0px
            background #f9ca48
            color #fff
            line-height 16px
          .add-action
            display flex
            justify-content center
            align-items center
            width 100%
            border 1px dashed #bfcbd9
            color #888
            font-size 12px
            line-height 32px
            &:hover
              border 1px dashed #20A0FF
              color #20A0FF
              cursor pointer

.delete
  position absolute
  top -1px
  right -1px

.copy
  position absolute
  right -1px
  bottom -1px

.default-form
  padding 20px
</style>
