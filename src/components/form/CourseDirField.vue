<script lang="ts">
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import StoreField from './StoreField.vue';
import { netdiskTeacherCourseDirsStore } from '@/store/modules/netdisk/teacher/course_dirs.store';

@Component({
  components: {
    StoreField,
  },
})
export default class CourseDirField extends Vue {
  @Model('change') value!: number | number[];

  @Prop({ type: Boolean, default: false }) disabled!: boolean;
  @Prop({ type: Boolean, default: false }) multiple!: boolean;

  get store() {
    return netdiskTeacherCourseDirsStore;
  }

  get config() {
    return {
      initParams: {
        params: { q: {} },
      },
      tableColumns: [
        { title: '名称', dataIndex: 'name', type: 'string', search: true },
        { title: '目录代码', dataIndex: 'code', type: 'string', search: true },
        { title: '所属学院', dataIndex: 'department_name', type: 'string', search: true },
        { title: '课程负责人', dataIndex: 'teacher_name', type: 'string', search: true },
      ],
    };
  }

  get localValue() {
    return this.value;
  }

  set localValue(val: number | number[]) {
    this.$emit('change', val);
  }
}
</script>

<template lang="pug">
.source-dir-field
  StoreField(
    v-model='localValue',
    recordName='课程',
    :store='store',
    :storeConfig='config',
    :disabled='disabled',
    :multiple='multiple',
  )
</template>

<style lang="stylus" scoped></style>
