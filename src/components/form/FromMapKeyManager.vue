<script lang="ts">
import { IFormTemplateItem } from '@/interfaces/formTemplate.interface';
import { IWorkflow } from '@/models/bpm/workflow';
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import widgets from './widgets';

@Component({
  components: {},
})
export default class FormMapKeyManager extends Vue {
  @Prop({ type: Object, required: true }) formData!: IWorkflow;

  templates: IFormTemplateItem[] = widgets;

  // @Watch('formData', { deep: true, immediate: true })
  // handleFormDataChange() {
  //   console.log(this.dynamicFields);
  //   console.log('dynamicFormFields', this.dynamicFormFields);
  //   console.log('dynamicPlaceFields', this.dynamicPlaceFields);
  //   console.log(this.formData);
  // }
  // mounted() {}

  get dynamicFormFields() {
    return this.formData.form?.fields?.filter(field => typeof field.map_key === 'string') || [];
  }

  get dynamicPlaceFields() {
    return (
      this.formData.core?.places
        ?.map(place => {
          return place.place_form?.fields?.filter(field => typeof field.map_key === 'string') || [];
        })
        .reduce((out, ary) => out.concat(ary)) || []
    );
  }

  get dynamicFields() {
    // NOTE: 去重保留第一个。place map_key 被覆盖
    const fields = [...this.dynamicFormFields, ...this.dynamicPlaceFields];
    // 去重
    return fields.filter(field => {
      return fields.findIndex(item => item.map_key === field.map_key) !== -1;
    });
  }
}
</script>

<template lang="pug">
.form-map-key-manager
  .layout-sider
    .sider-header
      span 表单控件
    .sider-main
      .widgets
        .column(
          v-for="(widget, index) in templates"
          :key="index"
          @click="selectWidget(widget)"
        )
          .form-item
            span {{ widget.name }}
</template>

<style lang="stylus" scoped>
.form-map-key-manager
  position relative
  overflow hidden
  width 100%
  height 100%
  .layout-sider
    position absolute
    top 0px
    left 0px
    overflow auto
    padding 0px 14px 14px
    width 300px
    height 100%
    background #fff
    .sider-header
      padding 15px 6px 5px
      font-weight 400
      font-size 14px
      line-height 26px
    .sider-main
      padding 6px 0px
      width 100%
      font-size 14px
      .widgets
        display flex
        flex-wrap wrap
        .column
          width 50%
          .form-item
            margin 6px
            padding 6px
            border 1px #e6e6e6 solid
            border-radius 4px
            background #fff
            color #808080
            text-align center
            line-height 20px
            cursor move
            &:hover
              border 1px #20a0ff dashed
              color #20a0ff
</style>
