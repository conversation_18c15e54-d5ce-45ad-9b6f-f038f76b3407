<script lang="ts">
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import { adminFormTemplateStore, teacherFormTemplateStore } from '@/store/modules/form_template.store';
import { typeOptions } from '@/models/forms/form_template';

@Component({
  components: {},
})
export default class FormTemplateSelector extends Vue {
  @Model('change', { type: Boolean, default: false }) value!: boolean;

  query: IObject = {};

  get visible() {
    return this.value;
  }
  set visible(val: boolean) {
    this.$emit('change', val);
  }
  get store() {
    return this.$utils.hasPermission('forms', 'admin') ? adminFormTemplateStore : teacherFormTemplateStore;
  }
  get typeMap() {
    return this.$utils.objectify(typeOptions, 'value');
  }

  @Watch('value')
  onOpen() {
    if (this.value) {
      this.fetchRecords(1);
    }
  }
  fetchRecords(page: number = 1, query: object = {}, pageSize: number = 15) {
    this.store.fetch({
      page,
      per_page: pageSize,
      q: {
        ...this.query,
        ...query,
      },
    });
  }
  select(o: object) {
    this.$emit('select', o);
    this.visible = false;
  }
}
</script>

<template lang="pug">
MainModal(v-model="visible")
  .templates-header(slot="title")
    strong.title 选择表单模板
    Searcher(
      v-model="query"
      :variables="['name']"
      tips="搜索表单"
      placeholder="搜索表单名称"
      @change="fetchRecords(1)")
  .templates-content
    AdminTable(
      :store="store"
      rowClassName="click-row"
      @rowClick="select"
      @change="fetchRecords")
        a-table-column(title="名称" dataIndex="name")
        a-table-column(title="类型" dataIndex="type")
          template(slot-scope="type")
            | {{ typeMap[type || ''].label }}
        a-table-column(title="创建者" dataIndex="creator_name")
        a-table-column(title="创建时间" dataIndex="created_at")
          template(slot-scope="created_at")
            | {{ created_at | format }}
        a-table-column(title="更新时间" dataIndex="updated_at")
          template(slot-scope="updated_at")
            | {{ updated_at | format }}
</template>

<style lang="stylus" scoped>
.templates-header
  display flex
  align-items center
  justify-content space-between
  padding-right 30px
  height 32px
  margin -4px 0
.templates-content
  padding 0 20px
</style>
