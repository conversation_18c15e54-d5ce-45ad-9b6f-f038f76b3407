<script lang="ts">
import { Component, Vue, Prop, Model } from 'vue-property-decorator';

@Component
export default class TagField extends Vue {
  @Model('change', { type: Array }) value!: string[];
  @Prop({ type: Boolean, default: true }) disabled!: boolean;

  inputValue: string = '';
  inputVisible: boolean = false;

  handleClose(index: number) {
    this.value.splice(index, 1);
    this.$emit('change', this.value);
  }

  handleInputConfirm() {
    const inputValue = this.inputValue;
    let tags = this.value || [];
    if (inputValue && tags.indexOf(inputValue) === -1) {
      tags = [...tags, inputValue];
    }
    this.inputVisible = false;
    this.inputValue = '';
    this.$emit('change', tags);
  }

  handleInputChange(e: any) {
    this.inputValue = e.target.value;
  }

  showInput() {
    this.inputVisible = true;
  }
}
</script>

<template lang="pug">
.tag-fields
  template(v-for="(tag, index) in value")
    a-tag(:key="tag" :closable="true" @close="() => handleClose(index)")
      | {{ tag }}
  a-input(
    v-if="inputVisible"
    type="text"
    size="small"
    :style="{ width: '78px' }"
    :value="inputValue"
    @change="handleInputChange"
    @blur="handleInputConfirm"
    @keyup.enter="handleInputConfirm"
  )
  a-tag(v-else style="background: #fff; borderStyle: dashed;" @click="showInput")
    a-icon(type="plus") 
    span 新建标签
</template>

<style lang="stylus" scoped></style>
