<script lang="ts">
/**
 * 人员选择器
 * 双向绑定 ids 数组
 */
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import UserSelectorDialog from '@/components/hr/UserSelectorDialog.vue';
import { Teacher } from '@/models/teacher';

@Component({
  components: {
    UserSelectorDialog,
  },
})
export default class UserManyField extends Vue {
  radioStyle: IObject = {
    display: 'block',
    height: '32px',
    lineHeight: '32px',
  };
  // member selector
  visible: boolean = false;
  selectedMembers: any[] = [];
  selectedMemberIds: number[] = [];

  @Model('change', { required: true }) readonly value!: number | number[]; // 多选：ids， 单选：id
  @Prop({ type: Array, default: () => [] }) readonly defaultOperators!: any[]; // 可选的审批人列表
  @Prop({ type: Boolean, default: false }) readonly disabled!: boolean;
  @Prop({ type: Boolean, default: false }) readonly multiple!: boolean;

  get role() {
    return this.$store.state.authRole;
  }

  @Watch('value', { immediate: true })
  onValueChange() {
    if (Array.isArray(this.value)) {
      this.selectedMemberIds = this.value.filter(i => i);
    } else if (Number.isInteger(this.value) || typeof this.value === 'string') {
      this.selectedMemberIds = [this.value];
    } else {
      this.selectedMemberIds = [];
    }
    this.fetchMembers();
  }
  async fetchMembers() {
    if (JSON.stringify(this.selectedMemberIds.sort()) === JSON.stringify(this.selectedMembers.map(m => m.id).sort()))
      return;
    const { data } = await new Teacher(this.role).index({
      page: 1,
      per_page: this.selectedMemberIds.length,
      q: {
        id_in: this.selectedMemberIds,
      },
    });
    this.selectedMembers = data.teachers;
  }
  onRadioChange(e: any) {
    const member = e.target.value;
    this.chooseMember([member.id], [member]);
  }
  openSelector() {
    this.visible = true;
  }
  chooseMember(memberIds: number[], members: any[]) {
    if (this.disabled) {
      return;
    }
    this.selectedMembers = members;
    this.selectedMemberIds = memberIds;
    this.syncValue();
  }
  removeMember(index: number) {
    if (this.disabled) {
      return;
    }
    this.selectedMembers.splice(index, 1);
    this.syncValue();
  }
  syncValue() {
    if (this.multiple) {
      this.$emit('change', this.selectedMemberIds);
    } else {
      this.$emit('change', this.selectedMemberIds[0]);
    }
  }
}
</script>

<template lang="pug">
.operator-select
  a-radio-group.radio-group(
    v-if="defaultOperators.length > 0"
    :value="selectedMemberIds[0]"
    @change="onRadioChange")
    a-radio(
      v-for="operator in defaultOperators"
      :key="operator.id"
      :style="radioStyle"
      :value="operator")
      | {{ operator.name }}

  .contact-member(v-else)
    a-tag.tag(
      v-for="(member, index) in selectedMembers"
      color="blue"
      :key="member.id"
      :closable="!disabled"
      @close="removeMember(index)")
      | {{ member.name }}
    a-button(
      v-if="!disabled"
      icon="edit"
      :disabled="disabled"
      @click="openSelector")
      | {{ selectedMembers.length > 0 ? '修改' : '选择' }}

  UserSelectorDialog(
    v-model="visible"
    title="人员选择"
    :multiple="multiple"
    :userIds="selectedMemberIds"
    @change="chooseMember")
</template>

<style lang="stylus" scoped>
.operator-select
  .contact-member
    display flex
    align-items center
    padding 4px 0
    flex-wrap wrap
    .tag
      padding 5px 10px
      .anticon-close
        margin-left 5px
        line-height 18px
  .radio-group
    padding 10px 14px
    width 100%
    border 1px solid #ddd
    border-radius 4px
    background #fff
</style>
