import { IFormTemplateItem } from '@/interfaces/formTemplate.interface';

const templateItems: IFormTemplateItem[] = [
  {
    name: '单行输入',
    layout: {
      component: 'input',
      placeholder: '请输入',
      required: true,
      span: 24,
    },
    model: {
      attr_type: 'string',
    },
  },
  {
    name: '多行输入',
    layout: {
      component: 'textarea',
      placeholder: '请输入',
      required: true,
      span: 24,
    },
    model: {
      attr_type: 'string',
    },
  },
  {
    name: '数字输入框',
    layout: {
      component: 'input',
      placeholder: '请输入数字',
      required: true,
      span: 24,
    },
    model: {
      attr_type: 'number',
    },
  },
  {
    name: '货币金额输入框',
    layout: {
      component: 'currency',
      placeholder: '请输入金额',
      required: true,
      span: 24,
    },
    model: {
      attr_type: 'number',
    },
  },
  {
    name: '单选框',
    layout: {
      component: 'radio',
      placeholder: '请选择',
      required: true,
      span: 24,
      options: [{ label: '选项1' }, { label: '选项2' }, { label: '选项3' }],
    },
    model: {
      attr_type: 'string',
    },
  },
  {
    name: '多选框',
    layout: {
      component: 'checkbox',
      placeholder: '请选择',
      required: true,
      span: 24,
      options: [{ label: '选项1' }, { label: '选项2' }, { label: '选项3' }],
    },
    model: {
      attr_type: 'array',
    },
  },
  {
    name: '下拉选择',
    layout: {
      component: 'select',
      placeholder: '请选择',
      required: true,
      span: 24,
      options: [{ label: '选项1' }, { label: '选项2' }, { label: '选项3' }],
    },
    model: {
      attr_type: 'string',
    },
  },
  {
    name: '日期',
    layout: {
      component: 'date',
      placeholder: '请选择',
      required: true,
      span: 24,
    },
    model: {
      attr_type: 'date',
    },
  },
  {
    name: '日期时间',
    layout: {
      component: 'datetime',
      placeholder: '请选择',
      required: true,
      span: 24,
    },
    model: {
      attr_type: 'datetime',
    },
  },
  {
    name: '图片',
    layout: {
      component: 'image',
      placeholder: '请选择',
      required: true,
      multiple: true,
      span: 24,
    },
    model: {
      attr_type: 'array',
    },
  },
  {
    name: '文件',
    layout: {
      component: 'file',
      placeholder: '请选择',
      required: true,
      multiple: true,
      span: 24,
    },
    model: {
      attr_type: 'array',
    },
  },
  // {
  //   name: '组织成员',
  //   layout: {
  //     component: 'contacts',
  //     placeholder: '请选择组织成员',
  //     required: true,
  //     multiple: true,
  //     span: 24,
  //   },
  //   model: {
  //     attr_type: 'array',
  //   },
  // },
  {
    name: '富文本',
    layout: {
      component: 'rich_text',
      placeholder: '请输入',
      required: true,
    },
    model: {
      attr_type: 'string',
    },
  },
  {
    name: '表格记录',
    layout: {
      component: 'table',
      span: 24,
      attrs: [],
    },
    model: {
      attr_type: 'array',
    },
  },
  {
    name: '选择公众号文章',
    layout: {
      component: 'wechat_articles',
      placeholder: '请选择文章',
      required: true,
      span: 24,
      wechat: {
        name: '',
        desc: '',
        appid: '',
      },
    },
    model: {
      attr_type: 'object',
    },
  },
  {
    name: '分割线',
    layout: {
      component: 'hr',
      span: 24,
    },
    model: {
      attr_type: 'string',
    },
  },
  {
    name: '分割线',
    layout: {
      component: 'hr',
      span: 24,
    },
    model: {
      attr_type: 'string',
    },
  },
  {
    name: '标签',
    accessibility: 'readonly',
    layout: {
      component: 'label',
      span: 3,
      rowspan: 1,
      textAlign: 'left',
    },
    model: {
      attr_type: 'string',
    },
  },
  {
    name: '可重复块',
    layout: {
      component: 'list_container',
      span: 3,
    },
    model: {
      attr_type: 'string',
    },
  },
  {
    name: '条件块',
    accessibility: 'hidden',
    layout: {
      component: 'if_container',
      span: 3,
    },
    model: {
      attr_type: 'string',
    },
  },
  {
    name: '专业',
    layout: {
      component: 'major',
      multiple: true,
      required: true,
    },
    model: {
      attr_type: 'array',
    },
  },
  {
    name: '二级学院',
    layout: {
      component: 'level_two_college',
      multiple: true,
      required: true,
    },
    model: {
      attr_type: 'array',
    },
  },
  {
    name: '教师',
    layout: {
      component: 'teacher',
      multiple: true,
      required: true,
    },
    model: {
      attr_type: 'array',
    },
  },
  {
    name: '学生',
    layout: {
      component: 'student',
      multiple: true,
      required: true,
    },
    model: {
      attr_type: 'array',
    },
  },
  {
    name: '课程目录',
    layout: {
      component: 'course_dir',
      multiple: true,
      required: true,
    },
    model: {
      attr_type: 'array',
    },
  },
  {
    name: '部门',
    layout: {
      component: 'department_new',
      multiple: true,
      required: true,
    },
    model: {
      attr_type: 'array',
    },
  },
  {
    name: '联级部门',
    layout: {
      component: 'department_new_nested',
      multiple: true,
      required: true,
    },
    model: {
      attr_type: 'array',
    },
  },
  {
    name: '预算调整（调增）',
    layout: {
      component: 'budget_adjust',
    },
    model: {
      attr_type: 'array',
    },
  },
  {
    name: '预算调整（调减）',
    layout: {
      component: 'budget_adjust_negative',
    },
    model: {
      attr_type: 'array',
    },
  },
  {
    name: '预算新增',
    layout: {
      component: 'budget_create',
    },
    model: {
      attr_type: 'array',
    },
  },
  {
    name: '预算请购（新建）',
    layout: {
      component: 'budget_lock_create',
    },
    model: {
      attr_type: 'array',
    },
  },
  {
    name: '预算请购（选择）',
    layout: {
      component: 'budget_lock_amount',
    },
    model: {
      attr_type: 'array',
    },
  },
  {
    name: '资金卡（管理员选负责人）',
    layout: {
      component: 'finance_project',
      multiple: true,
      required: true,
    },
    model: {
      attr_type: 'array',
    },
  },
  {
    name: '资金卡（管理员所有）',
    layout: {
      component: 'finance_project_admin_all',
      multiple: true,
      required: true,
    },
    model: {
      attr_type: 'array',
    },
  },
  {
    name: '资金卡（资金卡负责人）',
    layout: {
      component: 'finance_project_own',
      multiple: true,
      required: true,
    },
    model: {
      attr_type: 'array',
    },
  },
  {
    name: '资金卡（资金卡执行人）',
    layout: {
      component: 'finance_project_execute',
      multiple: true,
      required: true,
    },
    model: {
      attr_type: 'array',
    },
  },
];

export const componentNameMap: IObject = templateItems.reduce(
  (obj, w) => ({
    ...obj,
    [w.layout.component]: w.name,
  }),
  {},
);

export default templateItems;
