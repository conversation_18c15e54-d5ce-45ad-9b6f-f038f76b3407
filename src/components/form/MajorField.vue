<script lang="ts">
import { IModel, IModelConfig } from '@/lib/ActiveModel';
import { resTeacherMajorStore } from '@/store/modules/res/teacher/major.store';
import { Component, Vue, Prop, Model } from 'vue-property-decorator';
import StoreField from './StoreField.vue';

@Component({
  components: {
    StoreField,
  },
})
export default class MajorField extends Vue {
  @Model('change') value!: number | number[];

  @Prop({ type: Boolean, default: false }) disabled!: boolean;
  @Prop({ type: Boolean, default: false }) multiple!: boolean;

  get store() {
    return resTeacherMajorStore;
  }

  get config() {
    return {
      initParams: {
        params: { q: {} },
      },
      tableColumns: [
        { title: '名称', dataIndex: 'name', type: 'string', search: true },
        { title: '代号', dataIndex: 'code', type: 'string', search: true },
        { title: '学院', dataIndex: 'college_name', type: 'string', search: true },
      ],
    };
  }

  get localValue() {
    return this.value;
  }

  set localValue(val: number | number[]) {
    this.$emit('change', val);
  }
}
</script>

<template lang="pug">
.duty-field
  StoreField(
    v-model='localValue',
    recordName='专业',
    :store='store',
    :storeConfig='config',
    :disabled='disabled',
    :multiple='multiple',
  )
</template>

<style lang="stylus" scoped>
.department-field
  width 400px
</style>
