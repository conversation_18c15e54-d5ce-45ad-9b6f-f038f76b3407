<script lang="ts">
/**
 * 人员选择器
 * 双向绑定用户对象数组
 */
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import UserSelectorDialog from '@/components/hr/UserSelectorDialog.vue';

@Component({
  components: {
    UserSelectorDialog,
  },
})
export default class UserField extends Vue {
  radioStyle: IObject = {
    display: 'block',
    height: '32px',
    lineHeight: '32px',
  };
  // member selector
  visible: boolean = false;
  selectedUsers: any[] = [];

  @Model('change', {
    required: true,
    validator(v) {
      return Array.isArray(v);
    },
  })
  readonly value!: IObject[]; // user objects
  @Prop({ type: Array, default: () => [] }) readonly defaultOperators!: any[]; // 可选的审批人列表
  @Prop({ type: Boolean, default: false }) readonly disabled!: boolean;
  @Prop({ type: Boolean, default: false }) readonly multiple!: boolean;

  @Watch('value', { immediate: true })
  onValueChange() {
    this.selectedUsers = Array.isArray(this.value) ? this.value.concat() : [];
  }

  onRadioChange(e: any) {
    this.$emit('change', [e.target.value]);
  }
  openSelector() {
    this.visible = true;
  }
  chooseUsers(userIds: number[], members: any[]) {
    if (this.disabled) {
      return;
    }
    this.selectedUsers = members;
    this.syncValue();
  }
  removeMember(index: number) {
    if (this.disabled) {
      return;
    }
    this.selectedUsers.splice(index, 1);
    this.syncValue();
  }
  syncValue() {
    this.$emit('change', this.selectedUsers);
  }
}
</script>

<template lang="pug">
.operator-select
  a-radio-group.radio-group(
    v-if="defaultOperators.length > 0"
    :value="value[0]"
    @change="onRadioChange")
    a-radio(
      v-for="operator in defaultOperators"
      :key="operator.id"
      :style="radioStyle"
      :value="operator")
      | {{ operator.name }}

  .contact-member(v-else)
    a-tag.tag(
      v-for="(member, index) in selectedUsers"
      color="blue"
      :key="member.key || member.id"
      :closable="!disabled"
      @close="removeMember(index)")
      | {{ member.name }}
    a-button(
      v-if="!disabled"
      icon="edit"
      :disabled="disabled"
      @click="openSelector")
      | {{ selectedUsers.length > 0 ? '修改' : '选择' }}

  UserSelectorDialog(
    title="人员选择"
    v-model="visible"
    :multiple="multiple"
    :hasRoleType="false"
    :users="selectedUsers"
    @change="chooseUsers")
</template>

<style lang="stylus" scoped>
.operator-select
  .contact-member
    display flex
    align-items center
    padding 4px 0
    flex-wrap wrap
    .tag
      padding 5px 10px
      .anticon-close
        margin-left 5px
        line-height 18px
  .radio-group
    padding 10px 14px
    width 100%
    border 1px solid #ddd
    border-radius 4px
    background #fff
</style>
