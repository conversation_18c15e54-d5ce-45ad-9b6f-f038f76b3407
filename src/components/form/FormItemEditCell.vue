<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { IFormTemplateItem } from '@/interfaces/formTemplate.interface';

@Component({
  components: {},
})
export default class FormItemEditCell extends Vue {
  @Prop({ type: <PERSON><PERSON><PERSON> }) active!: boolean;
  @Prop({ type: Object }) formItem!: IFormTemplateItem;

  padding: number = 4;

  get style() {
    const span = this.formItem.layout.rowspan || 1;
    const height = span * 44 + this.padding * 2 * (span - 1);
    return {
      'text-align': this.formItem.layout.textAlign || 'left',
      height: `${height}px`,
      background: this.formItem.layout.component === 'label' ? '#f4f4f4' : '#ffffff',
    };
  }

  onClick() {
    this.$emit('click', this.formItem);
  }

  onRemoveFormItem() {
    this.$emit('remove', this.formItem);
  }

  onCopyFormItem() {
    this.$emit('copy', this.formItem);
  }
}
</script>

<template lang="pug">
.form-widget-item(:style="{ padding: `${padding}px` }")
  .widget-item(:style="style" :class="{'widget-active': active}" @click="onClick")
    .pre-label {{ formItem.name }}
    .placeholder(v-if="formItem.layout.component === 'file' || formItem.layout.component === 'video'")
      a-icon(type="camera", v-if="formItem.layout.component === 'image'")
      a-icon(type="paper-clip", v-else-if="formItem.layout.component === 'file'")
    .placeholder(v-else)
      span {{ formItem.layout.placeholder }}
      a-icon.icon(type="right", v-if="formItem.layout.right")
    .form-item-action.delete(@click.stop="onRemoveFormItem")
      a-icon(type="close")
    .form-item-action.copy(@click.stop="onCopyFormItem")
      a-icon(type="copy" style="color: #fff")
</template>

<style lang="stylus" scoped>
.form-widget-item
  padding 4px
  .widget-item
    position relative
    display flex
    justify-content space-between
    align-items center
    padding 0px 10px
    border 1px #fff solid
    background #fff
    overflow hidden
    &:hover
      border 1px #20A0FF dashed
      cursor pointer
      .form-item-action
        display block
    .pre-label
      color #000
      white-space pre-wrap
      overflow hidden
      text-overflow ellipsis
      width 100%
    .placeholder
      color #888
      flex-shrink 0
      margin-left 8px
    .form-item-action
      z-index 99
      display none
      padding 3px
      border-radius 0px
      background #20A0FF
      color #fff
      font-size 10px
      line-height 10px
      &:hover
        opacity 0.8
    .delete
      position absolute
      top -1px
      right -1px
    .copy
      position absolute
      right -1px
      bottom -1px

  .widget-active
    border 1px #20A0FF solid
    &:hover
      border 1px #20A0FF solid
</style>
