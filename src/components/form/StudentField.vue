<script lang="ts">
import { IModel, IModelConfig } from '@/lib/ActiveModel';
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import StoreField from './StoreField.vue';
import { resTeacherStudentStore } from '@/store/modules/res/teacher/student.store';
import TaImport from '../global/TaImport.vue';
import { TaIndexImportHeader } from '../global/TaIndex';

@Component({
  components: {
    StoreField,
  },
})
export default class StudentField extends Vue {
  @Model('change') value!: number | number[];

  @Prop({ type: Boolean, default: false }) disabled!: boolean;
  @Prop({ type: Boolean, default: false }) multiple!: boolean;

  get store() {
    return resTeacherStudentStore;
  }

  get config() {
    return {
      initParams: {
        params: { q: {} },
      },
      tableColumns: [
        { title: '姓名', dataIndex: 'name', type: 'string', search: true },
        { title: '学号', dataIndex: 'code', type: 'string', search: true },
        { title: '学院', dataIndex: 'department_name', type: 'string', search: true },
        { title: '专业', dataIndex: 'major_name', type: 'string', search: true },
        { title: '班级', dataIndex: 'adminclass_name', type: 'string', search: true },
      ],
    };
  }

  get localValue() {
    return this.value;
  }

  set localValue(val: number | number[]) {
    this.$emit('change', val);
  }

  get importHeaders() {
    return [
      {
        name: '学号',
        key: 'code',
        primary_key: true,
      },
    ];
  }

  onImport() {
    (this.$refs.importComp as TaImport).onClickFileInput();
  }

  onImportConfirm(options: { resultHeaders: TaIndexImportHeader[]; uid: string; resetImport: () => void }) {
    this.store
      .sendCollectionAction({
        action: 'find_by_file',
        config: {
          data: {
            headers: options.resultHeaders,
            uid: options.uid,
          },
        },
      })
      .then(({ data }) => {
        this.localValue = data.students.map((record: IObject) => record.id);
        options.resetImport();
      });
  }
}
</script>

<template lang="pug">
.duty-field
  StoreField(
    v-model='localValue',
    recordName='学生',
    :store='store',
    :storeConfig='config',
    :disabled='disabled',
    :multiple='multiple',
  )
    template(#modal-header)
      .modal-header
        .title 选择
        TextButton.button(icon='import', @click='onImport') Excel 导入
  TaImport.hidden(
    ref='importComp'
    :store='store',
    :headers='importHeaders',
    :onConfirmFunc='onImportConfirm',
  )
</template>

<style lang="stylus" scoped>
.modal-header
  display flex
  .title
    padding 10px 30px 10px 10px
.hidden
  display none
</style>
