<script lang="ts">
import { Component, Vue, Prop, Model } from 'vue-property-decorator';
import StoreField from './StoreField.vue';
/* eslint-disable prettier/prettier */
import {
  teachingTeacherCurrentSemesterCourseCatalogsStore
} from '@/store/modules/teaching/teacher/current_semester/course_catalogs.store';

@Component({
  components: {
    StoreField,
  },
})
export default class CourseCatalogField extends Vue {
  @Model('change') value!: number[];

  @Prop({ type: Number, required: true }) courseSetId!: boolean;
  @Prop({ type: Boolean, default: false }) disabled!: boolean;
  @Prop({ type: Boolean, default: false }) multiple!: boolean;

  get store() {
    return teachingTeacherCurrentSemesterCourseCatalogsStore;
  }

  get config() {
    return {
      tagKey: 'title',
      initParams: {
        parents: [{ type: 'course_sets', id: this.courseSetId }],
        params: { q: {} },
      },
      tableColumns: [
        { title: '名称', dataIndex: 'title', type: 'string' },
        { title: '修改权限', dataIndex: 'edit_permit', type: 'string', customRender: this.getPermitString },
        { title: '访问权限', dataIndex: 'view_permit', type: 'string', customRender: this.getPermitString },
        { title: '创建者', dataIndex: 'teacher_name', type: 'string', customRender: this.getTeahcerName },
        { title: '课程名称', dataIndex: 'course_set_name', type: 'string' },
        { title: '课程代码', dataIndex: 'course_set_code', type: 'string' },
      ],
    };
  }

  get localValue() {
    return this.value;
  }

  set localValue(val: number | number[]) {
    this.$emit('change', val);
  }

  getPermitString(permit: 'public' | 'private' | 'part') {
    return { public: '公有', private: '私有', part: '配置' }[permit]
  }

  getTeahcerName(name: string | null) {
    return name || '系统'
  }
}
</script>

<template lang="pug">
.duty-field
  StoreField(
    v-model='localValue',
    recordName='资源目录',
    :store='store',
    :storeConfig='config',
    :disabled='disabled',
    :multiple='multiple',
  )
</template>

<style lang="stylus" scoped>
.department-field
  width 400px
</style>
