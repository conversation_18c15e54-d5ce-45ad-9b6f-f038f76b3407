<script lang="ts">
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import StoreField from './StoreField.vue';
import { financeTeacherExecuteBudgetLocksStore } from '@/store/modules/finance/teacher/execute/budget_locks.store';
import { IFinanceBudgetLock } from '@/types/model';

@Component({
  components: {
    StoreField,
  },
})
export default class BudgetLockAmountField extends Vue {
  @Model('change') value!: { id: number; budget_id: number; amount: number }[];
  @Prop({ type: [Number, String], required: true }) projectId!: number | string;
  @Prop({ type: Boolean, default: false }) disabled!: boolean;

  budgetLocks: IFinanceBudgetLock[] = [];
  budgetLockMapping: IObject = {};

  get store() {
    return financeTeacherExecuteBudgetLocksStore;
  }

  get config() {
    return {
      initParams: {
        parents: [{ type: 'projects', id: this.projectId }],
        params: { q: { state_eq: 'doing' } },
      },
      tableColumns: [
        { title: '二级内容', dataIndex: 'budget.catalog_name', type: 'string', search: true },
        { title: '三级内容', dataIndex: 'budget.name', type: 'string', search: true },
        { title: '支付方式', dataIndex: 'budget.payment_way', type: 'string', search: true },
        { title: '科目', dataIndex: 'budget.subject_name', type: 'string', search: true },
        {
          title: '来源',
          dataIndex: 'budget.origin_name',
          customRender: (val: string) => val || '资金池统筹',
          type: 'string',
          search: true,
        },
        { title: '金额', dataIndex: 'amount', type: 'number' },
      ],
    };
  }

  get existBudgetLockIds() {
    return (this.value || []).map(item => item.id);
  }

  get budgetLocksWithAmount() {
    return this.budgetLocks.map(budgetLock => ({
      ...budgetLock,
      amount: this.budgetLockMapping[`${budgetLock.id}`],
    }));
  }

  get returnValue() {
    return this.budgetLocksWithAmount.map(budgetLock => ({
      id: budgetLock.id,
      budget_id: budgetLock.budget_id,
      amount: budgetLock.amount || 0,
      payment_way: budgetLock.budget.payment_way,
    }));
  }

  get jsonConfig() {
    return {
      include: [
        {
          budget: {
            methods: ['catalog_name', 'subject_name', 'origin_name'],
          },
        },
      ],
      methods: ['amount', 'processing_payment_amount', 'locking_amount', 'completed_payment_amount'],
    };
  }

  mounted() {
    this.budgetLockMapping = this.value.reduce((out, item) => {
      out[`${item.id}`] = Math.abs(item.amount);
      return out;
    }, {} as IObject);
  }

  @Watch('returnValue', { deep: true })
  onReturnValueChange() {
    this.$emit('change', this.returnValue);
  }

  getBalanceAmount(record: IFinanceBudgetLock) {
    return Number(record.locking_amount);
  }

  syncBudgetLocks(val: IFinanceBudgetLock[]) {
    this.budgetLocks = val;
  }
}
</script>

<template lang="pug">
.budget-adjust-field
  StoreField(
    :value='existBudgetLockIds',
    recordName='预算请购',
    :store='store',
    :storeConfig='config',
    :disabled='disabled',
    :multiple='true',
    :jsonConfig='jsonConfig',
    @selectedRecordsChange='syncBudgetLocks'
  )
    template(#tags)
      .empty
  a-table(:dataSource='budgetLocksWithAmount', :pagination='{ hideOnSinglePage: true }', :scroll='{ x: true }')
    a-table-column(title='报销金额', :width='150')
      template(slot-scope='value, record')
        .flex
          .positive +
          a-input-number(
            v-model='budgetLockMapping[`${record.id}`]',
            :max='getBalanceAmount(record)',
            :min='0',
            :disabled='disabled'
          )
    a-table-column(title='二级内容', dataIndex='budget.catalog_name', :width='150')
    a-table-column(title='三级内容', dataIndex='budget.name', :width='130')
      template(slot-scope='name')
        a-tooltip(:title='name')
          .three-line(style='max-width: 130px')
            | {{ name }}
    a-table-column(title='科目', dataIndex='budget.subject_name', :width='110')
    a-table-column(title='来源', dataIndex='budget.origin_name', :width='110')
      template(slot-scope='origin_name')
        | {{ origin_name || "资金池统筹" }}
    a-table-column(title='金额详情', :width='180')
      template(slot-scope='record')
        .info 可报销：{{ record.locking_amount | toCurrency }}
        .info 报销中：{{ record.processing_payment_amount | toCurrency }}
        .info 已报销：{{ record.completed_payment_amount | toCurrency }}
        .info 已锁定：{{ record.locking_amount | toCurrency }}
        .info(v-if='record.sub_project_amount > 0') 已分配：{{ record.sub_project_amount | toCurrency }}
</template>

<style lang="stylus" scoped>
.flex
  .positive, .negative
    margin-right 10px
    color red
</style>
