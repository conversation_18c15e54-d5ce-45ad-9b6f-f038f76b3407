<script lang="ts">
import budget, { IBudget } from '@/models/finance/budget';
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import StoreField from './StoreField.vue';
// import { financeTeacherBudgetsStore } from '@/store/modules/finance/teacher/budgets.store';
import { financeTeacherExecuteBudgetsStore } from '@/store/modules/finance/teacher/execute/budget.store';

@Component({
  components: {
    StoreField,
  },
})
export default class BudgetLockCreateField extends Vue {
  @Model('change') value!: { id: number; lock_amount: number }[];
  @Prop({ type: [Number, String], required: true }) projectId!: number | string;
  @Prop({ type: Boolean, default: false }) disabled!: boolean;

  budgets: IBudget[] = [];
  budgetsLockAmountMapping: IObject = {};

  get store() {
    return financeTeacherExecuteBudgetsStore;
  }

  get config() {
    return {
      initParams: {
        parents: [{ type: 'projects', id: this.projectId }],
        // params: { q: { asset: true } },
      },
      tableColumns: [
        { title: '二级内容', dataIndex: 'catalog_name', type: 'string', search: true },
        { title: '三级内容', dataIndex: 'name', type: 'string', search: true },
        { title: '科目', dataIndex: 'subject_name', type: 'string', search: true },
        {
          title: '来源',
          dataIndex: 'origin_name',
          customRender: (val: string) => val || '资金池统筹',
          type: 'string',
          search: true,
        },
        { title: '金额', dataIndex: 'amount', type: 'number' },
      ],
    };
  }

  get jsonConfig() {
    return {
      methods: [
        'catalog_name',
        'subject_name',
        'origin_name',
        'amount',
        'locking_amount',
        'processing_payment_amount',
        'completed_payment_amount',
      ],
    };
  }

  get existBudgetIds() {
    return (this.value || []).map(item => item.id);
  }

  get budgetsWithLockAmount() {
    return this.budgets.map(budget => ({
      ...budget,
      lock_amount: this.budgetsLockAmountMapping[`${budget.id}`],
    }));
  }

  get returnValue() {
    return this.budgetsWithLockAmount.map(budget => ({
      id: budget.id,
      lock_amount: budget.lock_amount || 0,
    }));
  }

  mounted() {
    this.budgetsLockAmountMapping = this.value.reduce((out, item) => {
      out[`${item.id}`] = Math.abs(item.lock_amount);
      return out;
    }, {} as IObject);
  }

  @Watch('returnValue', { deep: true })
  onReturnValueChange() {
    this.$emit('change', this.returnValue);
  }

  getBalanceAmount(record: IBudget) {
    return Number(record.amount) - Number(record.processing_payment_amount) - Number(record.completed_payment_amount);
  }

  getBalanceUnlockAmount(record: IBudget) {
    return Number(
      (
        Number(record.amount) -
        Number(record.processing_payment_amount) -
        Number(record.completed_payment_amount) -
        Number(record.locking_amount)
      ).toFixed(2),
    );
  }

  syncBudgets(val: IBudget[]) {
    this.budgets = val;
  }
}
</script>

<template lang="pug">
.budget-adjust-field
  StoreField(
    :value='existBudgetIds',
    recordName='预算',
    :store='store',
    :storeConfig='config',
    :disabled='disabled',
    :jsonConfig='jsonConfig',
    :multiple='true',
    @selectedRecordsChange='syncBudgets'
  )
    template(#tags)
      .empty
  a-table(:dataSource='budgetsWithLockAmount', :pagination='{ hideOnSinglePage: true }', :scroll='{ x: true }')
    a-table-column(title='请购金额', :width='150')
      template(slot-scope='value, record')
        .info(v-if='disabled') {{ budgetsLockAmountMapping[`${record.id}`] }}
        .flex(v-else)
          .positive +
          a-input-number(
            v-model='budgetsLockAmountMapping[`${record.id}`]',
            :max='getBalanceUnlockAmount(record)',
            :min='0',
            :disabled='disabled'
          )
    a-table-column(title='二级内容', dataIndex='catalog_name', :width='150')
    a-table-column(title='三级内容', dataIndex='name', :width='130')
      template(slot-scope='name')
        a-tooltip(:title='name')
          .three-line(style='max-width: 130px')
            | {{ name }}
    a-table-column(title='科目', dataIndex='subject_name', :width='110')
    a-table-column(title='来源', dataIndex='origin_name', :width='110')
      template(slot-scope='origin_name')
        | {{ origin_name || "资金池统筹" }}
    a-table-column(title='金额', dataIndex='amount', :width='130', align='right')
      template(slot-scope='amount')
        span {{ amount | toCurrency }}
    a-table-column(title='金额详情', :width='180')
      template(slot-scope='record')
        .info 未锁定：{{ getBalanceUnlockAmount(record) }}
        .info 锁定中：{{ record.locking_amount | toCurrency }}
        .info 报销中：{{ record.processing_payment_amount | toCurrency }}
        .info 已报销：{{ record.completed_payment_amount | toCurrency }}
        .info(v-if='record.sub_project_amount > 0') 已分配：{{ record.sub_project_amount | toCurrency }}
</template>

<style lang="stylus" scoped>
.flex
  .positive, .negative
    margin-right 10px
    color red
.notice
  color: red
</style>
