<script lang="ts">
import { IModel, IModelConfig } from '@/lib/ActiveModel';
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import StoreField from './StoreField.vue';
import { resAdminDutyStore } from '../../store/modules/res/admin/duty.store';
import DepartmentField from './DepartmentField.vue';

@Component({
  components: {
    StoreField,
    DepartmentField,
  },
})
export default class DutyField extends Vue {
  @Model('change') value!: number | number[];

  @Prop({ type: Boolean, default: false }) disabled!: boolean;
  @Prop({ type: Boolean, default: false }) multiple!: boolean;

  departmentIds: number[] = [];

  get store() {
    return resAdminDutyStore;
  }

  get config() {
    return {
      initParams: {
        params: { q: { department_id_in: this.departmentIds } },
      },
      tableColumns: [
        { title: '名称', dataIndex: 'name', type: 'string', search: true },
        { title: '部门', dataIndex: 'department_path_str', type: 'string' },
      ],
    };
  }

  get localValue() {
    return this.value;
  }

  set localValue(val: number | number[]) {
    this.$emit('change', val);
  }
}
</script>

<template lang="pug">
.duty-field
  StoreField(
    v-model='localValue',
    recordName='岗位',
    :store='store',
    :storeConfig='config',
    :disabled='disabled',
    :multiple='multiple',
    dataIndexKey='duties',
  )
    template(#actions)
      DepartmentField.department-field(
        v-model='departmentIds',
        :multiple='false'
        :nested='true',
      )
</template>

<style lang="stylus" scoped>
.department-field
  width 400px
</style>
