<script lang="ts">
/**
 * 表格记录编辑器
 */
import { Component, Vue, Prop, Model } from 'vue-property-decorator';
import requestGenerator from '@/utils/request';
import { AxiosInstance } from 'axios';
import FileSaver from 'file-saver';
import { IInstance } from '@/models/bpm/instance';
import { sum } from 'lodash';

const request = requestGenerator();

interface IAttr {
  key: string;
  name: string;
  value?: string;
  show?: boolean;
  layout?: {
    component: string;
    options: IObject[];
  };
}
interface IRecord {
  id?: number;
  index?: number;
  [key: string]: string | number | undefined;
}

@Component
export default class TableField extends Vue {
  @Model('change', { type: Array, default: () => [] }) value!: IRecord[];
  @Prop({ type: Array, default: () => [] }) attrs!: IAttr[]; // 列结构
  @Prop({ type: Boolean, default: false }) disabled!: boolean;
  @Prop({ type: String, default: 'default' }) mode!: 'print' | 'default';
  // 导出需要增加流水单号，传入 instance, 可选
  @Prop({ type: Object, default: () => ({}) }) private instance!: IInstance;

  request: AxiosInstance = requestGenerator();
  // records
  current: number = 1;
  pageSize: number = 10;
  // form
  visible: boolean = false;
  formData: IRecord = { id: 0 };
  // export import
  importLoading: boolean = false;
  exportLoading: boolean = false;

  get isValidValue() {
    return this.value instanceof Array;
  }
  get supportTypes() {
    return ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'].join(',');
  }
  get records() {
    return (this.value || []).map((o, i) => ({
      ...o,
      index: i + 1,
    }));
  }
  get showAttrs() {
    return (this.attrs || []).filter(o => o.show);
  }
  get template() {
    return this.showAttrs.map(({ name, key, value, layout }: IAttr) => ({
      key,
      name,
      accessibility: value ? 'readonly' : 'read_and_write',
      layout: {
        component: (layout && layout.component) || 'input',
        placeholder: '请输入' + name,
        span: 24,
        required: true,
        options: layout && layout.options,
      },
      model: {
        attr_type: 'string',
      },
    }));
  }
  get defaultObject() {
    return this.attrs.reduce((obj: IObject, o) => {
      if (o.value || !o.show) {
        obj[o.key] = o.value;
      }
      return obj;
    }, {});
  }
  get columns() {
    const left: IObject[] = [{ title: '序号', dataIndex: 'index', key: 'index', width: 100 }];
    const right: IObject[] = this.disabled
      ? []
      : [{ title: '', width: 100, fixed: 'right', center: true, scopedSlots: { customRender: 'actions' } }];
    return left
      .concat(
        this.showAttrs.map((attr: IAttr) => {
          const headerWidth = attr.name.trim().length * 16 + 32;
          return {
            title: attr.name,
            dataIndex: attr.key,
            key: attr.key,
            width: headerWidth > 80 ? headerWidth : 80,
          };
        }),
      )
      .concat(right);
  }
  get pagination() {
    return {
      total: this.records.length,
      pageSize: this.pageSize,
      current: this.current,
      hideOnSinglePage: false,
      showSizeChanger: true,
      showQuickJumper: false,
      pageSizeOptions: ['10', '20', '30', '40', '50'],
    };
  }
  get width() {
    return sum(this.columns.map(c => c.width));
  }

  mounted() {
    this.request = requestGenerator();
  }

  onNew() {
    this.formData = { ...this.defaultObject };
    this.visible = true;
  }
  onEdit(record: IRecord) {
    this.formData = { ...record };
    this.visible = true;
  }
  onDelete(record: IRecord) {
    const records = this.isValidValue ? this.value.concat() : [];
    const index = records.findIndex(o => o.id === record.id);
    records.splice(index, 1);
    this.$emit('change', records);
  }
  update(record: IRecord) {
    const records = this.isValidValue ? this.value.concat() : [];
    const index = records.findIndex(o => o.id === record.id);
    records.splice(index, 1, Object.assign(records[index], { ...this.defaultObject, ...record }));
    this.$emit('change', records);
    this.visible = false;
  }
  create(record: IRecord) {
    const records = this.isValidValue ? this.value.concat() : [];
    records.push({ ...this.defaultObject, ...record, id: Date.now(), index: records.length });
    this.$emit('change', records);
    this.visible = false;
  }
  // 导出模板
  async onExportTemplate() {
    try {
      this.exportLoading = true;
      const { data } = await this.request.post('/ex/teacher/exports/comm/exports', {
        attrs: this.attrs.filter(o => o.show),
        records: [],
      });
      FileSaver.saveAs(data.url, '导入模板.xlsx');
      this.exportLoading = false;
    } catch (error) {
      this.exportLoading = false;
    }
  }
  // 导出要加入流水单号
  async onExport() {
    try {
      this.exportLoading = true;
      const instanceSeqKey = 'col_seq1590314278718';
      const exportDateKey = 'col_date1590314278718';
      const extraAttrs = [
        { key: instanceSeqKey, name: '流水单号', value: '', show: false },
        { key: exportDateKey, name: '创建日期', value: '', show: false },
      ];
      const { data } = await this.request.post('/ex/teacher/exports/comm/exports', {
        attrs: this.attrs.concat(extraAttrs),
        records: this.records.map(r =>
          Object.assign(r, {
            [instanceSeqKey]: this.instance.seq,
            [exportDateKey]: this.$moment().format('YYYY-MM-DD'),
          }),
        ),
      });
      FileSaver.saveAs(data.url, `${this.$moment().format('YYYYMMDD_HHmmss')}.xlsx`);
      this.exportLoading = false;
    } catch (error) {
      this.exportLoading = false;
    }
  }
  async onImport(files: File[]) {
    if (!files.length) return;
    try {
      this.importLoading = true;
      const formData = new FormData();
      formData.append('file', files[0]);
      const { data } = await this.request.post<{ uid: string }>('/ex/admin/imports/read', formData);
      const res = await this.request.post('/ex/admin/imports/zip', {
        uid: data.uid,
        attrs: this.attrs,
      });
      const mergeRecords = (res.data.records || []).map((r: IRecord) => ({
        ...r,
        ...this.defaultObject,
      }));
      const records = this.isValidValue ? this.value : [];
      this.$emit('change', records.concat(mergeRecords));
      this.importLoading = false;
    } catch (error) {
      this.$message.error('导入失败');
      this.importLoading = false;
    }
  }
}
</script>

<template lang="pug">
.table-filed
  .table-filed__header(v-if="mode === 'default'")
    a-button(
      v-if="!disabled"
      icon="download"
      @click="onExportTemplate"
      :loading="exportLoading"
      :disabled="exportLoading")
      | 模板下载
    a-button(
      v-if="records.length"
      icon="download"
      type="primary"
      :loading="exportLoading"
      :disabled="exportLoading"
      @click="onExport")
      | 导出记录
    file-input.cell(
      v-if="!disabled"
      :multiple="false"
      :value="[]"
      :accept="supportTypes"
      @change="onImport")
      a-button(
        icon="upload"
        type="primary"
        :loading="importLoading"
        :disabled="importLoading")
        | 导入记录
    a-button(
      v-if="!disabled"
      type="primary"
      icon="plus"
      @click="onNew")
      | 添加记录
  a-table.table-filed__table(
    rowKey="id"
    :dataSource="records"
    :columns="columns"
    :pagination="false"
    :bordered="true"
    :loading="false"
    :scroll="{ x: '100%' }")
    .table-hover-col(slot-scope="record" slot="actions" v-if="!disabled && mode === 'default'")
      IconTooltip(icon="edit" tips="编辑" @click="onEdit(record)")
      PopoverConfirm(
        title="删除"
        content="您确认删除该记录吗？"
        @confirm="onDelete(record)")
        IconTooltip(icon="delete" tips="删除")

  TaFormDialog(
    v-model="visible"
    :title="formData.id ? '编辑记录' : '新建记录'"
    :formData="formData"
    :template="template"
    :loading="false"
    @update="update"
    @create="create")
</template>

<style lang="stylus" scoped>
.table-filed
  max-width 100%
  width 100%
  .table-filed__header
    margin-bottom 8px
    text-align right
    .cell
      margin 0 14px
  .table-filed__table
    width 100%
    max-width 100%
</style>
