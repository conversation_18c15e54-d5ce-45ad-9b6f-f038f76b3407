<script lang="ts">
import { IFormTemplateItem } from '@/interfaces/formTemplate.interface';
import budget, { IBudget } from '@/models/finance/budget';
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import { IProject } from '@/models/finance/project';
// import catalogModel from '@/models/finance/catalog';
import { Subject } from '@/models/finance/subject';
import { Origin } from '@/models/finance/origin';

@Component({
  components: {},
})
export default class BudgetCreateField extends Vue {
  @Model('change') value!: IBudget[];
  // @Prop({ type: [Number, String], required: true }) projectId!: number | string;
  @Prop({ type: Boolean, default: false }) disabled!: boolean;

  budgets: IBudget[] = [];
  visible = false;
  formData: Partial<IBudget> = {};
  project: IProject = {};
  catalogOptions: any = [];
  subjectOptions: any = [];
  originOptions: any = [];
  // :catalog_name, :amount, :number, :unit_price, :unit, :origin_id, :subject_id, :name, :purchase, :payment_way

  get template(): IFormTemplateItem[] {
    return [
      {
        key: 'catalog_name',
        name: '二级内容',
        layout: {
          component: 'select',
          placeholder: '请选择',
          options: [
            { label: '货物服务类及政府采购等经费' },
            { label: '党建经费' },
            { label: '福利费及工会经费' },
            { label: '校内教师、校外兼职教师经费、教师进修经费等劳务、培训等人员经费' },
            { label: '公务用车、招待费及会议费' },
            { label: '学生海外游学、教师出国经费等出国经费' },
            { label: '20万以下零星维修等后勤保障服务经费' },
            { label: '20万以上维修改造等基建经费' },
            { label: '科研类、知识产权类等科研经费' },
            { label: '图书经费' },
            { label: '信息化类、系统维护费、技防等经费' },
            { label: '教学资源经费' },
            { label: '安防消防等安全设施设备经费' },
            { label: '宣传文化走廊等宣传经费' },
            { label: '学生奖助学金、困难补助等学生经费' },
            { label: '其他教学、行政杂项经费' },
          ],
          required: false,
        },
        model: {
          attr_type: 'string',
        },
      },
      {
        key: 'name',
        name: '三级内容',
        layout: {
          component: 'input',
          placeholder: '请输入',
          required: true,
        },
        model: {
          attr_type: 'string',
        },
      },
      {
        key: 'amount',
        name: '预算总额',
        layout: {
          component: 'currency',
          placeholder: '请输入',
          required: true,
        },
        model: {
          attr_type: 'number',
        },
      },
      {
        key: 'number',
        name: '数量',
        layout: {
          component: 'input',
          placeholder: '请输入',
          required: false,
        },
        model: {
          attr_type: 'number',
        },
      },
      {
        key: 'unit_price',
        name: '单价',
        layout: {
          component: 'currency',
          placeholder: '请输入',
          required: false,
        },
        model: {
          attr_type: 'number',
        },
      },
      {
        key: 'unit',
        name: '单位',
        layout: {
          component: 'input',
          placeholder: '请输入',
          required: false,
        },
        model: {
          attr_type: 'string',
        },
      },
      {
        key: 'origin_id',
        name: '资金来源',
        layout: {
          component: 'select',
          placeholder: '请选择',
          required: false,
          options: this.originOptions,
        },
        model: {
          attr_type: 'number',
        },
      },
      {
        key: 'subject_id',
        name: '经济科目',
        layout: {
          component: 'select',
          placeholder: '请选择',
          required: false,
          options: this.subjectOptions,
        },
        model: {
          attr_type: 'number',
        },
      },
      {
        key: 'purchase',
        name: '采购方式',
        layout: {
          component: 'input',
          placeholder: '请输入',
          required: false,
        },
        model: {
          attr_type: 'string',
        },
      },
      {
        key: 'payment_way',
        name: '支付方式',
        layout: {
          component: 'input',
          placeholder: '请输入',
          required: false,
        },
        model: {
          attr_type: 'string',
        },
      },
    ];
  }

  @Watch('budgets', { deep: true })
  onBudgetsChange() {
    this.$emit('change', this.budgets);
  }

  mounted() {
    this.budgets = this.value || [];
    this.fetchTemplateOptions();
  }

  onNew() {
    this.visible = true;
  }

  onEdit(record: IBudget) {
    this.formData = record;
    this.visible = true;
  }

  onDelete(record: IBudget) {
    const index = this.budgets.findIndex(budget => budget.id === record.id);
    this.budgets.splice(index, 1);
  }

  onCreate(payload: IBudget) {
    const id = Date.now();
    this.budgets.push({ id, ...payload });
    this.visible = false;
  }

  onUpdate(payload: IBudget) {
    const index = this.budgets.findIndex(budget => budget.id === payload.id);
    this.budgets.splice(index, 1, payload);
    this.visible = false;
  }

  fetchTemplateOptions() {
    Promise.all([
      // catalogModel.indexByParent(this.projectId, { page: 1, per_page: 100 }),
      new Subject('teacher').index({ page: 1, per_page: 100 }),
      new Origin('teacher').index({ page: 1, per_page: 100 }),
    ]).then(([subjectRes, originRes]) => {
      // ]).then(([catalogRes, subjectRes, originRes]) => {
      // this.catalogOptions = catalogRes.data.catalogs.map(o => ({ label: o.name, value: o.id }));
      this.subjectOptions = subjectRes.data.subjects.map(o => ({ label: o.name, value: o.id, group: o.catalog }));
      this.originOptions = originRes.data.origins.map(o => ({ label: o.name, value: o.id }));
      this.originOptions.unshift({ label: '资金池统筹', value: null } as any);
    });
  }

  subjectIdToName(subjectId: number) {
    return this.subjectOptions.find((item: IObject) => item.value === subjectId)?.label;
  }

  originIdToName(originId: number) {
    return this.originOptions.find((item: IObject) => item.value === originId)?.label || '资金池统筹';
  }
}
</script>

<template lang="pug">
.budget-create-field
  TextButton(v-if='!disabled' icon='plus-circle' @click='onNew') 创建
  a-table(
    :dataSource='budgets'
    :pagination='{ hideOnSinglePage: true }'
    :scroll='{ x: true }'
  )
    a-table-column(title="二级内容" dataIndex="catalog_name" :width="150")
    a-table-column(title="三级内容" dataIndex="name" :width="130")
      template(slot-scope="name")
        a-tooltip(:title="name")
          .three-line(style="max-width: 130px")
            | {{ name }}
    a-table-column(title="经济科目" dataIndex="subject_id" :width="110")
      template(slot-scope="subject_id")
        | {{ subjectIdToName(subject_id) }}
    a-table-column(title="来源" dataIndex="origin_id" :width="110")
      template(slot-scope="origin_id")
        | {{ originIdToName(origin_id) }}
    a-table-column(title="数量" dataIndex="number" :width="110")
    a-table-column(title="单价" dataIndex="unit_price" :width="110")
    a-table-column(title="单位" dataIndex="unit" :width="110")
    a-table-column(title="金额" dataIndex="amount" :width="130" align="right")
      template(slot-scope="amount")
        span {{ amount | toCurrency }}
    a-table-column(title="采购方式" dataIndex="purchase" :width="80")
      template(slot-scope="purchase")
        a-tooltip(:title="purchase")
          .text-ellipsis(style="max-width: 150px;")
            | {{ purchase }}
    a-table-column(title="支付方式" dataIndex="payment_way", :width="80")
    a-table-column(v-if='!disabled', title="编辑" :width='110')
      template(slot-scope="text, record")
        IconTooltip(icon='edit' tip='编辑' @click='onEdit(record)')
        IconTooltip(icon='delete' tip='删除' @click='onDelete(record)')
  TaFormDialog(
    v-model='visible'
    :title='formData.id ? "编辑预算" : "创建预算"'
    :formData="formData"
    :template="template"
    :loading="false"
    @update="onUpdate"
    @create="onCreate"
  )
</template>

<style lang="stylus" scoped></style>
