<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import FileItem from '@/components/global/FileItem.vue';
import WechatArticle from './WechatArticle.vue';
import TableField from './TableField.vue';
import { IInstance } from '@/models/bpm/instance';
import { IFile } from '../../models/file';
import { IFormTemplateItem } from '@/interfaces/formTemplate.interface';
import TemplateFormatter from './TemplateFormatter.vue';
import MajorField from './MajorField.vue';
import LevelTwoCollegeField from './LevelTwoCollegeField.vue';
import { get } from 'lodash-es';
import TeacherField from './TeacherField.vue';
import StudentField from '@/components/form/StudentField.vue';
import CourseDirField from './CourseDirField.vue';
import DepartmentField from './DepartmentField.vue';
import BudgetAdjustField from './BudgetAdjustField.vue';
import BudgetLockCreateField from './BudgetLockCreateField.vue';
import BudgetLockAmountField from './BudgetLockAmountField.vue';
import BudgetCreateField from './BudgetCreateField.vue';
import FinanceProjectField from './FinanceProjectField.vue';
import FinanceProjectExecuteField from './FinanceProjectExecuteField.vue';
import FinanceProjectOwnField from './FinanceProjectOwnField.vue';
import FinanceProjectAdminAllField from './FinanceProjectAdminAllField.vue';
import UserIdsField from './UserIdsField.vue';

@Component({
  components: {
    FileItem,
    WechatArticle,
    TableField,
    TemplateFormatter,
    MajorField,
    LevelTwoCollegeField,
    TeacherField,
    StudentField,
    CourseDirField,
    DepartmentField,
    BudgetAdjustField,
    BudgetLockCreateField,
    BudgetLockAmountField,
    BudgetCreateField,
    FinanceProjectField,
    FinanceProjectAdminAllField,
    FinanceProjectExecuteField,
    FinanceProjectOwnField,
    UserIdsField,
  },
})
export default class TemplateFormViewer extends Vue {
  @Prop({ type: Array, default: () => [] }) template!: any[];
  @Prop({ type: Object, default: () => ({}) }) formData!: IObject;
  @Prop({ type: Boolean, default: true }) border!: boolean;
  @Prop({ type: Boolean, default: false }) editable!: boolean;
  @Prop({ type: String, default: 'default' }) mode!: 'print' | 'default';
  @Prop({ type: Boolean, default: false }) private showIcon!: boolean;
  @Prop({ type: Number }) private titleWidth!: number;
  // 可选
  @Prop({ type: Object, default: () => ({}) }) private instance!: IInstance;

  alignMap: IObject = {
    left: 'flex-start',
    center: 'center',
    right: 'flex-end',
  };

  formattedTemplate: IFormTemplateItem[] = [];

  get titleWidthStyle() {
    return this.titleWidth ? { width: `${this.titleWidth}px` } : {};
  }

  get templateFormatter() {
    return this.$refs.templateFormatter as TemplateFormatter;
  }

  get mapKeyValue() {
    return this.formattedTemplate.reduce((out, item) => {
      if (item.map_key) {
        out[item.map_key] = this.formData[item.key!];
      }
      return out;
    }, {} as IObject);
  }

  get projectId() {
    return (
      this.formData.projectId ||
      (this.mapKeyValue.project && this.mapKeyValue.project[0] && this.mapKeyValue.project[0].id)
    );
  }

  getContacts(value: any[]) {
    const members = value instanceof Array ? value : [];
    return members.map((o: any) => o.name).join('、');
  }
  // other
  getLabelStyle(item: any) {
    const span = item.layout.rowspan || 1;
    const height = span * 53 + 'px';
    return {
      height,
      justifyContent: this.alignMap[item.layout.textAlign || 'left'],
      whiteSpace: 'pre-wrap',
      display: 'flex',
      alignItems: 'center',
      fontSize: '16px',
      fontWeight: '400',
      overflow: 'hidden',
      color: '#333333',
      background: '#f9f9f9',
      padding: '16px 10px',
    };
  }

  loadValue(item: IObject, key: string) {
    return get(item, key);
  }

  selectValue(formData: IObject, item: IObject) {
    const value = this.loadValue(formData, item.key);
    return (
      item.layout.options.find((opt: IObject) => (opt.value && `${opt.value}` === `${value}`) || opt.label === value)
        ?.label || value
    );
  }

  getLabel(value: string, item: IObject) {
    return (
      item.layout.options.find((opt: IObject) => (opt.value && `${opt.value}` === `${value}`) || opt.label === value)
        ?.label || value
    );
  }
}
</script>

<template lang="pug">
.template-form-payload(:class='{ "no-border": !border }')
  TemplateFormatter(
    ref='TemplateFormatter',
    v-model='formattedTemplate'
    :payload='formData'
    :template='template'
  )
  a-row(:gutter='0')
    a-col(
      v-for='item in formattedTemplate',
      :span='item.layout.span || 24',
      :key='item.key',
      v-if='item.accessibility !== "hidden"'
    )
      .payload-item.widget-label(v-if='item.layout.component === "label"', :style='getLabelStyle(item)')
          .name {{ item.name }}
      .payload-item(v-else, :class='{ "no-border": !border }')
        .title(:style='titleWidthStyle')
          template(v-if='showIcon')
            a-icon.text-button(:type='item.icon || "border"')
            span.name(v-if='item.name') {{ item.name }}
          template(v-else)
            span(v-if='item.name') {{ item.name }}：
        .value(v-if='item.layout.component === "date"')
          | {{ loadValue(formData, item.key) | format("YYYY-MM-DD") }}
        .value(v-else-if='item.layout.component === "time"')
          | {{ loadValue(formData, item.key) | format("HH:mm:ss") }}
        .value(v-else-if='item.layout.component === "datetime"')
          | {{ loadValue(formData, item.key) | format("YYYY-MM-DD HH:mm:ss") }}
        .value(v-else-if='item.layout.component === "currency"')
          | {{ loadValue(formData, item.key) | toCurrency }}
        .value(v-else-if='item.layout.component === "contacts"')
          UserIdsField.user-id(
            :value='loadValue(formData, item.key)',
            :disabled='true',
          )
        .value(v-else-if='item.layout.component === "checkbox"')
          ul.options
            li.option(v-for='(val, index) in (loadValue(formData, item.key) || [])')
              | {{ getLabel(val, item) }}
        .value.wrap-value(v-else-if='item.layout.component === "file"')
          Attachments(:attachments='loadValue(formData, item.key) || []')
        .value.wrap-value(v-else-if='item.layout.component === "image"')
          Attachments(:attachments='loadValue(formData, item.key) || []')
        .value.wrap-value(v-else-if='item.layout.component === "rich_text"')
          .ck-content(v-html='loadValue(formData, item.key)')
        .value.wrap-value(v-else-if='item.layout.component === "wechat_articles"')
          WechatArticle(:article='loadValue(formData, item.key)', :editable='editable')
        .value.wrap-value(v-else-if='item.layout.component === "major"')
          MajorField(:value='loadValue(formData, item.key)', :disabled='true')
        .value.wrap-value(v-else-if='item.layout.component === "level_two_college"')
          LevelTwoCollegeField(:value='loadValue(formData, item.key)', :disabled='true')
        .value.wrap-value(v-else-if='item.layout.component === "teacher"')
          TeacherField(:value='loadValue(formData, item.key)', :disabled='true')
        .value.wrap-value(v-else-if='item.layout.component === "student"')
          StudentField(:value='loadValue(formData, item.key)', :disabled='true')
        .value.wrap-value(v-else-if='item.layout.component === "course_dir"')
          CourseDirField(:value='loadValue(formData, item.key)', :disabled='true')
        .value.wrap-value(v-else-if='item.layout.component === "department_new"')
          DepartmentField(:value='loadValue(formData, item.key)', :disabled='true')
        .value.wrap-value(v-else-if='item.layout.component === "department_new_nested"')
          DepartmentField(:value='loadValue(formData, item.key)', :disabled='true', :nested='true')
        .value.wrap-value(v-else-if='item.layout.component === "finance_project"')
          FinanceProjectField(:value='loadValue(formData, item.key)', :disabled='true' :instance='instance')
        .value.wrap-value(v-else-if='item.layout.component === "finance_project_admin_all"')
          FinanceProjectAdminAllField(:value='loadValue(formData, item.key)', :disabled='true')
        .value.wrap-value(v-else-if='item.layout.component === "finance_project_own"')
          FinanceProjectOwnField(:value='loadValue(formData, item.key)', :disabled='true')
        .value.wrap-value(v-else-if='item.layout.component === "finance_project_execute"')
          FinanceProjectExecuteField(:value='loadValue(formData, item.key)', :disabled='true')
        .value.wrap-value(v-else-if='item.layout.component === "table"')
          TableField(
            :value='loadValue(formData, item.key)',
            :attrs='item.layout.attrs',
            :instance='instance',
            :disabled='true',
            :mode='mode'
          )
        .value.wrap-value(v-else-if='item.layout.component === "budget_lock_create"')
          BudgetLockCreateField(
            v-if='projectId'
            :value='loadValue(formData, item.key)',
            :disabled='true',
            :projectId='+projectId'
          )
        .value.wrap-value(v-else-if='item.layout.component === "budget_lock_amount"')
          BudgetLockAmountField(
            v-if='projectId'
            :value='loadValue(formData, item.key)',
            :disabled='true',
            :projectId='+projectId'
          )
        .value.wrap-value(v-else-if='item.layout.component === "budget_adjust"')
          BudgetAdjustField(
            v-if='projectId'
            :value='loadValue(formData, item.key)',
            :disabled='true',
            :projectId='+projectId'
          )
        .value.wrap-value(v-else-if='item.layout.component === "budget_adjust_negative"')
          BudgetAdjustField(
            v-if='projectId'
            :value='loadValue(formData, item.key)',
            :disabled='true',
            :projectId='+projectId'
            :positive='false'
          )
        .value.wrap-value(v-else-if='item.layout.component === "budget_create"')
          BudgetCreateField(
            :value='loadValue(formData, item.key)',
            :disabled='true',
            :projectId='+projectId'
          )
        .value.wrap-value(v-else-if='item.layout.component === "textarea"')
          .pre-text {{ loadValue(formData, item.key) }}
        .value(v-else-if='item.layout.component === "select" || item.layout.component === "radio"')
          .input-value {{ selectValue(formData, item) }}
        .value(v-else-if='loadValue(formData, item.key) && item.processer')
          .input-value {{ item.processer(loadValue(formData, item.key)) }}
        .value(v-else-if='loadValue(formData, item.key) || loadValue(formData, item.key) === 0')
          .input-value {{ loadValue(formData, item.key) }}
        .value.text-gray(v-else)
</template>

<style lang="stylus" scoped>
.template-form-payload
  border-top 1px solid #eeeeee
  border-left 1px solid #eeeeee
  .payload-item
    display flex
    flex-wrap wrap
    padding 12px 10px
    border-right 1px solid #eeeeee
    border-bottom 1px solid #eeeeee
    .title
      color #808080
      white-space pre-wrap
      font-size 14px
      line-height 24px
      .text-button
        color #808080
        margin-right 14px
    .value
      overflow hidden
      min-height 24px
      color #333
      font-size 14px
      line-height 24px
      .options
        margin 0
        padding-left 20px
      .pre-text
        margin 0
        white-space pre-wrap
      .input-value
        width 100%
        min-height 24px
    .wrap-value
      flex-shrink 0
      width 100%

.no-border
  padding-right 0px !important
  padding-left 0px !important
  border none !important
.user-id
  height 10px
  >>>.ant-tag
    padding 0 4px !important
    margin-top -3px
    font-size 12px
</style>
