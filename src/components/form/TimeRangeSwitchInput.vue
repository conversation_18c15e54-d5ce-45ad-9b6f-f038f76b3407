<script lang="ts">
import { Moment } from 'moment';
import { Component, Vue, Model, Prop, Watch } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class TimeRangeSwitchInput extends Vue {
  @Model('change', { type: Array }) value!: string[];
  @Prop({ type: Boolean, default: false }) disabled!: boolean;
  @Prop({ type: Boolean, default: true }) showTime!: boolean;

  formData: (string | null)[] = [null, null];

  get timeFormat() {
    return this.showTime ? 'YYYY-MM-DD HH:mm' : 'YYYY-MM-DD';
  }

  @Watch('value', { immediate: true })
  onFormDataChange() {
    this.formData = [this.value[0] || null, this.value[1] || null];
  }

  onDatePickerChange(dateString: string, key: 'start' | 'end') {
    const index = key === 'start' ? 0 : 1;
    this.formData[index] = dateString;
    if (this.formData[index] === '') {
      this.confirm(false);
    }
  }

  confirm(open: boolean) {
    if (!open) {
      this.$emit('change', this.formData);
      this.$emit('confirm', this.formData);
    }
  }
}
</script>

<template lang="pug">
.time-range-switch-input
  template(v-if='!disabled')
    a-date-picker(
      v-model='formData[0]',
      :show-time='showTime ? { defaultValue: $moment("09:00:00", "HH:mm:ss") } : false',
      :format='timeFormat',
      placeholder='请选择开始时间',
      @change='(_, dateString) => { onDatePickerChange(dateString, "start"); }',
      @openChange='confirm'
    )
    span.ohhhh ~
    a-date-picker(
      v-model='formData[1]',
      :show-time='showTime ? { defaultValue: $moment("23:00:00", "HH:mm:ss") } : false',
      :format='timeFormat',
      placeholder='请选择结束时间',
      @change='(_, dateString) => { onDatePickerChange(dateString, "end"); }',
      @openChange='confirm'
    )
  template(v-else)
    .value
      | {{ $moment(formData[0]).format(timeFormat) }}
      | ~
      | {{ $moment(formData[1]).format(timeFormat) }}
</template>

<style lang="stylus" scoped>
.ohhhh
  margin 0 5px
</style>
