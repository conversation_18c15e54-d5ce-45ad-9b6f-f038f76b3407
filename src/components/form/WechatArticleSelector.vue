<script lang="ts">
/**
 * 公众号文章选择工具
 */
import { Component, Vue, Prop, Model } from 'vue-property-decorator';
import { Article, IArticle } from '@/models/wechat/article';
import { IFormItemArticle } from '@/interfaces/formTemplate.interface';
import { IApp } from '@/models/wechat/app';

@Component
export default class WechatArticleSelector extends Vue {
  articles: IArticle[] = [];
  currentPage: number = 1;
  totalCount: number = 0;
  perPage: number = 10;
  loading: boolean = false;
  selectedArticle: IArticle | object = {};
  activeAppId: string = '';
  articleModel: Article = new Article({ appId: '' });

  @Model('change', { type: Object }) readonly value!: IFormItemArticle; // 选择的文章
  @Prop({ type: Object, default: () => [], required: true }) readonly wechat!: IApp; // appid
  @Prop({ type: Boolean, default: false }) readonly disabled!: boolean;

  mounted() {
    if (this.value) {
      this.selectedArticle = { media_id: this.value.mediaId };
    }
    if (!this.disabled) {
      this.fetchData(1);
    }
  }

  async fetchData(page: number, query?: IObject) {
    try {
      this.loading = true;
      this.activeAppId = this.wechat.appid;
      this.articleModel = new Article({ appId: this.activeAppId });
      if (this.activeAppId) {
        const { data } = await this.articleModel.indexByParent(this.activeAppId, {
          page,
          per_page: this.perPage,
          q: {
            ...query,
          },
        });
        this.articles = data.item;
        this.currentPage = data.current_page;
        this.totalCount = data.total_count;
        this.loading = false;
      }
    } catch (error) {
      this.loading = false;
    }
  }
  onClick(record: IArticle) {
    if (this.disabled) {
      return;
    }
    this.selectedArticle = record;
    this.$emit('change', {
      appId: this.activeAppId,
      mediaId: record.media_id,
      count: record.content.news_item.length,
      headline: (record.content.news_item[0] || {}).title,
    } as IFormItemArticle);
  }
  getImageUrl(mediaId: string) {
    return this.articleModel.getImageUrl(mediaId);
  }
}
</script>

<template lang="pug">
.wechat-articles
  .disabled(v-if="disabled")
    | 微信文章选择
  AdminTable(
    v-else
    :data="articles"
    :currentPage="currentPage"
    :totalCount="totalCount"
    :perPage="perPage"
    :loading="loading"
    rowKey="media_id"
    rowClassName="click-row'"
    @rowClick="onClick"
    @change="fetchData")
    a-table-column(title="内容" key="media_id")
      template(slot-scope="record")
        .news-item(v-if="record.content.news_item[0]")
          img(:src="getImageUrl(record.content.news_item[0].thumb_media_id)" height="72" width="144")
          .title(v-if="record.content.news_item.length === 1")
            | {{ record.content.news_item[0].title }}
          ol.list(v-else)
            li(v-for="article in record.content.news_item" :key="article.thumb_media_id")
              | {{ article.title }}
    a-table-column(title="更新时间" dataIndex="update_time" key="update_time")
      template(slot-scope="update_time")
        | {{ (update_time * 1000) | format }}
    a-table-column(:width="120" align="center")
      template(slot-scope="record")
        a-icon.text-success(
          type="check-circle"
          v-if="selectedArticle.media_id === record.media_id"
          style="font-size: 28px;font-weight: bold;")
</template>

<style lang="stylus" scoped>
.disabled
  padding 10px 16px
  border-radius 4px
  background #eee
  color #999
  font-size 16px
  cursor not-allowed

.news-item
  display flex
  align-items center
  img
    flex-shrink 0
    border-radius 3px
    object-fit cover
  .title
    padding-left 20px
  .list
    margin-bottom 0
    padding-left 35px
    color rgba(56, 56, 56, 1)
    font-size 14px
    line-height 20px
</style>
