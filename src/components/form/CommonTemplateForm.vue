<script lang="ts">
// TODO: 此组件，作为通用组件，后续逐步完善

/**
 * 通用表单;交互组件，更新传入的 template; 渲染表单
 * 事件：
 * submit 提交
 * cancel 取消
 * 示例 Template [{
 *   key: name,
 *   label: '密码',
 *   type: 'string',
 *   component: 'input',
 *   componentType: 'password',
 *   options: [],
 *   rules: [],
 *   accept: 'image/*'
 * }]
 *
 * component（控件）：
 *   input, textarea, date-picker
 *   time-picker, date-time-picker, radio, checkbox, select, uploader,
 *   hr
 *
 * type（值类型）: string, number, boolean, array
 * visibility（是否显示）: function | Boolean
 * disabled（是否禁用）: function | Boolean
 * append（表单项追加文字）: string
 *
 * 控件特定的属性：
 * 【input】                        componentType: password | number ...（原生 input type）
 * 【input-number, number-range】   min: number ...
 * 【input-number, number-range】   max: number ...
 * 【radio,radio-button,checkbox,select】        options: [{ label: '', value: ''}]
 */

import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import { IFormTemplateItem } from '@/interfaces/formTemplate.interface';
import moment, { Moment } from 'moment';

@Component({
  components: {},
})
export default class CommonTemplateForm extends Vue {
  private form: IObject = {};
  private validateFields: IObject = {};

  @Prop({ type: Object, default: () => ({}) }) private formData!: IObject;
  @Prop({ type: Array, default: () => [], required: true }) private template?: any;
  @Prop({ type: Boolean, default: false }) private loading?: boolean;
  @Prop({ type: Boolean, default: false }) private disabled?: boolean;
  @Prop({ type: Boolean, default: false }) private showActions?: boolean;
  @Prop({ type: Number, default: 24 }) private labelCol?: number;

  @Watch('formData')
  public watchFormData() {
    this.updateFieldsValue();
  }

  @Watch('template')
  public watchTemplate() {
    this.updateFieldsValue();
  }

  public created() {
    this.form = this.$form.createForm(this);
    this.validateFields = this.form.validateFields;
  }

  public mounted() {
    this.updateFieldsValue();
  }

  // 更新表单的值
  public updateFieldsValue() {
    if (this.form) {
      const fields = this.template.reduce(
        (obj: IObject, item: IFormTemplateItem) => ({
          ...obj,
          [item.key!]: this.formData[item.key!],
        }),
        {},
      );
      this.$nextTick(() => {
        this.form.setFieldsValue(fields);
      });
    }
  }

  // 表单的 submit 事件，由原生表单触发
  public onFormSubmit(e: any) {
    e.preventDefault();
    this.submit();
  }

  // 表单的验证提交事件，可以供直接外部调用，发起提交
  public submit() {
    this.form.validateFields((err: any, formData: IObject) => {
      if (!err) {
        const payload: IObject = Object.entries(formData).reduce(
          (obj: IObject, [key, value]) => ({
            ...obj,
            [key]: value instanceof moment ? (value as Moment).format() : value,
          }),
          {},
        );
        if (this.formData && this.formData.id) {
          payload.id = this.formData.id;
        }
        this.$emit('submit', payload);
      } else {
        this.$emit('error', err);
      }
    });
  }

  dateTimeNormalize(value: string | Moment) {
    if (!value) return null;
    if (value instanceof moment) {
      return value;
    }
    const mo = moment(value);
    return mo.isValid() ? mo : null;
  }

  public getDisabledValue(item: IObject) {
    if (typeof item.disabled === 'boolean') {
      return item.disabled;
    }
    if (typeof item.disabled === 'function') {
      const disabled = item.disabled(this.formData, item.key);
      return typeof disabled === 'boolean' ? disabled : false;
    }
    return false;
  }

  public getFormItemVisibility(item: IObject) {
    if (typeof item.visibility === 'boolean') {
      return item.visibility;
    }
    if (typeof item.visibility === 'function') {
      const isShow = item.visibility(this.formData, item.key);
      return typeof isShow === 'boolean' ? isShow : false;
    }
    return true;
  }

  public filterOption(input: string, option: IObject) {
    return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  }

  public cancel() {
    this.form.resetFields();
    this.$emit('cancel');
  }

  public formatOptions(options: any[]) {
    return (options || []).map((item: any) => ({
      ...item,
      value: item.value !== undefined ? item.value : item.label,
    }));
  }
}
</script>

<template lang="pug">
.template-form-container
  a-form(:form="form" @submit="onFormSubmit")
    .template-form
      slot(name="top")
      a-row(:gutter="16")
        a-col(
          v-for="(item, index) in template"
          :key="index"
          :span="item.layout.span || 24")
          //- hr
          .boundary(v-if="item.layout.component === 'hr'")
            | {{ item.name }}
          //- form-item
          a-form-item(
            v-if="getFormItemVisibility(item)"
            :labelCol="{ span: 24 }"
            :wrapperCol="{ span: 24 }"
            :label="item.name")
            //- input
            a-input(
              v-if="item.layout.component === 'input'"
              v-decorator="[item.key, { rules: item.rules }]"
              :type="item.componentType"
              :disabled="getDisabledValue(item)"
              :placeholder="item.layout.placeholder || `请输入${item.name}`")
            //- textarea
            a-textarea(
              v-else-if="item.layout.component === 'textarea'"
              v-decorator="[item.key, { rules: item.rules }]"
              :autoSize="{ minRows: 4 }"
              :disabled="getDisabledValue(item)"
              :placeholder="item.layout.placeholder || `请输入${item.name}`")
            //- inputNumber
            a-input-number(
              v-else-if="item.layout.component === 'input' && item.type === 'number'"
              v-decorator="[item.key, { rules: item.rules }]"
              :disabled="getDisabledValue(item)"
              :min="typeof item.min === 'number' ? item.min : -Infinity"
              :max="typeof item.max === 'number' ? item.max : Infinity")
            //- select
            a-select(
              v-else-if="item.layout.component === 'select'"
              v-decorator="[item.key, { rules: item.rules }]"
              size="large"
              allowClear
              :disabled="getDisabledValue(item)"
              :options="item.layout.options"
              :placeholder="item.layout.placeholder || `请选择${item.name}`"
              :filterOption="filterOption")
            //- radio
            a-radio-group(
              v-else-if="item.layout.component === 'radio'"
              v-decorator="[item.key, { rules: item.rules }]"
              :name="item.key"
              :disabled="getDisabledValue(item)"
              :options="formatOptions(item.layout.options)")
            a-radio-group(
              v-else-if="item.layout.component === 'radio-button'"
              v-decorator="[item.key, { rules: item.rules }]"
              :name="item.key"
              :disabled="getDisabledValue(item)")
              a-radio-button(
                v-for="option in formatOptions(item.layout.options)"
                :key="option.value"
                :value="option.value"
                :disabled="option.disabled")
                | {{ option.label }}
            //- checkbox
            a-checkbox-group(
              v-else-if="item.layout.component === 'checkbox'"
              v-decorator="[item.key, { rules: item.rules }]"
              :disabled="getDisabledValue(item)"
              :options="formatOptions(item.layout.options)")
            //- switch
            a-switch(
              v-else-if="item.layout.component === 'switch'"
              v-decorator="[item.key, { rules: item.rules }]"
              :disabled="getDisabledValue(item)")
            //- date picker
            a-date-picker.picker(
              v-else-if="item.layout.component === 'date'"
              v-decorator="[item.key, { rules: item.rules, normalize: dateTimeNormalize }]"
              :disabled="getDisabledValue(item)"
              :placeholder="item.layout.placeholder || `请选择${item.name}`")
            //- time picker
            a-time-picker.picker(
              v-if="item.layout.component === 'time-picker'"
              v-decorator="[item.key, { rules: item.rules, normalize: dateTimeNormalize }]"
              :disabled="getDisabledValue(item)"
              :placeholder="item.layout.placeholder || `请选择${item.name}`")
            //- date-time picker
            a-date-picker.picker(
              v-else-if="item.layout.component === 'date-time-picker'"
              v-decorator="[item.key, { rules: item.rules, normalize: dateTimeNormalize }]"
              :disabled="getDisabledValue(item)"
              :placeholder="item.layout.placeholder || `请选择${item.name}`"
              :format="item.format || 'YYYY-MM-DD HH:mm:ss'"
              :showTime="true")
            //- uploader
            //- 注意：自定义组件，使用 decorator，绑定的 value prop 不要设置 default, 否则会报错：
            //- `getFieldDecorator` will override `value`, so please don't
            //- set `value and v-model` directly and use `setFieldsValue` to set it
            FileUploader(
              v-else-if="item.layout.component === 'file'"
              v-decorator="[item.key, { rules: item.rules, initialValue: formData[item.key] || [] }]"
              :disabled="getDisabledValue(item)"
              :multiple="item.layout.multiple")
            //- number-range
            NumberRange(
              v-else-if="item.layout.component === 'number-range'"
              v-decorator="[item.key, { rules: item.rules, initialValue: formData[item.key] || [0, 0] }]"
              :disabled="getDisabledValue(item)"
              :min="item.layout.min"
              :max="item.layout.max"
              :label="item.layout.gapText")
            //- append
            span.append(v-if="item.append")
              | {{ item.append }}
      //- last child
      slot
    .actions(v-if="showActions")
      slot(name="actions")
        a-button(@click="cancel")
          | 取消
        a-button(
          ref="submitBtn"
          type="primary"
          htmlType="submit"
          :loading="loading"
          :disabled="disabled")
          | {{ formData.id ? ' 更新 ' : ' 创建 ' }}
</template>

<style lang="stylus">
.template-form-container
  .template-form
    .picker
      min-width 200px
    .boundary
      margin-top 16px
      margin-bottom 8px
      border-bottom 1px solid #eee
      line-height 1.4
    .append
      margin-left 10px
    .option-item
      margin-bottom 8px
  .actions
    padding 10px 0
    text-align right
    button
      margin-left 10px
  .ant-input, .ant-calendar-picker, .ant-select-lg
    width 100%
    height 40px !important
</style>
