<script lang="ts">
import { IFormTemplateItem } from '@/interfaces/formTemplate.interface';
import { Component, Vue, Model } from 'vue-property-decorator';
import FormDesignerDialog from './FormDesignerDialog.vue';
import FormItemEditCell from './FormItemEditCell.vue';

@Component({
  components: {
    FormDesignerDialog,
    FormItemEditCell,
  },
})
export default class FormDesignerField extends Vue {
  @Model('change', { type: Array }) value!: IObject[];

  visibleForm = false;

  onValueChange(fields: IFormTemplateItem[]) {
    this.$emit('change', fields);
  }
}
</script>

<template lang="pug">
.form-designer-field
  a-row
    a-col(v-for='(item, index) in value', :key='index', :span='24')
      FormItemEditCell(:editable='false', :formItem='item', :active='false')
  a-button(type='primary', @click='visibleForm = true') 编辑字段
  FormDesignerDialog(v-model='visibleForm', :fields='value', @change='onValueChange')
</template>

<style lang="stylus" scoped></style>
