<script lang="ts">
/**
 * 公众号文章编辑工具
 */
import { Component, Vue, Prop, Model } from 'vue-property-decorator';
import { INewsItem } from '@/models/wechat/article';

@Component
export default class WechatNewsItemEditor extends Vue {
  @Model('input', { type: <PERSON>olean }) readonly value!: boolean;
  @Prop({ type: Object, default: () => ({}) }) readonly newsItem!: INewsItem;
  @Prop({ type: Boolean, default: false }) readonly loading!: boolean;
  @Prop({ type: Boolean, default: false }) readonly disabled!: boolean;

  get visible() {
    return this.value;
  }
  set visible(v: boolean) {
    this.$emit('input', v);
  }

  updateArticle() {
    const dom = document.getElementById('news') as HTMLDivElement;
    this.$emit('update', {
      ...this.newsItem,
      content: dom.innerHTML,
    });
  }
}
</script>

<template lang="pug">
MainModal(v-model="visible" title="编辑已选文章" width="90%")
  .media-wrapper
    #news(v-html="newsItem.content" :contenteditable="!disabled")
  .footer-action(slot="footer")
    a-button(@click="visible = false" size="large")
      | 取消
    PopoverConfirm(
      title="提示"
      content="确定要更新此篇微信文章吗？"
      @confirm="updateArticle")
      a-button(type="primary" size="large" :loading="loading" :disabled="disabled")
        | 更新文章
</template>

<style lang="stylus" scoped>
.media-wrapper
  height 100%
  width 100%
  padding 16px
  background #f4f4f4
  #news
    height 100%
    width 680px
    overflow auto
    margin 0 auto
    border 1px solid #eee
    padding 16px
    background #FFFFFF
.footer-action
  text-align right
  width 100%
</style>
