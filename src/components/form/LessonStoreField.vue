<script lang="ts">
// eslint-disable-next-line max-len
import { teachingAdminCurrentSemesterLessonsStore } from '@/store/modules/teaching/admin/current_semester/lessons.store';
import { Component, Vue, Prop, Model } from 'vue-property-decorator';
import StoreField from '@/components/form/StoreField.vue';

@Component({
  components: {
    StoreField,
  },
})
export default class LessonStoreField extends Vue {
  @Model('change') value!: number | number[];
  @Prop({ type: String, default: '课程' }) readonly title!: string;
  @Prop({ type: Boolean, default: false }) readonly multiple!: boolean;
  @Prop({ type: Boolean, default: false }) readonly disabled!: boolean;
  @Prop({ type: Number, default: 1 }) readonly week!: number;

  get storeConfig() {
    return {
      initParams: {
        params: {
          q: {
            week_eq: this.week,
          },
        },
      },
      tableColumns: [
        {
          title: '课程',
          dataIndex: 'course_set_name',
          searchKey: 'course_course_set_name',
          type: 'string',
          search: true,
        },
        { title: '上课时间', type: 'string', customRender: this.start_datetime },
        { title: '节数', type: 'string', customRender: this.select_format },
        { title: '班级', dataIndex: 'course_name', type: 'string', search: true },
        { title: '教师', dataIndex: 'teacher_name', type: 'string', search: true },
        { title: '人数', dataIndex: 'course_std_count', type: 'number', search: true },
        { title: '开课部门', dataIndex: 'course_department_name', type: 'string', search: true },
        { title: '授课部门', dataIndex: 'course_teach_depart_name', type: 'string', search: true },
        {
          title: '签到情况',
          dataIndex: 'register_stat',
          type: 'number',
          customRender: this.register_stat,
        },
        {
          title: '评价情况',
          dataIndex: 'evaluation_stat',
          type: 'number',
          customRender: this.evaluation_stat,
        },
      ],
      tagKey: 'course_set_name',
    };
  }
  select_format(object: IObject) {
    return `第${object.start_section} - ${object.end_section}节(共${object.end_section - object.start_section + 1}节)`;
  }
  formatTime(time: string) {
    return this.$moment(time).format('YYYY-MM-DD');
  }
  start_datetime(object: IObject) {
    return `${this.formatTime(object.start_datetime)} 星期${this.number_to_text(object.weekday)}`;
  }
  register_stat(object: IObject) {
    return `${object.done || 0}/${object.late || 0}/${object.done + object.late || 0}`;
  }
  evaluation_stat(object: IObject) {
    return `${object.done || 0}/${object.todo || 0}/${object.count || 0}`;
  }
  number_to_text(number: number) {
    switch (number) {
      case 1:
        return '一';
      case 2:
        return '二';
      case 3:
        return '三';
      case 4:
        return '四';
      case 5:
        return '五';
      case 6:
        return '六';
      case 7:
        return '日';
    }
    return number;
  }

  get store() {
    return teachingAdminCurrentSemesterLessonsStore;
  }

  get ids() {
    return this.value;
  }

  set ids(val) {
    this.$emit('change', val);
  }
  onOk(records: any) {
    this.$emit('onOk', records);
  }
}
</script>

<template lang="pug">
.user-store-field
  StoreField(
    v-model='ids',
    :recordName='title',
    :store='store',
    :storeConfig='storeConfig',
    :multiple='multiple',
    :disabled='disabled',
    @ok='onOk'
  )
    template(#tags) 
      .blank 
    template(#button='{ openSelector }')
      TextButton.select(icon='plus-circle', theme='filled', @click='openSelector')
        | 选择督导课程
</template>

<style lang="stylus" scoped></style>
