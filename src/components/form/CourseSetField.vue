<script lang="ts">
import { emsAdminCourseSetsDialogStore } from '@/store/modules/ems/admin/course_sets.store';
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import StoreField from './StoreField.vue';

@Component({
  components: {
    StoreField,
  },
})
export default class CourseSetField extends Vue {
  @Model('change') value!: number | number[];

  @Prop({ type: Boolean, default: false }) disabled!: boolean;
  @Prop({ type: Boolean, default: false }) multiple!: boolean;

  get store() {
    return emsAdminCourseSetsDialogStore;
  }

  get config() {
    return {
      initParams: {
        params: { q: {} },
      },
      tableColumns: [
        { title: '名称', dataIndex: 'name', type: 'string', search: true },
        { title: '代号', dataIndex: 'code', type: 'string', search: true },
        { title: '课程目录', dataIndex: 'course_dir_name', type: 'string' },
      ],
    };
  }

  get localValue() {
    return this.value;
  }

  set localValue(val: number | number[]) {
    this.$emit('change', val);
  }
}
</script>

<template lang="pug">
.duty-field
  StoreField(
    v-model='localValue',
    recordName='课程',
    :store='store',
    :storeConfig='config',
    :disabled='disabled',
    :multiple='multiple',
  )
</template>

<style lang="stylus" scoped>
.department-field
  width 400px
</style>
