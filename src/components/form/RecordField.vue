<script lang="ts">
/**
 * 对象编辑器
 * value: [
 *   { name: '百度' },
 * ]
 */
import { Component, Vue, Prop, Model } from 'vue-property-decorator';

interface IAttr {
  key: string;
  name: string;
}

@Component
export default class RecordField extends Vue {
  @Model('change', { type: Array }) value!: IObject[];
  @Prop({ type: Boolean, default: false }) disabled!: boolean;
  @Prop({ type: Array, default: () => [{ key: 'name', name: '名称' }] }) attrs!: IAttr[]; // 对象结构

  onNew() {
    const newItem = this.attrs.reduce((obj: object, attr: IAttr) => ({ ...obj, [attr.key]: '' }), {});
    this.$emit('change', (this.value || []).concat(newItem));
  }

  onDelete(index: number) {
    this.value.splice(index, 1);
    this.$emit('change', this.value);
  }

  onInput(item: IObject, index: number, key: string, e: any) {
    const records = this.value.concat();
    records[index][key] = e.target.value;
    this.$emit('change', records);
  }
}
</script>

<template lang="pug">
.object-fields
  .object-field(v-for="(item, index) in value" :key="index")
    .delete(v-if="!disabled")
      a-icon(type="delete" @click="onDelete(index)")
    .input-item(v-for="attr in attrs" :key="attr.key")
      .label {{ attr.name }}
      a-input.input(
        :value="item[attr.key]"
        :disabled="disabled"
        placeholder="请输入"
        @input="(e) => onInput(item, index, attr.key, e)")
  .new-button(@click="onNew" icon="plus-circle" theme="filled" v-if="!disabled")
    a-icon.icon(type="plus")
    span  添加选项
</template>

<style lang="stylus" scoped>
.object-fields
  .object-field
    background rgba(255,255,255,1)
    border-radius 4px
    border 1px solid rgba(232,232,232,1)
    padding 0 16px
    margin-bottom 12px
    position relative
    overflow hidden
    &:hover
      .delete
        display block
    .input-item
      display flex
      padding 10px 0
      border-bottom 1px solid #E8E8E8
      align-items center
      &:last-child
        border-bottom none
      .label
        width 64px
        flex-shrink 0
      .input
        font-size 14px
        display block
        border none
    .delete
      text-align center
      position absolute
      top 0
      right 0
      background #3DA8F5
      height 20px
      width 20px
      line-height 20px
      display none
      color #fff
      cursor pointer
      z-index 100
  .new-button
    color #808080
    font-weight 400
    text-align center
    height 40px
    font-size 14px
    background rgba(255,255,255,1)
    border-radius 3px
    border 1px dashed rgba(229,229,229,1)
    width 100%
    padding 0
    &:hover
      border-color #3DA8F5
      cursor pointer
    .icon
      font-size 16px
</style>
