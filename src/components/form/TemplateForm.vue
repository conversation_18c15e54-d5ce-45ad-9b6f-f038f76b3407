<script lang="ts">
/**
 * 工作流动态表单交互组件
 * 使用的是与后端约定的配置结构，参照 templates.json 文件
 * 事件：
 * submit 提交, 参数是 formData
 * cancel 取消
 * 示例 Template [{
 * key: 'select_1234',
 * name: '标题',
 * accessibility: 'read_and_write',
 * layout: {
 *   component: 'select',
 *   options: [{ label: '选项一' }, { label: '选项二' }],
 *   type: 'text',
 *   placeholder: '请输入标题',
 *   required: true,
 *   disabled: false,
 *   span: 24,
 * },
 * model: {
 *   attr_type: 'string',
 * },
 * }]
 *
 * accessibility（访问性）: read_and_write, readonly, hidden
 * attr_type（值类型）: string, number, json, array
 * required（是否必填）: Boolean
 * disabled（是否禁用）: Boolean
 *
 */
import moment, { Moment } from 'moment';
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { Workflow } from '@/models/bpm/workflow';
import { IFormTemplateItem } from '@/interfaces/formTemplate.interface';
import FileUploader from '@/components/global/FileUploader.vue';
import RichEditor from '@/components/rich-editor/RichEditor.vue';
import TableField from './TableField.vue';
import TagField from './TagField.vue';
import FormDesignerField from '../form/FormDesignerField.vue';
// ============== 业务数据 =============
import department from '@/models/department';
import UserIdsField from './UserIdsField.vue';
import RecordField from './RecordField.vue';
// ============== 业务 ===============
import WechatArticleSelector from './WechatArticleSelector.vue';
import StoreField from './StoreField.vue';
import TemplateFormatter from './TemplateFormatter.vue';
import { set, get, isEqual, cloneDeep } from 'lodash-es';
import MajorField from './MajorField.vue';
import LevelTwoCollegeField from './LevelTwoCollegeField.vue';
import TeacherField from './TeacherField.vue';
import StudentField from './StudentField.vue';
import CourseDirField from './CourseDirField.vue';
import DepartmentField from './DepartmentField.vue';
import CourseSetField from './CourseSetField.vue';
import BudgetAdjustField from './BudgetAdjustField.vue';
import BudgetLockAmountField from './BudgetLockAmountField.vue';
import BudgetLockCreateField from './BudgetLockCreateField.vue';
import BudgetCreateField from './BudgetCreateField.vue';
import FinanceProjectField from './FinanceProjectField.vue';
import FinanceProjectExecuteField from './FinanceProjectExecuteField.vue';
import FinanceProjectOwnField from './FinanceProjectOwnField.vue';
import FinanceProjectAdminAllField from './FinanceProjectAdminAllField.vue';
import { IInstance } from '@/models/bpm/instance';

@Component({
  components: {
    FileUploader,
    TableField,
    RichEditor,
    RecordField,
    TagField,
    UserIdsField,
    WechatArticleSelector,
    StoreField,
    FormDesignerField,
    TemplateFormatter,
    MajorField,
    LevelTwoCollegeField,
    TeacherField,
    StudentField,
    CourseDirField,
    DepartmentField,
    CourseSetField,
    BudgetLockCreateField,
    BudgetLockAmountField,
    BudgetAdjustField,
    BudgetCreateField,
    FinanceProjectField,
    FinanceProjectExecuteField,
    FinanceProjectOwnField,
    FinanceProjectAdminAllField,
  },
})
export default class BpmTemplateForm extends Vue {
  form: any = null;
  validateFields: any = null;
  fileSettledMap: IObject = {};
  // ============ 业务数据 ===========
  departments: any[] = [];
  alignMap: IObject = {
    left: 'flex-start',
    center: 'center',
    right: 'flex-end',
  };
  formattedTemplate: IFormTemplateItem[] = [];
  lastField: IObject = {};

  ruleExistsMap: IObject = {};

  @Prop({ type: Object, default: () => ({}) }) readonly formData!: IObject;
  @Prop({ type: Array, default: () => [], required: true }) readonly template!: IFormTemplateItem[];
  @Prop({ type: Boolean, default: false }) readonly loading!: boolean;
  @Prop({ type: Boolean, default: false }) readonly disabled!: boolean;
  @Prop({ type: Boolean, default: true }) readonly required!: boolean;
  @Prop({ type: Number, default: 24 }) readonly labelCol!: number;
  @Prop({ type: Number, default: 24 }) readonly wrapperCol!: number;
  @Prop({ type: Boolean, default: true }) readonly showLabel!: boolean;
  @Prop({ type: Boolean, default: false }) readonly showActions!: boolean;
  @Prop({ type: Boolean, default: false }) private showIcon!: boolean;
  // 可选
  @Prop({ type: Object, default: () => ({}) }) private instance!: IInstance;

  get templateFormatter() {
    return this.$refs.templateFormatter as TemplateFormatter;
  }

  get mapKeyValue() {
    return this.formattedTemplate.reduce((out, item) => {
      if (item.map_key) {
        out[item.map_key] = this.templateFormatter.lastField[item.key!]?.value;
      }
      return out;
    }, {} as IObject);
  }

  get projectId() {
    return (
      this.formData.projectId ||
      (this.mapKeyValue.project && this.mapKeyValue.project[0] && this.mapKeyValue.project[0].id) ||
      (this.instance.storage?.project && this.instance.storage.project[0].id)
    );
  }

  @Watch('formData', { deep: true, immediate: true })
  onFormDataChange() {
    if (this.formData) {
      this.updateFieldsValue();
    }
  }

  // @Watch('formattedTemplate')
  // public watchTemplate() {
  //   const fields = this.form.getFieldsValue();
  //   if (
  //     Object.keys(fields).length === 0 ||
  //     Object.keys(fields)
  //       .map(key => this.formData[key])
  //       .filter(i => i).length > Object.values(fields).filter(i => i).length
  //   ) {
  //     // formData 中，key 在 fields 中，且有值的数量比 fields 多，初始化表单值
  //     this.updateFieldsValue();
  //   }
  // }

  created() {
    this.form = this.$form.createForm(this, {
      onFieldsChange: this.onFieldsChange,
    });
    this.validateFields = this.form.validateFields;
    this.updateFieldsValue();
  }

  mounted() {
    // 选择部门组件
    if (this.template.find(o => o.layout.component === 'department')) {
      this.fetchDepartments();
    }
  }

  async fetchDepartments() {
    const { data } = await department.tree();
    const str = JSON.stringify(data.departments);
    const newStr = str.replace(/"id":/g, '"value":').replace(/"short_name":/g, '"label":');
    this.departments = JSON.parse(newStr);
  }

  // 更新表单的值
  updateFieldsValue() {
    if (this.form) {
      const fields = this.template.reduce((obj: IObject, item: IFormTemplateItem) => {
        set(
          obj,
          item.key!,
          get(this.formData, item.key!) === 0 ? 0 : get(this.formData, item.key!) || item.layout.defaultValue || null,
        );
        return obj;
      }, {});
      setTimeout(() => {
        this.form.setFieldsValue(fields);
      }, 200);
    }
  }

  getFieldsValue(...args: any[]) {
    return this.form.getFieldsValue(...args);
  }

  // 表单的 submit 事件，由原生表单触发
  onFormSubmit(e: any) {
    e.preventDefault();
    this.submit();
  }

  submit(options: { success?: any; fail?: any } = {}) {
    if (Object.values(this.fileSettledMap).every(Boolean)) {
      this.form.validateFields(Object.keys(this.ruleExistsMap), (err: any, formData: IObject) => {
        if (!err) {
          // 处理时间
          const payload: IObject = Object.entries(this.getFieldsValue()).reduce(
            (obj: IObject, [key, value]) => ({
              ...obj,
              [key]: value instanceof moment ? (value as Moment).format() : value,
            }),
            {},
          );
          if (this.formData && this.formData.id) {
            payload.id = this.formData.id;
          }
          this.$emit('submit', payload);
          if (options.success) {
            options.success(payload);
          }
        } else if (options.fail) {
          options.fail(err);
        }
      });
    } else {
      this.$message.warning('有「正在上传」或者「上传失败」的文件，请处理完成后提交');
    }
  }

  lastPayload = {};
  lastTemplate = {};

  clearHideFormValue() {
    const payload = this.form.getFieldsValue();
    const hiddenPayload = this.templateFormatter.clearHideFormValue(payload);

    if (!isEqual(payload, hiddenPayload) || !isEqual(this.lastTemplate, this.formattedTemplate)) {
      this.setDefaultValueToPayload(payload);
      setTimeout(() => {
        this.form.setFieldsValue(payload);
      }, 200);
    }

    this.lastPayload = payload;
  }

  setDefaultValueToPayload(payload: IObject) {
    this.formattedTemplate.forEach(item => {
      if (item.layout.defaultValue && !payload[item.key!]) {
        payload[item.key!] = item.layout.defaultValue;
      }
    });
  }

  onFieldsChange(props: any, fields: any) {
    if (Object.keys(fields).length === 0) return;
    this.lastTemplate = cloneDeep(this.formattedTemplate);
    this.templateFormatter.formatTemplate(fields);
    this.$emit('fieldsChange', props, { ...fields });
    this.clearHideFormValue();
  }

  getRules(item: IFormTemplateItem) {
    const index = this.templateFormatter.template.findIndex(formItem => formItem.key === item.key);
    const required =
      ['label', 'if_container', 'container_end'].includes(item.layout.component) ||
      this.templateFormatter.indexShouldHide.includes(index)
        ? false
        : this.required && item.layout.required;
    const rules: IObject = [
      {
        required: required,
        message: `请设置${item.name}`,
        type: Workflow.getRuleType(item),
        transform: undefined,
      },
    ];

    if (required) {
      this.ruleExistsMap[item.key!] = true;
    }

    // TODO 测试性修改，这里可能会产生其他问题
    // 如果是时间或日期选择控件，验证前先转换为字符串
    if (item.layout.component === 'date') {
      rules[0].transform = function(value: any) {
        const m = moment(value);

        if (m.isValid()) {
          return m.format('YYYY-MM-DD');
        }

        return null;
      };
    } else if (item.layout.component === 'datetime') {
      rules[0].transform = function(value: any) {
        const m = moment(value);

        if (m.isValid()) {
          return m.format('YYYY-MM-DD HH:mm:ss');
        }

        return null;
      };
    } else if (required && rules[0].type === 'array') {
      rules[0] = {
        required: true,
        // 可能对其他的附件检验有影响
        type: item.layout.component === 'file' ? 'array' : 'any',
        message: rules[0].message,
      };
    }

    return rules;
  }

  isDisabled(item: IFormTemplateItem) {
    return item.accessibility === 'readonly' || item.layout.notEditable || this.disabled;
  }

  isVisibility(item: IFormTemplateItem) {
    return item.accessibility !== 'hidden';
  }

  getOptions(item: IFormTemplateItem) {
    return (item.layout.options || []).map((o: any) => ({
      label: o.label,
      value: o.value === undefined || o.value === '' ? o.label : o.value,
    }));
  }

  dateTimeNormalize(value: string | Moment) {
    if (!value) return null;
    if (value instanceof moment) {
      return value;
    }
    const mo = moment(value);
    return mo.isValid() ? mo : null;
  }

  onFileUploaderStatusChanged(item: IFormTemplateItem, isAllSuccessed: boolean) {
    this.$set(this.fileSettledMap, item.key || 'normal', isAllSuccessed);
  }

  cancel() {
    this.form.resetFields();
    this.$emit('cancel');
  }

  isGroupSelect(item: IFormTemplateItem) {
    return item.layout.options && item.layout.options[0] && item.layout.options[0].group;
  }
  getSelectOptions(item: IFormTemplateItem) {
    const options = (item.layout.options || []).map((o: any) => ({
      label: o.label,
      value: o.value || o.label,
      group: o.group,
    }));
    return options[0] && options[0].group ? this.$utils.groupBy(options, 'group') : options;
  }

  // ============= 业务耦合 ============
  contactsNormalize(item: any) {
    return item.layout.multiple ? [] : null;
  }

  normalizeDepartment(value: any) {
    return value === null ? null : value;
  }

  // 占位标签
  getLabelStyle(item: IFormTemplateItem) {
    const span = item.layout.rowspan || 1;
    const height = (span - 1) * 16 + span * 70 + 'px';
    return {
      height,
      padding: 10 + 'px',
      justifyContent: this.alignMap[item.layout.textAlign || 'left'],
    };
  }
}
</script>

<template lang="pug">
.ta-template-form
  //- | lastPayload {{ lastPayload }}
  TemplateFormatter(
    ref='templateFormatter',
    v-model='formattedTemplate'
    :payload='formData'
    :template='template'
  )
  a-form.form-content(:form='form', @submit='onFormSubmit')
    .template-form
      slot(name='top')
      a-row(:gutter='16')
        a-col(
          v-for='(item, index) in formattedTemplate',
          :key='`${item.key}_${item.layout.component}`',
          :span='item.layout.span || 24'
        )
          //- 分割线 hr
          .form-item(v-if='item.layout.component === "hr"')
            .boundary {{ item.name }}
          //- 占位 label
          .form-item(v-else-if='item.layout.component === "label"')
            .form-widget-label(:style='getLabelStyle(item)')
              | {{ item.name }}
          //- form-item
          a-form-item.form-item(
            v-else-if='isVisibility(item)',
            :labelCol='{ span: labelCol }',
            :wrapperCol='{ span: wrapperCol }',
            :label='showLabel && item.name'
            :key='`${item.key}_${item.layout.component}`'
          )
            template(#label)
              template(v-if='showIcon')
                a-icon.text-button(:type='item.icon || "border"')
                span.name(v-if='item.name') {{ item.name }}
              template(v-else)
                span(v-if='item.name') {{ item.name }}：
            //- input
            a-input(
              v-if='item.layout.component === "input" && item.model.attr_type !== "number"',
              v-decorator='[item.key, { rules: getRules(item) }]',,
              :type='item.layout.type',
              :disabled='isDisabled(item)',
              :placeholder='item.layout.placeholder || `请输入${item.name}`'
            )
            //- textarea
            a-textarea(
              v-else-if='item.layout.component === "textarea"',
              v-decorator='[item.key, { rules: getRules(item) }]',,
              :autoSize='{ minRows: 4 }',
              :disabled='isDisabled(item)',
              :placeholder='item.layout.placeholder || `请输入${item.name}`'
            )
            //- inputNumber
            a-input-number.picker(
              v-else-if='item.layout.component === "input" && item.model.attr_type === "number"',
              v-decorator='[item.key, { rules: getRules(item) }]',,
              :disabled='isDisabled(item)',
              :placeholder='item.layout.placeholder || `请输入${item.name}`',
              :min='typeof item.layout.min === "number" ? item.layout.min : -Infinity',
              :max='typeof item.layout.max === "number" ? item.layout.max : Infinity'
              :formatter="(value)=> item.layout.unit ? `${value}${item.layout.unit}` : value"
            )
            //- currency input
            CurrencyInput.picker(
              v-else-if='item.layout.component === "currency"',
              v-decorator='[item.key, { rules: getRules(item) }]',,
              :disabled='isDisabled(item)',
              placeholder='请输入金额'
            )
            //- group select
            a-select(
              v-else-if='item.layout.component === "select" && isGroupSelect(item)',
              v-decorator='[item.key, { rules: getRules(item) }]',
              :name='item.key',
              :disabled='isDisabled(item)',
              :placeholder='item.layout.placeholder || `请选择${item.name}`',
              showSearch
            )
              a-select-opt-group(v-for='(options, group) in getSelectOptions(item)', :key='group')
                a-select-option(v-for='option in options', :key='option.label', :value='option.value || option.label')
                  | {{ option.label }}
            //- select
            a-select(
              v-else-if='item.layout.component === "select"',
              v-decorator='[item.key, { rules: getRules(item), initialValue: item.layout.defaultValue }]',
              :disabled='isDisabled(item)',
              :placeholder='item.layout.placeholder || `请选择${item.name}`',
              :dropdownMenuStyle='{ height: "300px", overflow: "auto" }',
              option-label-prop='label',
              option-filter-prop='label',
              show-search,
              allowClear
            )
              a-select-option(
                v-for='option in getOptions(item)',
                :key='option.label',
                :value='option.value',
                :label='option.label'
              )
                | {{ option.label }}
            //- radio
            a-radio-group(
              v-else-if='item.layout.component === "radio"',
              v-decorator='[item.key, { rules: getRules(item) }]',,
              :name='item.key',
              :disabled='isDisabled(item)',
              :options='getOptions(item)'
            )
            //- checkbox
            a-checkbox-group(
              v-else-if='item.layout.component === "checkbox"',
              v-decorator='[item.key, { rules: getRules(item) }]',,
              :disabled='isDisabled(item)',
              :options='getOptions(item)'
            )
            //- switch
            a-switch(
              v-else-if='item.layout.component === "switch"',
              v-decorator='[item.key, { rules: getRules(item) }]',,
              :disabled='isDisabled(item)'
            )
            //- date picker
            a-date-picker.picker(
              v-else-if='item.layout.component === "date"',
              v-decorator='[item.key, { rules: getRules(item), normalize: dateTimeNormalize }]',
              valueFormat='YYYY-MM-DD',
              :disabled='isDisabled(item)',
              :placeholder='item.layout.placeholder || `请选择${item.name}`'
            )
            //- time picker
            a-time-picker.picker(
              v-else-if='item.layout.component === "time"',
              v-decorator='[item.key, { rules: getRules(item), normalize: dateTimeNormalize }]',
              :disabled='isDisabled(item)',
              :placeholder='item.layout.placeholder || `请选择${item.name}`'
            )
            //- date-time picker
            a-date-picker.picker(
              v-else-if='item.layout.component === "datetime"',
              v-decorator='[item.key, { rules: getRules(item), normalize: dateTimeNormalize }]',
              :disabled='isDisabled(item)',
              :placeholder='item.layout.placeholder || `请选择${item.name}`',
              :format='item.format || "YYYY-MM-DD HH:mm:ss"',
              :showTime='true'
            )
            //- uploader
            //- 注意：自定义组件，使用 decorator，绑定的 value prop 不要设置 default, 否则会报错：
            //- `getFieldDecorator` will override `value`,
            //- so please don't set `value and v-model` directly and use `setFieldsValue` to set it
            FileUploader(
              v-else-if='item.layout.component === "file"',
              v-decorator='[item.key, { rules: getRules(item), initialValue: formData[item.key] || [] }]',
              :disabled='isDisabled(item)',
              :multiple='item.layout.multiple',
              :accept='item.layout.accept',
              @statusChange='onFileUploaderStatusChanged(item, $event)'
            )
            FileUploader(
              v-else-if='item.layout.component === "image"',
              v-decorator='[item.key, { rules: getRules(item), initialValue: formData[item.key] || [] }]',
              :disabled='isDisabled(item)',
              :multiple='item.layout.multiple',
              @statusChange='onFileUploaderStatusChanged(item, $event)',
              accept='image/*'
            )
            a-tree-select(
              v-else-if='item.layout.component === "department"',
              style='width: 100%',
              :dropdownStyle='{ maxHeight: "400px", overflow: "auto" }',
              :treeData='departments',
              placeholder='请选择部门',
              treeDefaultExpandAll,
              allowClear,
              showSearch,
              :multiple='item.layout.multiple',
              treeNodeFilterProp='label',
              v-decorator='[item.key, { rules: getRules(item), normalize: normalizeDepartment }]'
            )
            RichEditor.rich-editor(
              v-else-if='item.layout.component === "rich_text"',
              v-decorator='[item.key, { rules: getRules(item), initialValue: "" }]',
              :disabled='isDisabled(item)'
            )
            TableField(
              v-else-if='item.layout.component === "table"',
              v-decorator='[item.key, { rules: getRules(item), initialValue: formData[item.key] || [] }]',
              :disabled='isDisabled(item)',
              :attrs='item.layout.attrs || []'
            )
            RecordField(
              v-else-if='item.layout.component === "record"',
              v-decorator='[item.key, { rules: getRules(item) }]',,
              :disabled='isDisabled(item)',
              :attrs='item.layout.attrs || []'
            )
            UserIdsField(
              v-else-if='item.layout.component === "contacts"',
              v-decorator='[item.key, { rules: getRules(item), initialValue: contactsNormalize(item) }]',
              :disabled='isDisabled(item)',
              :multiple='item.layout.multiple'
            )
            WechatArticleSelector(
              v-else-if='item.layout.component === "wechat_articles"',
              v-decorator='[item.key, { rules: getRules(item), initialValue: formData[item.key] }]',
              :disabled='isDisabled(item)',
              :wechat='item.layout.wechat || {}'
            )
            WechatArticleEditor(
              v-else-if='item.layout.component === "wechat_articles_editor"',
              v-decorator='[item.key, { rules: getRules(item), initialValue: formData[item.key] || [] }]',
              :disabled='isDisabled(item)',
              :wechat='item.layout.wechat || {}'
            )
            TagField(
              v-else-if='item.layout.component === "tag"',
              v-decorator='[item.key, { rules: getRules(item), initialValue: formData[item.key] || [] }]',
              :disabled='isDisabled(item)'
            )
            StoreField(
              v-else-if='item.layout.component === "store_field"',
              v-decorator='[item.key, { rules: getRules(item), initialValue: formData[item.key] || [] }]',
              :disabled='isDisabled(item)',
              :multiple='item.layout.multiple',
              :recordName='item.name',
              :store='item.model.store',
              :storeConfig='item.model.storeConfig',
            )
            FormDesignerField(
              v-else-if='item.layout.component === "form_designer"',
              v-decorator='[item.key, { rules: getRules(item) }]',
            )
            MajorField(
              v-else-if='item.layout.component === "major"',
              v-decorator='[item.key, { rules: getRules(item) }]',
              :disabled='isDisabled(item)',
              :multiple='item.layout.multiple',
            )
            LevelTwoCollegeField(
              v-else-if='item.layout.component === "level_two_college"',
              v-decorator='[item.key, { rules: getRules(item) }]',
              :disabled='isDisabled(item)',
              :multiple='item.layout.multiple',
            )
            TeacherField(
              v-else-if='item.layout.component === "teacher"',
              v-decorator='[item.key, { rules: getRules(item) }]',
              :disabled='isDisabled(item)',
              :multiple='item.layout.multiple',
            )
            StudentField(
              v-else-if='item.layout.component === "student"',
              v-decorator='[item.key, { rules: getRules(item) }]',
              :disabled='isDisabled(item)',
              :multiple='item.layout.multiple',
            )
            CourseDirField(
              v-else-if='item.layout.component === "course_dir"',
              v-decorator='[item.key, { rules: getRules(item) }]',
              :disabled='isDisabled(item)',
              :multiple='item.layout.multiple',
            )
            DepartmentField(
              v-else-if='item.layout.component === "department_new"',
              v-decorator='[item.key, { rules: getRules(item) }]',
              :disabled='isDisabled(item)',
              :multiple='item.layout.multiple',
              :nested='false'
            )
            DepartmentField(
              v-else-if='item.layout.component === "department_new_nested"',
              v-decorator='[item.key, { rules: getRules(item) }]',
              :disabled='isDisabled(item)',
              :multiple='item.layout.multiple',
              :nested='true'
            )
            CourseSetField(
              v-else-if='item.layout.component === "course_set"',
              v-decorator='[item.key, { rules: getRules(item) }]',
              :disabled='isDisabled(item)',
              :multiple='item.layout.multiple',
              :nested='false'
            )
            TimeRangeSwitchInput(
              v-else-if='item.layout.component === "time_range_switch_input"',
              v-decorator='[item.key, { rules: getRules(item), initialValue: formData[item.key] || [null, null] }]',
              :disabled='isDisabled(item)',
              @confirm='onFieldConfirm'
            )
            //- 请购
            BudgetLockCreateField(
              v-else-if='item.layout.component === "budget_lock_create"'
              v-decorator='[item.key, { rules: getRules(item), initialValue: formData[item.key] || [] }]',
              :name='item.key',
              :disabled='isDisabled(item) || !projectId',
              :projectId='projectId'
            )
            //- 请购&金额
            BudgetLockAmountField(
              v-else-if='item.layout.component === "budget_lock_amount"'
              v-decorator='[item.key, { rules: getRules(item), initialValue: formData[item.key] || [] }]',
              :name='item.key',
              :disabled='isDisabled(item) || !projectId',
              :projectId='projectId'
            )
            //- 调增
            BudgetAdjustField(
              v-else-if='item.layout.component === "budget_adjust"'
              v-decorator='[item.key, { rules: getRules(item), initialValue: formData[item.key] || [] }]',
              :name='item.key',
              :disabled='isDisabled(item) || !projectId',
              :projectId='projectId'
            )
             //- 调减
            BudgetAdjustField(
              v-else-if='item.layout.component === "budget_adjust_negative"'
              v-decorator='[item.key, { rules: getRules(item), initialValue: formData[item.key] || [] }]',
              :name='item.key',
              :disabled='isDisabled(item) || !projectId',
              :projectId='projectId'
              :positive='false'
            )
            BudgetCreateField(
              v-else-if='item.layout.component === "budget_create"'
              v-decorator='[item.key, { rules: getRules(item), initialValue: formData[item.key] || [] }]',
              :name='item.key',
              :disabled='isDisabled(item)',
            )
            FinanceProjectField(
              v-else-if='item.layout.component === "finance_project"'
              v-decorator='[item.key, { rules: getRules(item), initialValue: formData[item.key] || [] }]',
              :name='item.key',
              :multiple='item.layout.multiple',
              :disabled='isDisabled(item)',
              :instance='instance',
            )
            FinanceProjectAdminAllField(
              v-else-if='item.layout.component === "finance_project_admin_all"'
              v-decorator='[item.key, { rules: getRules(item), initialValue: formData[item.key] || [] }]',
              :name='item.key',
              :multiple='item.layout.multiple',
              :disabled='isDisabled(item)',
            )
            FinanceProjectOwnField(
              v-else-if='item.layout.component === "finance_project_own"'
              v-decorator='[item.key, { rules: getRules(item), initialValue: formData[item.key] || [] }]',
              :name='item.key',
              :multiple='item.layout.multiple',
              :disabled='isDisabled(item)',
            )
            FinanceProjectExecuteField(
              v-else-if='item.layout.component === "finance_project_execute"'
              v-decorator='[item.key, { rules: getRules(item), initialValue: formData[item.key] || [] }]',
              :name='item.key',
              :multiple='item.layout.multiple',
              :disabled='isDisabled(item)',
            )
      //- last child
      slot
    .actions(v-if='showActions')
      slot(name='actions')
        a-button(@click='cancel', size='large')
          | 取消
        a-button(
          ref='submitBtn',
          type='primary',
          htmlType='submit',
          :loading='loading',
          :disabled='disabled',
          size='large'
        )
          | {{ formData.id ? " 更新 " : " 创建 " }}
</template>

<style lang="stylus">
.ant-input[disabled]
  color rgba(0, 0, 0, 0.65)

.ant-form-item-children
  display block
</style>

<style lang="stylus" scoped>
.form-content
  position relative
  height 100%
  line-height 20px
  .template-form
    overflow-x hidden
    overflow-y auto
    box-sizing border-box
    height 100%
    .form-item
      margin-bottom 16px
      // min-height 80px
      width 100%
      .form-widget-label
        display flex
        align-items center
        overflow hidden
        width 100%
        background #f4f4f4
        white-space pre-wrap
        font-weight 500
        font-size 16px
      .picker
        width 100% !important
      .boundary
        margin-top 16px
        margin-bottom 8px
        border-bottom 1px solid #eee
        line-height 1.4
      .append
        margin-left 10px
      .option-item
        margin-bottom 8px
      .rich-editor
        height calc(100vh - 224px)
  .actions
    position sticky
    bottom 0
    left 0
    z-index 10
    padding 8px 0px
    width 100%
    background #fff
    text-align right
    button
      margin-left 10px
.text-button
  color #808080
  margin-right 14px
</style>
