<script lang="ts">
import department, { IDepartment } from '@/models/department';
import Axios, { AxiosResponse } from 'axios';
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import { resTeacherDepartmentStore } from '../../store/modules/res/teacher/department.store';

@Component({
  components: {},
})
export default class DepartmentField extends Vue {
  @Model('change') value!: number[];

  @Prop({ type: Boolean, default: false }) disabled!: boolean;
  @Prop({ type: Boolean, default: false }) multiple!: boolean;
  @Prop({ type: Boolean, default: false }) nested!: boolean;

  departments: IObject[] = [];
  departmentMap: IObject = {};
  departmentNestedIds: number[] = [];
  existedDepartements: IDepartment[] = [];

  get store() {
    return resTeacherDepartmentStore;
  }

  get localValue() {
    return this.value;
  }

  set localValue(val: number | number[]) {
    if (this.nested) {
      const ary = typeof val === 'number' ? [val] : val;
      this.departmentNestedIds = [];
      this.setNestedIds(ary, { children: this.departments }, false);
      this.$emit('change', this.departmentNestedIds);
    } else {
      this.$emit('change', val);
    }
  }

  @Watch('value', { immediate: true })
  onValueChange() {
    if (this.disabled) {
      this.fetchExistDepartments();
    }
  }

  mounted() {
    if (!this.disabled) {
      this.fetchDepartments();
    }
  }

  async fetchDepartments() {
    this.store.init();
    const { data } = await this.store.sendCollectionAction({
      action: 'tree',
    });
    const str = JSON.stringify(data.departments);
    const newStr = str.replace(/"id":/g, '"value":').replace(/"short_name":/g, '"label":');
    this.departments = JSON.parse(newStr);
  }

  setNestedIds(selected: number[], department: IObject, mark = false) {
    department.children?.forEach((child: IObject) => {
      if (selected.includes(child.value) || mark) {
        this.departmentNestedIds.push(child.value);
        this.setNestedIds(selected, child, true);
      } else {
        this.setNestedIds(selected, child, mark);
      }
    });
  }

  fetchExistDepartments() {
    this.store.init();
    this.store.model.index({ per_page: this.value.length, q: { id_in: this.value } }).then((res: AxiosResponse) => {
      this.existedDepartements = res.data[this.store.model.dataIndexKey];
    });
  }
}
</script>

<template lang="pug">
.department-field
  template(v-if='disabled')
    a-tag(
      v-for='record in existedDepartements',
      color='blue',
      :key='record.id',
      :closable='false',
    )
      span {{ record.name }}
  template(v-else)
    a-tree-select(
      v-model='localValue',
      style='width: 100%',
      :dropdownStyle='{ maxHeight: "400px", overflow: "auto" }',
      :treeData='departments',
      :multiple='multiple',
      :disabled='disabled',
      placeholder='请选择部门',
      treeDefaultExpandAll,
      allowClear,
      showSearch,
      treeNodeFilterProp='label',
      :maxTagCount='4'
    )
</template>
<style lang="stylus" scoped></style>
