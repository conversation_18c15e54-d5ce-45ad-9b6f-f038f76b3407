<script lang="ts">
import { IFormTemplateItem } from '@/interfaces/formTemplate.interface';
import { Component, Vue, Prop, Watch, Model } from 'vue-property-decorator';
import { cloneDeep } from 'lodash-es';

@Component({
  components: {},
})
export default class TemplateFormatter extends Vue {
  @Model('change') private formattedTemplate!: IFormTemplateItem[];
  @Prop({ type: Object, required: true }) payload!: IObject;
  @Prop({ type: Array, required: true }) template!: IFormTemplateItem[];

  lastField: IObject = [];
  indexShouldHide: number[] = [];

  get templateExceptModel() {
    return this.template.map(item => {
      return Object.keys(item).reduce((out, key) => {
        if (!(key === 'model')) {
          out[key] = (item as IObject)[key];
        }
        return out;
      }, {} as IObject);
    });
  }

  @Watch('payload', { deep: true, immediate: true })
  handlePayloadChange() {
    this.formatTemplate(this.payload);
  }

  @Watch('templateExceptModel', { deep: true, immediate: true })
  handleTemplateChange() {
    this.formatTemplate(this.payload);
  }

  clearHideFormValue(payload: IObject) {
    const result = { ...payload };

    this.indexShouldHide.forEach(index => {
      const key = this.template[index].key!;
      // 如果在更新后的表单中没有出现该 key，移除 form 的对应值
      if (!this.formattedTemplate.find(item => item.key === key)) {
        delete result[key];
      }
    });

    return result;
  }

  formatTemplate(fields: IObject) {
    //
    // fields:
    // {
    //   input_1591863850579: {
    //     dirty: false
    //     errors: undefined
    //     name: "input_1591863850579"
    //     touched: true
    //     validating: false
    //     value: "1"}
    // }
    //  or
    // payload
    this.lastField = Object.assign(this.lastField, fields);
    const ifContainerItems = this.template.filter(item => item.layout.component === 'if_container');
    // 条件块内 item 默认隐藏
    let indexShouldHide: number[] = ifContainerItems
      .map(item => item.layout.templateIndexAry as number[])
      .reduce((o, v) => (o as number[]).concat(v as number | number[]), []);

    ifContainerItems.forEach(item => {
      const targetValue = this.lastField[item.layout.conditionKey as string];
      if (
        // 改变项 fields 含有 if_container 的 conditionKey 且 改值符合数据
        Object.keys(this.lastField).includes(item.layout.conditionKey as string) &&
        ((targetValue && targetValue.value) || targetValue) === item.layout.conditionValue
      ) {
        // 符合条件，去掉隐藏
        indexShouldHide = indexShouldHide.filter(i => !(item.layout.templateIndexAry as number[]).includes(i));
      }
    });
    this.indexShouldHide = indexShouldHide;
    const result: IFormTemplateItem[] = this.template.filter((_, index) => !indexShouldHide.includes(index));

    this.$emit('change', result);
  }
}
</script>

<template lang="pug">
.template-formatter
</template>
