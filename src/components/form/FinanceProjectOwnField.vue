<script lang="ts">
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import StoreJsonField from './StoreJsonField.vue';
import { financeTeacherOwnActivitiesProjectStore } from '@/store/modules/finance/teacher/own/project.store';

@Component({
  components: {
    StoreJsonField,
  },
})
export default class FinanceProjectOwnField extends Vue {
  @Model('change') value!: number | number[];

  @Prop({ type: Boolean, default: false }) disabled!: boolean;
  @Prop({ type: Boolean, default: false }) multiple!: boolean;

  get store() {
    return financeTeacherOwnActivitiesProjectStore;
  }

  get config() {
    return {
      initParams: {
        params: {
          q: {
            // owner_id_eq: this.$store.state.currentUser.id,
            state_in: ['pending', 'approving', 'using'],
          },
        },
      },
      tableColumns: [
        { title: '部门名称', dataIndex: 'department_name', type: 'string' },
        { title: '名称', dataIndex: 'name', type: 'string', search: true },
        { title: 'uid', dataIndex: 'uid', type: 'string', search: true },
        { title: '负责人', dataIndex: 'owner_name', type: 'string', search: true },
        { title: '总金额', dataIndex: 'amount', type: 'string', search: true },
      ],
    };
  }

  get localValue() {
    return this.value;
  }

  set localValue(val: number | number[]) {
    this.$emit('change', val);
  }
}
</script>

<template lang="pug">
.source-dir-field
  StoreJsonField(
    v-model='localValue',
    recordName='资金卡',
    :store='store',
    :storeConfig='config',
    :disabled='disabled',
    :multiple='multiple',
  )
</template>

<style lang="stylus" scoped></style>
