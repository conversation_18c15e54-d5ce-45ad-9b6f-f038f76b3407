<script lang="ts">
import budget, { IBudget } from '@/models/finance/budget';
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import { financeTeacherOwnBudgetStore } from '@/store/modules/finance/teacher/own/budget.store';
import StoreField from './StoreField.vue';
import { financeTeacherBudgetsStore } from '@/store/modules/finance/teacher/budgets.store';

@Component({
  components: {
    StoreField,
  },
})
export default class BudgetAdjustField extends Vue {
  @Model('change') value!: { id: number; delta: number }[];
  @Prop({ type: [Number, String], required: true }) projectId!: number | string;
  @Prop({ type: Boolean, default: false }) disabled!: boolean;
  @Prop({ type: Boolean, default: true }) positive!: boolean;

  budgets: IBudget[] = [];
  budgetsDeltaMapping: IObject = {};

  get store() {
    return financeTeacherBudgetsStore;
  }

  get config() {
    return {
      initParams: {
        parents: [{ type: 'projects', id: this.projectId }],
      },
      tableColumns: [
        { title: '二级内容', dataIndex: 'catalog_name', type: 'string', search: true },
        { title: '三级内容', dataIndex: 'name', type: 'string', search: true },
        { title: '科目', dataIndex: 'subject_name', type: 'string', search: true },
        {
          title: '来源',
          dataIndex: 'origin_name',
          customRender: (val: string) => val || '资金池统筹',
          type: 'string',
          search: true,
        },
        { title: '金额', dataIndex: 'amount', type: 'number' },
      ],
    };
  }

  get existBudgetIds() {
    return (this.value || []).map(item => item.id);
  }

  get budgetsWithDelta() {
    return this.budgets.map(budget => ({
      ...budget,
      delta: this.budgetsDeltaMapping[`${budget.id}`],
    }));
  }

  get returnValue() {
    return this.budgetsWithDelta.map(budget => ({
      id: budget.id,
      delta: (this.positive ? 1 : -1) * budget.delta || 0,
    }));
  }

  mounted() {
    this.budgetsDeltaMapping = this.value.reduce((out, item) => {
      out[`${item.id}`] = Math.abs(item.delta);
      return out;
    }, {} as IObject);
  }

  @Watch('returnValue', { deep: true })
  onReturnValueChange() {
    this.$emit('change', this.returnValue);
  }

  getBalanceAmount(record: IBudget) {
    return Number(record.amount) - Number(record.processing_payment_amount) - Number(record.completed_payment_amount);
  }

  syncBudgets(val: IBudget[]) {
    this.budgets = val;
  }
}
</script>

<template lang="pug">
.budget-adjust-field
  StoreField(
    :value='existBudgetIds',
    recordName='教师',
    :store='store',
    :storeConfig='config',
    :disabled='disabled',
    :multiple='true',
    @selectedRecordsChange='syncBudgets'
  )
    template(#tags)
      .empty
  a-table(:dataSource='budgetsWithDelta', :pagination='{ hideOnSinglePage: true }', :scroll='{ x: true }')
    a-table-column(:title='positive ? "预算增加金额" : "预算减少金额"', :width='120')
      template(slot-scope='value, record')
        .flex
          .positive(v-if='positive') +
          .negative(v-else) -
          a-input-number(
            v-model='budgetsDeltaMapping[`${record.id}`]',
            :max='positive || disabled ? Infinity : getBalanceAmount(record)',
            :min='0',
            :disabled='disabled'
          )
    a-table-column(title='二级内容', dataIndex='catalog_name', :width='150')
    a-table-column(title='三级内容', dataIndex='name', :width='130')
      template(slot-scope='name')
        a-tooltip(:title='name')
          .three-line(style='max-width: 130px')
            | {{ name }}
    a-table-column(title='科目', dataIndex='subject_name', :width='110')
    a-table-column(title='来源', dataIndex='origin_name', :width='110')
      template(slot-scope='origin_name')
        | {{ origin_name || "资金池统筹" }}
    a-table-column(title='金额', dataIndex='amount', :width='130', align='right')
      template(slot-scope='amount')
        span {{ amount | toCurrency }}
    a-table-column(title='金额详情', :width='180')
      template(slot-scope='record')
        .info 可报销：{{ getBalanceAmount(record) | toCurrency }}
        .info 报销中：{{ record.processing_payment_amount | toCurrency }}
        .info 已报销：{{ record.completed_payment_amount | toCurrency }}
        .info 已锁定：{{ record.locking_amount | toCurrency }}
        .info(v-if='record.sub_project_amount > 0') 已分配：{{ record.sub_project_amount | toCurrency }}
</template>

<style lang="stylus" scoped>
.flex
  .positive, .negative
    margin-right 10px
    color red
</style>
