<script lang="ts">
import ActiveModel, { IModel } from '@/lib/ActiveModel';
import { ActiveStore } from '@/lib/ActiveStore';
import { FormsResourceInfoFindByIds } from '@/models/find_by_ids.api';
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import { TaIndexViewTabInterface } from '../global/TaIndex';
import TaIndexView from '../global/TaIndexView.vue';

@Component({
  components: {},
})
export default class StoreField<T extends IModel> extends Vue {
  @Model('change') value!: number[];

  @Prop({ type: String }) readonly recordName!: string;
  @Prop({ type: Boolean, default: false }) readonly disabled!: boolean;
  @Prop({ type: Boolean, default: false }) readonly multiple!: boolean;
  @Prop({ type: Object, required: true }) store!: ActiveStore<T>;
  @Prop({ type: Object, default: () => ({}) }) storeConfig!: IObject;
  @Prop({ type: Array }) tabs!: TaIndexViewTabInterface[];
  @Prop({ type: Object, default: () => ({}) }) jsonConfig!: IObject;

  selectedRecordsRaw: T[] = [];
  visible = false;
  oldValue: number[] = [];

  get selectedRecordIds() {
    return this.selectedRecords.map(i => i.id);
  }

  get resourceModel() {
    return new FormsResourceInfoFindByIds();
  }

  get selectedRecords() {
    return this.selectedRecordsRaw;
  }

  set selectedRecords(val) {
    this.selectedRecordsRaw = val;
    this.$emit('selectedRecordsChange', val);
  }

  get config() {
    return {
      recordName: this.recordName,
      store: this.store,
      showSelectionByDefault: true,
      mode: 'table',
      searcherSimpleOptions: this.searcherOptions,
      searcherComplicatedOptions: this.searcherOptions,
      tableConfig: {
        scroll: { y: '60vh' },
      },
    };
  }

  get initParams() {
    return this.storeConfig.initParams || {};
  }

  get tagKey() {
    return this.storeConfig.tagKey || 'name';
  }

  get tableColumns() {
    return this.storeConfig.tableColumns || [];
  }

  get searcherOptions() {
    return this.tableColumns
      ?.filter((column: IObject) => column.search)
      ?.map(
        (column: IObject) =>
          ({
            label: column.title,
            key: column.searchKey || column.dataIndex,
            type: column.type || 'string',
          } || []),
      );
  }

  @Watch('value')
  onValueChange() {
    if (JSON.stringify(this.value) === JSON.stringify(this.oldValue)) {
      return;
    }
    if (this.value && Object.getPrototypeOf(this.store.model).constructor !== ActiveModel) {
      const ids = this.value.filter(i => i);
      this.fetchExistRecords(ids);
      this.oldValue = ids;
    }
  }

  @Watch('visible')
  handleVisibleChange() {
    if (this.visible) {
      this.store.reset();
      this.$nextTick(() => {
        this.store.init(this.initParams);
        (this.$refs.taIndexView as TaIndexView<T>).fetchData();
      });
      this.$emit('openModal');
    }
  }

  @Watch('initParams')
  handleInitParamsChange() {
    // 这里特殊处理，暂时没办法，在表单中会因为parents参数里的特殊情况导致initParams变化，这里又触发了调用，否则会出现异常
    if (this.initParams.parents && this.initParams.parents[0] && !this.initParams.parents[0].id) {
      return;
    } else {
      this.store.init(this.initParams);
    }
  }

  mounted() {
    this.store.reset();
    this.store.init(this.initParams);
    this.onValueChange();
  }

  async fetchExistRecords(ids: (string | number)[]) {
    if (JSON.stringify(ids.sort()) === JSON.stringify(this.selectedRecords.map(i => i.id).sort())) return;
    if (ids.length === 0) {
      this.selectedRecords = [];
    } else {
      const { data } =
        Object.keys(this.jsonConfig).length > 0
          ? await this.resourceModel.create({
              path: this.store.model.resourcePath,
              ids: ids,
              config: this.jsonConfig,
            })
          : await this.store.model.index({ per_page: ids.length, q: { id_in: ids } });

      this.selectedRecords =
        Object.keys(this.jsonConfig).length > 0 ? data['records'] : data[this.store.model.dataIndexKey];
    }
  }

  removeRecord(index: number) {
    if (this.disabled) {
      return;
    }
    this.selectedRecords.splice(index, 1);
    this.syncValue();
  }

  syncValue() {
    // 单选多选都返回数组
    this.$emit('change', this.selectedRecordIds);
  }

  openSelector() {
    this.visible = true;
    this.store.init(this.initParams);
    this.$emit('open');
  }

  handleOk() {
    this.visible = false;
    this.syncValue();
    this.$emit('ok', this.selectedRecords);
  }

  handleCancel() {
    this.visible = false;
    this.onValueChange();
    this.$emit('cancel');
  }

  onSelect(newValue: T[], oldValue: T[]) {
    // 单选
    if (!this.multiple) {
      this.selectedRecords = newValue.filter(item => !oldValue.includes(item));
    }
    this.$emit('onSelect', newValue, oldValue);
  }

  clearUpRecord() {
    this.selectedRecords = [];
    this.syncValue();
  }

  onTabChange(activeTab: TaIndexViewTabInterface) {
    this.$emit('tabChange', activeTab);
  }
}
</script>

<template lang="pug">
.store-field
  slot(name='tags', :records='selectedRecords')
    a-tag.tag(
      v-for="(record, index) in selectedRecords"
      color="blue"
      :key="record.id"
      :closable="!disabled"
      @close="removeRecord(index)"
    )
      slot(name='tag-text', :record='record')
        span {{ record[tagKey] }}
  slot(name='button', :disabled='disabled',:openSelector='openSelector')
    a-button(
      v-if="!disabled"
      icon="edit"
      :disabled="disabled"
      @click.stop="openSelector"
    )
      | {{ selectedRecords.length > 0 ? '修改' : '选择' }}
  a-modal(
    v-model='visible',
    :title='`选择${recordName}`',
    width='90%',
    @ok='handleOk',
    @cancel='handleCancel',
  )
    .modal
      .table
        TaIndexView.ta-index-view(
          v-model='selectedRecords',
          ref='taIndexView',
          :config='config',
          :tabs='tabs',
          @tabChange='onTabChange'
          @onSelect='onSelect'
        )
          template(v-slot:[`${tab.key}_tab`], v-for='tab in tabs')
            slot(:name='`${tab.key}_tab`')
          template(#header, v-if='!tabs')
            slot(name='modal-header')
              .header 选择
          template(#actions='{ record, selectedRecords }')
            slot(name='actions', :record='record', :selectedRecords='selectedRecords')
          template(#table)
            a-table-column(
              v-for='(column, index) in tableColumns',
              :key='index',
              :title='column.title',
              :customRender='column.customRender'
              :dataIndex='column.dataIndex'
            )
      .exist-list
        .exist-list-header
          span 已选（{{ selectedRecords.length }}）
          IconTooltip(icon='delete', tips='清空', @click='clearUpRecord')
        .exist-group
          .item(v-for="(record, index) in selectedRecords")
            a-popover
              template(#content)
                .label {{ record[tagKey] }}
              .one-line.label {{ record[tagKey] }}
            IconTooltip(icon='close-circle', tips='删除', @click='removeRecord(index)')
</template>

<style lang="stylus" scoped>
.ta-index-view
  padding 0 10px
.modal
  height 75vh
  display flex
  .table
    border 1px solid #e8e8e8
    .header
      margin-left 10px
  .exist-list
    overflow hidden
    height 100%
    padding 10px
    border 1px solid #e8e8e8
    flex 0 0 240px
    margin-left 20px
    color #888
    .exist-list-header
      display flex
      justify-content space-between
      margin 0 10px 10px 0
    .exist-group
      height 100%
      overflow-y scroll
      padding-bottom 50px
      .item
        display flex
        justify-content space-between
        margin-bottom 4px
        padding 10px 10px 10px 0
        line-height 24px
</style>
