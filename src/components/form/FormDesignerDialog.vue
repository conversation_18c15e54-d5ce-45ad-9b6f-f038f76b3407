<script lang="ts">
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import FormDesigner from './FormDesigner.vue';
import { cloneDeep } from 'lodash/fp';

@Component({
  components: {
    FormDesigner,
  },
})
export default class FormDesignerDialog extends Vue {
  @Model('input', { type: Boolean, default: false }) value!: boolean;
  @Prop({ type: Array, default: () => [] }) fields!: object[];

  formFields: object[] = [];

  @Watch('value', { immediate: true })
  watchValue() {
    if (Array.isArray(this.fields)) {
      this.formFields = cloneDeep(this.fields || []);
    }
  }

  get visible() {
    return this.value;
  }
  set visible(v: boolean) {
    this.$emit('input', v);
  }

  confirm() {
    this.$emit('change', this.formFields);
    this.visible = false;
  }
}
</script>

<template lang="pug">
MainModal(
  v-model="visible"
  :destroyOnClose="true"
  title="模板表单编辑器"
  width="95%")
  .fields-designer
    FormDesigner(v-model="formFields")
  .fields-footer(slot="footer")
    a-button(size="large" @click="visible = false")
      | 取消
    a-button(size="large" type="primary" @click="confirm")
      | 确认
</template>

<style lang="stylus" scoped>
.fields-designer
  height 100%
.fields-footer
  width 100%
  text-align right
</style>
