<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class ComPrint extends Vue {
  @Prop({ type: String, default: '' }) prop!: string;
  table = {
    name: '',
    gender: '',
    dateBirth: '',
    political: '',
    level: '',
    current: '',
    report: '',
  };

  public print() {
    window.print();
  }
}
</script>

<template lang="pug">
.print-box
  .header
    .word-item-name 附件五
    .word-title 年度考核登记表
  .year
    span.left （
    span 年度）
  .unit 单位：
  .table-box.table-box-one
    .table-box-one-box
      .table-header
        .name-box.width-27
          .item-title 姓名
          .item-val {{table.name}}
        .gender-box.width-27
          .item-title 性别
          .item-val {{table.gender}}
        .date-birth-box.width-46
          .item-title 出生年月
          .item-val {{table.dateBirth}}
        .political-box.width-27
          .item-title 政治面貌
          .item-val {{table.political}}
        .level-box.width-27
          .item-title 文化程度
          .item-val {{table.level}}
        .current-box.width-46
          .item-title 现任职务（岗位）
          .item-val {{table.current}}
      .table-main
        .main-header 本人述职报告（总结）

  .table-box.table-box-two
    .table-box-two-main
      .item
        .title-box
          .span 主管领导
          .span 评鉴意见
        .main-box
          //- 内容
      .item
        .title-box
          .span 考核机构
          .span 审核意见
        .main-box
          //- 内容
      .item
        .title-box
          .span.line-6 单位确定等次
        .main-box
          //- 内容
      .item
        .title-box
          .span.line-6 被考核人意见
        .main-box
          //- 内容
      .item
        .title-box
          .span 复核意见
        .main-box
          //- 内容
      .item
        .title-box
          .span 申诉审理
          .span.line-4 结果
        .main-box
          //- 内容
    .table-footer 上海市人事局印制
  .footer
      a-button(type="primary" size="large" @click="print") 打印

</template>

<style lang="stylus" scoped>
.footer
  position fixed
  bottom 0px
  width 100vw
  height 50px
  background #eeeeee
  display flex
  padding 0 40px
  left 0
  justify-content flex-end
.print-box
  display block
  width 100
  padding 70px 200px 0
  .header
    position relative
    font-size 20px
    font-weight bold
    height 40px
    line-height 40px
    .word-item-name
      position absolute
    .word-title
      width 100%
      letter-spacing 2em
      text-align center
  .year
    height 30px
    line-height 30px
    text-align center
    margin-top 70px
    .left
      margin-right 4em
  .unit
    height 20px
    line-height 20px
    margin-top 40px
    margin-bottom 10px

.table-box-one

  // height calc(1090px - 220px)
  // height 1300px
  width 100%
  padding-bottom 150px
  height 1280px
  .table-box-one-box
    height 1130px
    border 1px solid #000
    width 100%
  .table-header
    height 80px
    width 100%
    overflow hidden
    .width-27
      width 27%
      float left
      height 38px
      line-height 38px
      border-right 1px solid #000
      border-bottom 1px solid #000
    .width-46
      float left
      height 38px
      line-height 38px
      width 46%
      border-bottom 1px solid #000
    .item-title
      width 50%
      height 38px
      font-weight bold
      padding  0 3px
      display: inline-block;
      border-right 1px solid #000
      justify-content space-between
      text-align: justify;
    .item-title:after
      display: inline-block;
      width: 100%;
      content: '';
  .table-main
    padding 10px
    .main-header
      text-decoration underline
      text-align center
.table-box-two
  margin-top 70px
  height 1400px
  padding-top 150px
  .table-box-two-main
    border 1px solid #000
    width 100%
    // height 100%
  .item
    height 140px
    border-bottom 1px solid #000
    .title-box
      width 10%
      border-right 1px solid #000
      height 100%
      text-align justify
      display flex
      justify-content center
      align-items center
      .span
        width 1em
        line-height 24px
        margin 0 6px
      .line-4
        line-height 72px
      .line-6
        line-height 20px

.table-footer
  margin-right 50px
  float right
  letter-spacing: 0.8em


@media print
  .print-box
    width 100%
    height unset
    margin 0 auto
    background #fff
    visibility visible
    padding 70px 100px
    .footer
      display none

@page
  size a4 portrait
</style>
