<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { IStudent } from '../../models/student';

@Component({
  components: {},
})
export default class StudentTags extends Vue {
  @Prop({ type: Array, default: () => [] }) students!: IStudent[];
  @Prop({ type: Boolean, default: false }) removeable!: boolean;

  get classStudents() {
    return this.$utils.groupBy(this.students, 'adminclass_name');
  }

  removeStudent(student: IStudent, index: number) {
    this.$emit('remove', student, index);
  }
}
</script>

<template lang="pug">
.student-tags
  Empty(v-if="students.length === 0" desc="暂无学生")
  .class(v-for="(stds, className) in classStudents" :key="className")
    .class-name {{ className && className !== 'null' ? className : '无班级信息' }}
    .students
      .student(v-for="(student, index) in stds" :key="student.id")
        .remove-btn(@click="removeStudent(student, index)" v-if="removeable")
          a-icon(type="close")
        .tag.ta-tag-primary
          div {{ student.name }}
          div.code {{ student.code }}
</template>

<style lang="stylus" scoped>
.student-tags
  .class
    padding 16px 0
    .class-name
      padding-left 8px
      font-weight bold
      font-size 18px
    .students
      display flex
      flex-wrap wrap
      overflow hidden
      padding 10px 0
      width 100%
      .student
        position relative
        flex-shrink 0
        padding 8px
        width 12.5%
        &:hover
          .remove-btn
            display block
        .remove-btn
          position absolute
          top 4px
          right 4px
          display none
          width 18px
          height 18px
          border-radius 10px
          background #3da8f5
          color #fff
          text-align center
          font-size 10px
          line-height 16px
        .tag
          padding 10px
          width 100%
          text-align center
          font-weight bold
          .code
            word-break break-all
            font-weight 400
            font-size 13px
</style>
