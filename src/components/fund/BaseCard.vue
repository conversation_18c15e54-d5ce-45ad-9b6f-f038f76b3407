<template lang="pug">
.page
  Panel.info-box
    .info-title 资金卡
    .info-main
      a-row
        a-col(:span="12" v-for="(info, index) in infos" :key="index")
          Cell(:label="info.label" :value="info.value" :isPrice="info.type === 'price'")
            template(slot="value")
              span(v-if="info.key === 'endDate'") {{ info.value }}（剩余{{ totalDays }}天）
  Panel
    .panel-mian
      a-row
        a-col(:span="11")
          .panel-title 二级内容
          .list
            .list-item(v-for="(item, index) in routine_budgets" :key="index"
              :class="{'list-item-active': budget.id === item.id}"
              @click="onSelect(item)")
              .title {{ item.budgetName }}
              .desc
                .cell
                  span 比例：{{ item.proportion }}%
                  span(style="margin: 0px 12px") 预期使用率：{{ item.expect_rate }}%
                  span 实际使用率：{{ item.reality_rate }}%
                .cell
                  span 金额：￥{{ item.amountOfMoney | toCurrency }}
                  span(style="margin-left: 12px") 可报销金额：￥{{ item.avalible_amount | toCurrency }}
              .payment
                IconTooltip(icon="file-search" tips="查看单据" @click="onPayment(item, 'budget')")
        a-col(:span="13")
          .panel-title(style="border-left: 1px #E8E8E8 solid") 「{{ budget.budgetName }}」下三级内容
          .table-box
            AdminTable(
              :data="routine_thirds"
              :totalCount="totalCount"
              :currentPage="currentPage"
              :totalPages="totalPages"
              :perPage="perPage"
              :showHeader="true")
              a-table-column(title="名称" :width="140")
                template(slot-scope="scope")
                  a-tooltip
                    template(slot="title") {{scope.routineBase_third_name}}
                    .title {{ scope.short_name }}
              a-table-column(title="金额" :width="100" align="center")
                template(slot-scope="scope")
                  span ￥{{ scope.amount_money | toCurrency }}
              a-table-column(title="可报销金额" :width="100" align="center")
                template(slot-scope="scope")
                  span ￥{{ scope.avalible_amount | toCurrency}}
              a-table-column(dataIndex="quantity" title="数量" :width="100" align="center")
              a-table-column(title="单价" :width="100" align="center")
                template(slot-scope="scope")
                  span ￥{{ scope.unit_price | toCurrency }}
              a-table-column(dataIndex="is_equiment_dic_name" title="是否设备" :width="100" align="center")
              a-table-column(:width="60")
                template(slot-scope="scope")
                  IconTooltip(icon="file-search" tips="查看单据" @click="onPayment(scope, 'third')")

  //- 单据
  MainModal(
    v-model="visibleModalPayment"
    :width="1000"
    :maskClosable="false")
    .modal-row(slot="title")
      .title {{ select.budgetName || select.routineBase_third_name }}
    PaymentTable(:parentId.sync="select.id" :parent="selectType" v-if="visibleModalPayment")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import Cell from '@/components/global/Cell.vue';
import PaymentTable from '@/components/fund/PaymentTable.vue';
import routineBaseStore from '@/store/modules/routine_base.store';

@Component({
  components: {
    Cell,
    PaymentTable,
  },
})
export default class BaseCard extends Vue {
  private currentPage: number = 1;
  private totalPages: number = 1;
  private totalCount: number = 0;
  private perPage: number = 1000;
  private budget: IObject = {};
  private select: IObject = {};
  private selectType: string = 'budget';
  private visibleModalPayment: boolean = false;
  get routine_base() {
    return routineBaseStore.routineBase || {};
  }
  get infos() {
    return [
      {
        label: '资金卡号',
        key: 'capitalCardNumber',
      },
      {
        label: '负责人',
        key: 'person_in_charge_teacher_name',
      },
      {
        label: '资金卡名称',
        key: 'moneyCardName',
      },
      {
        label: '部门',
        key: 'department_ref_name',
      },
      {
        label: '财务编号',
        key: 'financeNumber',
      },
      {
        label: '部门领导',
        key: 'department_leadership_teacher_name',
      },
      {
        label: '学校',
        key: 'school_dic_name',
      },
      {
        label: '开始日期',
        key: 'startDate',
      },
      {
        label: '金额',
        key: 'amountOfMoney',
        type: 'price',
      },
      {
        label: '结束日期',
        key: 'endDate',
      },
      {
        label: '可报销金额',
        key: 'avalible_amount',
        type: 'price',
      },
      {
        label: '状态',
        key: 'state_dic_name',
      },
    ].map((e: IObject) => ({
      ...e,
      value: this.routine_base[e.key],
    }));
  }
  get routine_budgets() {
    const data: IObject[] =
      (this.routine_base.routine_budgets || []).map((item: IObject) => ({
        ...item,
        proportion: this.$tools.getRate(item.amountOfMoney, this.routine_base.amountOfMoney),
        expect_rate: this.$tools.getRate(item.payment_paid_amount + item.payment_paying_amount, item.amountOfMoney),
        reality_rate: this.$tools.getRate(item.payment_paid_amount, item.amountOfMoney),
      })) || [];
    if (!this.budget.id) {
      this.budget = data[0] || {};
    }
    return this.routine_base.id ? data : [];
  }
  get routine_thirds() {
    const data: object[] = (this.routine_base.routine_thirds || []).filter(
      (e: IObject) => e.zjk_budget_id === this.budget.id,
    );
    return this.budget.id ? data : [];
  }
  get totalDays() {
    return this.$tools.dateDiff(this.routine_base.endDate) || 0;
  }

  public onSelect(val: IObject) {
    if (this.budget.id !== val.id) {
      this.budget = val;
    }
  }

  public onPayment(val: IObject, type: string) {
    this.select = val;
    this.selectType = type;
    this.visibleModalPayment = true;
  }
}
</script>

<style lang="stylus" scoped>
.page
  width 100%
  height 100%
  .info-box
    margin-bottom 12px
    padding 0px 20px
    background #fff
    .info-title
      padding 18px 0px
      border-bottom 1px #E8E8E8 solid
      font-weight 500
    .info-main
      padding 12px 0px
  .panel-title
    padding 18px 20px
    border-bottom 1px #E8E8E8 solid
    font-weight 500
  .panel-mian
    width 100%
    .list
      overflow-y auto
      padding-bottom 20px
      max-height 400px
      width 100%
      height 100%
      .list-item
        position relative
        padding 18px 0px 0px 20px
        width 100%
        border-left 2px #fff solid
        background #fff
        cursor pointer
        .title
          line-height 20px
        .desc
          margin-top 4px
          padding-bottom 18px
          width 100%
          border-bottom 1px #E8E8E8 solid
          color #808080
          line-height 22px
        &:hover
          .payment
            display inline
        .payment
          position absolute
          top 40px
          right 10px
          display none
      .list-item-active
        border-left 2px #3DA8F5 solid
        background #F7FCFF
        color #3DA8F5
    .table-box
      overflow-x auto
      padding 0px 20px
      width 100%
      height 400px
      border-left 1px #E8E8E8 solid
      tr:hover
        .title
          color #3DA8F5
          cursor pointer
      .title
        overflow hidden
        width 120px
        text-overflow ellipsis
        white-space nowrap
</style>
