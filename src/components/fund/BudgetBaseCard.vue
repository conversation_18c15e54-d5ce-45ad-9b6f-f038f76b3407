<template lang="pug">
.page
  Panel.info-box
    .info-title 二级资金卡
    .info-main
      a-row
        a-col(:span="12" v-for="(info, index) in infos" :key="index")
          Cell(:label="info.label" :value="info.value" :isPrice="info.type === 'price'")
  Panel
    .panel-title 「{{ routine_budget.budgetName }}」下三级内容
    .table-box
      AdminTable(
        :data="routine_thirds"
        :totalCount="routine_thirds.length"
        :showHeader="true")
        a-table-column(title="名称" :width="140")
          template(slot-scope="scope")
            a-tooltip
              template(slot="title") {{scope.routineBase_third_name}}
              .title {{ scope.routineBase_third_name }}
        a-table-column(title="金额" :width="100" align="center")
          template(slot-scope="scope")
            span ￥{{ scope.amount_money | toCurrency }}
        a-table-column(title="可报销金额" :width="100" align="center")
          template(slot-scope="scope")
            span ￥{{ scope.avalible_amount | toCurrency}}
        a-table-column(dataIndex="quantity" title="数量" :width="100" align="center")
        a-table-column(title="单价" :width="100" align="center")
          template(slot-scope="scope")
            span ￥{{ scope.unit_price | toCurrency }}
        a-table-column(dataIndex="is_equiment_dic_name" title="是否设备" :width="100" align="center")
        a-table-column(:width="60")
          template(slot-scope="scope")
            IconTooltip(icon="file-search" tips="查看单据" @click="onPayment(scope, 'third')")

  //- 单据
  MainModal(
    v-model="visibleModalPayment"
    :width="1000"
    :maskClosable="false")
    .modal-row(slot="title")
      .title {{ third.routineBase_third_name }}
    PaymentTable(:parentId.sync="third.id" parent="third" v-if="visibleModalPayment")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import Cell from '@/components/global/Cell.vue';
import PaymentTable from '@/components/fund/PaymentTable.vue';
import routineBudgetStore from '@/store/modules/routine_budget.store';

@Component({
  components: {
    Cell,
    PaymentTable,
  },
})
export default class BudgetBaseCard extends Vue {
  private third: IObject = {};
  private visibleModalPayment: boolean = false;
  get routine_budget() {
    return routineBudgetStore.routineBudget || {};
  }
  get routine_thirds() {
    return this.routine_budget.routine_thirds || [];
  }
  get infos() {
    return [
      {
        label: '资金卡号',
        key: 'capitalCardNumber',
      },
      {
        label: '负责人',
        key: 'person_in_charge_teacher_name',
      },
      {
        label: '资金卡名称',
        key: 'moneyCardName',
      },
      {
        label: '二级资金卡名称',
        key: 'budgetName',
      },
      {
        label: '部门',
        key: 'department_ref_name',
      },
      {
        label: '财务编号',
        key: 'financeNumber',
      },
      {
        label: '部门领导',
        key: 'department_leadership_teacher_name',
      },
      {
        label: '学校',
        key: 'school_dic_name',
      },
      {
        label: '金额',
        key: 'amountOfMoney',
        type: 'price',
      },
      {
        label: '可报销金额',
        key: 'avalible_amount',
        type: 'price',
      },
    ].map((e: IObject) => ({
      ...e,
      value: this.routine_budget[e.key],
    }));
  }

  public onPayment(val: IObject, type: string) {
    this.third = val;
    this.visibleModalPayment = true;
  }
}
</script>

<style lang="stylus" scoped>
.page
  width 100%
  height 100%
  .info-box
    margin-bottom 12px
    padding 0px 20px
    background #fff
    .info-title
      padding 18px 0px
      border-bottom 1px #E8E8E8 solid
      font-weight 500
    .info-main
      padding 12px 0px
  .panel-title
    padding 18px 20px
    border-bottom 1px #E8E8E8 solid
    font-weight 500
  .list
    overflow-y auto
    padding-bottom 20px
    max-height 400px
    width 100%
    height 100%
    .list-item
      position relative
      padding 18px 0px 0px 20px
      width 100%
      border-left 2px #fff solid
      background #fff
      cursor pointer
      .title
        line-height 20px
      .desc
        margin-top 4px
        padding-bottom 18px
        width 100%
        border-bottom 1px #E8E8E8 solid
        color #808080
        line-height 22px
      &:hover
        .payment
          display inline
      .payment
        position absolute
        top 40px
        right 10px
        display none
    .list-item-active
      border-left 2px #3DA8F5 solid
      background #F7FCFF
      color #3DA8F5
  .table-box
    overflow-x auto
    padding 0px 20px
    width 100%
    height 400px
    border-left 1px #E8E8E8 solid
    tr:hover
      .title
        color #3DA8F5
        cursor pointer
    .title
      overflow hidden
      width 120px
      text-overflow ellipsis
      white-space nowrap
</style>
