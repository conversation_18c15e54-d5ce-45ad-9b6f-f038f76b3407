<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class AssetFileCell extends Vue {
  @Prop({ type: Object, default: '' }) private item?: Object;
}
</script>

<template lang="pug">
.asset-file-cell
  a.key-box(:href="item.url" target="_blank")
    .asset-head
      img.icon(src="@/assets/images/file/file.png")
      .managerattr {{ item.managerattr }}
    .value {{ item.code }}
    .price {{ item.price }} 元
</template>

<style lang="stylus" scoped>
.asset-file-cell
  padding 16px 0px
  font-size 14px
  line-height 20px
  .key-box
    display grid
    grid-template-columns 1fr 1fr 1fr 1fr 1fr
    margin-right 20px
    min-width 118px
    color #808080
    line-height 20px
    .asset-head
      display flex
      align-items center
    .icon
      margin-right 8px
      width 32px
      height 32px
    .value
      min-width 130px
      color #808080
      text-align right
    .price
      min-width 70px
      float right
      color #808080
      text-align right
</style>
