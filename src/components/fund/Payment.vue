<template lang="pug">
.show-page
  .info-box
    .qrcode-box
      QrCode.qrcode(:data="pathUrl")
      .qrcode-title 单据{{ payment.numberOfDocuments || 0 }}张
    .name {{ type === 'routine' ? '付款凭单' : '外埠出差消费单' }}
    .module
      Cell(label="部门" :value="payment.department_ref_name")
      Cell(label="流水单号" :value="payment.codeNumber")
      Cell(label="创建日期" :value="$moment(payment.insert_d).format('YYYY-MM-DD HH:mm:ss')")
      Cell(label="当前状态" :value="payment.state")
    .module
      a-row(:gutter="20")
        a-col(:span="24")
          Cell(label="一级名称" :value="payment.routine_base_name")
        a-col(:span="24")
          Cell(label="二级名称" :value="payment.routine_budget_name")
        a-col(:span="24")
          Cell(label="三级级名称" :value="payment.routine_third_name")
        a-col(:span="24")
          Cell(label="资金卡号" :value="payment.routine_base_code")
        a-col(:span="24")
          Cell(label="发起人" :value="payment.owner_name")
        a-col(:span="12")
          Cell(label="项目负责人" :value="payment.person_in_charge_teacher_name")
        a-col(:span="12")
          Cell(label="分管负责人" :value="payment.department_leadership_teacher_name")
        a-col(:span="12")
          Cell(label="分管领导" :value="payment.manager_teacher_name")
        a-col(:span="12")
          Cell(label="学院领导" :value="payment.dean_teacher_name")
    .module
      template(v-if="type === 'routine'")
        .module-title 付款信息
        Cell(label="付款方式" :value="payment.payment_type_dic_name")
        Cell(label="收款人（单位）" :value="payment.payeeOrg")
        Cell(label="付款用途" :value="payment.paymentPurpose")
      template(v-else)
        .module-title 出差人信息
        a-row(:gutter="20")
          a-col(:span="12")
            Cell(label="姓名" :value="payment.business_person_name")
          a-col(:span="12")
            Cell(label="部门" :value="payment.department_ref_name")
          a-col(:span="24")
            Cell(label="付款方式" :value="payment.payment_type_dic_name")
          a-col(:span="24" v-if="payment.paymentTypeComment")
            Cell(label="付款方式说明" :value="payment.paymentTypeComment")
          a-col(:span="24")
            Cell(label="事由" :value="payment.reason")
    .module
      .flex-justify-between
        .module-title 报销费用合计
        .price ￥{{ payment.reimbursementAmount | toCurrency }}
      template(v-if="type !== 'routine'")
        OutsideInfo(:data="outsides")
    .module(style="border: none")
      .module-title 单据附件({{ files.length || 0 }})
      div(v-for="(item, index) in files" :key="index" v-if="item.url")
        router-link(:to="getPreviewPath(item)" target="_blank")
          FileCell(:label="item.name" :value="$moment(item.created_at).format('YYYY/MM/DD HH:mm')")
  .flow-box(:style="hasOperationPermit ? 'padding-bottom: 65px' : 'padding-bottom: 0px'")
    Empty(type="school" v-if="steps.length === 0")
    a-steps(direction="vertical" size="small")
      a-step(v-for="(item, index) in steps" :key="index" :title="item.checkType" status="process")
        template(slot="description")
          .state(:style="{color: formatColor(item.checkResult)}") {{ item.checkResult }}
          .desc-box
            .name-box
              span {{ item.operator_name || '系统' }}
              .datetime {{ $moment(item.insert_d).format('YYYY/MM/DD HH:mm:ss') }}
            .option {{ item.opinion }}
  .footer
    .print-box
      router-link(
        :to="printUrl"
        target="_blank")
        a-button(type="primary" size="large" icon="printer" :disabled="!canPrint") 打印
    .flow-action(v-if="hasOperationPermit")
      .action-box(v-for="(event, index) in events" :key="index")
        Popover(
          :value="visible && eventType === event.type"
          placement="top"
          :title="formatTitle(event.type)"
          @change="o => visible = o")
          template(#main)
            .popover-box
              a-textarea(v-model="opinion" placeholder="请输入理由" :autoSize="{ minRows: 2, maxRows: 6 }")
          template(#footer)
            a-button(type="primary" size="large" block @click="onAction(event)")
              | 确认
          a-tooltip
            template(slot='title') {{ event.action }}
            a-button(
              :type="event.type === 'next' ? 'primary' : 'default'"
              size="large"
              :loading="loading"
              :icon="event.type === 'next' ? 'check-circle' : 'close-circle'"
              @click="onReview(event)"
              v-if="$tools.formatRole(event.role) || formatPermit(event)")
              span {{ formatTitle(event.type) }}
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import routinePaymentStore from '@/store/modules/routine_payment.store';
import outsidePaymentStore from '@/store/modules/outside_payment.store';
import storeIndex from '@/store';
import Cell from '@/components/global/Cell.vue';
import QrCode from '@/components/global/QrCode.vue';
import OutsideInfo from '@/components/fund/OutsideInfo.vue';
import FileCell from './FileCell.vue';

@Component({
  components: {
    Cell,
    QrCode,
    FileCell,
    OutsideInfo,
  },
})
export default class Payment extends Vue {
  @Prop({ type: String, default: 'routine' }) private type?: string;
  @Prop({ type: String, default: 'admin' }) private role?: string;
  private visible: boolean = false;
  private loading: boolean = false;
  private opinion: string = '';
  private eventType: string = '';

  get payment() {
    return this.type === 'routine' ? routinePaymentStore.payment : outsidePaymentStore.payment;
  }
  get pathUrl() {
    return this.payment.codeNumber || '';
  }
  get steps() {
    return this.payment.payment_checks || [];
  }
  get events() {
    return this.payment.permit_events || [];
  }
  get files() {
    return this.payment.files || [];
  }
  get outsides() {
    return this.payment.outsides || [];
  }
  get event_roles() {
    return [
      {
        code: this.payment.person_in_charge,
        type: 'person_in_charge', // 负责人
      },
      {
        code: this.payment.manager,
        type: 'manager', // 分管领导
      },
      {
        code: this.payment.dean,
        type: 'dean', // 院长
      },
    ];
  }
  get authCode() {
    const currentUser: IObject = storeIndex.state.currentUser || {};
    return currentUser.code || '';
  }
  get hasOperationPermit() {
    return (
      (this.events || []).some((e: IObject) => this.$tools.formatRole(e.role)) ||
      (this.events || []).some((e: IObject) => this.formatPermit(e))
    );
  }
  get printUrl() {
    return this.role === 'teacher'
      ? `/fund/teacher/payments/${this.payment.id}/print?type=${this.type}`
      : `/fund/payments/${this.payment.id}/print?type=${this.type}`;
  }
  get canPrint() {
    const states: string[] = ['凭证出纳审核通过', '凭证财务记账通过', '凭证已收件', '付款成功'];
    return states.some((e: string) => e === this.payment.state);
  }

  public onReview(val: IObject) {
    this.eventType = val.type;
    this.opinion = val.action;
    this.visible = true;
  }

  public async onAction(val: IObject) {
    this.$confirm({
      title: '确定要继续操作吗？',
      okText: '确定',
      onOk: () => {
        const payment: any = {
          id: this.payment.id,
          event: val.action,
          opinion: this.opinion,
        };
        this.fire(payment);
      },
    });
  }

  public async fire(payment: any) {
    try {
      this.loading = true;
      if (this.type === 'routine') {
        await routinePaymentStore.fire(payment);
        await routinePaymentStore.find(payment.id);
      } else {
        await outsidePaymentStore.fire(payment);
        await outsidePaymentStore.find(payment.id);
      }
      this.loading = false;
      this.$message.success('操作成功');
    } catch (error) {
      this.loading = false;
      this.$message.error('操作失败');
    }
  }

  public formatColor(val: string = ''): string {
    const fail: boolean =
      val.includes('不通过') || val.includes('已退件') || val.includes('付款失败') || val.includes('收回') || false;
    return fail ? '#FF8478' : '#75C940';
  }

  public formatPermit(val: IObject) {
    const permit: IObject = this.event_roles.find((e: IObject) => e.type === val.role) || {};
    return permit.code === this.authCode;
  }

  formatTitle(type: string): string {
    return (
      ({
        next: '通过',
        reject: '驳回',
        rewind: '打回',
      } as IObject)[type] || '未知'
    );
  }
  getPreviewPath(item: any) {
    const { url, name, file_type } = item;
    return `/fund/file_preview?file_url=${url}&file_type=${file_type}&file_name=${encodeURIComponent(name)}`;
  }
}
</script>

<style lang="stylus" scoped>
.show-page
  display flex
  padding-bottom 57px
  width 100%
  height 100%
  .info-box
    position relative
    overflow auto
    padding 0px 32px 18px
    min-width 640px
    width 640px
    height 100%
    border-right 1px #e6e6e6 solid
    .name
      margin 22px 0px 4px
      font-weight 500
      font-size 20px
      line-height 28px
    .module-title
      font-weight 500
      font-size 16px
      line-height 40px
    .module
      padding 14px 0px
      border-bottom 1px #e6e6e6 solid
      .price
        text-align right
        font-weight 500
        font-size 16px
        line-height 40px
    .qrcode-box
      position absolute
      top 24px
      right 20px
      .qrcode
        width 80px
        height 80px
      .qrcode-title
        margin-top 12px
        text-align center
        letter-spacing 1px
        font-size 14px
        line-height 20px
  .flow-box
    overflow auto
    padding 20px 20px 0px
    width 348px
    height 100%
    line-height 20px
    .state
      margin 4px 0px 8px
      line-height 20px
    .desc-box
      margin-bottom 20px
      padding 8px 10px
      border-radius 3px
      background #F5F5F5
      .name-box
        display flex
        justify-content space-between
        align-items center
        .datetime
          color #A6A6A6
          font-size 12px
      .option
        margin-top 6px
        color #A6A6A6
  .footer
    position absolute
    right 0px
    bottom 0px
    display flex
    justify-content flex-end
    width 100%
    border-top 1px #e5e5e5 solid
    background #fff
    .print-box
      display flex
      justify-content flex-end
      padding 12px
      width 100%
      border-right 1px #e5e5e5 solid
    .flow-action
      display flex
      justify-content flex-end
      padding 12px
      min-width 360px
      .action-box
        padding-left 18px
        &:first-child
          padding 0px
    button
      display flex
      justify-content center
      align-items center
      width 100px

.flex-justify-between
  display flex
  justify-content space-between
  align-items center

.popover-box
  padding 12px 16px
</style>
