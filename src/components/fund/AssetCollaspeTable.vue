<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class AssetCollaspeTable extends Vue {
  @Prop({ type: Object, required: true, default: () => {} }) private item?: any;
  activeKey = [];
  asset_info = {
    assets: [],
    code: '01CG20210816',
    price: null,
    supply: null,
  };
  columns = [
    {
      title: '资产分类',
      dataIndex: 'managerattr',
      key: 'managerattr',
    },
    {
      title: '设备名称',
      dataIndex: 'equname',
      key: 'equname',
    },
    {
      title: '单价',
      dataIndex: 'unit_price',
      key: 'unit_price',
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
    },
    {
      title: '总价',
      dataIndex: 'price',
      key: 'price',
    },
    {
      title: '入库单号',
      dataIndex: 'code',
      key: 'code',
      scopedSlots: { customRender: 'code' },
    },
  ];
  data: Array<IObject> = [];
  totalPrice = 0;
  loading = false;

  get customStyle() {
    return 'margin-right: 24px';
  }

  mounted() {
    this.asset_info = this.item;
    this.pushData(this.asset_info);
  }
  get calcTotalPrice() {
    let price = 0;
    this.asset_info.assets.map((asset: any) => {
      price += asset?.price || 0;
    });
    return price;
  }
  pushData(asset_info: any) {
    if (asset_info) {
      this.loading = true;
      this.data = [];
      let key = 0;
      asset_info.assets.map((asset: any) => {
        Object.assign(asset, { key: key });
        key += 1;
        this.data.push(asset);
      });
      this.loading = false;
    }
  }
  public customRow(record: IObject, index: number): object {
    return {
      on: {
        click: () => {
          this.$utils.open(`${record.url}`, '_blank');
        },
      },
    };
  }
  rowClassName() {
    return 'clickable';
  }
}
</script>

<template lang="pug">
.asset-collaspe-table
  a-collapse(v-model='activeKey')
    a-collapse-panel(key='1')
      template(slot='header')
        .header
          .title 采购单
          .code 单号:{{ asset_info.code || "无" }}
          .price 金额:{{ asset_info.price || 0 }}
          .assets_count 入库单数量:{{ asset_info.assets.length || 0 }}
          .company 公司:{{ asset_info.supply || "无" }}
      a-table(
        :columns='columns',
        :dataSource='data',
        :loading='loading',
        :customRow='customRow',
        :pagination='false',
        :rowClassName='rowClassName'
      )
        template(slot='code', slot-scope='text,record,index')
          .code_more
           .text {{text}} 
           a-icon.icon(type="file")
          
</template>

<style lang="stylus" scoped>
.asset-collaspe-table
  background-color white
  .header
    margin-left 20px
    display grid
    grid-template-columns 1fr 2fr 1fr 1fr 3fr
    align-items center
  .clickable
    cursor pointer
  .code_more
    display flex
    align-items center
    .icon
      color #3399ff
</style>
