<template lang="pug">
.cell
  .key-box
    img.icon(src="@/assets/images/file/file.png")
    span {{ label }}
  .value {{ value }}
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class FileCell extends Vue {
  @Prop({ type: String, default: '' }) private label?: string;
  @Prop({ type: String, default: '' }) private value?: string;
}
</script>

<style lang="stylus" scoped>
.cell
  display flex
  justify-content space-between
  align-items center
  padding 16px 0px
  border-bottom 1px #E8E8E8 solid
  font-size 14px
  line-height 20px
  .key-box
    display flex
    align-items center
    margin-right 20px
    min-width 118px
    color #808080
    line-height 20px
    .icon
      margin-right 8px
      width 32px
      height 32px
  .value
    min-width 130px
    color #808080
    text-align right
</style>
