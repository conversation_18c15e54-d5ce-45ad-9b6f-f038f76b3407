<template lang="pug">
.g2-pie(:id="id")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import G2 from '@antv/g2';

@Component({
  components: {},
})
export default class G2HistogramReceipt extends Vue {
  @Prop({ type: Array, default: () => [] }) private charData: any;
  @Prop({ type: String, default: '' }) private id?: string;
  @Prop({ type: Number, default: 320 }) private boxHeight?: number;
  @Prop({ type: Boolean, default: true }) private forceFit?: boolean;
  // data
  private chart: any = null;
  private colors: string[] = ['#75C940', '#6ABDF7'];
  get size() {
    const count = this.charData.length;
    const { clientWidth } = this.$el;
    const width = +((clientWidth - 6 * count) / count).toFixed();
    return width > 30 ? 20 : width;
  }
  @Watch('charData')
  public watchChange() {
    this.chart.destroy();
    this.drawChart();
  }

  public mounted() {
    this.drawChart();
  }
  public drawChart() {
    this.chart = new G2.Chart({
      container: this.id,
      height: this.boxHeight,
      forceFit: this.forceFit,
      animate: true,
      padding: [20, 10, 36, 30],
    });
    this.chart.source(this.charData);
    this.chart.scale('count', {
      formatter: function formatter(val: number) {
        // 在此处设置了， X，Y轴都会生效
        return `${val}单`;
      },
    });
    this.chart.axis('type', {
      // X轴
      label: {
        rotate: 20,
        textStyle: {
          fill: '#808080',
          fontSize: '12',
        },
      },
      tickLine: {
        alignWithLabel: true,
        length: 0,
      },
    });
    this.chart.axis('count', {
      // Y轴
      label: {
        textStyle: {
          fill: '#808080',
        },
      },
    });
    this.chart
      .interval()
      .position('type*count')
      .color('name', this.colors)
      .size(this.size)
      // .label('count', {
      //   textStyle: {
      //     fill: '#CCCCCC',
      //   },
      //   offset: 8,
      // })
      .adjust([
        {
          type: 'dodge',
          marginRatio: 1 / 32,
        },
      ]);
    this.chart.render();
  }
}
</script>

<style lang="stylus" scoped></style>
