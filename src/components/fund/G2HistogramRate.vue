<template lang="pug">
.g2-pie(:id="id")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import G2 from '@antv/g2';

@Component({
  components: {},
})
export default class G2HistogramRate extends Vue {
  @Prop({ type: Array, default: () => [] }) private charData: any;
  @Prop({ type: String, default: '' }) private id?: string;
  @Prop({ type: Number, default: 230 }) private boxHeight?: number;
  @Prop({ type: Boolean, default: true }) private forceFit?: boolean;
  // data
  private chart: any = null;
  private colors: string[] = ['#1FA1FF', '#75C940'];
  // get size() {
  //   const count = this.charData.length;
  //   const clientWidth = this.$el.clientWidth;
  //   const width = +((clientWidth - 6 * count) / count).toFixed();
  //   return width > 20 ? 20 : width;
  // }
  @Watch('charData')
  public watchChange() {
    this.chart.destroy();
    this.drawChart();
  }
  public mounted() {
    this.drawChart();
  }
  public drawChart() {
    this.chart = new G2.Chart({
      container: this.id,
      height: this.boxHeight,
      forceFit: this.forceFit,
      padding: [20, 10, 36, 60],
    });
    this.chart.source(this.charData);
    this.chart.scale('rate', {
      formatter: function formatter(val: number) {
        return `${val}%`;
      },
    });
    this.chart.axis('type', {
      // X轴
      label: {
        rotate: 20,
        textStyle: {
          fill: '#808080',
          fontSize: '12',
        },
      },
      tickLine: {
        alignWithLabel: true,
        length: 0,
      },
    });
    this.chart.axis('rate', false);
    this.chart
      .intervalStack()
      .position('type*rate')
      .color('name', this.colors)
      .label('rate', {
        textStyle: {
          fill: '#cccccc',
        },
        offset: 8,
      })
      .adjust([
        {
          type: 'dodge',
          marginRatio: 1 / 32,
        },
      ])
      .label('rate', (val: any) => val >= 10)
      .size(20);
    this.chart.render();
  }
}
</script>

<style lang="stylus" scoped></style>
