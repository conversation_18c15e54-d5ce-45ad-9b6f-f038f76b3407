<template lang="pug">
Panel.panel
  .panel-tabs
    .tab(v-for="(tab, index) in tabs" :key="index"
      :class="{'tab-active': tabIndex === tab.value}"
      @click="onTab(tab.value)") {{ tab.label }} · {{ tab.count }}
    .filter-box
      Searcher(
        tips="检索金额"
        placeholder="检索报销金额"
        @change="filterReimbursementAmount")
      IconTooltip(tips="导出Excel")
        a-button.export(slot="custom" size="small" v-loading="exportLoading" @click="expertExcel")
          span 导出
          a-icon(type="cloud-download")
      Popover(
        v-model="visibleState"
        placement="topRight"
        :width="160")
        .popover(slot="main")
          .popover-item(v-for="(item, index) in states" :key="index" @click="changeState(item.value)")
            span {{ item.label }}
            a-icon(type="check" v-if="stateIndex === item.value")
        ThDropdown(v-model="visibleState" :title="formatStateTitle" @click="visibleState=!visibleState")
  AdminTable(
    :data="store.payments"
    :totalCount="store.totalCount"
    :currentPage="store.currentPage"
    :totalPages="store.totalPages"
    :perPage="store.perPage"
    :showHeader="true"
    :loading="store.loading"
    :scroll="{ x: 1000 }"
    :hideOnSinglePage="true"
    @rowClick="onShow"
    @paginate="onPaginate")
    a-table-column(dataIndex="codeNumber" title="流水单号" :width="100")
    a-table-column(dataIndex="routine_base_code" title="资金卡号" :width="100" align="center")
    a-table-column(title="资金卡名" :width="160")
      template(slot-scope="scope")
        a-tooltip
          template(slot="title") {{ scope.routine_base_name }}
          .title.text-ellipsis {{ scope.routine_base_name }}
    a-table-column(dataIndex="department_ref_name" title="所属部门" :width="100" align="center")
    a-table-column(title="三级目录名" :width="160")
      template(slot-scope="scope")
        a-tooltip
          template(slot="title") {{ scope.routine_third_name }}
          .title.text-ellipsis {{ scope.routine_third_name }}
    template(v-if="tabIndex === 'outside'")
      a-table-column(title="出差事由" :width="100")
        template(slot-scope="scope")
          a-tooltip
            template(slot="title") {{ scope.reason }}
            .title.text-ellipsis {{ scope.reason }}
      a-table-column(dataIndex="business_person_name" title="出差人" :width="100")
      a-table-column(title="付款方式说明" :width="100")
        template(slot-scope="scope")
          a-tooltip
            template(slot="title") {{ scope.paymentTypeComment }}
            .title.text-ellipsis {{ scope.paymentTypeComment }}
    template(v-if="tabIndex === 'routine'")
      a-table-column(title="付款用途" :width="100")
        template(slot-scope="scope")
          a-tooltip
            template(slot="title") {{ scope.paymentPurpose }}
            .title.text-ellipsis {{ scope.paymentPurpose }}
      a-table-column(title="收款人或组织机构" :width="100")
        template(slot-scope="scope")
          a-tooltip
            template(slot="title") {{ scope.payeeOrg || scope.payee }}
            .title.text-ellipsis {{ scope.payeeOrg || scope.payee }}
    a-table-column(title="报销金额(￥)" :width="100")
      template(slot-scope="scope")
        span {{ scope.reimbursementAmount | toCurrency }}
    a-table-column(dataIndex="state" title="状态" :width="100" align="center")
    a-table-column(title="报销日期" :width="100")
      template(slot-scope="scope")
        span {{ scope.insert_d ? $moment(scope.insert_d).format('YYYY-MM-DD HH:mm') : '' }}
    a-table-column(dataIndex="owner_name" title="发起人" :width="100" align="center")

  MainModal(
    v-model="visibleModal"
    width="1000px"
    :footer="false"
    :maskClosable="false"
    @change="() => { fetchData(store.currentPage) }")
    .modal-row(slot="title")
      span
        template(v-if="store.payment && store.payment.routine_third_name") {{ store.payment.routine_third_name }} -
        span 单据详情
    Payment(:type="tabIndex" :role="role")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import FileSaver from 'file-saver';
import routinePaymentStore from '@/store/modules/routine_payment.store';
import outsidePaymentStore from '@/store/modules/outside_payment.store';
import routine_payment from '@/models/fund/routine_payment';
import outside_payment from '@/models/fund/outside_payment';
import Payment from '@/components/fund/Payment.vue';

@Component({
  components: {
    Payment,
  },
})
export default class PaymentTable extends Vue {
  @Prop({ type: String, default: 'base' }) private parent!: string;
  @Prop({ type: [Number, String], default: null }) private parentId?: number;
  private queryObject: object = {};
  private visibleState: boolean = false;
  private visibleModal: boolean = false;
  private exportLoading: boolean = false;
  private visible: string = '';
  private tabIndex: string = 'routine';
  private stateIndex: string = 'all';
  private states: IObject[] = [
    {
      label: '全部',
      value: 'all',
    },
    {
      label: '已报销',
      value: 'paid',
    },
    {
      label: '报销中',
      value: 'paying',
    },
    {
      label: '可打印',
      value: 'print',
    },
  ];
  get tabs() {
    return [
      {
        label: '日常报销',
        value: 'routine',
        count: this.routinePaymentStore.totalCount || 0,
      },
      {
        label: '出差报销',
        value: 'outside',
        count: this.outsidePaymentStore.totalCount || 0,
      },
    ];
  }
  get formatStateTitle() {
    return ({
      all: '状态-全部',
      paid: '状态-已报销',
      paying: '状态-报销中',
      print: '状态-可打印',
    } as any)[this.stateIndex];
  }
  get role() {
    return this.$route.name === 'teacher_fund_routine_base_show' ? 'teacher' : 'admin';
  }
  get routinePaymentStore() {
    return routinePaymentStore || {};
  }
  get outsidePaymentStore() {
    return outsidePaymentStore || {};
  }
  get store() {
    return this.tabIndex === 'routine' ? this.routinePaymentStore : this.outsidePaymentStore;
  }

  public mounted() {
    this.initParentConfig();
    this.fetchData();
  }

  public fetchData(page: number = 1) {
    const params = {
      parentId: this.parentId as number,
      page,
      per_page: 10,
      q: {
        ...this.formatStateFilter(),
        ...this.queryObject,
      },
    };
    // TODO: 下面的逻辑需要重构，逻辑不清晰 (By TangMingChao)
    if (page === 1) {
      routinePaymentStore.fetchByParent(params);
      outsidePaymentStore.fetchByParent(params);
    } else if (this.tabIndex === 'routine') {
      routinePaymentStore.fetchByParent(params);
    } else {
      outsidePaymentStore.fetchByParent(params);
    }
  }

  public onShow(val: IObject) {
    if (this.tabIndex === 'routine') {
      routinePaymentStore.find(val.id);
    } else {
      outsidePaymentStore.find(val.id);
    }
    this.visibleModal = true;
  }

  // extends
  public initParentConfig() {
    routinePaymentStore.changeNamespace(this.role);
    outsidePaymentStore.changeNamespace(this.role);
    switch (this.parent) {
      case 'base':
        routinePaymentStore.changeParentResource('routine_bases');
        outsidePaymentStore.changeParentResource('routine_bases');
        break;
      case 'budget':
        routinePaymentStore.changeParentResource('routine_budgets');
        outsidePaymentStore.changeParentResource('routine_budgets');
        break;
      case 'third':
        routinePaymentStore.changeParentResource('routine_thirds');
        outsidePaymentStore.changeParentResource('routine_thirds');
        break;
      default:
        break;
    }
  }

  public onTab(val: string) {
    if (this.tabIndex !== val) {
      this.tabIndex = val;
    }
  }

  public changeState(val: string) {
    if (this.stateIndex !== val) {
      this.stateIndex = val;
      this.fetchData();
    }
    this.visibleState = false;
  }

  public onPaginate(page: number) {
    this.fetchData(page);
  }

  // 搜索报销金额
  public filterReimbursementAmount(date: any, val: number) {
    Object.assign(this.queryObject, { reimbursementAmount_eq: val });
    this.fetchData();
  }

  public formatStateFilter() {
    return ({
      all: {},
      paid: { paid: true },
      paying: { paying: true },
      print: { printable: true },
    } as any)[this.stateIndex];
  }

  public async expertExcel() {
    try {
      this.exportLoading = true;
      const params: IObject = {
        page: 1,
        per_page: 1000,
        q: {
          ...this.formatStateFilter(),
        },
      };
      const parentName = ({
        base: 'routine_bases',
        budget: 'routine_budgets',
        third: 'routine_thirds',
      } as any)[this.parent];
      const roleUrl = `${this.role}/${parentName}/${this.parentId}`; // admin/routine_bases/id
      const payment = this.tabIndex === 'routine' ? routine_payment : outside_payment;
      const { data } = await payment.exportByParent(params, roleUrl);
      FileSaver.saveAs(data.url, data.url.split('/').pop());
      this.exportLoading = false;
    } catch (error) {
      this.exportLoading = false;
      this.$message.error('导出失败');
    }
  }
}
</script>

<style lang="stylus" scoped>
.panel
  padding 0px 20px 20px
  width 100%
  height 100%
  background #fff
  overflow hidden
  .panel-tabs
    position relative
    display flex
    align-items center
    width 100%
    border-bottom 1px #E8E8E8 solid
    font-weight 500
    .tab
      margin-right 44px
      padding 18px 0px 14px
      border-bottom 4px #fff solid
      color #808080
      font-weight 500
      cursor pointer
    .tab-active
      border-bottom 4px #3DA8F5 solid
      color #383838
    .filter-box
      position absolute
      top 0px
      right 0px
      display flex
      align-items center
      padding 18px 0px
      .export
        margin-right 16px
        color #808080
        font-weight 500
        border none
        &:hover
          color #3da8f5

.title
  max-width 160px
</style>
