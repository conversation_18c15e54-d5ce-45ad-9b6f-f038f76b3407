<template lang="pug">
.page(ref="container")
  Panel.panel
    .panel-header 资金卡统计
    .panel-mian
      a-row(:gutter="20")
        a-col(:span="7")
          .cell
            .key 总金额
            .value ￥{{ routine_base.amountOfMoney | toCurrency }}
          .cell
            .key 可报销金额
            .value ￥{{ routine_base.avalible_amount | toCurrency }}
          .cell
            .key 实际使用率
            .value.text-success {{ base_reality_rate }}%
          .cell
            .key 预期使用率
            .value.text-primary {{ base_expect_rate }}%
        a-col(:span="10")
          G2PieBase(:charData.sync="basePieData" id="A1" v-if="basePieData.length")
        a-col(:span="7")
          .cell
            .key 已报销金额
            .value.text-success ￥{{ routine_base.payment_paid_amount | toCurrency }}
          .cell
            .key 已报销单据
            .value.text-success {{ routine_base.payment_paid_count || 0 }}
          .cell
            .key 报销中金额
            .value.text-primary ￥{{ routine_base.payment_paying_amount | toCurrency }}
          .cell
            .key 报销中单据
            .value.text-primary {{ routine_base.payment_paying_count || 0 }}
      a-row(:gutter="20")
        a-col(:span="12")
          a-card
            template(slot="title") 实际使用率
            .progress
              a-progress(
                type="dashboard"
                :percent="$tools.getRate(routine_base.payment_paid_amount, routine_base.amountOfMoney)"
                strokeColor="#75C940")
        a-col(:span="12")
          a-card
            template(slot="title") 预期使用率
            .progress
              a-progress(
                type="dashboard"
                :percent="budget_expect_rate"
                strokeColor="#3DA8F5")
      a-row(:gutter="20" style="margin-top: 20px")
        a-col(:span="12")
          a-card
            template(slot="title") 按内容统计
            G2PieContent(:charData="budgetContentPieData" id="A2" v-if="budgetContentPieData.length")
            Empty(type="survey" desc="暂无统计数据" v-else)
        a-col(:span="12")
          a-card
            template(slot="title") 二级类目的执行率统计
            G2HistogramRate(:charData="budgetRateHistogramData" id="A3" v-if="budgetRateHistogramData.length")
            Empty(type="survey" desc="暂无统计数据" v-else)
      a-row(:gutter="20" style="margin-top: 20px")
        a-col(:span="12")
          a-card
            template(slot="title") 二级类目的金额统计
            G2HistogramAmount(:charData="budgetAmountHistogramData" id="A4" v-if="budgetAmountHistogramData.length")
            Empty(type="survey" desc="暂无统计数据" v-else)
        a-col(:span="12")
          a-card
            template(slot="title") 二级类目的单据统计
            G2HistogramReceipt(:charData="budgetReceiptHistogramData" id="A5" v-if="budgetReceiptHistogramData.length")
            Empty(type="survey" desc="暂无统计数据" v-else)
  Panel.panel(style="margin-top: 12px")
    a-tabs(v-model="tabIndex")
      a-tab-pane(
        v-for="(tab, index) in tabs" :key="tab.value"
        :tab="tab.label")
    .panel-mian
      a-row(:gutter="20")
        a-col(:span="7")
          .cell
            .key 总金额
            .value ￥{{ routine_budget.amountOfMoney | toCurrency }}
          .cell
            .key 可报销金额
            .value ￥{{ routine_budget.avalible_amount | toCurrency }}
          .cell
            .key 实际使用率
            .value.text-success {{ budget_reality_rate }}%
          .cell
            .key 预期使用率
            .value.text-primary {{ budget_expect_rate }}%
        a-col(:span="10")
          G2PieBase(:charData="budgetPieData" id="B1" v-if="budgetPieData.length")
        a-col(:span="7")
          .cell
            .key 已报销金额
            .value.text-success ￥{{ routine_budget.payment_paid_amount | toCurrency }}
          .cell
            .key 已报销单据
            .value.text-success {{ routine_budget.payment_paid_count || 0 }}
          .cell
            .key 报销中金额
            .value.text-primary ￥{{ routine_budget.payment_paying_amount | toCurrency }}
          .cell
            .key 报销中单据
            .value.text-primary {{ routine_budget.payment_paying_count|| 0 }}
      a-row(:gutter="20")
        a-col(:span="12")
          a-card
            template(slot="title") 实际使用率
            .progress
              //- TODO: 代码冗余，记得修改
              a-progress(
                type="dashboard"
                :percent="$tools.getRate(routine_budget.payment_paid_amount, routine_budget.amountOfMoney)"
                strokeColor="#75C940")
        a-col(:span="12")
          a-card
            template(slot="title") 预期使用率
            .progress
              //- TODO: 使用了 budget_expect_rate，替代了原来的冗余表达式，负责人记得检查
              a-progress(
                type="dashboard"
                :percent="budget_expect_rate"
                strokeColor="#3DA8F5")
      a-row(:gutter="20" style="margin-top: 20px")
        a-col(:span="12")
          a-card
            template(slot="title") 按内容统计
            G2PieContent(:charData="thirdContentPieData" id="B2" v-if="thirdContentPieData.length")
            Empty(type="survey" desc="暂无统计数据" v-else)
        a-col(:span="12")
          a-card
            template(slot="title") 三级类目的执行率统计
            G2HistogramRate(:charData="thirdRateHistogramData" id="B3" v-if="thirdRateHistogramData.length")
            Empty(type="survey" desc="暂无统计数据" v-else)
      a-row(:gutter="20" style="margin-top: 20px")
        a-col(:span="12")
          a-card
            template(slot="title") 三级类目的金额统计
            G2HistogramAmount(:charData="thirdAmountHistogramData" id="B4" v-if="thirdAmountHistogramData.length")
            Empty(type="survey" desc="暂无统计数据" v-else)
        a-col(:span="12")
          a-card
            template(slot="title") 三级类目的单据统计
            G2HistogramReceipt(:charData="thirdReceiptHistogramData" id="B5" v-if="thirdReceiptHistogramData.length")
            Empty(type="survey" desc="暂无统计数据" v-else)
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import G2PieBase from '@/components/fund/G2PieBase.vue';
import G2PieContent from '@/components/fund/G2PieContent.vue';
import G2HistogramRate from '@/components/fund/G2HistogramRate.vue';
import G2HistogramAmount from '@/components/fund/G2HistogramAmount.vue';
import G2HistogramReceipt from '@/components/fund/G2HistogramReceipt.vue';
import routineBaseStore from '@/store/modules/routine_base.store';

@Component({
  components: {
    G2PieBase,
    G2PieContent,
    G2HistogramRate,
    G2HistogramAmount,
    G2HistogramReceipt,
  },
})
export default class PaymentChart extends Vue {
  private tabIndex: string = '';
  private tabs: any = [];
  get routine_base() {
    const routineBase: any = routineBaseStore.routineBase || {};
    return routineBase;
  }
  get base_expect_rate() {
    // 预期使用率
    const done: number = this.routine_base.payment_paid_amount + this.routine_base.payment_paying_amount;
    return done ? this.$tools.getRate(done, this.routine_base.amountOfMoney) : 0;
  }
  get base_reality_rate() {
    // 实际使用率
    return this.routine_base.id
      ? this.$tools.getRate(this.routine_base.payment_paid_amount, this.routine_base.amountOfMoney)
      : 0;
  }
  get budget_expect_rate() {
    // 预期使用率
    const done: number = this.routine_budget.payment_paid_amount + this.routine_budget.payment_paying_amount;
    return done ? this.$tools.getRate(done, this.routine_budget.amountOfMoney) : 0;
  }
  get budget_reality_rate() {
    // 实际使用率
    return this.routine_budget.id
      ? this.$tools.getRate(this.routine_budget.payment_paid_amount, this.routine_budget.amountOfMoney)
      : 0;
  }
  get basePieData() {
    // 报销额统计
    const data: object[] = [
      {
        label: '已报销金额',
        key: 'payment_paid_amount',
      },
      {
        label: '报销中金额',
        key: 'payment_paying_amount',
      },
      {
        label: '可报销金额',
        key: 'avalible_amount',
      },
    ].map((item: any) => ({
      state: item.label,
      amount: +this.routine_base[item.key],
      percent: this.getRoutineBaseRate(this.routine_base[item.key]),
    }));
    return this.routine_base.id ? data : [];
  }
  // budget
  get routine_budgets() {
    const routineBudgets =
      (this.routine_base.routine_budgets || []).filter((e: any) => e.payment_paid_count || e.payment_paying_count) ||
      [];
    this.tabs = (routineBudgets || []).map((item: any) => ({
      label: item.budgetName,
      value: item.id,
    }));
    this.tabIndex = this.tabs[0] && this.tabs[0].value ? this.tabs[0].value : '';
    return routineBudgets;
  }
  get budgetContentPieData() {
    const data: object[] = (this.routine_budgets || []).map((item: any) => ({
      state: item.budgetName,
      amount: +item.amountOfMoney,
      percent: this.getRoutineRate(item.amountOfMoney, this.routine_base.amountOfMoney),
    }));
    return this.routine_base.id ? data : [];
  }
  get budgetRateHistogramData() {
    const data: object[] = ['实际使用率', '预期使用率'].reduce((res: IObject[], key: string) => {
      const arr: IObject[] = (this.routine_budgets || []).map((item: IObject) => ({
        type: key,
        name: item.budgetName,
        rate:
          key === '实际使用率'
            ? this.getRoutineRate(item.payment_paid_amount, this.routine_base.amountOfMoney)
            : this.getRoutinePerformRate(item, item.amountOfMoney),
      }));
      return res.concat(arr);
    }, []);
    return this.routine_base.id ? data : [];
  }
  get budgetAmountHistogramData() {
    // 单据数量统计
    const data: object[] = (this.routine_budgets || []).reduce((res: object[], item: any) => {
      const arr: object[] = [
        {
          label: '可报销金额',
          key: 'avalible_amount',
        },
        {
          label: '报销中金额',
          key: 'payment_paying_amount',
        },
        {
          label: '已报销金额',
          key: 'payment_paid_amount',
        },
      ].map((obj: any) => ({
        name: obj.label,
        type: item.budgetName,
        amount: +item[obj.key] > 0 ? +item[obj.key] : 0,
      }));
      return res.concat(arr);
    }, []);
    return this.routine_base.id ? data : [];
  }
  get budgetReceiptHistogramData() {
    const routineBudgets: object[] = (this.routine_budgets || []).map((item: any) => ({
      ...item,
      payment_paid_count: item.outside_payment_paid_count + item.routine_payment_paid_count,
      payment_paying_count: item.outside_payment_paying_count + item.routine_payment_paying_count,
    }));
    const data: object[] = (routineBudgets || []).reduce((res: object[], item: any) => {
      const arr: object[] = [
        {
          label: '已报销金额',
          key: 'payment_paid_count',
        },
        {
          label: '报销中金额',
          key: 'payment_paying_count',
        },
      ].map((obj: any) => ({
        name: obj.label,
        type: item.budgetName,
        count: +item[obj.key],
      }));
      return res.concat(arr);
    }, []);
    return this.routine_base.id ? data : [];
  }
  get budgetPieData() {
    // 报销额统计
    const data: object[] = [
      {
        label: '已报销金额',
        key: 'payment_paid_amount',
      },
      {
        label: '报销中金额',
        key: 'payment_paying_amount',
      },
      {
        label: '可报销金额',
        key: 'avalible_amount',
      },
    ].map((item: any) => ({
      state: item.label,
      amount: +this.routine_budget[item.key] > 0 ? +this.routine_budget[item.key] : 0, // 可报销金额为负数的情况
      percent: this.getRoutineBaseRate(this.routine_budget[item.key]),
    }));
    return this.routine_budget.id ? data : [];
  }
  // third
  get routine_thirds() {
    const data: object[] = (this.routine_base.routine_thirds || []).filter(
      (e: any) => e.zjk_budget_id === this.routine_budget.id && e.payment_paid_amount + e.payment_paying_amount,
    );
    return this.routine_budget.id ? data : [];
  }
  get thirdContentPieData() {
    const data: object[] = (this.routine_thirds || []).map((item: any) => ({
      state: item.short_name,
      amount: +item.payment_paid_amount + item.payment_paying_amount,
      percent: this.getRoutineRate(item.amount_money, this.routine_budget.amountOfMoney),
    }));
    return this.routine_base.id ? data : [];
  }
  get thirdRateHistogramData() {
    const data: object[] = ['实际使用率', '预期使用率'].reduce((res: IObject[], key: string) => {
      const arr: IObject[] = (this.routine_thirds || []).map((item: IObject) => ({
        type: key,
        name: item.short_name,
        rate:
          key === '实际使用率'
            ? this.getRoutineRate(item.payment_paid_amount, this.routine_base.amountOfMoney)
            : this.getRoutinePerformRate(item, item.amount_money),
      }));
      return res.concat(arr);
    }, []);
    return this.routine_base.id ? data : [];
  }
  get thirdAmountHistogramData() {
    // 金额统计
    const data: object[] = (this.routine_thirds || []).reduce((res: object[], item: any) => {
      const arr: object[] = [
        {
          label: '可报销金额',
          key: 'avalible_amount',
        },
        {
          label: '报销中金额',
          key: 'payment_paying_amount',
        },
        {
          label: '已报销金额',
          key: 'payment_paid_amount',
        },
      ].map((obj: any) => ({
        name: obj.label,
        type: item.short_name,
        amount: +item[obj.key] > 0 ? +item[obj.key] : 0, // 可报销金额为负数的情况
      }));
      return res.concat(arr);
    }, []);
    return this.routine_base.id ? data : [];
  }
  get thirdReceiptHistogramData() {
    // 单据数量统计
    const data: object[] = (this.routine_thirds || []).reduce((res: object[], item: any) => {
      const arr: object[] = [
        {
          label: '已报销单据',
          key: 'payment_paid_count',
        },
        {
          label: '报销中单据',
          key: 'payment_paying_count',
        },
      ].map((obj: any) => ({
        name: obj.label,
        type: item.short_name,
        count: +item[obj.key],
      }));
      return res.concat(arr);
    }, []);
    return this.routine_base.id ? data : [];
  }
  get routine_budget() {
    const data = (this.routine_budgets || []).find((e: any) => e.id === this.tabIndex);
    return data && data.id ? data : {};
  }
  // extends
  public onTab(val: string) {
    if (this.tabIndex !== val) {
      this.tabIndex = val;
    }
  }

  public getRoutineBaseRate(val: number) {
    const rate: number = val / this.routine_base.amountOfMoney;
    return +(rate * 100).toFixed(2);
  }

  public getRoutineRate(amount: number, total: number): number {
    // 内容比率
    const rate: number = amount / total;
    return +(rate * 100).toFixed(2);
  }

  public getRoutinePerformRate(val: any, total: number): number {
    // 执行率
    const done: number = val.payment_paid_amount + val.payment_paying_amount;
    const rate: number = +done / total;
    return +(rate * 100).toFixed(2);
  }
}
</script>

<style lang="stylus" scoped>
.page
  width 100%
  height 100%
  .panel
    padding 0px 20px
    background #fff
    .panel-header
      padding 16px 0px
      border-bottom 1px #E8E8E8 solid
      font-weight 500
    .panel-mian
      padding 12px 0px 20px
      .cell
        display flex
        align-items center
        padding 14px 0px
        .key
          width 100px
          color #808080
          line-height 20px
        .value
          font-weight 500
          font-size 24px
          font-family 'DINCondensed-Bold'
          line-height 24px

.progress
  display flex
  justify-content center
  padding 20px 0px
</style>
