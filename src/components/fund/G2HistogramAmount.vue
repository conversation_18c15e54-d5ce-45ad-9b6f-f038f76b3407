<template lang="pug">
.g2-pie(:id="id")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import G2 from '@antv/g2';

@Component({
  components: {},
})
export default class G2HistogramAmount extends Vue {
  @Prop({ type: Array, default: () => [] }) private charData: any;
  @Prop({ type: String, default: '' }) private id?: string;
  @Prop({ type: Number, default: 320 }) private boxHeight?: number;
  @Prop({ type: Boolean, default: true }) private forceFit?: boolean;
  private colors: string[] = ['#EDEDED', '#6ABDF7', '#75C940'];
  // data
  private chart: any = null;
  // get size() {
  //   const count = this.charData.length;
  //   const clientWidth = this.$el.clientWidth;
  //   const width = +((clientWidth - 6 * count) / count).toFixed();
  //   return width > 20 || width < 10 ? 20 : width;
  // }
  @Watch('charData')
  public watchChange() {
    this.chart.destroy();
    this.drawChart();
  }

  public mounted() {
    this.drawChart();
  }
  public drawChart() {
    this.chart = new G2.Chart({
      container: this.id,
      height: this.boxHeight,
      forceFit: this.forceFit,
      animate: true,
      padding: [20, 10, 36, 70],
    });
    this.chart.source(this.charData);
    this.chart.scale('amount', {
      formatter: function formatter(val: number) {
        // 在此处设置了， X，Y轴都会生效
        const priceArray = val.toFixed(2).split('.');
        const newPrice = `${Number(priceArray[0]).toLocaleString('en-US')}.${
          priceArray[1] ? priceArray[1].padEnd(2, '0') : '00'
        }`;
        return `￥${val ? newPrice : 0}`;
      },
    });
    this.chart.axis('type', {
      // X轴
      label: {
        rotate: 20,
        textStyle: {
          fill: '#808080',
          fontSize: '12',
        },
      },
      tickLine: {
        alignWithLabel: true,
        length: 0,
      },
    });
    this.chart.axis('amount', {
      // Y轴
      label: {
        textStyle: {
          fill: '#808080',
        },
      },
    });
    this.chart
      .intervalStack()
      .position('type*amount')
      .color('name', this.colors)
      .size(20);
    this.chart.render();
  }
}
</script>

<style lang="stylus" scoped></style>
