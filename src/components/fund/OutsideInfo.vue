<template lang="pug">
.outside-page
  template(v-if="type === 'show'")
    .table-module
      .table-title 行程
      table
        tr
          td(
            v-for="(label, index) in ['出发-到达', '出发时间', '到达时间', '票价']"
            :key="index"
            :class="{'text-right': index === 3}")
            | {{ label }}
        tr(v-for="(item, index) in data" :key="index" v-if="item.startPlace || item.endPlace")
          td.black {{ item.startPlace }}-{{ item.endPlace }}
          td {{ item.startDate ? $moment(item.startDate).format('YYYY.MM.DD HH:mm') : '' }}
          td {{ item.endDate ? $moment(item.endDate).format('YYYY.MM.DD HH:mm') : '' }}
          td.text-right ￥{{ item.ticketAmount | toCurrency }}
        tr
          td(colspan="3") 合计
          td.text-right ￥{{ ticketAmount | toCurrency }}
    .table-module(v-for="(mod, modIndex) in modules" :key="modIndex" v-if="mod.amount")
      .table-title {{ mod.title }}
      table
        tr(v-if="mod.keys.length")
          td(
            v-for="(label, index) in mod.keys"
            :key="index"
            :colspan="modIndex === 4 ? 2 : 1"
            :class="{'text-right': index === mod.keys.length - 1}")
            | {{ label }}
        template(v-if="modIndex !== 4")
          tr(v-for="(item, index) in data" :key="index" v-if="item[mod.amountKey]")
            td {{ item[mod.personKey] }}
            td {{ item[mod.dateKey] }}
            td.text-right ￥{{ item[mod.amountKey] | toCurrency }}
        template(v-else)
          tr(v-for="(item, index) in data" :key="index" v-if="item[mod.amountKey]")
            td(colspan="2")
              .black.text-ellipsis {{ item[mod.contentKey] }}
            td.gray.text-right ￥{{ item[mod.amountKey] | toCurrency }}
        tr
          td(colspan="2") 合计
          td.text-right ￥{{ mod.amount | toCurrency }}
  template(v-else)
    table
      tr
        td 报销项
        td(colspan="5") 报销细则
        td 报销金额合计
      //- 行程
      tr
        td(:rowspan="data.length + 1").title 行程
        td(v-for="(label, index) in ['出发地', '出发时间', '到达地', '到达时间', '票价']" :key="index") {{ label }}
        td(:rowspan="data.length + 1")
          span(v-if="ticketAmount") ￥{{ ticketAmount | toCurrency }}
      tr(v-for="(item, index) in data" :key="index" v-if="item.startPlace || item.endPlace")
        td.black {{ item.startPlace }}
        td {{ item.startDate ? $moment(item.startDate).format('YYYY.MM.DD HH:mm') : '' }}
        td.black {{ item.endPlace }}
        td {{ item.endDate ? $moment(item.endDate).format('YYYY.MM.DD HH:mm') : '' }}
        td
          span(v-if="item.ticketAmount") ￥{{ item.ticketAmount | toCurrency }}
      //- 未乘卧铺补贴
      tr
        td(:rowspan="subsidys.length + 1").title 未乘卧铺补贴
        td(v-for="(label, index) in ['人', '天', '金额']" :key="index" :colspan="index === 2 ? 1 : 2") {{ label }}
        td(:rowspan="subsidys.length + 1")
          span(v-if="subsidyAmount") ￥{{ subsidyAmount | toCurrency }}
      tr(v-for="(item, index) in subsidys" :key="index + 100")
        td(colspan="2") {{ item.subsidyPerson }}
        td(colspan="2") {{ item.subsidyDate }}
        td
          span(v-if="subsidyAmount") ￥{{ item.subsidyAmount | toCurrency }}
      //- 住宿费
      tr
        td(:rowspan="accommodations.length + 1").title 住宿费
        td(v-for="(label, index) in ['人', '天', '金额']" :key="index" :colspan="index === 2 ? 1 : 2") {{ label }}
        td(:rowspan="accommodations.length + 1")
          span(v-if="accommodationAmount") ￥{{ accommodationAmount | toCurrency }}
      tr(v-for="(item, index) in accommodations" :key="index + 200")
        td(colspan="2") {{ item.accommodationPerson }}
        td(colspan="2") {{ item.accommodationDate }}
        td
          span(v-if="item.accommodationAmount") ￥{{ item.accommodationAmount | toCurrency }}
      //- 住勤补贴
      tr
        td(:rowspan="livingallowances.length + 1").title 住勤补贴
        td(v-for="(label, index) in ['人', '天', '金额']" :key="index" :colspan="index === 2 ? 1 : 2") {{ label }}
        td(:rowspan="livingallowances.length + 1")
          span(v-if="livingallowanceAmount") ￥{{ livingallowanceAmount | toCurrency }}
      tr(v-for="(item, index) in livingallowances" :key="index + 300")
        td(colspan="2") {{ item.livingallowancePerson }}
        td(colspan="2") {{ item.livingallowanceDate }}
        td
          span(v-if="item.livingallowanceAmount") ￥{{ item.livingallowanceAmount | toCurrency }}
      //- 市内交通费
      tr
        td.title 市内交通费
        td(colspan="5")
        td
          span(v-if="localTransportationCharges") ￥{{ localTransportationCharges | toCurrency }}
      //- 其他费用
      tr
        td(:rowspan="moneys.length + 1").title 其他费用
        td(
          v-for="(label, index) in ['摘要', '金额']"
          :key="index"
          :colspan="index === 0 ? 4 : 1"
          :class="{'text-right': index === 3}")
          | {{ label }}
        td(:rowspan="moneys.length + 1")
          span(v-if="money") ￥{{ money | toCurrency }}
      tr(v-for="(item, index) in moneys" :key="index + 400")
        td(colspan="4") {{ item.summary }}
        td
          span(v-if="item.money") ￥{{ item.money | toCurrency }}
      //- 报销金额合计
      tr
        td(rowspan="3").title
          strong 报销金额合计
        td(colspan="5")
          span 付款方式：
          span
            strong {{ payment.payment_type_dic_name }}
          span &nbsp&nbsp&nbsp&nbsp出差人：
          span
            strong {{ payment.business_person_name }}
        td(rowspan="3")
          strong(style="font-size: 24px") ￥{{ payment.reimbursementAmount | toCurrency }}
      tr
        td(colspan="5")
          span 付款方式说明：
          span
            strong {{ payment.paymentTypeComment }}
      tr
        td(colspan="5")
          span 人民币(大写)：
          strong {{ payment.moneyd }}
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class OutsideInfo extends Vue {
  @Prop({ type: Array, default: () => [] }) private data?: IObject[];
  @Prop({ type: Object, default: () => ({}) }) private payment?: IObject;
  @Prop({ type: String, default: 'show' }) private type?: string;
  // 交通费
  get ticketAmount() {
    return (this.data || []).reduce((res, item) => res + item.ticketAmount, 0);
  }
  // 未乘卧铺补贴
  get subsidys() {
    const placeholder: IObject = {
      subsidyPerson: '',
      subsidyDate: '',
      subsidyAmount: '',
    };
    const res: IObject[] = (this.data || []).filter((e: IObject) => e.subsidyAmount);
    return res.length > 0 ? res : [placeholder];
  }
  get subsidyAmount() {
    return (this.data || []).reduce((res, item) => res + item.subsidyAmount, 0);
  }
  // 住宿费
  get accommodations() {
    const placeholder: IObject = {
      accommodationPerson: '',
      accommodationDate: '',
      accommodationAmount: '',
    };
    const res: IObject[] = (this.data || []).filter((e: IObject) => e.accommodationAmount);
    return res.length > 0 ? res : [placeholder];
  }
  get accommodationAmount() {
    return (this.data || []).reduce((res, item) => res + item.accommodationAmount, 0);
  }
  // 住勤补贴
  get livingallowances() {
    const placeholder: IObject = {
      livingallowancePerson: '',
      livingallowanceDate: '',
      livingallowanceAmount: '',
    };
    const res: IObject[] = (this.data || []).filter((e: IObject) => e.livingallowanceAmount);
    return res.length > 0 ? res : [placeholder];
  }
  get livingallowanceAmount() {
    return (this.data || []).reduce((res, item) => res + item.livingallowanceAmount, 0);
  }
  // 市内交通费
  get localTransportationCharges() {
    return (this.data || []).reduce((res, item) => res + item.LocalTransportationCharges, 0);
  }
  // 其他费用
  get moneys() {
    const placeholder: IObject = {
      summary: '',
      money: '',
    };
    const res: IObject[] = (this.data || []).filter((e: IObject) => e.money);
    return res.length > 0 ? res : [placeholder];
  }
  get money() {
    return (this.data || []).reduce((res, item) => res + item.money, 0);
  }
  get modules() {
    return [
      {
        title: '未乘卧铺补贴',
        personKey: 'subsidyPerson',
        dateKey: 'subsidyDate',
        amountKey: 'subsidyAmount',
        keys: ['人', '天', '金额'],
        amount: this.subsidyAmount,
      },
      {
        title: '住宿费',
        personKey: 'accommodationPerson',
        dateKey: 'accommodationDate',
        amountKey: 'accommodationAmount',
        keys: ['人', '天', '金额'],
        amount: this.accommodationAmount,
      },
      {
        title: '住勤补贴',
        personKey: 'livingallowancePerson',
        dateKey: 'livingallowanceDate',
        amountKey: 'livingallowanceAmount',
        keys: ['人', '天', '金额'],
        amount: this.livingallowanceAmount,
      },
      {
        title: '市内交通费',
        keys: [],
        amount: this.localTransportationCharges,
      },
      {
        title: '其他费用',
        keys: ['摘要', '金额'],
        contentKey: 'summary',
        amountKey: 'money',
        amount: this.money,
      },
    ];
  }
}
</script>

<style lang="stylus" scoped>
fontSize = 16px
lineHeight = 20px

.outside-page
  color #383838
  font-weight 500
  font-size fontSize
  line-height lineHeight
  .table-module
    padding 14px 0px
    width 100%
    border-top 1px #e8e8e8 solid
    font-weight 400
    font-size 14px
    line-height 40px
    table, td
      padding 0px
      border none
    .table-title
      font-weight 500
      font-size 16px
    .black
      color #383838

table
  width 100%
  border 1px #808080 solid
  td
    padding 8px
    height 36px
    border 1px #808080 solid

.title
  width 100px
</style>
