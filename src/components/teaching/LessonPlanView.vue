<script lang="ts">
// 课程资源 lesson_plan
import { Component, Vue, Prop } from 'vue-property-decorator';
import { ILessonPlan, LessonPlan } from '@/models/teaching/lesson_plan';
import { compact } from 'lodash';
import Attachments from '@/components/global/Attachments.vue';
import { LessonItem, ILessonItem } from '@/models/teaching/lesson_item';
import TargetProgress from '../statistic/TargetProgress.vue';

@Component({
  components: {
    Attachments,
    TargetProgress,
  },
})
export default class LesssonPlanView extends Vue {
  @Prop({ type: Object, default: () => ({}) }) plan!: ILessonPlan;
  @Prop({ type: Boolean, default: false }) showRecorderEntry!: boolean;

  customStyle: string = 'background: #FAFAFA;border-radius: 4px;margin-bottom: 20px;border: 0;overflow: hidden';

  get activeKey() {
    return [`${this.plan.id}`];
  }
  get itemTypeZh() {
    return LessonItem.itemTypeZh;
  }
  get isStudent() {
    return this.$store.getters.currentRole === 'student';
  }

  getPlanSectionTitle() {
    return LessonPlan.getSectionTitle(this.plan);
  }
  getSafeLink(url: string) {
    if (!url) return 'javascript:;';
    return url.substr(0, 4) === 'http' ? url : `http://${url}`;
  }
  onPlanItemPreview(item: ILessonItem, index: number) {
    this.$emit('preview', item, index);
  }
  getDuration(seconds?: number) {
    return typeof seconds === 'number' ? this.$utils.parseSeconds(seconds).toString() : '-';
  }
  showItem(item: ILessonItem) {
    this.$emit('showItem', item);
  }
}
</script>

<template lang="pug">
a-collapse.lesson-plan-panel(:bordered="false" :activeKey="activeKey")
  template(v-slot:expandIcon="props")
    a-icon(type="caret-right" :rotate="props.isActive ? 90 : 0")
  a-collapse-panel.hiden-panel(key="0" v-if="!plan.id")
  a-collapse-panel(:style="customStyle" :key="`${plan.id}`" v-else)
    .lesson-plan-header(slot="header")
      .sections
        | {{ getPlanSectionTitle() }}
    .lesson-plan-content
      .title {{ plan.title }}
      .subject {{ plan.subject }}
      .ck-content.plan-body(v-html="plan.body")
      //- lesson plan item
      .lesson-item(v-for="(item, index) in plan.lesson_items" :key="item.id")
        template(v-if="item.item_type === 'paper'")
          .sub-title 题库

        template(v-else-if="item.item_type === 'link'")
          .sub-title 资源链接
          a.link(
            :href="getSafeLink(item.attachments.link)"
            :alt="item.title"
            target="_blank"
            v-if="item.attachments.link")
            | {{ item.title || '超链接' }}
          a-tooltip(title="无效链接" v-else)
            .disabled-link
              | {{ item.title || '超链接' }}

        template(v-else)
          .lesson-item-header(v-if="isStudent")
            TargetProgress(name="课前" :text="getDuration(item.student_recorder.prepare_in_sec)")
            TargetProgress(name="课中" :text="getDuration(item.student_recorder.study_in_sec)")
            TargetProgress(name="课后" :text="getDuration(item.student_recorder.review_in_sec)")
            TargetProgress(name="合计" :text="getDuration(item.student_recorder.total_in_sec)")
          .sub-title.flex-between
            span {{ itemTypeZh[item.item_type] || '其他附件' }}
            TextButton(icon="pie-chart" v-if="showRecorderEntry" @click="showItem(item)")
              | 学习情况
          .attachments
            Attachments(
              :attachments="item.attachments.attachments"
              :previewable="!isStudent"
              :downloadable="!isStudent"
              :display="false"
              @preview="onPlanItemPreview(item, index)")
          .sub-title {{ itemTypeZh[item.item_type] || '其他附件' }}描述
          .pre-wrap.desc {{ item.title }}
</template>

<style lang="stylus" scoped>
.lesson-plan-panel
  .hiden-panel
    display none
  .lesson-plan-header
    .sections
      color #383838
      font-weight bold
      padding 4px 0
    .actions
      padding 0 16px
      flex-shrink 0
  .lesson-plan-content
    border-top 1px solid #e8e8e8
    padding 16px 0
    .pre-wrap
      white-space pre-wrap
    .title
      font-size 16px
      font-weight 500
      color rgba(56,56,56,1)
      line-height 22px
      margin-bottom 12px
    .subject
      font-size 14px
      color rgba(56,56,56,1)
      line-height 22px
      margin-bottom 12px
    .plan-body
      font-size 14px
      color #333
      line-height 20px
      margin-bottom 16px
      margin-top 0
    .lesson-item
      background rgba(255,255,255,1)
      border-radius 4px
      border 1px solid rgba(232,232,232,1)
      padding 12px 16px
      margin-bottom 8px
      .lesson-item-header
        display flex
        align-items center
        justify-content space-around
        margin-bottom 14px
      .sub-title
        font-size 14px
        color #808080
        line-height 20px
        margin-bottom 8px
      .desc
        font-size 14px
        font-weight 400
        color rgba(56,56,56,1)
        line-height 20px
        margin 0
      .attachments
        margin-bottom 16px
      .link
        text-decoration underline
        color #3DA8F5
        display inline-block
      .disabled-link
        text-decoration underline
        color #888888
        display inline-block
</style>
