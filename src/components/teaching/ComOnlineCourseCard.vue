<template lang="pug">
.com-online-course-card
  .card-element(@click.stop='click(course)')
    .card-header
      img(src="@/assets/icons/bookmark_tag_solid.svg")
      span {{ course.teach_depart_name }}
    .card-title {{ course.course_set_name }}
    .card-bottom
      .left
        .e
          a-icon(type="user")
          span {{ course.std_count }}
        .e
          a-icon(type="clock-circle")
          span {{ course.period }}
      .right {{ `·${course.teachers.map(i => i.name).join('、')}` }}
</template>

<script lang="ts">
import { ICourse } from '@/models/teaching/course';
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class ComOnlineCourseCard extends Vue {
  @Prop({ type: Object, required: true }) private readonly course!: ICourse;
  click(course: any) {
    this.$emit('click', course);
  }
}
</script>

<style lang="stylus" scoped>
.com-online-course-card
  width 100%
  .card-element
    height 100px
    border-width 1px
    border-style solid
    border-color #ddd
    border-radius 3px
    padding 10px
    cursor pointer
    .card-header
      display block
      font-size 12px
      span
        margin-left 5px
        color #888
    .card-title
      display block
      font-weight bold
      margin 5px 0 0 0
    .card-bottom
      display flex
      justify-content space-between
      margin-top 10px
      color #aaa
      .left
        display flex
        .e
          margin-right 10px
          span
            margin-left 5px
      .right
        color #888
</style>
