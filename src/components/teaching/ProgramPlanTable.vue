<script lang="ts">
/**
 * 人才培养方案: program::plan
 */
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { IPlanCourseSetGroup } from '@/models/teaching/plan_course_set_group';
import { IPlanCourseSet } from '@/models/teaching/plan_course_set';
import { IPlan } from '@/models/teaching/plan';

// 组件课程数据
interface IGroupCourse extends IPlanCourseSetGroup {
  key: string;
  termWeekHours: string[];
  rowStartIndex: number;
  rowEndIndex: number;
  rowSpan: number;
  colSpan: number;
  planCourseSet: IPlanCourseSet;
}

@Component
export default class TeachingProgramPlanTable extends Vue {
  @Prop({ type: Boolean, default: false }) loading!: boolean;
  @Prop({ type: Object, default: () => ({}) }) programPlan!: IPlan;

  flatGroupCourses: IGroupCourse[] = [];
  columns: any[] = [
    {
      title: '分类',
      dataIndex: 'course_type',
      width: 60,
      customRender: (value: string, row: IGroupCourse, index: number) => {
        const obj = {
          children: value,
          attrs: {} as any,
        };
        if (index === row.rowStartIndex) {
          obj.attrs.rowSpan = row.rowSpan;
          obj.attrs.colSpan = row.colSpan;
          obj.attrs.class = 'group-cell';
        } else {
          obj.attrs.rowSpan = 0;
        }
        return obj;
      },
    },
    {
      title: '课程代码',
      dataIndex: 'planCourseSet.course_set_code',
      key: 'course_set_code',
      width: 120,
      customRender: (value: string, row: IGroupCourse, index: number) => {
        return {
          children: value,
          attrs: {
            colSpan: row.colSpan === 3 ? 0 : 1,
          },
        };
      },
    },
    {
      title: '课程名称',
      dataIndex: 'planCourseSet.course_set_name',
      key: 'course_set_name',
      width: 180,
      customRender: (value: string, row: IGroupCourse, index: number) => {
        return {
          children: value,
          attrs: {
            colSpan: row.colSpan === 3 ? 0 : 1,
          },
        };
      },
    },
    { title: '考核方式', dataIndex: 'planCourseSet.exam_mode', key: 'exam_mode', width: 70, align: 'center' },
    { title: '总学分', dataIndex: 'planCourseSet.course_set.credits', width: 70, align: 'center' },
    { title: '总学时', dataIndex: 'planCourseSet.course_set.period', width: 70, align: 'center' },
    {
      title: '讲课课时',
      dataIndex: 'planCourseSet.course_set.course_hours.讲课课时',
      key: 'course_duration',
      width: 70,
      align: 'center',
    },
    {
      title: '实验课时',
      dataIndex: 'planCourseSet.course_set.course_hours.实验课时',
      key: 'test_duration',
      width: 70,
      align: 'center',
    },
    {
      title: '上机课时',
      dataIndex: 'planCourseSet.course_set.course_hours.上机课时',
      key: 'exp_duration',
      width: 70,
      align: 'center',
    },
    {
      title: '按学期总课时',
      align: 'center',
      key: 'terms',
    },
    {
      title: '授课部门',
      dataIndex: 'planCourseSet.course_set.department_name',
      key: 'department_name',
      align: 'center',
      width: 180,
    },
  ];

  @Watch('programPlan', { immediate: true })
  onPlanChange() {
    this.initFlatGroupCourses();
  }

  initFlatGroupCourses() {
    const termsCount = Number(this.programPlan.terms_count || 0);
    const planCourseSetGroups = this.programPlan.plan_course_set_groups || [];
    // 展开为 group-set 一对一记录
    let courseSetCount = 0;
    this.flatGroupCourses = planCourseSetGroups.reduce((groupCourses: any[], group) => {
      const { term_week_hours, term_credits, course_type, plan_course_sets } = group;
      // 展开为 grou - set 对象
      let cache: IGroupCourse[] = [];
      if (plan_course_sets.length) {
        cache = plan_course_sets.map((planCourseSet: IPlanCourseSet) => {
          // planCourseSet.terms 格式：'4' | ',4,' | ',1,2,'
          const termWeekHours = Array(termsCount).fill(null);
          const showTerms = this.$utils.stringToArray(planCourseSet.terms!, ',');
          showTerms.forEach((index: string) => {
            termWeekHours[Number(index) - 1] = planCourseSet!.course_set!.period;
          });
          return {
            ...group,
            key: `${group.id}-${planCourseSet.id}`,
            planCourseSet,
            termWeekHours: termWeekHours,
            rowStartIndex: courseSetCount,
            rowEndIndex: courseSetCount + plan_course_sets.length - 1,
            rowSpan: plan_course_sets.length,
            colSpan: 1,
          };
        });
        courseSetCount += plan_course_sets.length;
      } else {
        cache = [
          {
            ...group,
            key: `${group.id}-0`,
            planCourseSet: {
              course_set: {},
            },
            termWeekHours: [],
            rowStartIndex: courseSetCount,
            rowEndIndex: courseSetCount + 1,
            rowSpan: 1,
            colSpan: 3,
          },
        ];
        courseSetCount += 1;
      }
      return groupCourses.concat(cache);
    }, []);
    // 计算课时分配
    const termsArray = Array(termsCount).fill(null);
    this.columns.splice(9, 1, {
      title: '按学期总课时',
      align: 'center',
      children: termsArray.map((h, index) => ({
        title: `${index + 1}`,
        align: 'center',
        dataIndex: `termWeekHours.${index}`,
        width: 50,
      })),
    });
  }
}
</script>

<template lang="pug">
a-table.teaching-course-set-group-tablle(
  :columns="columns"
  :dataSource="flatGroupCourses"
  :pagination="false"
  :loading="loading"
  :scroll="{ x: '100%' }"
  rowKey="key"
  size="middle"
  bordered)
</template>

<style lang="stylus">
.teaching-course-set-group-tablle
  .ant-table-thead
    background #FAFAFA
  .group-cell
    background #FAFAFA
</style>
