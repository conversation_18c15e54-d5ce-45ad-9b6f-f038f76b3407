<script lang="ts">
import { Component, Vue, Prop, Model } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class WeekSelector extends Vue {
  @Model('change', { type: Number, default: 1 }) value!: number;
  @Prop({ type: Number, default: 18 }) max!: number;

  get week() {
    return this.value;
  }
  set week(val: number) {
    this.$emit('change', val);
  }

  changeWeek(val: number) {
    const number = this.week + val;
    if (number < 1) {
      this.week = 1;
      return;
    }
    if (number > this.max) {
      this.week = this.max;
      return;
    }
    this.week = number;
  }
}
</script>

<template lang="pug">
a-button-group.week-buttons
  a-button(@click="changeWeek(-1)" :disabled="week === -1" ghost)
    a-icon(type="left")
  a-button(ghost)
    span.week-number 第 {{ week }} 周
  a-button(@click="changeWeek(+1)" :disabled="week === max" ghost)
    a-icon.week-icon(type="right")
</template>

<style lang="stylus" scoped>
.week-buttons
  margin-left 12px
  &:first-child
    margin-left 0px
  button
    margin-left 26px
    padding 0px
    border none
    color #808080
    &:hover
      color #3DA8F5
  .week-number
    vertical-align 0.02em
    padding 0 10px
    font-weight bold
    width 70px
</style>
