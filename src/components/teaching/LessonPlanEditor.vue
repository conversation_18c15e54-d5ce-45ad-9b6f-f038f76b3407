<script lang="ts">
/**
 * Events:
 *  updatePlan(formData)
 *  deletePlan(formData)
 *  createItem(formData)
 *  updateItem(formData)
 *  deleteItem(id)
 */
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { ILessonPlan } from '@/models/teaching/lesson_plan';
import { ILessonItem } from '@/models/teaching/lesson_item';
import { compact } from 'lodash';
import { IFile } from '@/models/file';
import Attachments from '@/components/global/Attachments.vue';
import FileUploader from '@/components/global/FileUploader.vue';

interface LocalLessonPlan extends ILessonPlan {
  sectionsArray: string[];
}

@Component({
  components: {
    FileUploader,
    Attachments,
  },
})
export default class LessonPlanEditor extends Vue {
  @Prop({ type: Object, default: () => ({}) }) plan!: ILessonPlan;
  @Prop({ type: Array, default: () => [] }) sectionArray!: string[]; // 可选课时列表

  innerPlan: LocalLessonPlan = {
    sectionsArray: [],
  };
  showMenuPlanId: number = 0;
  planTimer: any = null;
  itemTimer: any = null;
  itemLoading: boolean = false;

  @Watch('plan', { deep: true })
  onPlanChange() {
    this.innerPlan = Object.assign(this.plan, {
      sectionsArray: compact((this.plan.sections || '').split(',')),
    });
  }

  // lesson_plan sections
  showSectionMenu() {
    this.showMenuPlanId = this.innerPlan.id!;
  }
  onSectionMenuChange(visible: boolean) {
    this.showMenuPlanId = visible ? this.showMenuPlanId : 0;
  }
  addPlanSection(section: string) {
    this.updatePlanSections([...this.innerPlan.sectionsArray, section]);
  }
  removePlanSection(sectionIndex: number) {
    const sectionsArray = [...this.innerPlan.sectionsArray];
    sectionsArray.splice(sectionIndex, 1);
    this.updatePlanSections(sectionsArray);
  }
  updatePlanSections(sectionsArray: string[]) {
    this.$set(this.innerPlan, 'sectionsArray', sectionsArray);
    const sectionsString = sectionsArray.length ? `,${sectionsArray.toString()},` : '';
    this.$set(this.innerPlan, 'sections', sectionsString);
    // Event: update plan sections
    this.$emit('updatePlan', {
      id: this.plan.id,
      sections: sectionsString,
    });
  }
  // lesson_plan manage
  onDeletePlan(id: number) {
    this.$emit('deletePlan', id);
  }
  onPlanInputChange() {
    clearTimeout(this.planTimer);
    this.planTimer = setTimeout(() => {
      this.$emit('updatePlan', this.innerPlan);
    }, 1000);
  }
  // lesson_item manage
  onDeleteItem(itemId: number, itemIndex: number) {
    this.$delete(this.innerPlan.lesson_items!, itemIndex);
    this.$emit('deleteItem', itemId, itemIndex);
  }
  onItemInputChange(item: ILessonItem) {
    clearTimeout(this.itemTimer);
    this.itemTimer = setTimeout(() => {
      this.$emit('updateItem', item);
    }, 1000);
  }
  // item 新增附件
  onFileUploaded(file: IFile) {
    this.$emit('createItem', {
      parentId: this.plan.id!,
      item_type: file.mimeType.split('/').shift() || 'application',
      title: '',
      attachments: {
        attachments: [file],
      },
    });
  }
  // item 文件重传
  onItemFileStartUpdate(item: ILessonItem) {
    this.itemLoading = true;
  }
  async onItemFileUpddated(file: IFile, item: ILessonItem, itemIndex: number) {
    const patchData = {
      id: item.id,
      item_type: file.mimeType.split('/').shift() || 'application',
      attachments: {
        attachments: [file],
      },
    };
    this.$emit('updateItem', patchData);
    this.$set(this.innerPlan.lesson_items!, itemIndex, { ...item, ...patchData });
    this.itemLoading = false;
  }
  // 链接类型 item
  addLinkItem(plan: LocalLessonPlan) {
    this.$emit('createItem', {
      parentId: plan.id!,
      item_type: 'link',
      title: '',
      attachments: {
        link: '',
      },
    });
  }
}
</script>

<template lang="pug">
.lesson-plan-editor
  .plan-header.flex-between
    .sections
      .label 课时：
      a-tag.section(
        v-for="(section, sectionIndex) in innerPlan.sectionsArray"
        :key="section"
        :closable="true"
        color="blue"
        @close="removePlanSection(sectionIndex)")
        | 课时 {{ section }}
      a-dropdown(
        v-if="activatedSections.length > 0"
        :overlayStyle="{ height: '200px', overflow: 'auto', 'box-shadow': '0px 7px 21px 0px rgba(0,0,0,.1)' }"
        @change="onSectionMenuChange")
        a-menu(slot="overlay")
          a-menu-item(
            v-for="sec in activatedSections"
            :key="sec"
            @click="addPlanSection(sec)")
            span.menu-text &nbsp;&nbsp;课时 {{ sec }}&nbsp;&nbsp;
        a-button.new-section-button(
          icon="plus"
          shape="circle"
          size="small"
          type="primary"
          @click="showSectionMenu")
    span.plan-actions(v-loading="lessonPlanStore.loading")
      PopoverConfirm(
        title="删除"
        content="您确认要删除该资源吗？"
        placement="bottomRight"
        @confirm="onDeletePlan(innerPlan.id)")
        IconTooltip(icon="delete" tips="删除")
  .info
    .input-item
      .label 标题
      a-input.input(v-model="innerPlan.title" placeholder="请输入标题" @input="onPlanInputChange")
    .input-item
      .label 副标题
      a-input.input(v-model="innerPlan.subject" placeholder="请输入副标题" @input="onPlanInputChange")
    .input-item
      .label 描述
      a-textarea.input(
        v-model="innerPlan.body"
        placeholder="请输入描述"
        :autoSize="{ minRows: 2 }"
        @input="onPlanInputChange")
  .lesson-item(v-for="(item, itemIndex) in innerPlan.lesson_items" :key="item.id")
    .delete-item
      PopoverConfirm(
        title="删除"
        content="您确认要删除该资源吗？"
        placement="bottomRight"
        @confirm="onDeleteItem(item.id, itemIndex)")
        a-icon(type="delete")
    //- 链接
    a-row(:gutter="16" v-if="item.item_type === 'link'")
      a-col(:span="12")
        .box
          .name 链接描述
          a-input(v-model="item.title" placeholder="请输入链接描述" @input="onItemInputChange(item)")
      a-col(:span="12")
        .box
          .name 链接地址
          a-input(v-model="item.attachments.link" placeholder="请输入链接地址" @input="onItemInputChange(item)")
    //- 文件
    a-row(:gutter="16" v-else)
      a-col(:span="12")
        .box
          .name {{ itemTypeZh[item.item_type] || '其他附件' }} (可预览)
          Attachments(:attachments="item.attachments.attachments")
          .update-uploader
            FileUploader(
              :multiple="false"
              :showList="false"
              :useCdn="true"
              :removeAfterSuccess="true"
              @start="onItemFileStartUpdate(item)"
              @success="(file) => { onItemFileUpddated(file, item, itemIndex) }")
              TextButton(
                icon="redo"
                :loading="loadingItemId === item.id"
                :disabled="loadingItemId === item.id")
                | 重新上传
      a-col(:span="12")
        .box
          .name {{ itemTypeZh[item.item_type] || '其他附件' }}描述
          a-textarea(
            v-model="item.title"
            placeholder="请输入描述"
            :autoSize="{ minRows: 4 }"
            @input="onItemInputChange(item)")
  .new-lesson-item
    a-row(:gutter="16")
      a-col(:span="12")
        FileUploader.uploader(
          :useCdn="true"
          :removeAfterSuccess="true"
          @success="onFileUploaded")
          a-button(
            block
            icon="plus-circle"
            theme="filled"
            size="large")
            | 上传附件
      a-col(:span="12")
        a-button(
          block
          icon="plus-circle"
          theme="filled"
          size="large"
          @click="addLinkItem(plan, planIndex)")
          | 资源链接
</template>

<style lang="stylus" scoped>
.lesson-plan-editor
  background #FAFAFA
  border-radius 4px
  margin-bottom 20px
  border 0
  padding 0 16px 16px
  .plan-header
    padding 12px 0
    .sections
      display flex
      align-items center
      flex-wrap wrap
      width 100%
      .label
        font-size 14px
        color rgba(56,56,56,1)
        line-height 20px
      .section
        height 28px
        border-radius 28px
        padding 0 12px
        line-height 26px
        font-size 14px
        &:hover
          padding-right 30px
        .anticon-close-circle
          margin-left 5px
          line-height 28px
    .plan-actions
      flex-shrink 0
  .info
    background rgba(255,255,255,1)
    border-radius 4px
    border 1px solid rgba(232,232,232,1)
    padding 0 16px
    margin-bottom 8px
    position relative
    overflow hidden
    .input-item
      display flex
      padding 10px 0
      border-bottom 1px solid #E8E8E8
      align-items baseline
      &:last-child
        border-bottom none
      .label
        width 64px
        flex-shrink 0
        color #808080
      .input
        font-size 14px
        display block
        border none
  .lesson-item
    background rgba(255,255,255,1)
    border-radius 4px
    border 1px solid rgba(232,232,232,1)
    padding 12px 16px
    margin-bottom 8px
    position relative
    overflow hidden
    &:hover
      .delete-item
        display block
    .delete-item
      text-align center
      position absolute
      top 0
      right 0
      background #3DA8F5
      height 20px
      width 20px
      line-height 20px
      display none
      color #fff
      cursor pointer
      z-index 100
    .box
      .name
        font-size 14px
        color rgba(128,128,128,1)
        line-height 20px
        margin-bottom 8px
      .update-uploader
        padding 12px
        text-align center
  .new-lesson-item
    .uploader
      width 100%
</style>
