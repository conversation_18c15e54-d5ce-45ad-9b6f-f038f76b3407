<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class CountDownClock extends Vue {
  @Prop({ type: Number, default: 0 }) seconds!: number;
  @Prop({ type: Number, default: 0 }) total!: number;
  @Prop({ type: Number, default: 80 }) width!: number;
  @Prop({ type: Boolean, default: false }) loading!: boolean;
  @Prop({ type: String, default: 'progress' }) mode!: string;
  @Prop({ type: String, default: '' }) text!: string;
  @Prop({ type: String, default: '' }) finishText!: string;
  @Prop({
    type: String,
    default: 'primary',
    validator: (val: string) => {
      return ['primary', 'success', 'warning', 'error'].includes(val);
    },
  })
  status!: string;

  duration: number = 0;
  hourString: string = '00';
  minuteString: string = '00';
  secondString: string = '00';
  percent: number = 0;
  timer: any = null;
  clickDisable: boolean = false;

  get strokeColor() {
    return (
      ({
        primary: '#3DA8F5',
        success: '#75C940',
        warning: '#eb9e05',
        error: '#e50114',
      } as IObject)[this.status] || '#bbb'
    );
  }
  get active() {
    return this.duration > 0;
  }

  mounted() {
    this.start();
  }

  @Watch('seconds')
  onSecondsChange() {
    this.start();
  }

  beforeDestroy() {
    this.stop();
  }

  start() {
    this.duration = this.seconds;
    if (this.seconds > 0) {
      clearInterval(this.timer);
      this.nextTick();
      this.timer = setInterval(this.nextTick, 1000);
    }
  }

  nextTick() {
    const secPerMinute = 60;
    const secPerHour = 60 * 60;
    const hours = Math.floor(this.duration / secPerHour);
    this.hourString = String(hours).padStart(2, '0');
    const minutesLeft = this.duration - hours * secPerHour;
    const minutes = Math.floor(minutesLeft / secPerMinute);
    this.minuteString = String(minutes).padStart(2, '0');
    const seconds = minutesLeft - minutes * secPerMinute;
    this.secondString = String(seconds).padStart(2, '0');
    this.percent = Math.round((this.duration / (this.total || this.seconds)) * 100);
    if (this.duration === 0) {
      this.stop();
      this.$nextTick(() => {
        this.$emit('finish');
      });
    } else {
      this.duration -= 1;
    }
    this.$nextTick(() => {
      this.$emit('tick', this.duration);
    });
  }

  stop() {
    clearInterval(this.timer);
  }

  delayClick() {
    if (this.clickDisable || !this.active || this.loading) return;
    this.$emit('click', this.duration);
    this.clickDisable = true;
    setTimeout(() => {
      this.clickDisable = false;
    }, 1000);
  }
}
</script>

<template lang="pug">
.clock-wrapper
  .count-down-clock(v-if="mode === 'progress'")
    a-progress(
      type="circle"
      :percent="percent"
      :width="width"
      :strokeWidth="2"
      :strokeColor="strokeColor"
      status="normal"
      strokeLinecap="square"
      :format="() => ''")
    .progress-content(
      :class="{ 'disabled-content': !active, [`content-${status}`]: true }"
      v-loading="loading"
      @click="delayClick")
      slot(:hours="hourString" :minutes="minuteString" :seconds="secondString")
        .clock-info
          .name
            | {{ active ? text : finishText }}
          .times
            span.tick {{ hourString }}:
            span.tick {{ minuteString }}:
            span.tick {{ secondString }}
  .text-mode(v-else)
    .tick {{ hourString }}
    .symbol :
    .tick {{ minuteString }}
    .symbol :
    .tick {{ secondString }}
</template>

<style lang="stylus" scoped>
.clock-wrapper
  display inline-flex
  align-items center

.count-down-clock
  position relative
  display inline-block
  .progress-content
    position absolute
    top 4px
    left 4px
    right 4px
    bottom 4px
    background #3DA8F5
    color #fff
    border-radius 50%
    cursor pointer
    transition background 1s linear 0.5s
    &:hover
      background darken(#3DA8F5, 10%)
    .clock-info
      text-align center
      position absolute
      width 100%
      top 50%
      transform translateY(-50%)
      .name
        font-size 14px
        font-weight 500
        color rgba(255,255,255,1)
        line-height 20px
        margin-bottom 2px
      .times
        font-size 12px
        font-weight 400
        color rgba(255,255,255,1)
        line-height 16px
  .disabled-content
    background #bbb
    color #888888
    cursor not-allowed
    &:hover
      background #bbb

.text-mode
  display inline-flex
  align-items center
  font-size 28px
  letter-spacing 2px
  font-weight bold
  line-height 1
  .tick
    font-family 'DINCond'
  .symbol
    margin 0 4px
</style>
