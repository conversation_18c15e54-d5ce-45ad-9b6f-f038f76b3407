<script lang="ts">
import { Component, Vue, Prop, Model } from 'vue-property-decorator';
import { IQuestionSet, CatalogQuestionSet, QuestionSetTypeText, QuestionSetTypes } from '@/service/question_set';
import { IQuestionCatalog, QuestionCatalogService } from '@/service/question_catalog';

@Component({
  components: {},
})
export default class QuestionSetSelector extends Vue {
  @Model('input', { type: Boolean, default: true }) private value!: boolean;
  @Prop({ type: Boolean, default: false }) private isExam!: boolean;

  catalogs: IQuestionCatalog[] = [];
  activeCatalog: IQuestionCatalog = {};
  questionSets: IQuestionSet[] = [];
  loading: boolean = false;

  get visible() {
    return this.value;
  }
  set visible(visible: boolean) {
    this.$emit('input', visible);
  }
  get QuestionSetTypeText() {
    return QuestionSetTypeText;
  }

  mounted() {
    this.fetchCatalogs();
  }

  async fetchCatalogs() {
    const { data } = await new QuestionCatalogService().fetch({
      page: 1,
      per_page: 1000,
    });
    this.catalogs = data.res_catalogs;
    this.activeCatalog = this.catalogs[0] || {};
    if (this.activeCatalog.id) {
      this.fetchQuestionSets();
    }
  }

  async fetchQuestionSets() {
    this.loading = true;
    const questionSetService = new CatalogQuestionSet({
      questionCatalogId: this.activeCatalog.id!,
    });
    const q: IObject = {};
    if (!this.isExam) {
      q.type_not_eq = QuestionSetTypes.Exam;
    }
    const { data } = await questionSetService
      .fetch({
        page: 1,
        per_page: 1000,
        q,
      })
      .finally(() => {
        this.loading = false;
      });
    this.questionSets = data.question_sets;
  }

  clickCatalog(catalog: IQuestionCatalog) {
    this.activeCatalog = catalog;
    this.fetchQuestionSets();
  }

  select(set: IQuestionSet) {
    this.$emit('select', { ...set, question_catalog_id: this.activeCatalog.id }, this.activeCatalog);
    this.visible = false;
  }
}
</script>

<template lang="pug">
a-modal(v-model="visible" title="选择试卷" :footer="null" width="700px" centered)
  .set-selector
    .catalogs
      .catalog(
        v-for="catalog in catalogs"
        :key="catalog.id"
        :class="{ 'active-catalog': activeCatalog.id === catalog.id }"
        @click="clickCatalog(catalog)")
        | {{ catalog.title }}
    .sets(slot="right" v-loading="loading")
      .title 试卷列表
      Empty(desc="暂无试卷" v-if="questionSets.length === 0")
      .set(v-for="set in questionSets" :key="set.id" @click="select(set)")
        h4.title {{ set.name }}
        .mode.flex-between
          span(v-if="set.answer_mode === 'disable'")
            | 隐藏答案
          span(v-else-if="set.answer_mode === 'enable'")
            | 公布答案
          span(v-else-if="set.answer_mode === 'auto'")
            | 答题后自动公布答案
          span {{ QuestionSetTypeText[set.type] }}
</template>

<style lang="stylus" scoped>
.set-selector
  display flex
  margin -24px
  height 500px
  .title
    margin-bottom 10px
    font-weight bold
    font-size 14px
  .catalogs
    width 280px
    border-right 1px solid #eeeeee
    .catalog
      margin-bottom 1px
      padding 16px
      background #f8f8f8
      cursor pointer
      &:hover
        color #3DA8F5
    .active-catalog
      background rgba(237, 247, 255, 1)
      color #3DA8F5
      &:hover
        color #3DA8F5
  .sets
    overflow-y auto
    padding 14px 24px
    width 100%
    height 100%
    .set
      margin-bottom 10px
      padding 10px
      border-radius 4px
      background #f8f8f8
      cursor pointer
      &:hover
        .title
          color #3DA8F5
</style>
