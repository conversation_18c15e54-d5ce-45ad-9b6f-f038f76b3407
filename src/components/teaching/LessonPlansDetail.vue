<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { ILessonPlan, LessonPlan } from '@/models/teaching/lesson_plan';
import LessonPlanView from './LessonPlanView.vue';
import { ILessonItem } from '@/models/teaching/lesson_item';

@Component({
  components: {
    LessonPlanView,
  },
})
export default class TeachingLessonPlansDetail extends Vue {
  @Prop({ type: Array, default: () => [] }) lessonPlans!: ILessonPlan[];
  @Prop({ type: Boolean, default: false }) showRecorderEntry!: boolean;
  @Prop({ type: Boolean, default: false }) showLoadMore!: boolean;

  selectedPlanId: number = 0;

  getPlanSectionTitle(plan: ILessonPlan) {
    return LessonPlan.getSectionTitle(plan);
  }
  scrollToPlan(plan: ILessonPlan) {
    this.selectedPlanId = plan.id!;
    const dom: HTMLElement | null = document.getElementById(`plan_${plan.id}`);
    if (dom) {
      dom.scrollIntoView();
    }
  }
  // 预览 lesson plan_item 附件
  onPreviewLessonItem(lessonPlan: ILessonPlan, planIndex: number, lessonItem: ILessonItem, itemIndex: number) {
    this.$emit('itemPreview', {
      lessonPlan,
      planIndex,
      lessonItem,
      itemIndex,
    });
  }
  onShowItem(lessonItem: ILessonItem) {
    this.$emit('showItem', lessonItem);
  }
}
</script>

<template lang="pug">
.lesson-plans-collapse
  Empty(desc="暂无课程资源" v-if="lessonPlans.length === 0")
  a-row.plans-row(:gutter="20" v-else)
    a-col.plans-col(:span="7")
      .catalogue
        .item(
          v-for="plan in lessonPlans"
          :key="plan.id"
          @click="scrollToPlan(plan)"
          :class="{ 'item-active': selectedPlanId === plan.id }")
          .header
            .section {{ getPlanSectionTitle(plan) }}
            .icon(v-if="selectedPlanId === plan.id")
              a-icon(type="right")
          .title {{ plan.title }}
    a-col.plans-col(:span="17")
      .lesson-plans
        LessonPlanView(
          v-for="(plan, index) in lessonPlans"
          :id="`plan_${plan.id}`"
          :key="plan.id"
          :plan="plan"
          :showRecorderEntry="showRecorderEntry"
          @showItem="(lessonItem) => { onShowItem(lessonItem) }"
          @preview="(lessonItem, itemIndex) => { onPreviewLessonItem(plan, index, lessonItem, itemIndex) }")
</template>

<style lang="stylus" scoped>
.lesson-plans-collapse
  padding 20px 0
  margin 0 auto
  display block
  height 100%
  max-width 1200px
  .plans-row, .plans-col
    height 100%
  .catalogue
    background #FAFAFA
    padding 7px 16px
    overflow auto
    max-height 100%
    .item
      width 100%
      padding 9px 0
      font-size 14px
      color rgba(128,128,128,1)
      line-height 20px
      cursor pointer
      .header
        display flex
        margin-bottom 2px
        .section
          width 100%
          overflow hidden
          text-overflow ellipsis
          white-space nowrap
        .icon
          flex-shrink 0
          padding-left 20px
      .title
        font-size 14px
        font-weight 400
        color rgba(166,166,166,1)
        line-height 20px
        text-overflow ellipsis
        overflow hidden
        width 100%
        white-space nowrap
      &:hover
        .header .section
          color #383838
    .item-active
      color #3DA8F5
      &:hover
        .section
          color #3DA8F5 !important
  .lesson-plans
    height 100%
    overflow auto
</style>
