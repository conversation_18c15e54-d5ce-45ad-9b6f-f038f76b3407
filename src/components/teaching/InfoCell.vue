<template lang="pug">
.cell-page
  .cell-title {{ info.title }}
  .cell-content
    .cover-image(:style="{ backgroundImage: 'url(' + info.cover_image + ')'}")
    .info
      .content(:class="{'line-four': cellType === 'news'}")
        span(v-if="info.subject") {{ info.subject}}
        span(v-html="info.content" v-else)
      template(v-if="cellType === 'info'")
        //- button 查看全部
          a-icon(type="right")
        .info-bottom
          span
            a-tooltip(:title="$moment(info.created_at).format('YYYY/MM/DD HH:mm:ss')")
              span {{ $moment(info.created_at).fromNow() }}
            a-divider(type="vertical")
            span {{ info.likes_count }} {{ $t('common.like') }}
            a-divider(type="vertical")
            span {{ info.stars_count }} {{ $t('common.star') }}
            a-divider(type="vertical")
            span {{ info.views_count }} {{ $t('common.view') }}
          span {{ info.created_at | format }}
  .cell-bottom(v-if="cellType === 'news'")
    span {{ $t('common.view') }} {{ info.views_count }}
    span {{ info.created_at | format }}
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import store from '@/store';

@Component({
  components: {},
})
export default class InfoCell extends Vue {
  @Prop({ type: Object, default: () => [] }) private info?: any;
  @Prop({ type: String, default: 'info' }) private cellType?: string;
}
</script>

<style lang="stylus" scoped>
.cell-page
  padding 24px 0px
  width 100%
  cursor pointer
  .cell-title
    width 100%
    color rgba(51, 51, 51, 1)
    font-weight 500
    font-size 18px
    line-height 24px
  .cell-content
    display flex
    margin-top 10px
    width 100%
    .cover-image
      margin-right 12px
      min-width 150px
      width 150px
      height 88px
      border-radius 2px
      background #eee
      background-position center
      background-size cover
      background-repeat no-repeat
    .info
      max-height 88px
      width 100%
      color rgba(102, 102, 102, 1)
      font-size 14px
      line-height 22px
      .content
        display -webkit-box
        overflow hidden
        width 100%
        -webkit-box-orient vertical
        -webkit-line-clamp 3
      .line-four
        -webkit-line-clamp 4
      button
        border none
        color rgba(201, 101, 101, 1)
        font-size 14px
        cursor pointer
        .anticon
          vertical-align -0.1em !important
      .info-bottom
        display flex
        justify-content space-between
        align-items center
        margin-top 8px
        color #B2B2B2
        font-weight 500
        font-size 14px
        line-height 14px
  .cell-bottom
    display flex
    justify-content space-between
    align-items center
    margin-top 10px
    color #B2B2B2
    font-weight 500
    font-size 14px
    line-height 14px
</style>
