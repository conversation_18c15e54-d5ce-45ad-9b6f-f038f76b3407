<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { ITermPlan } from '@/models/teaching/term_plan';
import { IPlanCourseSet } from '@/models/teaching/plan_course_set';
import { ICourse } from '@/models/teaching/course';
import { ICourseSet } from '@/models/teaching/course_set';
import { ICourseActivity } from '@/models/teaching/course_activity';
import courseActivityModel from '@/models/teaching/course_activity';

// 课程(course_set)-开课任务(course) 一对一 结构：
interface ICourseSetCourse extends IPlanCourseSet {
  key: string;
  planCourseSetId?: number;
  currentCourse: ICourse;
  groupIndex?: number;
  course_teachers_string: string;
  week_range: string;
  rowStartIndex: number;
  rowEndIndex: number;
  rowSpan: number;
  colSpan: number;
}

@Component({
  components: {},
})
export default class TermPlanTable extends Vue {
  @Prop({ type: Object, default: () => ({ plan_course_sets: [] }) }) termPlan!: ITermPlan;
  @Prop({ type: Boolean, default: false }) loading!: boolean;

  columns: any[] = [
    {
      title: '序号',
      dataIndex: 'groupIndex',
      key: 'groupIndex',
      width: 50,
      fixed: 'left',
      align: 'center',
      customRender: this.courseSetColRender,
    },
    {
      title: '课程序号',
      dataIndex: 'course_set_code',
      key: 'course_set_code',
      width: 70,
      customRender: this.courseSetColRender,
    },
    {
      title: '课程名称',
      dataIndex: 'course_set_name',
      key: 'course_set_name',
      customRender: this.courseSetColRender,
    },
    {
      title: '考核方式',
      dataIndex: 'exam_mode',
      key: 'exam_mode',
      align: 'center',
      customRender: this.courseSetColRender,
    },
    {
      title: '课程类别',
      dataIndex: 'course_set.category',
      align: 'center',
      customRender: this.courseSetColRender,
    },
    {
      title: '起始周',
      dataIndex: 'week_range',
      key: 'week_range',
      align: 'center',
      customRender: this.courseColRender,
    },
    { title: '课时', dataIndex: 'currentCourse.period', align: 'center', customRender: this.courseColRender },
    {
      title: '教师',
      dataIndex: 'course_teachers_string',
      key: 'course_teachers_string',
      align: 'center',
      customRender: this.courseColRender,
    },
    {
      title: '教学班',
      dataIndex: 'currentCourse.name',
      align: 'center',
      width: 200,
      customRender: this.courseColRender,
    },
    { title: '上课人数', dataIndex: 'currentCourse.std_count', align: 'center', customRender: this.courseColRender },
    {
      title: '',
      dataIndex: 'currentCourse.course_activity_count',
      key: 'course_activity_count',
      width: 80,
      align: 'center',
      scopedSlots: { customRender: 'course_activity_count' },
    },
  ];
  visible: boolean = false;
  currentCourseId: number = 0;
  courseActivities: ICourseActivity[] = [];
  activityLoading: boolean = false;

  // course_set - course 一对一展开
  // key: course.id
  get courseSetCourses(): ICourseSetCourse[] {
    let courseCount = 0;

    return (this.termPlan.plan_course_sets || [])
      .reduce((courseSetCourses: ICourseSetCourse[], planCourseSet: IPlanCourseSet, index: number) => {
        const courses = planCourseSet.courses || [];
        let cache: ICourseSetCourse[] = [];
        if (courses.length) {
          cache = courses.map((currentCourse: ICourse) => ({
            ...planCourseSet,
            key: `course_id_${currentCourse.id}`,
            groupIndex: index + 1,
            id: currentCourse.id, // course.id
            planCourseSetId: planCourseSet.id, // planCourseSet.id
            currentCourse,
            course_teachers_string: (currentCourse.teachers || []).map(o => o.name).join('、'),
            week_range: `${currentCourse.start_week}~${currentCourse.end_week}周`,
            rowStartIndex: courseCount,
            rowEndIndex: courseCount + courses.length - 1,
            rowSpan: courses.length,
            colSpan: 1,
          }));
          courseCount += courses.length;
        } else {
          cache = [
            {
              ...planCourseSet,
              key: `plan_course_set_id_${planCourseSet.id}`,
              groupIndex: index + 1,
              id: 0, // course.id
              planCourseSetId: planCourseSet.id, // planCourseSet.id
              currentCourse: {},
              course_teachers_string: '',
              week_range: '',
              rowStartIndex: courseCount,
              rowEndIndex: courseCount + 1,
              rowSpan: 1,
              colSpan: 1,
            },
          ];
          courseCount += 1;
        }
        return courseSetCourses.concat(cache);
      }, [])
      .map((o: ICourseSetCourse, index: number) => ({ ...o, index: index + 1 }));
  }

  created() {
    (window as any).onCourseSetColClick = this.onCourseSetColClick;
    (window as any).onCourseColClick = this.onCourseColClick;
  }
  onCourseSetColClick(courseSetId: number) {
    if (courseSetId) {
      window.open(`/teaching/admin/course_sets/${courseSetId}`, '_blank');
    }
  }
  onCourseColClick(courseId: number) {
    if (courseId) {
      window.open(`/teaching/teacher/courses/${courseId}`, '_blank');
    }
  }
  courseSetColRender(value: string, row: ICourseSetCourse, index: number) {
    return {
      children: value,
      attrs: {
        rowSpan: index === row.rowStartIndex ? row.rowSpan : 0,
        onclick: `onCourseSetColClick(${row.course_set_id})`,
        'data-cours_set_id': row.planCourseSetId,
      },
    };
  }
  courseColRender(value: string, row: ICourse, index: number) {
    return {
      children: value,
      attrs: {
        onclick: `onCourseColClick(${row.id})`,
        'data-cours_id': row.id,
      },
    };
  }
  async showActivities(record: ICourseSetCourse) {
    this.courseActivities = [];
    this.currentCourseId = record.id!;
    this.visible = true;
    setTimeout(this.fetchActivities, 300);
  }
  async fetchActivities() {
    try {
      this.activityLoading = true;
      // courseActivityModel 无需设置 role, fetchByCourseId 为通用接口
      const { data } = await courseActivityModel.fetchByCourseId(this.currentCourseId, { page: 1, per_page: 100 });
      this.courseActivities = data.course_activities;
      this.activityLoading = false;
    } catch (error) {
      this.courseActivities = [];
      this.activityLoading = false;
    }
  }
}
</script>

<template lang="pug">
a-table.emphasize-ant-table(
  :columns="columns"
  :dataSource="courseSetCourses"
  :pagination="false"
  :loading="loading"
  :scroll="{ x: '100%' }"
  :rowClassName="() => 'click-row'"
  rowKey="key"
  size="middle"
  bordered)
  template(slot="course_activity_count" slot-scope="text, record")
    Popover(
      v-if="record.id"
      :visible="visible && record.id === currentCourseId"
      title="课时详情"
      placement="bottomRight")
      template(#main)
        .course-activities(v-loading="activityLoading")
          .empty(v-if="!(courseActivities.length && visible)")
            | 无课程
          .activity(v-for="ac in courseActivities" :key="ac.id")
            .name {{ ac.teacher_name }}
            KvCell(
              name="周"
              size="small"
              icon="calendar"
              :value="ac.week_arr_zh")
            KvCell(
              name="时间"
              size="small"
              icon="clock-circle"
              :value="`每${ac.weekday_zh}，${ ac.start_unit } ~ ${ac.end_unit} 节`")
            KvCell(
              name="教室"
              size="small"
              icon="home"
              :value="ac.classroom_name")
      span.count-hover(@click="showActivities(record)")
        a-icon(type="eye")
        |  {{ text }}
</template>

<style lang="stylus" scoped>
.count-hover
  &:hover
    cursor pointer
    color #3DA8F5
.course-activities
  height 210px
  overflow auto
  margin -6px
  .empty
    color #a8a8a8
    text-align center
    margin 30px
  .activity
    background rgba(255,255,255,1)
    border-radius 4px
    border 1px solid rgba(232,232,232,1)
    margin-bottom 8px
    padding 14px 14px 8px
    .name
      font-size 14px
      font-weight 500
      color rgba(56,56,56,1)
      line-height 20px
</style>
