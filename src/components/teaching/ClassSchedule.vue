<script lang="ts">
/**
 * 学期课表的样式组件
 * model: lesson
 * RowHead 会显示具体的【日期】、【星期】和【节数】
 */
import { Component, Vue, Prop } from 'vue-property-decorator';
import ScrollTable from '@/components/teaching/ScrollTable.vue';
import lesson, { Lesson, ILesson } from '@/models/teaching/lesson';
import { defaultColumns, IScheduleColumn } from '@/models/teaching/semester';

@Component({
  components: {
    ScrollTable,
  },
})
export default class ClassSchedule extends Vue {
  @Prop({ type: Array, default: () => [] }) lessons!: ILesson[]; // 所有课程
  @Prop({ type: Array, default: () => defaultColumns }) columns!: IScheduleColumn[]; // 时间列
  @Prop({ type: Boolean, default: false }) loading!: boolean;
  @Prop({ type: Boolean, default: false }) isEvaluation!: boolean;

  get tableRecords() {
    return Lesson.getScheduleRecords(this.lessons);
  }
  get role() {
    return this.$store.getters.currentRole;
  }

  getLessonClass(lessonData: ILesson) {
    const classes: IObject = {};
    if (lessonData.start_unit! < 5) {
      classes.am = true;
    } else if (lessonData.start_unit! < 9) {
      classes.pm = true;
    } else {
      classes.night = true;
    }
    return classes;
  }
  getColHeadStyle(index: number) {
    const styles: IObject = { borderBottom: '1px solid #e8e8e8', borderRight: '1px solid rgba(232, 232, 232, 0.3)' };
    if (index < 4) {
      return { ...styles, background: '#68B4EB' };
    }
    if (index < 8) {
      return { ...styles, background: '#F9AF36' };
    }
    return { ...styles, background: '#2F4169' };
  }
  getCellStyle(index: number) {
    switch (index) {
      case 0:
        return {
          borderLeft: '1px dashed #68B4EB',
          borderRight: '1px dashed #e8e8e8',
          borderBottom: '1px solid #e8e8e8',
        };
      case 3:
        return {
          borderRight: '1px dashed #68B4EB',
          borderBottom: '1px solid #e8e8e8',
        };
      case 7:
        return {
          borderRight: '1px dashed #F9AF36',
          borderBottom: '1px solid #e8e8e8',
        };
      case 11:
        return {
          borderRight: '1px dashed #2F4169',
          borderBottom: '1px solid #e8e8e8',
        };
      default:
        return {
          borderRight: '1px dashed #e8e8e8',
          borderBottom: '1px solid #e8e8e8',
        };
    }
  }
  onClickLesson(lesson: ILesson) {
    this.$emit('show', lesson, document.getElementById(`lesson_${lesson.id}`));
  }
}
</script>

<template lang="pug" scoped>
ScrollTable.component-class-schedule(:records="tableRecords" :columns="columns" :loading="loading")
  template(#colHead="{ data, colIndex }")
    .col-head(:style="getColHeadStyle(colIndex)")
      .title {{ data.title }}
      .range {{ data.range }}
  template(#rowHead="{ data }")
    .week-day
      .date {{ data.week }}
      .date {{ data.date }}
      .count 共{{ data.lessonCount }}节
  template(#grid-cell="{ colIndex }")
    .grid-cell(:style="getCellStyle(colIndex)")
  template(#default="{ data }")
    .lesson-cell(
      :id="`lesson_${data.id}`"
      @click="onClickLesson(data)"
      :class="{ 'lesson-pointer': isEvaluation }")
      .lesson(:class="getLessonClass(data)")
        .name {{ data.course_set_name }} [{{ data.course_set_exam_mode }}]
        .info
          span {{ data.teacher_name || '教师？' }}，
          span {{ data.classroom_name || '班级？' }}，
          span {{ data.course_name }}，
          span {{ data.start_section }}~{{ data.end_section }}课时
        template(v-if="isEvaluation")
          //- 教师
          .tags.flex-between(v-if="role === 'teacher'")
            span
              span.text-primary(v-if="data.evaluation_stat.count")
                | 评价：{{ data.evaluation_stat.done || '-' }}
                |  / {{ data.evaluation_stat.count }}
              span.text-gray(v-else)
                | 无评价活动
            span.tag-cell
              TaTag(size="mini" type="primary" v-if="data.report_stat && data.report_stat.total")
                | 有作业
              TaTag(size="mini" v-else)
                | 无作业
          //- 学生
          .tags(v-if="role === 'student'")
            span.tag-cell
              TaTag(size="mini" v-if="!data.register_state")
                | 等待签到开启
              TaTag(size="mini" type="primary" v-else-if="data.register_state === 'undo'")
                | 待签到
              TaTag(size="mini" type="success" v-else-if="data.register_state === 'done'")
                | 已签到
              TaTag(size="mini" type="warning" v-else-if="data.register_state === 'late'")
                | 迟到
            span.tag-cell
              TaTag(size="mini" type="primary" v-if="data.student_evaluation_state === 'todo'")
                | 待评价
              TaTag(size="mini" type="success" v-else-if="data.student_evaluation_state === 'done'")
                | 已评价
              TaTag(size="mini" type="warning" v-else-if="data.student_evaluation_state === 'doing'")
                | 待提交评价
            span.tag-cell
              TaTag(type="default" size="mini" v-if="data.report_state === 'none'")
                | 无作业
              TaTag(type="warning" size="mini" v-if="data.report_state === 'pending'")
                | 未提交
              TaTag(type="primary" size="mini" v-if="data.report_state === 'published'")
                | 已提交
              TaTag(type="success" size="mini" v-if="data.report_state === 'scored'")
                | 已评分
</template>

<style lang="stylus">
.component-class-schedule
  .grid-cell
    width 100%
    height 100%
    border-right 1px dashed rgba(232, 232, 232, 1)
  .col-head
    padding 10px 6px
    height 100%
    border-right 1px solid rgba(232, 232, 232, 0.3)
    color #fff
    .title
      margin-bottom 6px
      letter-spacing 0
      font-weight 500
      font-size 14px
      line-height 20px
    .range
      letter-spacing 0
      font-size 11px
      line-height 16px
  .week-day
    padding 20px 0
    width 66px
    text-align center
    .date
      margin-bottom 4px
      color rgba(128, 128, 128, 1)
      font-weight 500
      font-size 14px
      line-height 20px
    .count
      margin-top 16px
      color rgba(166, 166, 166, 1)
      font-weight 500
      font-size 12px
  .lesson-cell
    padding 6px
    .lesson-pointer
      cursor pointer
    .lesson
      padding 6px 4px
      border-radius 2px
      cursor pointer
      .name
        overflow hidden
        margin-bottom 8px
        width 100%
        color rgba(56, 56, 56, 1)
        text-overflow ellipsis
        white-space nowrap
        letter-spacing 0
        font-weight 500
        font-size 12px
        line-height 14px
      .info
        color rgba(128, 128, 128, 1)
        letter-spacing 0
        font-weight 400
        font-size 12px
        line-height 18px
        .tag-cell:first-child
          margin-right 6px
      .tags
        margin-top 6px
        .tag-cell
          margin-right 6px
          &:last-child
            margin-right 0px
      .evaluation
        display flex
        align-items center
        .ant-progress-inner
          background-color rgba(61, 168, 245, 0.2)
        .count
          flex-shrink 0
          margin-left 4px
          width 46px
          height 12px
          color #3DA8F5
          font-weight 600
          font-size 12px
          line-height 12px
        .completed
          color #75C940
    .am
      background #E1F0FB
    .pm
      background #FEF3E1
    .night
      background #E0E3E9
</style>
