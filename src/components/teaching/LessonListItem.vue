<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class LessonListItem extends Vue {
  @Prop({ type: Object, default: () => {} }) private lesson!: IObject;

  get lessonObj() {
    return {
      ...this.lesson,
      register_stat: this.lesson.register_stat || {},
      evaluation_stat: this.lesson.evaluation_stat || {},
    };
  }

  get role() {
    return this.$store.getters.currentRole;
  }

  onClickLesson(lesson: object) {
    this.$emit('click', lesson);
  }
}
</script>

<template lang="pug">
.lesson(@click="onClickLesson(lesson)")
  .name {{ lessonObj.course_set_name }}
  .item
    label 日期
    .gap-box
      span {{ lessonObj.date }}
      span {{ lessonObj.start_time }} - {{ lessonObj.end_time }}
      span {{ $utils.weekDay(lessonObj.weekday) }}
  .item
    label 节数
    span {{ lessonObj.start_unit }} - {{ lessonObj.end_unit }}（共 {{ lessonObj.unit_count }} 节）
  .item
    label 课时
    span {{ lessonObj.start_section }} ~ {{ lessonObj.end_section }}（共 {{ lessonObj.unit_count }} 课时）
  .item
    label 教师
    span {{ lessonObj.teacher_name }}
  .item
    label 教室
    span {{ lessonObj.classroom_name }}
  .item
    label 班级
    span {{ lessonObj.course_name }}
  .item
    label 人数
    span {{ lessonObj.course_std_count }} 人
  .statistic(v-if="role === 'teacher'")
    .count-info
      div 签到情况
      .count
        a-tooltip
          template(slot="title")
            span 已签到/迟到/未签到
          span.text-success {{ lessonObj.register_stat.done || '-' }} /
          span.text-warning  {{ lessonObj.register_stat.late || '-' }} /
          span.text-gray  {{ lessonObj.register_stat.undo || '-' }}
    .count-info
      div 评价
      .count(v-if="lessonObj.evaluation_stat.count")
        a-tooltip
          template(slot="title")
            span 已评价/评价总数
          span.text-primary(
            :class="{ 'text-success': lessonObj.evaluation_stat.todo === 0 }")
            | {{ lessonObj.evaluation_stat.done || '-' }} / {{ lessonObj.evaluation_stat.count }}
      .count.text-gray(v-else)
        | 无评价活动
    .count-info
      div 作业
      .count(v-if="lessonObj.report_stat && lessonObj.report_stat.total")
        a-tooltip
          template(slot="title")
            span 已提交/已评分/作业总数
          span.text-primary {{ Number(lessonObj.report_stat.total) - Number(lessonObj.report_stat.pending) }}/
          span.text-success {{ lessonObj.report_stat.scored }}/
          span.text-gray {{ lessonObj.report_stat.total }}
      .count.text-gray(style="font-size: 14px" v-else)
        | 未布置作业
  .statistic(v-if="role === 'student'")
    span.tag-cell
      TaTag(v-if="!lessonObj.register_state")
        | 等待签到开启
      TaTag(type="primary" v-else-if="lessonObj.register_state === 'undo'")
        | 待签到
      TaTag(type="success" v-else-if="lessonObj.register_state === 'done'")
        | 已签到
      TaTag(type="warning" v-else-if="lessonObj.register_state === 'late'")
        | 迟到
    span.tag-cell
      TaTag(type="primary" v-if="lessonObj.student_evaluation_state === 'todo'")
        | 待填写评价
      TaTag(type="success" v-else-if="lessonObj.student_evaluation_state === 'done'")
        | 已填写评价
      TaTag(type="warning" v-else-if="lessonObj.student_evaluation_state === 'doing'")
        | 待提交评价
    span.tag-cell
      TaTag(type="default" v-if="lessonObj.report_state === 'none'")
        | 无作业
      TaTag(type="warning" v-if="lessonObj.report_state === 'pending'")
        | 未提交
      TaTag(type="primary" v-if="lessonObj.report_state === 'published'")
        | 已提交
      TaTag(type="success" v-if="lessonObj.report_state === 'scored'")
        | 已评分
</template>

<style lang="stylus" scoped>
.lesson
  margin-bottom 12px
  padding 16px
  border 1px solid rgba(0, 0, 0, 0.08)
  border-radius 4px
  cursor pointer
  .name
    margin-bottom 8px
    color rgba(56, 56, 56, 1)
    font-weight 500
    font-size 14px
    line-height 20px
  .item
    display flex
    margin-bottom 4px
    height 20px
    color rgba(128, 128, 128, 1)
    font-size 14px
    line-height 20px
    label
      width 58px
    .gap-box
      display inline-block
      span
        margin-right 6px
  .statistic
    display flex
    margin-top 8px
    padding-top 12px
    border-top 1px solid #E8E8E8
    .count-info
      flex-grow 1
      color rgba(56, 56, 56, 1)
      font-weight 400
      font-size 14px
      line-height 20px
      .count
        margin-top 4px
        height 25px
        font-weight 500
        font-size 18px
        line-height 25px
    .tag-cell
      margin-right 6px
</style>
