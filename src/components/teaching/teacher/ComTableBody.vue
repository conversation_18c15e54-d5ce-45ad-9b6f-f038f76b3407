<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class StudentTableHeader extends Vue {
  @Prop({ type: String, default: '' }) prop!: string;
  @Prop({ type: Object, default: () => {} }) students!: IObject;

  get undo() {
    return (
      (this.students.studying_stat.lesson_register.undo || 0) +
      (this.students.studying_stat.lesson_register.absent || 0)
    );
  }
}
</script>

<template lang="pug">
.container-table-header
  .table-body
    .name {{ students.name }}
    .student-id {{ students.code }}
    .college {{ students.college_name }}
    .class {{ students.adminclass_name }}
    .sign-in
      .left {{ students.studying_stat.lesson_register.done || 0 }}
      .center {{ students.studying_stat.lesson_register.late || 0 }}
      .right {{ undo }}
    .evaluation
      .left {{ students.studying_stat.topic.count }}
      .right {{ students.studying_stat.topic.commnt_count }}
    .homework 
      .left {{ students.studying_stat.homework.pending || 0 }}
      .center {{ students.studying_stat.homework.published || 0 }}
      .right {{ students.studying_stat.homework.scored || 0 }}
</template>

<style lang="stylus" scoped>
.container-table-header
  .table-body
    display flex
    flex-direction row
    align-items center
    width 100%
    height 54px
    border-bottom 1px solid #E8E8E8
    border-left 1px solid #E8E8E8
    color #383838
    font-weight 400
    font-size 14px
    font-family PingFangSC-Regular, PingFang SC
    .name, .student-id, .college, .class
      overflow hidden
      padding-left 14px
      height 100%
      border-right 1px solid #E8E8E8
      text-overflow ellipsis
      white-space nowrap
      line-height 54px
    .name
      width 9%
    .student-id
      width 10%
    .college
      width 13.84%
    .class
      width 11.88%
    .sign-in, .homework
      width 20.8%
      height 100%
      border-right 1px solid #E8E8E8
    .evaluation
      width 15.21%
      height 100%
      border-right 1px solid #E8E8E8
    .sign-in, .evaluation, .homework
      display flex
      flex-direction row
      font-weight 400
      font-size 15px
      font-family PingFangSC-Regular, PingFang SC
      .left
        color #6DC37D
      .center
        color #3DA8F5
      .right
        color #FF4F3E
      .left, .center
        border-right 1px solid #E8E8E8
      .right, .left, .center
        display flex
        justify-content center
        align-items center
        width 33.333%
    .evaluation
      .right
        width 50%
        color #3DA8F5
      .left
        width 50%
    .homework
      .left, .right, .center
        color #333
</style>
