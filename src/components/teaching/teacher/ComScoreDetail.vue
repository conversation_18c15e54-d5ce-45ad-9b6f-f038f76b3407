<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import G2Radar from '@/components/teaching/teacher/ComScoreDetailRader.vue';

@Component({
  components: { G2Radar },
})
export default class ComScoreDetail extends Vue {
  @Prop({ type: Object, default: null }) data!: object;

  state = '';

  detailPageVisible = false;

  card1List = [
    { title: '知识目标', score: 9.5, ranking: 1 },
    { title: '素养目标', score: 8.2, ranking: 2 },
    { title: '技能目标', score: 8.1, ranking: 3 },
    { title: '学习态度', score: 7.9, ranking: 4 },
  ];

  card2List = [
    { title: '工程流程', score: 10, ranking: 1 },
    { title: '专业规范', score: 9.4, ranking: 2 },
    { title: '勘查设计', score: 7.9, ranking: 12 },
    { title: '制图技能', score: 8.9, ranking: 4 },
    { title: '工程预算', score: 7.7, ranking: 8 },
    { title: '资料管理', score: 8.5, ranking: 5 },
    { title: '工程意识', score: 7.7, ranking: 8 },
    { title: '诚信严谨', score: 8.2, ranking: 6 },
    { title: '沟通协作', score: 9.0, ranking: 3 },
    { title: '学习态度', score: 7.9, ranking: 7 },
  ];

  chartData1 = [
    { item: '知识目标', 个人得分: 71, 平均分: 76 },
    { item: '素养目标', 个人得分: 65, 平均分: 70 },
    { item: '技能目标', 个人得分: 69, 平均分: 56 },
    { item: '学习态度', 个人得分: 72, 平均分: 62 },
  ];

  chartData2 = [
    { item: '工程流程', 个人得分: 61, 平均分: 70 },
    { item: '专业规范', 个人得分: 71, 平均分: 60 },
    { item: '勘查设计', 个人得分: 51, 平均分: 70 },
    { item: '制图技能', 个人得分: 61, 平均分: 60 },
    { item: '工程预算', 个人得分: 51, 平均分: 70 },
    { item: '资料管理', 个人得分: 41, 平均分: 60 },
    { item: '工程意识', 个人得分: 51, 平均分: 70 },
    { item: '诚信严谨', 个人得分: 61, 平均分: 60 },
    { item: '沟通协作', 个人得分: 71, 平均分: 60 },
    { item: '学习态度', 个人得分: 41, 平均分: 70 },
  ];

  get groupName() {
    const num = '一二三四五六七八九十'[(this.data as any).group - 1];
    return `第${num}小组`;
  }

  mounted() {}

  onClose() {
    this.$emit('close');
  }
}
</script>

<template lang="pug">
.container-score-detail(@click='onClose')
  .header
    a-breadcrumb.breadcrumb(separator='>')
      a-breadcrumb-item 项目4团队得分
      a-breadcrumb-item {{ groupName }}
    .title 得分统计
    .print
      a-icon(type='download', style='margin-right:5px;')
      | 打印预览
  .user-card
    .left
      .name 通信工程制图与概运算
      .college 二级学院：{{ data.college }}
    .btn(@click.stop='detailPageVisible = true') 详情
  template(v-if='!detailPageVisible')
    .content-card
      .card-title 四个维度比分
      .body
        .left
          .tag-element(v-for='item in card1List', :key='item.title')
            .title {{ item.title }}
            .bottom
              .score {{ item.score }}
              .ranking 排名{{ item.ranking }}
        .chart
          G2Radar#radar1(:charData='chartData1', :width='400')
    .content-card
      .card-title 十项内容比分
      .body
        .left
          .tag-element(v-for='item in card2List', :key='item.title')
            .title {{ item.title }}
            .bottom
              .score {{ item.score }}
              .ranking 排名{{ item.ranking }}
        .chart
          G2Radar#radar2(:charData='chartData2', :width='400')
  img.temp-img(v-else, src='@/assets/images/teaching/tmp.png')
</template>

<style lang="stylus" scoped>
.container-score-detail
  padding 1px 20px 120px 20px
  min-height 100%
  width 100%
  background #fff
  .header
    display flex
    justify-content space-between
    align-items center
    margin-top 10px
    border-width 0 0 1px 0
    border-style solid
    border-color #ededed
    .title
      padding-bottom 7px
      border-width 0 0 4px 0
      border-style solid
      border-color #59ABED
      font-weight 500
      font-size 18px
    .print
      display flex
      align-items center
      color #59ABED
      cursor pointer
  .user-card
    display flex
    justify-content space-between
    align-items center
    margin-top 20px
    width 100%
    height 100px
    border-radius 6px
    background-color #59ABED
    color #fff
    .left
      padding-top 1px
      .name
        margin 0px 0 15px 20px
        font-weight 500
        font-size 18px
      .college
        margin-left 20px
      .user-id
        margin 0 20px
    .btn
      margin-right 20px
      width 100px
      height 30px
      border 1px solid #fff
      text-align center
      font-size 18px
      font-size 14px
      line-height 30px
      cursor pointer
  .content-card
    margin-top 30px
    padding 0 10px
    width 100%
    .card-title
      margin-bottom 10px
      width 100%
      height 40px
      border-width 0 0 1px 0
      border-style solid
      border-color #ededed
      line-height 40px
    .body
      display flex
      justify-content space-between
      align-items center
      .left
        display flex
        flex-wrap wrap
        justify-content space-between
        align-items center
        margin-left 5%
        width 60%
        height 100%
        .tag-element
          margin-bottom 20px
          width 20%
          .title
            color #888
            font-size 14px
          .bottom
            display flex
            align-items flex-end
            margin-top 10px
            .score
              position relative
              height 26px
              font-weight 600
              font-size 26px
              line-height 26px
              &:after
                position absolute
                top 3px
                right -15px
                color #888
                content '分'
                font-size 14px
            .ranking
              margin-left 20px
              padding 3px 3px
              border-radius 6px
              background #EDF8FE
              color #97C4E4
              font-size 10px
      .chart
        width 400px
        height 300px

.temp-img
  width 100%
  height 800px
</style>
