<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class ComTeachingPlanDialog extends Vue {
  @Prop({ type: Array, default: () => [] }) private readonly teachers!: [];
  @Prop({ type: Object, default: () => {} }) private readonly formData!: object;
  private lableDate: Array<string> = ['任课老师', '课时', '课时数', '节次', '教学周', '日期'];
}
</script>

<template lang="pug">
.container
  .body-top
    span(v-for="item in lableDate") {{ item }}
  .body-bottom 
    span(v-for='item in teachers', :index='item.id') {{ item.name }} 
    span {{ `${formData.start_section}-${formData.end_section}` }}
    span {{ formData.end_unit - formData.start_unit + 1 }}
    span {{ `${formData.start_section}, ${formData.end_section}节` }}
    span {{ `第 ${formData.week} 周 ` }}
    span {{ formData.date | format("YYYY年MM月DD日") }}
</template>

<style lang="stylus" scoped>
.container
  display flex
  flex-direction  column
  .body-top
    display flex
    flex-direction row
    margin-bottom 20px
    width 100%
    span
      width 25%
      color #808080
      font-weight 500
      font-size 15px
      font-family PingFangSC-Medium, PingFang SC
  .body-bottom
    width 100%
    display flex
    flex-direction row
    margin-bottom 34px
    span
      width 25%
      color #383838
      font-size 14px
      font-weight 600
      font-family PingFangSC-Semibold, PingFang SC
</style>
