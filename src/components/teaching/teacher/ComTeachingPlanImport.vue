<script lang="ts">
import { TaIndexViewConfigInterface, TaIndexViewTabInterface } from '@/components/global/TaIndex';
import { IScheduleCourses } from '@/models/teaching/schedule_courses';
import { netdiskOwnedItemStore } from '@/store/modules/netdisk/owned/item.store';
import { netdiskSharedItemStore } from '@/store/modules/netdisk/share/item.store';
import { IItem } from '@/types/model';
import { Component, Vue, Prop } from 'vue-property-decorator';
import ComNetdiskItemPreviewer from '../../netdisk/ComNetdiskItemPreviewer.vue';

/* eslint-disable prettier/prettier */
import {
  teachingTeacherCurrentSemesterScheduleLessonStore
} from '@/store/modules/teaching/teacher/schedule_lesson.store';

@Component({
  components: {
    ComNetdiskItemPreviewer,
  },
})
export default class ComTeachingPlanImport extends Vue {
  @Prop({ type: Object, required: true }) scheduleCourse!: IScheduleCourses;
  visible = false;
  templateVisible = false;
  activeTemplate: IItem = {};
  selectedRecords: IItem[] = [];
  store = this.ownedNetdiskStore;

  tabs: TaIndexViewTabInterface[] = [
    { label: '我上传的', key: 'owned', mode: 'table', store: this.ownedNetdiskStore },
    { label: '共享给我的', key: 'shared', mode: 'table', store: this.sharedNetdiskStore },
  ];

  get config(): TaIndexViewConfigInterface {
    return {
      recordName: '教学进度模板',
      store: this.store,
      showSelectionByDefault: true,
    };
  }

  get ownedNetdiskStore() {
    return netdiskOwnedItemStore;
  }

  get sharedNetdiskStore() {
    return netdiskSharedItemStore;
  }

  get scheduleLessionStore() {
    return teachingTeacherCurrentSemesterScheduleLessonStore;
  }

  mounted() {
    this.ownedNetdiskStore.init({
      params: {
        q: { type_eq: 'NetDisk::ScheduleCourseItem' },
      },
    });
  }

  onShow() {
    this.visible = true;
  }

  onSelect(newValue: IItem[], oldValue: IItem[]) {
    this.selectedRecords = newValue.filter(item => !oldValue.includes(item));
  }

  onOk() {
    this.scheduleLessionStore.init({
      parents: [
        { type: 'courses', id: this.scheduleCourse.course_id },
        { type: 'schedule_course', id: undefined },
      ],
    });

    this.scheduleLessionStore
      .sendCollectionAction({
        action: 'import',
        config: {
          params: { target_id: this.selectedRecords[0].id },
        },
      })
      .then(() => {
        this.$message.success('导入成功');
        this.visible = false;
        this.$emit('afterImport');
      })
      .catch(err => {
        this.$message.error('导入失败');
        throw err;
      });
  }

  onShowTemplateDetail(record: IItem) {
    this.activeTemplate = record;
    this.templateVisible = true;
  }

  ontabChange(tab: TaIndexViewTabInterface) {
    if (tab.key === 'owned') {
      this.store = this.ownedNetdiskStore;
      this.ownedNetdiskStore.init({
        params: {
          q: { type_eq: 'NetDisk::ScheduleCourseItem' },
        },
      });
    } else if (tab.key === 'shared') {
      this.store = this.sharedNetdiskStore;
      this.sharedNetdiskStore.init({
        params: {
          q: { type_eq: 'NetDisk::ScheduleCourseItem' },
        },
      });
    }
  }
}
</script>

<template lang="pug">
.com-teaching-plan-import
  TextButton(icon='import', @click='onShow') 导入
  a-modal(v-model='visible', title='导入课程进度', :confirmLoading='ownedNetdiskStore.loading', @ok='onOk')
    TaIndexView(
      v-model='selectedRecords',
      :config='config',
      :tabs='tabs',
      :tabsLeftMargin='0'
      @onSelect='onSelect'
      @onShow='onShowTemplateDetail'
      @tabChange='ontabChange'
    )
      template(#table)
        a-table-column(title='名称', dataIndex='name')
        a-table-column(title='描述', dataIndex='desc')
  ComNetdiskItemPreviewer(v-model='templateVisible', :netdiskItem='activeTemplate')
</template>

<style lang="stylus" scoped></style>
