<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import ComTableHeader from '@/components/teaching/teacher/ComTableHeader.vue';
import ComTableBody from '@/components/teaching/teacher/ComTableBody.vue';

@Component({
  components: { ComTableHeader, ComTableBody },
})
export default class ComStudentList extends Vue {
  @Prop({ type: String, default: '' }) prop!: string;
  @Prop({ type: Array }) list!: [];
  private noet: number = 0;
  private state: string = '';
}
</script>

<template lang="pug">
.container-student-body
  .student-title
    .title 学生列表
    .export-list
      slot(name='actions')
  ComTableHeader
  .table-content
    .box(v-for='item in list')
      ComTableBody(:students='item')
  slot(name='pagination')
</template>

<style lang="stylus" scoped>
.container-student-body
  overflow scroll
  padding 0px 20px
  padding-bottom 20px
  width 100%
  height 100%
  background white
  .table-content
    margin-bottom 20px
  .student-title
    display flex
    flex-direction row
    justify-content space-between
    margin-bottom 10px
    height 48px
    border-bottom 1px solid #efefef
    line-height 48px
    .title
      color #383838
      font-weight 600
      font-size 15px
      font-family PingFangSC-Semibold, PingFang SC
    .export-list
      display flex
      flex-direction row
</style>
