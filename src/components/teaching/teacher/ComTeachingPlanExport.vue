<script lang="ts">
import { IScheduleCourses } from '@/models/teaching/schedule_courses';
import { netdiskOwnedItemStore } from '@/store/modules/netdisk/owned/item.store';
import { IItem } from '@/types/model';
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class ComTeachingPlanExport extends Vue {
  @Prop({ type: Object, required: true }) scheduleCourse!: IScheduleCourses;

  visible = false;
  formData: Partial<IItem> = {};

  get netdiskItemStore() {
    return netdiskOwnedItemStore;
  }

  onShow() {
    this.visible = true;
    this.formData.name = `${this.scheduleCourse.course_name}课程进度`;
    this.formData.desc = '';
  }

  onOk() {
    if (!this.formData.name) {
      this.$message.error('请输入模板名称');
      return;
    }
    this.netdiskItemStore.init();
    this.netdiskItemStore
      .create({
        ...this.formData,
        course_id: this.scheduleCourse.id,
        type: 'Netdisk::ScheduleCourseItem',
      })
      .then(() => {
        this.$message.success('导出成功，可在个人资源库中查看模板哦。');
        this.visible = false;
      })
      .catch(() => {
        this.$message.error('导出模板失败');
      });
  }
}
</script>

<template lang="pug">
.com-teaching-plan-export
  TextButton(icon='export', @click='onShow') 保存到资源库
  a-modal(v-model='visible', title='导出课程进度', :confirmLoading='netdiskItemStore.loading', @ok='onOk')
    a-form-item(label='模板名称', :required='true')
      a-input(v-model='formData.name', placeholder='请输入模板名称')
    a-form-item(label='模板描述')
      a-textarea(v-model='formData.desc', placeholder='请输入模板描述')
</template>

<style lang="stylus" scoped></style>
