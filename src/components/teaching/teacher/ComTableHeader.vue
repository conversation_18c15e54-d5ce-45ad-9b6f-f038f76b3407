<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class StudentTableHeader extends Vue {
  @Prop({ type: String, default: '' }) prop!: string;

  state: string = '';

  mounted() {
    this.fetchData();
  }

  fetchData() {}
}
</script>

<template lang="pug">
.container-table-header
  .table-header
    .name 姓名
    .student-id 学号
    .college 学院
    .class 班级
    .sign-in
      .top 签到情况
      .bottom
        .left 正常
        .center 迟到
        .right 旷课
    .evaluation
      .top 论坛评价情况
      .bottom
        .left 未评价
        .right 已评价
    .homework 
      .top 作业情况
      .bottom
        .left 未提交
        .center 待评分
        .right 已评分
</template>

<style lang="stylus" scoped>
.container-table-header
  .table-header
    display flex
    flex-direction row
    align-items center
    width 100%
    height 60px
    border-top 1px solid #E8E8E8
    border-bottom 1px solid #E8E8E8
    border-left 1px solid #E8E8E8
    color #383838
    font-weight 400
    font-size 14px
    font-family PingFangSC-Regular, PingFang SC
    .name, .student-id, .college, .class, .score
      padding-left 14px
      height 100%
      border-right 1px solid #E8E8E8
      line-height 60px
  .name
    width 9%
  .student-id
    width 10%
  .college
    width 13.84%
  .class
    width 11.88%
  .sign-in, .homework
    width 20.8%
    height 100%
    border-right 1px solid #E8E8E8
  .evaluation
    width 15.21%
    height 100%
    border-right 1px solid #E8E8E8
  .sign-in, .evaluation, .homework
    display flex
    flex-direction column
    .top
      display flex
      justify-content center
      align-items center
      padding-left 14px
      height 50%
      border-bottom 1px solid #E8E8E8
    .bottom
      display flex
      flex 1
      flex-direction row
      .left, .center
        border-right 1px solid #E8E8E8
      .right, .left, .center
        display flex
        justify-content center
        align-items center
        padding-left 14px
        width 33.333%
  .evaluation
    .bottom
      .right
        width 50%
      .left
        width 50%
</style>
