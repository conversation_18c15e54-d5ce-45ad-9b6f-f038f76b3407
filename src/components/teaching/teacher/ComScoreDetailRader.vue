<template lang="pug">
.g2(:id='id')
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import G2 from '@antv/g2';
import { DataView } from '@antv/data-set';

@Component({
  components: {},
})
export default class G2Radar extends Vue {
  @Prop({ type: Array, default: () => [] }) private charData: any;
  @Prop({ type: String, default: '' }) private id!: string;
  @Prop({ type: Number, default: 600 }) private width?: number;

  public mounted() {
    this.drawChart();
  }
  public drawChart() {
    const dv = new DataView().source(this.charData);
    dv.transform({
      type: 'fold',
      fields: ['个人得分', '平均分'], // 展开字段集
      key: 'user', // key字段
      value: 'score', // value字段
    });
    const chart = new G2.Chart({
      container: this.id,
      width: this.width,
      forceFit: true,
      height: 500,
      padding: [20, 20, 95, 20],
    });
    chart.source(dv, {
      score: {
        min: 0,
        max: 80,
      },
    });
    chart.coord('polar', {
      radius: 0.8,
    });
    chart.axis('item', {
      tickLine: null,
      grid: {
        lineStyle: {},
        hideFirstLine: false,
      },
    });
    chart.axis('score', {
      tickLine: null,
      grid: {
        type: 'polygon',
        lineStyle: {},
      },
    });
    chart.legend('user', {
      marker: 'circle',
      offsetX: 30,
    });
    chart
      .line()
      .position('item*score')
      .color('user')
      .size(2);
    chart
      .point()
      .position('item*score')
      .color('user')
      .shape('circle')
      .size(4)
      .style({
        stroke: '#fff',
        lineWidth: 1,
        fillOpacity: 1,
      });
    chart
      .area()
      .position('item*score')
      .color('user');
    chart.render();
  }
}
</script>

<style lang="stylus" scoped>
::-webkit-scrollbar
  display none

html, body
  overflow hidden
  margin 0
  height 100%

.g2
  width 100%
  height 100%
</style>
