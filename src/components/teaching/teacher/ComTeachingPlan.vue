<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { PlanTemplate } from '@/views/teaching/teacher/course_activities/template';
import ObjectBlock from '@/components/ObjectBlock.vue';
import FileUploader from '@/components/global/FileUploader.vue';
import ComTeachingPlanDialog from './ComTeachingPlanDialog.vue';
import Attachments from '@/components/global/Attachments.vue';
import ComTeachingPlanExport from './ComTeachingPlanExport.vue';
import { IScheduleCourses } from '@/models/teaching/schedule_courses';
import ComTeachingPlanImport from './ComTeachingPlanImport.vue';

@Component({
  components: {
    FileUploader,
    Attachments,
    ComTeachingPlanDialog,
    ComTeachingPlanExport,
    ComTeachingPlanImport,
  },
})
export default class ComTeachingPlan extends Vue {
  @Prop({ type: Array }) teachers!: [];
  @Prop({ type: Array, default: () => [] }) tableData!: any;
  @Prop({ type: Boolean, default: false }) Loading!: boolean;
  @Prop({ type: Boolean, default: false }) show!: boolean;
  @Prop({ type: Object, required: true }) scheduleCourse!: IScheduleCourses;
  @Prop({ type: Boolean, default: true }) ellipsisState!: boolean;
  @Prop({ type: Boolean, default: true }) showFile!: boolean;
  @Prop({ type: Boolean, default: false }) print!: boolean;
  @Prop({ type: Boolean, default: true }) updateDateState!: boolean;

  private visible: boolean = true;
  private visibleFiles = false;
  private formVisible: boolean = false;
  private formData: IObject = {};
  private loading: boolean = false;
  private visibleDetails: boolean = false;
  private payload: any = {};
  private config: any = { bordered: true, showHeader: true, scroll: { x: '90vw', y: '50vh' } };
  private lessons: any = [];
  private pageSize: number = 15;
  private current: number = 1;

  get template() {
    return PlanTemplate;
  }

  get state() {
    return this.scheduleCourse.state;
  }

  created() {
    if (this.print) {
      this.config.perPage = 9999999;
      this.config.scroll = null;
      this.config.hideOnSinglePage = true;
    }
  }

  public onShow(record: any) {
    this.formVisible = true;
    this.formData = record;
  }

  public onShowDetails(result: any) {
    this.visibleDetails = true;
    this.formData = result;
    this.payload = JSON.parse(JSON.stringify(result.payload));
  }

  public onUpdate(formDate: IObject) {
    this.formVisible = false;
  }

  public onCreate(formDate: IObject) {
    this.formVisible = false;
    this.formData.payload = formDate;
    this.$emit('update', this.formData);
  }

  public onChange(page: number, q: any, per_page: number) {
    const parmse = {
      page: page,
      per_page: per_page,
    };
    this.$emit('change', parmse);
  }

  public getIndex(text: string, record: any, index: number) {
    // return this.current === 1 ? `${index + 1}` : `${index + 1 + this.pageSize}`;
    return `${index + 1}`;
  }

  public handleCancel() {
    this.visibleDetails = false;
  }

  public getLength(val: any) {
    return (val && val.length) || 0;
  }

  private handleOk(): void {
    this.formData.payload.remark = this.payload.remark;
    this.formData.payload.schema_page = this.payload.schema_page;
    this.formData.payload.schema_file = this.payload.schema_file;
    this.updateDateState ? this.$emit('update', this.formData) : '';
    this.visibleDetails = false;
  }

  private rowClick(): void {
    this.$emit('rowClick');
  }

  private getFileCount(val: any) {
    return val.schema_file !== null || val.schema_page !== null
      ? val.schema_file.length || 0 + val.schema_page.length || 0
      : '0';
  }

  // private pagination(val: any) {
  //   this.current = val.pageSize;
  //   this.pageSize = val.pageSize;
  // }

  refresh() {
    this.$emit('refresh');
  }

  showFiles(record: any) {
    this.payload = JSON.parse(JSON.stringify(record.payload));
    this.visibleFiles = true;
  }
}
</script>

<template lang="pug">
.container-plan
  .actions
    ComTeachingPlanImport(v-if="state === 'todo'", :scheduleCourse='scheduleCourse', @afterImport='refresh')
    ComTeachingPlanExport(v-if='!print' :scheduleCourse='scheduleCourse')
    slot(name='actions')
  .plan-table
    TaIndexTable(:data="tableData || []" :config="config")
      a-table-column(:width='50', title='#', :customRender='getIndex')
      a-table-column(width='20vw', :ellipsis='ellipsisState', title='教学信息')
        template(slot-scope='scope')
          a-tooltip(placement='top')
            template(slot='title')
              span 任课老师:
              span(v-for='item in teachers', :index='item.id') {{ item.name }}
              span 课时:
              span {{ `${scope.start_section}-${scope.end_section}` }}}
              p
                span 教学周:
                span {{ `第 ${scope.week} 周` }}
                span 节次数:
                span {{ `${scope.start_unit}, ${scope.end_unit}节` }}
              p
                span 课时数:
                span {{ scope.end_unit - scope.start_unit + 1 }}
                span 日期:
                span {{ scope.date | format("YYYY年MM月DD日") }}
            .column-div
              span 任课老师:
              span(v-for='item in teachers', :index='item.id') {{ item.name }}
              span 课时:
              span {{ `${scope.start_section}-${scope.end_section}` }}
              p
                span 教学周:
                span {{ `第 ${scope.week} 周` }}
                span 节次数:
                span {{ `${scope.start_unit}, ${scope.end_unit}节` }}
              p
                span 课时数:
                span {{ scope.end_unit - scope.start_unit + 1 }}
                span 日期:
                span {{ scope.date | format("YYYY年MM月DD日") }}
      a-table-column(width='20vw', title='教学内容')
        template(slot-scope='scope')
          a-tooltip(placement='topLeft')
            template(slot='title') {{ scope.payload.content }}
            span.eudcation-context {{ scope.payload.content }}
      a-table-column(width='10vw', title='教学形式')
        template(slot-scope='scope')
          a-tooltip(placement='topLeft')
            template(slot='title') {{ scope.payload.teaching_form }}
            span.eudcation-context {{ scope.payload.teaching_form }}
      a-table-column(width='10vw', title='课外作业')
        template(slot-scope='scope')
          a-tooltip(placement='topLeft')
            template(slot='title') {{ scope.payload.homework }}
            span {{ scope.payload.homework }}
      a-table-column(v-if='!print' :width='100', dataIndex="payload1", title='教案首页')
        template(slot-scope='scope, record')
          Attachments(:attachments='record.payload.schema_page || []')
      a-table-column(v-if='!print' :width='100', dataIndex="payload2", title='教案')
        template(slot-scope='scope, record')
          Attachments(:attachments='record.payload.schema_file || []')
      a-table-column(v-if='!print' title='教学后记')
        template(slot-scope='scope')
          a-tooltip(placement='topLeft')
            template(slot='title') {{ scope.payload.remark }}
            span.eudcation-context {{ scope.payload.remark }}
      a-table-column(:width='80', v-if='show', title='操作', align='center' fixed='right')
        template(slot-scope='scope',)
          .column-actions
            IconTooltip(icon='edit', v-if="state === 'todo'" tips='修改', @click.stop='onShow(scope)')
            IconTooltip(icon='eye', tips='详情', @click.stop="onShowDetails(scope)")
    //-  教学进度修改
    TaFormDialog(
      v-model='formVisible',
      :title='"填写教学进度"',
      :formData='formData.payload',
      :loading='loading',
      :template='template',
      @update='onUpdate',
      @create='onCreate'
    )
      template(slot='body')
        ComTeachingPlanDialog(:teachers="teachers" :formData="formData")

    //-  课程详情显示框
    a-modal(
      width="900px"
      @ok="handleOk"
      title='课程详情',
      @cancel="visibleDetails = false"
      :visible='visibleDetails'
    )
      a-row.row(:gutter='21')
        ComTeachingPlanDialog(:teachers="teachers" :formData="formData")
        a-col.col(:span='23')
          label.education-lable 教学内容
          a-textarea(
            v-model='payload.content'
            :disabled='true'
            :auto-size='{ minRows: 4, maxRows: 6 }'
          )
        a-col.col(:span='23')
          label.education-lable 教学形式
          a-textarea(
            v-model='payload.teaching_form'
            :disabled='true'
            :auto-size='{ minRows: 4, maxRows: 6 }'
          )
        a-col.col(:span='23')
          label.education-lable 课外作业
          a-textarea(
            v-model='payload.homework'
            :disabled='true'
            :auto-size='{ minRows: 4, maxRows: 6 }'
          )
        a-col.col.schema_page(:span='23')
          label.education-lable 教案首页
          FileUploader(v-model='payload.schema_page', style='width:,100%', v-if='showFile')
          Attachments(:attachments='payload.schema_page || []', v-else)
        a-col.col.schema_file(:span='23')
          label.education-lable 教案
          FileUploader(v-model='payload.schema_file', style='width: 100%', v-if='showFile')
          Attachments(:attachments='payload.schema_file || []', v-else)
        a-col.col(:span='23')
          label.education-lable 教学后记
          a-textarea(
            v-model='payload.remark'
            :auto-size='{ minRows: 4, maxRows: 6 }'
          )

</template>

<style lang="stylus" scoped>
.container-plan
  // margin-bottom 100px
  padding 10px 20px 0 20px
  width 100%
  // height 100%
  background white
  .actions
    display flex
    justify-content flex-end
    padding 0 0 10px

  .plan-table
    .table
      min-height 200px
    .column-actions
      display flex
      justify-content center

.tags
  width 70px
  height 30px
  border-radius 4px
  background #F0F9F2
  font-size 30px

.body-content
  .body-top
    display flex
    flex-direction row
    margin-bottom 20px
    width 100%
    span
      width 25%
      color #808080
      font-weight 500
      font-size 15px
      font-family PingFangSC-Medium, PingFang SC
  .body-bottom
    display flex
    flex-direction row
    margin-bottom 34px
    width 100%
    span
      width 25%
      color #383838
      font-weight 600
      font-size 14px
      font-family PingFangSC-Semibold, PingFang SC

.column-div
  overflow hidden
  width 100%
  text-overflow ellipsis
  white-space nowrap
  span
    margin-right 10px
  p
    overflow hidden
    text-overflow ellipsis
    white-space nowrap

.eudcation-context
  display -webkit-box
  overflow hidden
  text-overflow ellipsis
  -webkit-line-clamp 3
  -webkit-box-orient vertical

.row
  .col
    // margin-top 5px
    margin-bottom 10px
    .label, .context-col
      display inline-block
      width 16.66666%
      color #808080
      font-weight 500
      font-size 15px
      font-family PingFangSC-Medium, PingFang SC
    .education-lable
      display block
      font-size 14px
      margin-bottom 5px
      color rgba(0, 0, 0, 0.85);
.context-col
  margin-bottom 30px
  .context
    display inline-block
    width 16.66666%
    color #383838
    font-weight 600
    font-size 14px
    font-family PingFangSC-Semibold, PingFang SC
.schema_page, .schema_file
  min-height 50px
</style>
