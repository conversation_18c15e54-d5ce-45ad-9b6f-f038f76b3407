<script lang="ts">
/**
 * 课程整体安排组件
 * model: course_activity
 * RowHead 只显示 周几
 */
import { Component, Vue, Prop } from 'vue-property-decorator';
import ScrollTable from '@/components/teaching/ScrollTable.vue';
import courseActivityModel, { CourseActivity, ICourseActivity } from '@/models/teaching/course_activity';
import { defaultColumns, IScheduleColumn } from '@/models/teaching/semester';
import semesterModel from '@/models/teaching/semester';

@Component({
  components: {
    ScrollTable,
  },
})
export default class ActivitySchedule extends Vue {
  @Prop({ type: Array, default: () => [] }) activities!: ICourseActivity[]; // 所有 activity
  @Prop({ type: Array, default: () => defaultColumns }) columns!: IScheduleColumn[]; // 时间
  @Prop({ type: Boolean, default: false }) loading!: boolean;
  @Prop({ type: Boolean, default: false }) isEvaluation!: boolean;

  scheduleColumns: IScheduleColumn[] = [];

  get tableRecords() {
    return CourseActivity.getScheduleRecords(this.activities);
  }

  created() {
    this.fetchCurrentSemester();
  }
  async fetchCurrentSemester() {
    const role = this.$store.getters.currentRole;
    const { data } = await semesterModel.setRole(role).current();
    this.scheduleColumns = data._scheduleColumns;
  }
  getCourseActivityClass(activity: ICourseActivity) {
    const classes: IObject = {};
    if (activity.start_unit! < 5) {
      classes.am = true;
    } else if (activity.start_unit! < 9) {
      classes.pm = true;
    } else {
      classes.night = true;
    }
    return classes;
  }
  getColHeadStyle(index: number) {
    const styles: IObject = { borderBottom: '1px solid #e8e8e8', borderRight: '1px solid rgba(232, 232, 232, 0.3)' };
    if (index < 4) {
      return { ...styles, background: '#68B4EB' };
    }
    if (index < 8) {
      return { ...styles, background: '#F9AF36' };
    }
    return { ...styles, background: '#2F4169' };
  }
  getCellStyle(index: number) {
    switch (index) {
      case 0:
        return {
          borderLeft: '1px dashed #68B4EB',
          borderRight: '1px dashed #e8e8e8',
          borderBottom: '1px solid #e8e8e8',
        };
      case 3:
        return {
          borderRight: '1px dashed #68B4EB',
          borderBottom: '1px solid #e8e8e8',
        };
      case 7:
        return {
          borderRight: '1px dashed #F9AF36',
          borderBottom: '1px solid #e8e8e8',
        };
      case 11:
        return {
          borderRight: '1px dashed #2F4169',
          borderBottom: '1px solid #e8e8e8',
        };
      default:
        return {
          borderRight: '1px dashed #e8e8e8',
          borderBottom: '1px solid #e8e8e8',
        };
    }
  }
  onClick(activity: ICourseActivity) {
    this.$emit('show', activity, document.getElementById(`activities${activity.id}`));
  }
}
</script>

<template lang="pug">
ScrollTable.class-schedule(
  :records="tableRecords"
  :columns="scheduleColumns"
  :loading="loading")
  template(#colHead="{ data, colIndex }")
    .col-head(:style="getColHeadStyle(colIndex)")
      .title {{ data.title }}
      .range {{ data.range }}
  template(#rowHead="{ data }")
    .week-day
      .date {{ data.week }}
  template(#grid-cell="{ colIndex }")
    .grid-cell(:style="getCellStyle(colIndex)")
  template(#default="{ data }")
    .lesson-cell(
      :id="`activities_${data.id}`"
      @click="onClick(data)"
      :class="{ 'cell-pointer': isEvaluation }")
      .lesson(:class="getCourseActivityClass(data)")
        .name {{ data.course_set_name }} [{{ data.course_set_exam_mode }}]
        .info
          span {{ data.teacher_name || '教师？' }}，
          span {{ data.week_arr_zh}}，
          span {{ data.classroom_name || '班级？' }}，
          span {{ data.course_name }}
</template>

<style lang="stylus" scoped>
.class-schedule
  .grid-cell
    width 100%
    height 100%
    border-right 1px dashed rgba(232, 232, 232, 1)
  .col-head
    padding 10px 6px
    height 100%
    border-right 1px solid rgba(232, 232, 232, 0.3)
    color #fff
    .title
      margin-bottom 6px
      letter-spacing 0
      font-weight 500
      font-size 14px
      line-height 20px
    .range
      letter-spacing 0
      font-size 11px
      line-height 16px
  .week-day
    padding 20px 0
    width 66px
    text-align center
    .date
      margin-bottom 4px
      color rgba(128, 128, 128, 1)
      font-weight 500
      font-size 14px
      line-height 20px
    .count
      margin-top 16px
      color rgba(166, 166, 166, 1)
      font-weight 500
      font-size 12px
  .lesson-cell
    padding 6px
    .cell-pointer
      cursor pointer
    .lesson
      padding 10px 4px
      border-radius 2px
      cursor pointer
      .name
        margin-bottom 8px
        color rgba(56, 56, 56, 1)
        letter-spacing 0
        font-weight 500
        font-size 12px
        line-height 16px
      .info
        color rgba(128, 128, 128, 1)
        letter-spacing 0
        font-weight 400
        font-size 12px
        line-height 18px
    .am
      background #E1F0FB
    .pm
      background #FEF3E1
    .night
      background #E0E3E9
</style>
