<template lang="pug">
.pie-chart(ref="chart" :styles="{ height: `${height}px` }")
</template>

<script lang="ts">
// 签到统计图
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import G2 from '@antv/g2';
import { isEqual } from 'lodash';

@Component({
  components: {},
})
export default class Check<PERSON>hart extends Vue {
  @Prop({ type: Array, default: () => [] }) private data!: IObject[];
  @Prop({ type: String, default: '统计图' }) private title?: string;
  @Prop({ type: Number, default: 260 }) private height?: number;
  @Prop({ type: Number, default: 0.8 }) private radius?: number;
  @Prop({ type: Number, default: 0.6 }) private innerRadius?: number;
  @Prop({ type: Boolean, default: false }) private loading?: boolean;
  @Prop({ type: String, default: '' }) private unit?: string;
  @Prop({ type: String, default: 'blue' }) private theme?: string;
  @Prop({ type: Array }) colors?: string[];

  chart: any = null;
  cacheData: IObject[] = [];
  colorsHash: any = {
    green: ['#6DC37D', '#FA8C15', '#A6A6A6'],
    blue: ['#1FA1FF', '#6CC2FF', '#B6E1FF', '#e2f0fb'],
  };

  @Watch('data', { immediate: true })
  watchChange() {
    if (isEqual(this.cacheData, this.data)) {
      return;
    }
    if (this.chart) {
      this.chart.destroy();
    }
    this.$nextTick(() => {
      if (this.data && this.data.length && this.$refs.chart) {
        this.cacheData = this.data;
        this.drawChart();
      }
    });
  }

  drawChart() {
    this.chart = new G2.Chart({
      container: this.$refs.chart as any,
      height: this.height,
      forceFit: true,
      animate: true,
      padding: 35,
    });
    this.chart.source(this.data.filter(o => typeof o.percent === 'number'));
    this.chart.legend(false);
    this.chart.coord('theta', {
      radius: this.radius,
      innerRadius: this.innerRadius,
    });
    this.chart.scale('percent', {
      formatter: (percent: number): string => {
        return `${percent}%`;
      },
    });
    this.chart
      .intervalStack()
      .position('percent')
      .color('name', this.colors || this.colorsHash[this.theme!])
      .label('percent', {
        formatter: (_: any, item: any): string => {
          return `${item.point.name} ${item.point.count}${this.unit}`;
        },
      })
      .tooltip('name*count*percent', (name: any, count: any, percent: any) => ({
        title: name,
        name: '占比',
        value: `${percent}%`,
      }))
      .style({
        lineWidth: 0,
        stroke: '#fff',
      });
    this.chart.render();
  }
}
</script>

<style lang="stylus" scoped>
.pie-chart
  width 100%
</style>
