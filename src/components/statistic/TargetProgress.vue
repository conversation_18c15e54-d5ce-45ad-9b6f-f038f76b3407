<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component
export default class TargetProgress extends Vue {
  @Prop({ type: String }) private name!: string;
  @Prop({ type: [Number] }) private count!: number;
  @Prop({ type: [String] }) private text!: string;
  @Prop({ type: Number }) private total!: number;
  @Prop({ type: String }) private unit!: string;
  @Prop({ type: String }) private prefix!: string;
  @Prop({ type: String, default: '—' }) private placeholder!: string;

  get percent() {
    const count = typeof this.count === 'number' ? this.count : 0;
    return Math.round((count / this.total) * 100);
  }

  get isValidCount() {
    return typeof this.count === 'number' && !Number.isNaN(this.count);
  }

  onClick(...args: any) {
    this.$emit('click', ...args);
  }
}
</script>

<template lang="pug">
.target-progress(@click="onClick")
  .name {{ name }}
  .count
    sub(v-if="prefix") {{ prefix }}
    span(v-if="isValidCount") {{ count }}
    span.text(v-else-if="text") {{ text }}
    span.placeholder(v-else-if="!$slots.default") {{ placeholder }}
    sub(v-if="unit") {{ unit }}
    slot
  a-progress.progress(
    v-if="typeof total === 'number'"
    :percent="percent"
    :showInfo="false"
    :strokeWidth="3"
    size="small")
</template>

<style lang="stylus" scoped>
.target-progress
  width 100%
  .name
    margin 0 0 12px 0
    color rgba(166, 166, 166, 1)
    font-weight 500
    font-size 12px
    line-height 14px
  .count
    span
      display inline-block
      color rgba(56, 56, 56, 1)
      font-weight 500
      font-size 28px
      font-family 'DINCond'
      line-height 28px
    .placeholder
      display inline-block
      color rgba(56, 56, 56, 1)
      font-weight 500
      font-size 20px
      font-family 'PingFang SC'
      line-height 28px
    sub
      display inline-block
      margin-left 4px
      margin-right 4px
      height 18px
      color rgba(128, 128, 128, 1)
      vertical-align 4px
      font-weight 400
      font-size 12px
      font-family PingFang SC
      line-height 18px
  .progress
    width 100%
    line-height 20px
</style>
