<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import Check<PERSON><PERSON> from '@/components/statistic/CheckChart.vue';
import TargetProgress from '@/components/statistic/TargetProgress.vue';

interface dataItem {
  name: string; // 维度名称
  count: number; // 统计数据
  percent: number; // 统计占比
}

@Component({
  components: {
    CheckChart,
    TargetProgress,
  },
})
export default class PieChartPanel extends Vue {
  @Prop({ type: Array, default: () => [] }) data!: dataItem[]; // 维度数据
  @Prop({ type: String, default: '' }) title!: string;
  @Prop({ type: String, default: 'blue', validator: v => ['green', 'blue'].includes(v) }) theme!: 'green' | 'blue';
  @Prop({ type: Array }) colors!: string[];
  @Prop({ type: Boolean, default: false }) loading!: boolean;
  @Prop({ type: Boolean, default: true }) bordered!: boolean;
}
</script>

<template lang="pug">
.statistic-panel(:class="{ 'no-border': !bordered }")
  .header
    span(v-if="title") {{ title }}
    slot(name="actions")
  .content(v-loading="loading")
    a-row.statistic(:gutter="16")
      a-col.counts(:span="12")
        TargetProgress.count-item(
          v-for="(item, i) in data"
          :key="i"
          :name="item.name"
          :count="item.count")
      a-col.chart(:span="10")
        CheckChart(
          :data="data"
          :theme="theme"
          :colors="colors")
</template>

<style lang="stylus" scoped>
.statistic-panel
  background rgba(255,255,255,1)
  border-radius 3px
  border 1px solid rgba(232,232,232,1)
  margin-bottom 20px
  .header
    font-size 14px
    color rgba(56,56,56,1)
    line-height 20px
    padding 12px
    border-bottom 1px solid #E8E8E8
    display flex
    justify-content space-between
    align-items center
    &:empty
      display none
  .content
    padding 10px
    .targets
      padding 20px 60px 20px 100px
    .statistic
      height 260px
      padding 0 0 0 100px
      .chart
        height 100%
      .counts
        height 100%
        display flex
        justify-content space-between
        align-items center
        .count-item
          .name
            margin 0 0 8px 0
            color rgba(166, 166, 166, 1)
            font-weight 500
            font-size 12px
            line-height 14px
          .count
            span
              display inline-block
              height 30px
              color rgba(56, 56, 56, 1)
              font-weight 500
              font-size 30px
              font-family 'DINCond'
              line-height 30px
            sub
              display inline-block
              margin-left 4px
              height 18px
              color rgba(128, 128, 128, 1)
              vertical-align 4px
              font-weight 400
              font-size 12px
              font-family PingFang SC
              line-height 18px

.no-border
  border-color transparent
  .header
    border-color transparent
</style>
