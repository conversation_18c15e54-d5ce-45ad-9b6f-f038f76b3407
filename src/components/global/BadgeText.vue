<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component
export default class BadgeText extends Vue {
  @Prop({ type: Number }) private count!: number;
}
</script>

<template lang="pug">
.badge-text(v-if="count")
  | {{ count > 99 ? '99+' : count }}
</template>

<style lang="stylus" scoped>
.badge-text
  font-size 12px
  color #ffffff
  background #e50114
  display inline-block
  padding 2px 6px
  line-height 16px
  align-self center
  border-radius 20px
  margin 0 6px
</style>
