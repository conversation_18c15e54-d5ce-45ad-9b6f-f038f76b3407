<template lang="pug">
a-button.fliter(:size="size" @click="onChange")
  span {{ title }}
  a-icon(:type="icon")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class ThDropdown extends Vue {
  @Prop({ type: Boolean, default: false }) private value!: boolean;
  @Prop({ type: String, default: 'small' }) private size!: string;
  @Prop({ type: String, default: '' }) private title!: string;
  @Prop({ type: String, default: 'filter' }) private icon!: string;
  public onChange() {
    this.$emit('click');
  }
}
</script>

<style lang="stylus" scoped>
.fliter
  height 20px
  border none
  color #A6A6A6
  font-weight 450
  line-height 20px
  span
    color #808080
  &:hover
    color #3DA8F5
</style>
