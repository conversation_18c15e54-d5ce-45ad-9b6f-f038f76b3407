<template lang="pug">
.avatar-page(:style="{ width: boxSize, height: boxSize }")
  template(v-if="url")
    a-avatar.avatar(:src="url" :size="size" :style="{ borderRadius: borderRadius }")
  template(v-else-if="nameText")
    .avatar(
      :style="{ background: sex ? bgColor : color, fontSize: fontSize, borderRadius: borderRadius, color: textColor }") 
      | {{ nameText }}
  template(v-else-if="useName")
    a-avatar(
      :size="size" 
      :style="{ background: sex ? bgColor : color }") 
      | {{ name }}
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class TaAvatar extends Vue {
  @Prop({ type: String, default: '50%' }) private borderRadius?: string;
  @Prop({ type: String, default: '' }) private url!: string;
  @Prop({ type: String, default: '' }) private name!: string;
  @Prop({ type: String, default: 'small' }) private size?: 'large' | 'small' | 'default';
  @Prop({ type: String, default: '36px' }) private boxSize?: string;
  @Prop({ type: String, default: '32px' }) private fontSize?: string;
  @Prop({ type: String, default: '#58A8EF' }) private color!: string;
  @Prop({ type: String, default: '#fff' }) private textColor!: string;
  @Prop({ type: String, default: '' }) private sex!: string;
  @Prop({ type: Boolean, default: false }) private short!: boolean;
  get useName() {
    return !this.url && this.name;
  }
  get nameText() {
    return this.short && this.name ? this.name.slice(0, 1).toLocaleUpperCase() : '';
  }
  get bgColor() {
    return ['男', 'male', 'Male'].includes(this.sex) ? '#58A8EF' : '#F28E36';
  }
}
</script>

<style lang="stylus" scoped>
.avatar-page
  width 36px
  height 36px
  .avatar
    display flex
    justify-content center
    align-items center
    width 100%
    height 100%
    background #eee
</style>
