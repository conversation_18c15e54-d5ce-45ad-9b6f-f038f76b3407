<script lang="ts">
import { IFormTemplateItem } from '@/interfaces/formTemplate.interface';
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import TemplateFormViewer from '../form/TemplateFormViewer.vue';
import TemplateForm from '@/components/form/TemplateForm.vue';
import { IModel } from '@/lib/ActiveModel';
import TaTemplateForm from './TaTemplateForm.vue';

@Component({
  components: {
    TemplateFormViewer,
    TemplateForm,
  },
})
export default class TaTemplateFormShow<T extends IModel> extends Vue {
  @Prop({ type: Array }) template!: IFormTemplateItem[];
  @Prop({ type: Object }) record!: Partial<T>;
  @Prop({ type: Boolean }) loading!: boolean;
  @Prop({ type: String }) recordName!: string;
  @Prop({ type: Boolean }) showIcon!: boolean;
  // 特别的时候编辑菜单要有权限管理
  @Prop({ type: Boolean, default: true }) disabled!: boolean;

  visibleForm: boolean = false;
  formData: Partial<T> = {};

  @Watch('record', { immediate: true })
  onRecordChange() {
    this.formData = this.record;
  }

  handleOk() {
    if (this.loading) return;
    (this.$refs.form as TaTemplateForm).submit({
      success: (formData: T) => {
        this.$emit('submit', formData);
        if (this.formData.id) {
          this.$emit('update', { id: this.formData.id, ...formData });
        } else {
          this.$emit('create', formData);
        }
        this.visibleForm = false;
      },
    });
  }

  onDelete() {
    this.$emit('delete', this.record);
  }
}
</script>

<template lang="pug">
.ta-template-form-show
  slot(name='header')
    TaTitleHeader(:title='`${recordName}`')
      template
        template(v-if='visibleForm')
          TextButton(icon='save', @click='handleOk') 保存
          TextButton(icon='close', @click='visibleForm = false') 取消
        template(v-else)
          PopoverConfirm(title='删除', :content='`确认要删除该${recordName}吗？`', placement='bottomRight', @confirm='onDelete')
            TextButton(icon="delete") 删除
          TextButton(v-if='disabled', icon='edit', @click='visibleForm = true') 编辑
  slot(name='content')
  template(v-if='visibleForm')
    TemplateForm(ref='form', :template='template', :formData='formData')
  template(v-else)
    TemplateFormViewer(:template='template', :formData='record', :border='false', :showIcon='showIcon')
</template>

<style lang="stylus" scoped></style>
