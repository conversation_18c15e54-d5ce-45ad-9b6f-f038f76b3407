<script lang="ts">
import { ActiveStore } from '@/lib/ActiveStore';
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { TaIndexImportHeader } from './TaIndex';

@Component({
  components: {},
})
export default class TaImport extends Vue {
  @Prop({ type: Object, required: true }) store!: ActiveStore;
  @Prop({ type: Array, default: () => [] }) headers!: TaIndexImportHeader[]; // model headers
  @Prop({ type: Function, default: undefined }) onConfirmFunc!: (options: {
    resultHeaders: TaIndexImportHeader[];
    uid: string;
    resetImport: () => void;
  }) => void;

  @Prop({ type: Object, default: () => ({}) }) confirmOptions!: IObject;
  @Prop({ type: String, default: '导入' }) title!: String;

  uid = '';
  visibleImport = false;
  loading = false;
  // excel 的表头
  dataTitles: string[] = [];
  // excel 的数据
  rowData: IObject[] = [];
  // 有改动的 key 值数组，用 index 到 dataHeaders 找对应 name
  keysChanged: string[] = [];
  currentPage = 0;
  totalCount = 0;
  totalPages = 0;
  localHeaders: TaIndexImportHeader[] = [];

  get optionalHeaders() {
    return [...this.dynamicHeaders, { key: '', name: '不选择' }];
  }

  get dynamicHeaders() {
    return this.headers.length === 0 ? this.localHeaders : this.headers;
  }

  @Watch('dynamicHeaders', { deep: true, immediate: true })
  handleHeadersChange() {
    this.setDefaultOptionsValue();
  }

  @Watch('dataTitles', { deep: true, immediate: true })
  handleDataTitlesChange() {
    this.setDefaultOptionsValue();
  }

  fetchHeaders() {
    if (this.headers.length === 0) {
      return this.store
        .sendCollectionAction({
          action: 'import_headers',
        })
        .then(res => {
          this.localHeaders = res.data.headers;
        });
    } else {
      return Promise.resolve();
    }
  }

  setDefaultOptionsValue() {
    this.keysChanged = [];
    this.dataTitles.forEach(dataTitle => {
      const existHeader = this.dynamicHeaders.find(i => i.name === dataTitle);
      const key = existHeader ? existHeader.key : '';
      this.keysChanged.push(key);
    });
  }

  fileChange(e: any) {
    const file = [...e.target.files][0] || {};
    if (file && file.name) {
      this.onImport(file);
      // 解决再次上传不生效问题
      e.target.value = '';
    } else {
      this.$message.error('上传文件失败！');
    }
  }

  onImport(file: any) {
    this.loading = true;
    const formData = new FormData();
    formData.append('file', file);
    this.store
      .sendCollectionAction({
        action: 'upload_excel',
        config: { data: formData },
      })
      .then(res => {
        this.uid = res.data.uid;
        this.fetchHeaders()
          .then(() => {
            this.rawDataIndex()
              .then(res => {
                this.dataTitles = res.data.titles;
                this.visibleImport = true;
                this.$message.success('上传文件完成！');
                this.loading = false;
              })
              .catch(() => {
                this.loading = false;
                this.$message.error('获取表格数据异常！');
              });
          })
          .catch(() => {
            this.loading = false;
            this.$message.error('获取导入表头失败');
          });
      })
      .catch(error => {
        this.loading = false;
        this.$message.error('上传文件失败！');
        throw error;
      });
  }

  onCancel() {
    this.resetImport();
  }

  resetImport() {
    this.visibleImport = false;
    this.uid = '';
    this.keysChanged = [];
  }

  onConfirm() {
    // headers 后端返回的，或者 props 传进来的
    const headers = JSON.parse(JSON.stringify(this.dynamicHeaders));
    const resultHeaders: TaIndexImportHeader[] = [];

    this.keysChanged.forEach((key, index) => {
      if (!key) {
        return;
      }
      const newTitle = this.dataTitles[index];
      const targetHeader = headers.find((header: TaIndexImportHeader) => header.key === key);
      targetHeader.name = newTitle;
      resultHeaders.push(targetHeader);
    });

    if (this.onConfirmFunc) {
      this.onConfirmFunc({ resultHeaders, uid: this.uid, resetImport: this.resetImport });
    } else {
      this.store
        .sendCollectionAction({
          action: 'import',
          config: {
            data: { headers: resultHeaders, uid: this.uid, ...this.confirmOptions },
          },
        })
        .then(() => {
          this.resetImport();
          this.$emit('success');
        });
    }
  }

  rawDataIndex(page = 1, perPage = 15) {
    return this.store
      .sendCollectionAction({
        action: 'excel',
        config: { params: { uid: this.uid, page: page, per_page: perPage } },
      })
      .then(res => {
        this.currentPage = res.data.current_page;
        this.totalCount = res.data.total_count;
        this.totalPages = res.data.total_page;
        this.rowData = res.data.records;
        return res;
      });
  }

  onTableChange(page = 1, _: IObject, perPage = 15) {
    this.rawDataIndex(page, perPage);
  }

  onClickFileInput() {
    (this.$refs.fileInput as any).click();
  }
}
</script>

<template lang="pug">
.ta-import
  a-tooltip
    template(#title) 导入
    .uploader
      TextButton(:icon='loading ? "loading" : "upload"')
        | {{ title }}
      slot
      input.file-input(ref='fileInput', type='file', :multiple='false', @change='fileChange')
  a-modal(v-model='visibleImport', :width='1280', title='导入')
    .header
      //- a-steps(:current='currentStep')
      //-   a-step(v-for='(title, index) in steps' :key='index' :title='title')
    .main
      //- a-form(labelAlign='left')
        a-form-item(
          label='选择参考字段'
          :label-col='{ span: 3 }'
          :wrapper-col='{ span: 18 }')
          a-select(
            v-model='validKeys'
            mode='multiple'
            placeholder='请选择参考字段'
            style='width: 460px')
            a-select-option(
              v-for='(item, ind) in primaryKeys'
              :key='ind'
              :value='item.key')
              | {{ item.name }}
      TaIndexTable(
        :data='rowData',
        :currentPage='currentPage',
        :totalCount='totalCount',
        :totalPages='totalPages',
        :config='{ showSizeChanger: true }',
        @change='onTableChange'
      )
        a-table-column(v-for='(dataTitle, index) in dataTitles', :key='index', :dataIndex='dataTitle', :width='120')
          template(#title)
            a-select(v-model='keysChanged[index]', placeholder='请选择表头', style='width: 100%; min-width: 100px')
              a-select-option(v-for='(item, index) in optionalHeaders', :key='index', :value='item.key')
                | {{ item.name }}
            .table-title {{ dataTitle }}
    template(#footer)
      a-button(@click='onCancel') 取消
      a-button(type='primary', :loading='store.loading', @click='onConfirm') 确定上传
</template>

<style lang="stylus" scoped>
.ta-import
  cursor pointer
  .uploader
    position relative
    overflow hidden
    display flex
    align-items center
    .icon
      margin-right 5px
    .file-input
      width 100%
      height 100%
      position absolute
      top 0
      left 0
      z-index 9
      display inline
      margin 0
      padding 0
      outline none
      border none
      opacity 0
  .header
    padding 14px 120px
    width 100%
    border-bottom 1px #e8e8e8 solid
    background #eee
  .main
    overflow auto
    padding 0px 20px 20px
    width 100%
    height 100%
.table-title
  padding 10px 10px 0px
  color #808080
</style>
