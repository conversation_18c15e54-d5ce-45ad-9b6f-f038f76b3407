<template lang="pug">
a-timeline
  a-timeline-item(
    v-for="(item, index) in menus"
    :key="index"
    :color="inactiveColor"
    @click.native="onMenu(item)")
    a-icon(
      :type="item.icon"
      :style="{ fontSize: iconSize, color: activeKey === item.path ? activeColor : inactiveColor }"
      slot="dot")
    .module
      .module-top(:class="{ 'is-path': item.path }")
        .title(:style="{ color: activeKey === item.path ? activeColor : inactiveColor }")
          | {{ item.label }}
        .right
          template(v-if="item.tag && item.tag.text")
            .ta-tag(:class="`ta-tag-${item.tag.type}`")
              | {{ item.tag.text }}
          a-icon(type="right" :color="inactiveColor" v-if="item.path")
      .module-middle(v-if="$slots[item.slot]")
        slot(:name="item.slot")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class MenuList extends Vue {
  @Prop({ type: Array, default: () => [] }) private menus?: any;
  @Prop({ type: String, default: '' }) private activeKey?: string;
  @Prop({ type: String, default: '#383838' }) private activeColor?: string;
  @Prop({ type: String, default: '#A6A6A6' }) private inactiveColor?: string;
  @Prop({ type: String, default: '18px' }) private iconSize?: string;

  public onMenu(val: any) {
    this.$emit('click', val);
  }

  public tagStyle(type: string = 'info') {
    return ({
      info: {
        color: '#f5f5f5',
        textColor: '#808080',
      },
      primary: {
        color: '#EDF7FF',
        textColor: '#3DA8F5',
      },
      success: {
        color: '#F0F9F2',
        textColor: '#6DC37D',
      },
      warning: {
        color: '#ffe1e1',
        textColor: '#f2826a',
      },
      danger: {
        color: '#ffe1e1',
        textColor: '#ad0000',
      },
    } as any)[type];
  }
}
</script>

<style lang="stylus" scoped>
.ant-timeline
  overflow auto
  margin-right 16px
  padding 20px
  min-width 300px
  width 300px
  height 100%
  background #fff
  .module
    padding 3px 0px 8px 4px
    width 100%
    .is-path
      cursor pointer
    .module-top
      display flex
      justify-content space-between
      align-items center
      margin-top -4px
      min-height 26px
      width 100%
      .title
        white-space nowrap
      .right
        display flex
        flex-wrap nowrap
        justify-content flex-end
        align-items center
        .ta-tag
          padding 5px 6px
          font-size 12px
          line-height 16px
    .module-middle
      margin-top 12px
      padding 12px 16px
      background #FAFAFA
</style>
