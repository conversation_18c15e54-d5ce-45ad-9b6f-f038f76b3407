<template lang="pug">
.export-view(@click="onModal")
  slot
    TextButton(
      :icon="icon"
      style="min-width: 80px"
      :loading="exportLoading"
      :disabled="exportLoading")
      | {{ tipText }}

  MainModal(
    v-model="visibleExport"
    title="选择导出信息字段"
    width="960px")
    .table-view
      a-checkbox(
        :indeterminate="indeterminate"
        @change="onCheckAllChange"
        :checked="checkAll")
        | 全选
      a-checkbox-group(v-model="checkedList" @change="onChange")
        a-row(:gutter="20")
          a-col(
            :span="6"
            v-for="(item, index) in templates"
            :key="index")
            a-checkbox(:value="item.key") {{ item.label }}
      .footer
        a-button(size="large" @click="visibleExport=false") 取消
        a-button(
          type="primary"
          size="large"
          :loading="exportLoading"
          :disabled="checkedList.length === 0 || exportLoading"
          @click="onConfirm") 确定
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import { ExportModel, IType } from '@/service/export';

const importModel = new ExportModel();

@Component({
  components: {},
})
export default class TaExportExcel extends Vue {
  @Prop({ type: String, default: '' }) type!: IType;
  @Prop({ type: Object, default: () => ({}) }) params: any;
  @Prop({ type: Boolean, default: true }) selectTitles?: boolean;
  // 按钮信息
  @Prop({ type: String, default: '导出Excel' }) tipText?: string;
  @Prop({ type: String, default: 'download' }) icon?: string;
  // 选择导出字段参数
  templates: any[] = [];
  checkedList: any[] = [];
  indeterminate: boolean = true;
  checkAll: boolean = false;
  exportLoading: boolean = false;
  visibleExport: boolean = false;

  onModal() {
    if (this.selectTitles) {
      this.fetchData();
    } else {
      this.onExport({});
    }
  }

  async fetchData() {
    const { data } = await importModel.tableKeys({ type: this.type });
    this.templates = (data.datas || []).map((e: any) => ({
      label: e.name,
      key: e.key,
    }));
    this.visibleExport = true;
  }

  onConfirm() {
    const resObj = this.checkedList.reduce((res: any, key: string) => {
      res[key] = this.templates.find((e: any) => e.key === key).label;
      return res;
    }, {});
    this.onExport(resObj);
  }

  // 导出
  async onExport(val: any) {
    try {
      this.exportLoading = true;
      const params = {
        titles: val,
        args: {
          ...this.params,
        },
      };
      const { data } = await importModel.exportExcel(this.type, params);
      this.$helper.exportExcel(data);
      this.exportLoading = false;
      this.visibleExport = false;
      this.$message.success('导出成功！');
      this.$emit('success');
    } catch (error) {
      this.exportLoading = false;
      this.visibleExport = false;
      this.$message.error('导出失败！');
    }
  }

  onChange(checkedList: string[]) {
    this.indeterminate = !!checkedList.length && checkedList.length < this.templates.length;
    this.checkAll = checkedList.length === this.templates.length;
  }

  onCheckAllChange(e: any) {
    Object.assign(this, {
      checkedList: e.target.checked ? this.templates.map((e: any) => e.key) : [],
      indeterminate: false,
      checkAll: e.target.checked,
    });
  }
}
</script>

<style lang="stylus" scoped>
.table-view
  position relative
  padding 20px
  width 100%
  height 100%
  .ant-checkbox-group
    margin-top 14px
    padding 10px 0px
    width 100%
    border-top 1px #e8e8e8 solid
    .ant-col-6
      margin 5px 0px
  .footer
    position absolute
    bottom 0px
    left 0px
    display flex
    justify-content flex-end
    padding 10px
    width 100%
    border-top 1px #e8e8e8 solid
    button
      width 80px
</style>
