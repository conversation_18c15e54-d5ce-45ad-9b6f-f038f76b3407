<template lang="pug">
a-tooltip
  template(slot='title' v-if="tips") {{ tips }}
  slot(name="custom")
    a-button.icon-button(:type="type" :disabled="disabled" @click.stop="onClick")
      a-icon.icon(:type="icon" :theme="theme" style="font-size: 16px;")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';

@Component
export default class IconTooltip extends Vue {
  @Prop({ type: String, default: 'copy' }) private icon!: string;
  @Prop({ type: String, default: '' }) private tips!: string;
  @Prop({ type: String, default: 'outlined' }) private theme!: string; // outlined,  filled, twoTone
  @Prop({ type: String, default: 'default' }) private type!: string;
  @Prop({ type: Boolean, default: false }) private disabled!: boolean;

  public onClick(...args: any) {
    this.$emit('click', ...args);
  }
}
</script>

<style lang="stylus" scoped>
.icon-button
  display inline-flex
  justify-content center
  align-items center
  overflow hidden
  margin-left 20px
  padding 0px
  height unset
  border none
  background none
  color #A6A6A6
  vertical-align -2px
  vertical-align sub
  font-size 16px
  line-height 16px
  &:first-child
    margin-left 0
  &:hover
    background none
    color #3DA8F5
</style>
