<script lang="ts">
import { Component, Vue, Prop, Watch, Model } from 'vue-property-decorator';

export interface TaTabTabsInterface {
  key: string;
  label: string;
  num?: number;
}

@Component
export default class TaTab extends Vue {
  @Model('change', { type: String, default: '' }) value!: string;
  @Prop({ type: Array, default: () => [] }) private readonly tabs!: TaTabTabsInterface[];

  private cursorLeft = 0;
  private cursorWidth = 0;

  get cursorStyle() {
    return {
      width: `${this.cursorWidth}px`,
      transform: `translate3d(${this.cursorLeft}px, 0px, 0px)`,
    };
  }

  @Watch('value')
  onValueChange() {
    this.initActiveCursor();
  }

  @Watch('tabs', { deep: true })
  onTabsChange() {
    this.initActiveCursor();
  }

  mounted() {
    // 没有选中 tab ，且有传入 tabs 时，默认切换到第一个 tab
    if (!this.value && this.tabs && this.tabs[0] && this.tabs[0].key !== this.value) {
      this.$emit('change', this.tabs[0].key);
    }

    this.initActiveCursor();
  }

  onTabChange(key: string) {
    this.$emit('beforeChange', key);
    this.$emit('change', key);
  }

  initActiveCursor() {
    this.$nextTick(() => {
      const activePane = (this.$refs.activePane && (this.$refs.activePane as HTMLElement[]))[0];
      if (activePane) {
        this.cursorLeft = activePane.offsetLeft;
        this.cursorWidth = activePane.clientWidth;
      }
    });
  }

  isActivePane(tab: TaTabTabsInterface) {
    return this.value === tab.key;
  }
}
</script>

<template lang="pug">
.ta-tab
  .tabs
    .pane(
      v-for='tab in tabs',
      :key='tab.key',
      :ref='isActivePane(tab) ? "activePane" : null',
      @click='onTabChange(tab.key)'
    )
      slot(name='tab', :tab='tab', :isActive='isActivePane(tab)')
        .tab.flex-between(:class='{ "tab-active": isActivePane(tab) }')
          .tab-label {{ tab.label }}
    .cursor(:style='cursorStyle')
</template>

<style lang="stylus" scoped>
.ta-tab
  width 100%
  .tabs
    // width 100%
    position relative
    display flex
    overflow-x scroll
    .pane
      flex-shrink 0
      margin 0 15px
      cursor pointer
      &:first-child
        margin 0 15px 0 0
    .tab-active
      .tab-label
        color black
    .tab
      height 48px
      color #808080
      font-size 14px
      line-height 48px
  .cursor
    position absolute
    bottom 0
    width 100%
    height 4px
    background #3da8f5
    transition all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1)
</style>
