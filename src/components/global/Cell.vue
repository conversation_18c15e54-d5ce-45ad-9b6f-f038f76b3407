<template lang="pug">
.cell
  .key {{ label }}
  .value
    slot(name="value")
      span(v-if="isPrice") ￥{{ value | toCurrency }}
      span(v-else) {{value}}
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class Cell extends Vue {
  @Prop({ type: String, default: '' }) private label!: string;
  @Prop({ default: '' }) private value!: string;
  @Prop({ type: Boolean, default: false }) private isPrice!: boolean;
}
</script>

<style lang="stylus" scoped>
.cell
  display flex
  padding 8px 0px
  font-size 14px
  line-height 22px
  .key
    min-width 100px
    color #808080
  .value
    color #383838

@media print
  .cell
    padding 0px
</style>
