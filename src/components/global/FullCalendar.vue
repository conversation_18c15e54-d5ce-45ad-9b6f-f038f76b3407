<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import FullCalendar, { CalendarOptions, EventApi, DateSelectArg, EventClickArg, EventInput } from '@fullcalendar/vue';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import { INITIAL_EVENTS, createEventId } from './fullcalendar-event-utils';
import zhLocale from '@fullcalendar/core/locales/zh-cn'; // 中文语言包
import resourceTimelinePlugin from '@fullcalendar/resource-timeline';
import { log } from 'util';
import { IActivity } from '@/models/access/activity';

@Component({
  components: {
    FullCalendar,
  },
})
export default class FullCalendarComponent extends Vue {
  @Prop({ type: Object, default: {} }) private data!: EventInput[];
  @Prop({ type: Object, default: () => {} }) private store!: IObject;

  private activities: Array<IActivity> = [];
  calendarOptions: CalendarOptions = {
    plugins: [
      dayGridPlugin,
      timeGridPlugin,
      interactionPlugin, // 日期点击
      resourceTimelinePlugin, // 资源时间线
    ],
    headerToolbar: {
      left: 'today prev,next',
      center: 'title',
      right: '',
    },
    buttonText: {
      prev: '‹',
      next: '›',
      prevYear: '«',
      nextYear: '»',
      today: '今天',
      month: '月',
      week: '星期',
      day: '天',
    },
    locale: zhLocale,
    initialView: 'dayGridMonth',
    initialEvents: INITIAL_EVENTS, // alternatively, use the `events` setting to fetch from a feed
    editable: false,
    selectable: false,
    selectMirror: true,
    dayMaxEvents: true,
    weekends: true,
    select: this.handleDateSelect,
    eventClick: this.handleEventClick,
    eventsSet: this.handleEvents,
    /* you can update a remote database when these fire:
    eventAdd:
    eventChange:
    eventRemove:
    */
  };

  mounted() {
    // this.getActive();
  }

  private async getActive(): Promise<void> {
    this.store.init();
    let { data } = await this.store.index();
    this.activities = data.activities;
  }

  private currentEvents: EventApi[] = [];

  get fullcalendarOptions() {
    return Object.assign(this.calendarOptions, this.data);
  }

  private handleDateSelect(selectInfo: DateSelectArg) {
    let title = prompt('请为你的事件输入一个新的标题');
    let calendarApi = selectInfo.view.calendar;

    calendarApi.unselect(); // clear date selection

    if (title) {
      calendarApi.addEvent({
        id: createEventId(),
        title,
        start: selectInfo.startStr,
        end: selectInfo.endStr,
        allDay: selectInfo.allDay,
      });
    }
  }

  handleEventClick(clickInfo: EventClickArg) {
    if (confirm(`你确定要删除吗？ '${clickInfo.event.title}'`)) {
      clickInfo.event.remove();
    }
  }

  handleEvents(events: EventApi[]) {
    this.currentEvents = events;
  }
}
</script>

<template lang="pug">
FullCalendar(:options='fullcalendarOptions')
  template(v-slot:eventContent='arg')
    b {{ arg.timeText }}
    i {{ arg.event.title }}
</template>

<style lang="stylus">
b
  /* used for event dates/times */
  margin-right 3px
</style>
