<template lang="pug">
.cell-box
  .key(:style="{ minWidth: labelWidth + 'px' }")
    label.text-danger(v-if="required") *
    a-icon(:type="icon")
    span {{ label }}
  .value
    slot(name="scope")
      span(v-if="leftUnit") {{ leftUnit }}
      span {{ value }}
      span(v-if="rightUnit") {{ rightUnit }}
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class IconCell extends Vue {
  @Prop({ type: String, default: '' }) private icon!: string;
  @Prop({ type: String, default: '' }) private label!: string;
  @Prop({ type: [String, Number], default: '' }) private value!: string;
  @Prop({ type: String, default: '' }) private leftUnit!: string;
  @Prop({ type: String, default: '' }) private rightUnit!: string;
  @Prop({ type: <PERSON><PERSON><PERSON>, default: false }) private required!: boolean;
  @Prop({ type: Number, default: 152 }) private labelWidth!: number;
  @Prop({ type: Boolean, default: false }) private valueSlot!: boolean;
}
</script>

<style lang="stylus" scoped>
.cell-box
  display flex
  align-items center
  padding 12px 0px
  line-height 20px
  .key
    height 20px
    label
      padding 0px 1px
      vertical-align middle
    span
      margin-left 12px
      color #808080
      vertical-align middle
      font-size 14
  .value
    display flex
    flex-wrap wrap
    align-items center
    width 100%
</style>
