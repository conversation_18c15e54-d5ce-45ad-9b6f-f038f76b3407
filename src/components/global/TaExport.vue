<script lang="ts">
import loading from '@/helpers/directives/loading';
import { ActiveStore } from '@/lib/ActiveStore';
import { merge } from 'lodash';
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { TaIndexImportHeader } from './TaIndex';

@Component({
  components: {},
})
export default class TaExport extends Vue {
  @Prop({ type: Object, required: true }) store!: ActiveStore;
  @Prop({ type: Object, default: () => ({}) }) temporaryQuery!: IObject;
  @Prop({ type: Boolean, default: false }) exportAll!: boolean;

  loading = false;
  visible = false;
  indeterminate = true;
  checkAll = false;
  headers: { key: string; name: string }[] = [];
  selectedKey: string[] = [];

  get selectedHeaders() {
    return this.headers.filter(header => this.selectedKey.includes(header.key));
  }

  onHeaders() {
    if (this.loading) {
      return;
    }
    this.loading = true;

    this.store
      .sendCollectionAction({
        action: 'export_headers',
        config: { data: this.store.model.params },
      })
      .then(res => {
        this.headers = res.data.headers;
        this.loading = false;
        this.visible = true;
      })
      .catch(err => {
        this.loading = false;
        throw err;
      });
  }

  onCancel() {
    this.visible = false;
  }

  onConfirm() {
    if (this.loading) {
      return;
    }

    this.loading = true;
    const params = this.exportAll
      ? {}
      : merge({ q: { ...this.store.query, ...this.temporaryQuery } }, this.store.model.params);
    this.store
      .sendCollectionAction({
        action: 'export',
        config: {
          params,
          data: { headers: this.selectedHeaders },
          responseType: 'arraybuffer',
        },
      })
      .then(res => {
        this.loading = false;
        const url = window.URL.createObjectURL(
          new Blob([res.data as string], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          }),
        );
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
      })
      .catch(err => {
        this.loading = false;
        throw err;
      });
  }

  onChange(checkedList: string[]) {
    this.indeterminate = !!checkedList.length && checkedList.length < this.headers.length;
    this.checkAll = checkedList.length === this.headers.length;
  }

  onCheckAllChange(e: any) {
    Object.assign(this, {
      selectedKey: e.target.checked ? this.headers.map(header => header.key) : [],
      indeterminate: false,
      checkAll: e.target.checked,
    });
  }
}
</script>

<template lang="pug">
.ta-export
  TextButton(:icon='loading ? "loading" : "export"', @click='onHeaders')
    slot
      | 导出
  a-modal(title='选择导出字段', :visible='visible', :width='960', :footer='null', @cancel='onCancel')
    a-checkbox(:indeterminate="indeterminate" :checked="checkAll", @change='onCheckAllChange') 全选
    a-checkbox-group(
      v-model='selectedKey',
      @change='onChange'
    )
      a-row(:gutter="20")
        a-col(
          :span="6"
          v-for="item in headers"
          :key="item.key"
        )
          a-checkbox(:value="item.key") {{ item.name }}
    //- template(#footer)
    .footer
      a-button(size='large' @click='onCancel') 取消
      a-button(
        type='primary',
        size='large',
        :loading='loading',
        :disabled='selectedKey.length === 0',
        @click='onConfirm',
      ) 确定
</template>

<style lang="stylus" scoped>
.ta-export
  cursor pointer
  display flex
.ant-checkbox-group
  margin 14px 0 60px
  padding 10px 0px
  width 100%
  border-top 1px #e8e8e8 solid
  .ant-col-6
    margin 5px 0px

.footer
  position absolute
  bottom 0px
  left 0px
  display flex
  justify-content flex-end
  padding 10px
  width 100%
  border-top 1px #e8e8e8 solid
  button
    width 80px
</style>
