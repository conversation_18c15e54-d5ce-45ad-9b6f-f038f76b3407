<template lang="pug">
.speech-control
  a-popover(
    v-model="visible"
    placement="bottomRight"
    trigger="click"
    :overlayClassName="'speech-control-popover'"
  )
    template(slot="content")
      .speech-panel
        .panel-header
          h4 语音播报设置
          a-switch(
            :checked="settings.enabled"
            @change="handleEnabledChange"
            checked-children="开启"
            un-checked-children="关闭"
          )
        
        .panel-content(v-if="settings.enabled")
          .control-group
            label 播放控制
            .control-buttons
              a-button(
                :type="status.isPlaying ? 'primary' : 'default'"
                @click="handlePlayPause"
                :disabled="!status.isSpeaking && status.queueLength === 0"
              )
                a-icon(:type="status.isPlaying ? 'pause' : 'play-circle'")
                span {{ status.isPlaying ? '暂停' : '播放' }}
              a-button(@click="handleStop" :disabled="!status.isSpeaking")
                a-icon(type="stop")
                span 停止
              a-button(@click="handleTestSpeech")
                a-icon(type="sound")
                span 测试

          .control-group
            label 语速: {{ settings.rate }}
            a-slider(
              :min="0.5"
              :max="2"
              :step="0.1"
              :value="settings.rate"
              @change="handleRateChange"
            )

          .control-group
            label 音量: {{ Math.round(settings.volume * 100) }}%
            a-slider(
              :min="0"
              :max="1"
              :step="0.1"
              :value="settings.volume"
              @change="handleVolumeChange"
            )

          .control-group(v-if="voices.length > 0")
            label 语音选择
            a-select(
              :value="currentVoiceName"
              @change="handleVoiceChange"
              style="width: 100%"
              placeholder="选择语音"
            )
              a-select-option(
                v-for="voice in chineseVoices"
                :key="voice.voiceURI"
                :value="voice.voiceURI"
              )
                | {{ voice.name }} ({{ voice.lang }})

          .control-group
            label 快捷操作
            .quick-actions
              a-button(size="small" @click="speakPageTitle")
                a-icon(type="file-text")
                span 播报页面标题
              a-button(size="small" @click="speakTime")
                a-icon(type="clock-circle")
                span 播报时间

        .panel-footer(v-if="!isSupported")
          a-alert(
            message="您的浏览器不支持语音合成功能"
            type="warning"
            show-icon
          )

    a-button.speech-toggle(
      :type="settings.enabled ? 'primary' : 'default'"
      shape="circle"
      :title="settings.enabled ? '语音播报已开启' : '语音播报已关闭'"
    )
      a-icon(:type="settings.enabled ? 'sound' : 'sound-muted'")
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import speechService, { SpeechSettings } from '@/utils/speechService';

@Component({
  name: 'SpeechControl',
})
export default class SpeechControl extends Vue {
  visible = false;
  settings: SpeechSettings = speechService.getSettings();
  voices: SpeechSynthesisVoice[] = [];
  status = speechService.getStatus();
  
  // 定时器用于更新状态
  private statusTimer: number | null = null;

  get isSupported(): boolean {
    return speechService.isSupported();
  }

  get currentVoiceName(): string {
    return this.settings.voice?.voiceURI || '';
  }

  get chineseVoices(): SpeechSynthesisVoice[] {
    return this.voices.filter(voice => 
      voice.lang.includes('zh') || voice.lang.includes('CN')
    );
  }

  created() {
    this.loadVoices();
    this.startStatusUpdate();
  }

  beforeDestroy() {
    this.stopStatusUpdate();
  }

  private loadVoices(): void {
    this.voices = speechService.getVoices();
    
    // 如果语音列表为空，等待加载
    if (this.voices.length === 0) {
      setTimeout(() => {
        this.voices = speechService.getVoices();
      }, 100);
    }
  }

  private startStatusUpdate(): void {
    this.statusTimer = window.setInterval(() => {
      this.status = speechService.getStatus();
    }, 500);
  }

  private stopStatusUpdate(): void {
    if (this.statusTimer) {
      clearInterval(this.statusTimer);
      this.statusTimer = null;
    }
  }

  handleEnabledChange(enabled: boolean): void {
    speechService.setEnabled(enabled);
    this.settings = speechService.getSettings();
    
    if (enabled) {
      speechService.speak('语音播报已开启');
    }
  }

  handlePlayPause(): void {
    if (this.status.isPlaying) {
      speechService.pause();
    } else if (this.status.isPaused) {
      speechService.resume();
    }
  }

  handleStop(): void {
    speechService.stop();
  }

  handleTestSpeech(): void {
    speechService.speak('这是语音播报测试，您好！', { priority: 'high', interrupt: true });
  }

  handleRateChange(value: number): void {
    speechService.updateSettings({ rate: value });
    this.settings = speechService.getSettings();
  }

  handleVolumeChange(value: number): void {
    speechService.updateSettings({ volume: value });
    this.settings = speechService.getSettings();
  }

  handleVoiceChange(voiceURI: string): void {
    const selectedVoice = this.voices.find(voice => voice.voiceURI === voiceURI);
    if (selectedVoice) {
      speechService.updateSettings({ voice: selectedVoice });
      this.settings = speechService.getSettings();
    }
  }

  speakPageTitle(): void {
    speechService.speakPageTitle();
  }

  speakTime(): void {
    const now = new Date();
    const timeStr = now.toLocaleTimeString('zh-CN');
    speechService.speak(`当前时间：${timeStr}`, { priority: 'high' });
  }
}
</script>

<style lang="stylus" scoped>
.speech-control
  display inline-block
  margin-right 8px

.speech-toggle
  width 32px
  height 32px
  display flex
  align-items center
  justify-content center

.speech-panel
  width 320px
  padding 16px

.panel-header
  display flex
  justify-content space-between
  align-items center
  margin-bottom 16px
  
  h4
    margin 0
    font-size 16px
    font-weight 500

.panel-content
  .control-group
    margin-bottom 16px
    
    label
      display block
      margin-bottom 8px
      font-weight 500
      color rgba(0, 0, 0, 0.85)

.control-buttons
  display flex
  gap 8px
  
  .ant-btn
    flex 1

.quick-actions
  display flex
  gap 8px
  
  .ant-btn
    flex 1

.panel-footer
  margin-top 16px
</style>

<style lang="stylus">
.speech-control-popover
  .ant-popover-content
    padding 0
  
  .ant-popover-inner
    padding 0
    border-radius 8px
    box-shadow 0 4px 12px rgba(0, 0, 0, 0.15)
</style>
