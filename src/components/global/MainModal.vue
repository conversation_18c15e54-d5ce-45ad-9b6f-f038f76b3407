<script lang="ts">
import { Component, Vue, Prop, Model } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class MainModal extends Vue {
  @Model('change', { type: Boolean, default: false }) private value!: boolean;
  @Prop({ type: String, default: '' }) private title!: string;
  @Prop({ type: [String, Number], default: '960px' }) private width!: string;
  @Prop({ type: Boolean, default: false }) private maskClosable!: boolean;
  @Prop({ type: Boolean, default: true }) private closable!: boolean;
  @Prop({ type: Boolean, default: true }) private destroyOnClose!: boolean;
  @Prop({ type: [String, Number], default: '0px' }) private bottom!: string;

  get hasRight() {
    return !!this.$slots.right;
  }
  get visible() {
    return this.value;
  }
  get bottomGap() {
    return typeof this.bottom === 'string' ? this.bottom : `${this.bottom}px`;
  }

  set visible(visible) {
    this.$emit('change', visible);
  }

  change(visible: boolean) {
    this.$emit('change', visible);
  }
}
</script>

<template lang="pug">
a-modal.main-modal-component(
  v-model="visible"
  :footer="false"
  :maskClosable="maskClosable"
  :width="width"
  :bodyStyle="{ padding: '0px' }"
  :style="{ 'padding-bottom': bottomGap }"
  :closable="closable"
  :destroyOnClose="destroyOnClose"
  @change="change")
  template(slot="title")
    slot(name="title")
      span {{ title }}
  .main-modal__body
    .main-modal__content(:class="{ 'has-footer': !!$slots.footer }")
      .main-content
        slot
      .side(v-if="hasRight")
        slot(name="right")
    .main-modal__footer(v-if="$slots.footer")
      slot(name="footer")
</template>

<style lang="stylus">
.main-modal-component
  top 0px !important
  bottom 0px
  padding-bottom 0px
  height 100%
  overflow hidden
  min-width 1200px
  .ant-modal
    padding-bottom 0 !important
  .ant-modal-content
    height calc(100vh - 50px)
    top 50px
  .ant-modal-body
    height calc(100% - 55px)
  .main-modal__body
    display flex
    flex-direction column
    height 100%
    background #fff
    .main-modal__content
      display flex
      height 100%
      .main-content
        flex auto
        overflow auto
        height 100%
      .side
        flex none
        overflow auto
        height 100%
        border-left 1px solid #e6e6e6
    .has-footer
      height calc(100% - 58px)
    .main-modal__footer
      flex-shrink 0
      padding 8px 16px
      border-top 1px solid #E5E5E5
      background #fff
</style>
