<template lang="pug">
.table-header
  .table-header__title-box
    slot(name="title")
      .table-header__title
        | {{ title }}
    slot(name="actions")
  .table-header__actions
    slot
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';

@Component
export default class TaTitleHeader extends Vue {
  @Prop({ type: String }) title!: string;
}
</script>

<style lang="stylus" scoped>
.table-header
  display flex
  justify-content space-between
  align-items stretch
  height 48px
  .table-header__title-box
    align-self center
    flex-shrink 0
    display flex
    align-items center
    .table-header__title
      color #383838
      color #383838
      font-weight 500
      font-size 18px
      line-height 20px
  .table-header__actions
    display flex
    justify-content flex-end
    align-items center
    max-height 100%
    width 100%
    button
      margin-left 14px
</style>
