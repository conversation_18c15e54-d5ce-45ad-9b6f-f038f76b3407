<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { IFile } from '@/models/file';
import FileServer from '@/models/file';
import FileSaver from 'file-saver';
import FilePreviewer from '@/components/global/FilePreviewer.vue';
import audio from '@/assets/icons/file/sound_32.svg';
import video from '@/assets/icons/file/video_32.svg';
import attachment from '@/assets/icons/file/attachment_32.svg';

@Component({
  components: {
    FilePreviewer,
  },
})
export default class AttachmentFile extends Vue {
  @Prop({ type: Object, default: () => ({}) }) attachment!: IFile;
  @Prop({ type: Boolean, default: false }) display!: boolean;
  @Prop({ type: Boolean, default: false }) showActions!: boolean;
  @Prop({ type: Boolean, default: true }) downloadable!: boolean;
  @Prop({ type: Boolean, default: true }) previewable!: boolean;
  @Prop({ type: Number }) imageMaxHeight!: number;

  fileServer = new FileServer({ useCdn: false });
  iconPath: IObject = {
    audio,
    video,
    attachment,
  };
  typeMap: IObject = {
    image: ['gif', 'jpg', 'jpeg', 'png', 'bmp', 'webp', 'svg'],
    video: ['mp4', 'm3u8', 'rmvb', 'avi', 'swf', '3gp', 'mkv', 'flv'],
    audio: ['mp3', 'wav', 'wma', 'ogg', 'aac', 'flac'],
  };
  // 预览
  visible: boolean = false;

  get fileItem() {
    const { fileSize } = this.attachment;
    return {
      ...this.attachment,
      url: this.fileServer.getDownloadUrl(this.attachment),
      size_zh: FileServer.getSizeText(fileSize),
    };
  }

  getThumbnailUrl() {
    const { fileType, file } = this.fileItem;
    if (this.typeMap.image.includes(fileType)) {
      return file && file.name ? URL.createObjectURL(file) : this.fileServer.getThumbnailUrl(this.fileItem, 64, 64);
    }
    if (this.typeMap.video.includes(fileType)) {
      return this.iconPath.video;
    }
    if (this.typeMap.audio.includes(fileType)) {
      return this.iconPath.audio;
    }
    return this.iconPath.attachment;
  }
  isSuccess() {
    return this.fileItem.status === 'done' || this.fileItem.percent >= 100;
  }
  preview() {
    if (this.isSuccess() && this.previewable) {
      this.visible = true;
    } else {
      this.$emit('preview', this.fileItem);
    }
  }
  remove() {
    this.$emit('remove', this.fileItem);
  }
  restart() {
    this.$emit('restart', this.fileItem);
  }
  download() {
    FileSaver.saveAs(this.fileItem.url, this.fileItem.fileName);
  }
}
</script>

<template lang="pug">
.attachment-wrapper
  .media-wrapper(v-if="display && fileItem.mimeType.includes('video')")
    TaVideo.video(:src="fileItem.url" :type="fileItem.mimeType")
  .audio-wrapper(v-else-if="display && fileItem.mimeType.includes('audio')")
    audio.audio(:src="fileItem.url" controls)
  .image-wrapper(v-else-if="display && fileItem.mimeType.includes('image')")
    img.display-image(
      :src="fileItem.url"
      :alt="fileItem.fileName"
      :style="{ 'max-height': imageMaxHeight ? `${imageMaxHeight}px` : null }"
      @click="preview")
  .attachment(v-else :class="fileItem.status" @click="preview")
    a-progress.progress(
      v-if="!isSuccess()"
      :percent="fileItem.percent"
      :strokeWidth="3"
      :showInfo="false"
      size="small")
    img.thumbnail(:src="getThumbnailUrl()")
    .file-name
      | {{ fileItem.fileName }}
    .file-size
      | {{ fileItem.size_zh }}
    .actions
      .file-button(@click.stop="download" v-if="isSuccess() && downloadable")
        a-icon(type="download")
      template(v-if="showActions")
        .file-button(@click.stop="remove")
          a-icon(type="close")
        .file-button(@click.stop="restart" v-if="fileItem.status === 'error'")
          a-icon(type="reload")
      slot(name="actions")

  FilePreviewer(
    v-if="previewable"
    v-model="visible"
    :attachment="attachment")
</template>

<style lang="stylus" scoped>
$contentHeight = 32px

.attachment-wrapper
  margin-top 10px
  max-width 100%
  &:first-child
    margin-top 0
  .media-wrapper
    width 100%
    background #000
    .video
      width 100%
  .audio-wrapper
    width 100%
    .audio
      width 100%
  .image-wrapper
    width 100%
    .display-image
      width 100%
      object-fit cover
      object-position center
  .attachment
    position relative
    display flex
    align-items center
    width 100%
    padding 8px
    height $contentHeight + (2 * 6)px
    border-radius 4px
    background #f0f0f0
    color #383838
    font-size 14px
    .thumbnail
      flex-shrink 0
      width $contentHeight
      height $contentHeight
      border-radius 4px
      background-color #E9F7FE
      text-align center
      font-size 18px
      line-height $contentHeight
    .progress
      position absolute
      right 0px
      bottom -7px
      left 0px
    .file-name
      width 0
      flex-grow 1
      overflow hidden
      padding 0 12px 0 6px
      // width 100%
      text-overflow ellipsis
      white-space nowrap
      line-height $contentHeight
    .file-size
      flex-shrink 0
      margin-right 8px
      color #999
      font-size 12px
    &:before
      position absolute
      top 0
      right 0
      bottom 0
      left 0
      z-index 2
      border 1px solid transparent
      border-radius 4px
      content ''
      transition all 0.1s cubic-bezier(0.645, 0.045, 0.355, 1)
      pointer-events none
    &:hover, &:focus
      cursor pointer
      &:before
        border 1px solid transparent
        box-shadow 0 0 6px 0 rgba(50, 150, 250, 0.1)
    .actions
      display inline-flex
      justify-content flex-end
      align-items center
      .file-button
        margin-left 8px
        width 20px
        height 20px
        color #8c8c8c
        font-size 20px
        line-height 1
        &:hover
          color #3296fa
  .error
    background rgba(255, 0, 0, 0.1)
</style>
