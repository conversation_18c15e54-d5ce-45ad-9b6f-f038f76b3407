<script lang="ts">
/**
 * 文件上传器
 *  slot 内容，只接受 Button 触发，不可使用 @click.stop
 * Events:
 * add (fileItem, fileItems)
 * start (fileItem, fileItems)
 * progress (fileItem)
 * success (fileItem)
 * error (fileItem, error)
 * preview (fileItem)
 * remvoe (fileItem, index)
 *
 * change 事件参数:
 *  this.fileItems 总的文件列表，包含所有可能的状态文件
 *  statusFiles 按照状态分组的文件列表
 *  allSettled 文件列表是否都已处理完毕，文件状态为 成功 或 失败
 * change (allFileItems, { todo: [], doing: [], done: [], error: [] }, isAllSettled: boolean)
 */
import { Component, Vue, Prop, Model, Watch, PropSync } from 'vue-property-decorator';
import FileServer, { IFile, IFileStatus } from '@/models/file';
import Attachments from './Attachments.vue';
import store from '@/store';
import Axios, { Canceler } from 'axios';

type IStatusFiles = {
  [status in IFileStatus]: IFile[];
};

@Component({
  components: {
    Attachments,
  },
})
export default class FileUploader extends Vue {
  fileServer = new FileServer({
    useCdn: false,
  });
  fileItems: IFile[] = [];
  isComputing: boolean = false;

  @Model('change', { type: Array }) value!: IFile[]; // file 对象数组
  @Prop({ type: Boolean, default: false }) useCdn!: boolean; // 是否使用 CDN
  // 是否所有文件都已处理完毕，状态都是：[success, error]
  @Prop({ type: Boolean, default: false }) isAllSettled!: boolean;
  @Prop({ type: Number, default: Number(4 * 1024 * 1024) }) chunkSize!: number; // 分片大小
  @Prop({ type: String, default: '*' }) accept!: string;
  @Prop({ type: Boolean, default: false }) disabled!: boolean;
  @Prop({ type: Boolean, default: true }) multiple!: boolean;
  @Prop({ type: Boolean, default: true }) checkFile!: boolean; // 是否检查文件存在性
  @Prop({ type: Boolean, default: true }) chunkable!: boolean; // 是否启用分片上传
  @Prop({ type: Boolean, default: true }) showList!: boolean; // 是否显示列表
  @Prop({ type: Boolean, default: false }) removeAfterSuccess!: boolean; // 上传之后是否从列表移除
  @Prop({ type: Number, default: 5000 }) limitMb!: number; // 文件允许最大尺寸 100MB

  get cdnTag() {
    return this.useCdn ? 1 : 0;
  }

  @Watch('value', { immediate: true, deep: true })
  onValueChange() {
    this.fileItems = (this.value || []).concat().map(
      (item: IFile): IFile => ({
        ...item,
        file: new File([], item.fileName),
        chunkSize: this.chunkSize,
        chunks: Math.ceil(item.fileSize / this.chunkSize),
        chunkId: 0,
        loaded: 0,
        percent: 0,
        status: 'done',
      }),
    );
  }

  @Watch('useCdn', { immediate: true })
  onCdnChange() {
    this.fileServer = new FileServer({
      useCdn: this.useCdn,
      token: store.state.token,
      userCode: store.state.currentUser.code,
    });
  }

  mergeFileItem(fileItem: IFile, payload: IObject) {
    for (let key in payload) {
      this.$set(fileItem, key, payload[key]);
    }
    const index = this.fileItems.findIndex(f => f.fileKey === fileItem.fileKey);
    if (index >= 0) {
      this.$set(this.fileItems, index, fileItem);
    }
  }

  handleFileInputChange(e: any) {
    const addFileHandlers = [...e.target.files].map(this.addFile);
    Promise.all(addFileHandlers).finally(() => {
      this.onChange();
    });
  }

  async addFile(file: File) {
    this.isComputing = true;
    const fileCategory = FileServer.getFileCategory(file);
    if (fileCategory === 'audio' && file.size > 30 * 1024 * 1024) {
      this.$message.warning(`【${file.name}】文件尺寸超过 30MB 大小限制。`);
      this.isComputing = false;
      return;
    } else if (fileCategory === 'application' && file.size > this.limitMb * 1024 * 1024) {
      this.$message.warning(`【${file.name}】文件尺寸超过 ${this.limitMb}MB 大小限制。`);
      this.isComputing = false;
      return;
    } else if (file.size > this.limitMb * 1024 * 1024) {
      this.$message.warning(`【${file.name}】文件尺寸超过 ${this.limitMb}MB 大小限制。`);
      this.isComputing = false;
      return;
    }
    const fileItem: IFile = await this.fileServer.getFileItem({
      file,
      chunkSize: this.chunkSize,
      shouldCompress: true,
    });
    this.isComputing = false;
    if (this.fileItems.find(f => f.fileKey === fileItem.fileKey)) {
      this.$message.warning('文件已经在上传列表中');
      return;
    }
    this.fileItems.push(fileItem);
    this.$emit('add', fileItem, this.fileItems);
    // begin upload
    this.start(fileItem);
  }

  // Start fileItem upload logic.
  start(fileItem: IFile) {
    this.mergeFileItem(fileItem, { status: 'doing', loaded: 0, chunkId: 0, percent: 0.2 });
    this.$emit('start', fileItem, this.fileItems);
    if (this.chunkable) {
      if (this.checkFile) {
        this.checkChunk(fileItem);
      } else {
        this.uploadChunk(fileItem);
      }
    } else {
      this.uploadFile(fileItem);
    }
  }

  // Upload file directly.
  async uploadFile(fileItem: IFile) {
    this.mergeFileItem(fileItem, { status: 'doing', percent: 0.2 });

    try {
      const { data } = await this.fileServer.upload(fileItem, {
        onUploadProgress: (e: ProgressEvent) => {
          this.updateProgress(e, fileItem);
        },
      });
      this.mergeFileItem(fileItem, data);
      this.success(fileItem);
      this.delayHideProgressBar(fileItem);
    } catch (error) {
      this.error(error, fileItem);
    }
  }

  // Upload with slice file into many chunks.
  async checkChunk(fileItem: IFile) {
    try {
      const { data } = await this.fileServer.checkChunk(fileItem);
      if (data.exist) {
        this.delayHideProgressBar(fileItem);
        this.mergeFileItem(fileItem, { ...data.stats, ...data });
        this.success(fileItem);
      } else {
        this.uploadChunk(fileItem);
      }
    } catch (error) {
      this.error(error, fileItem);
    }
  }

  async uploadChunk(fileItem: IFile) {
    this.mergeFileItem(fileItem, { status: 'doing', percent: 0.2 });

    const { chunks, file } = fileItem;
    const fileReader: FileReader = new FileReader();

    const loadNextChunk = () => {
      const { chunkSize, fileSize, chunkId } = fileItem;
      const start = chunkId * chunkSize;
      const end = start + chunkSize >= fileSize ? fileSize : start + chunkSize;
      fileReader.readAsArrayBuffer(file.slice(start, end));
    };

    fileReader.onload = async () => {
      try {
        const chunkBlob = new Blob([fileReader.result as ArrayBuffer]);
        const { data } = await this.fileServer.chunkUpload(
          {
            ...fileItem,
            file: chunkBlob,
            chunks,
          },
          {
            onUploadProgress: (e: ProgressEvent) => {
              this.updateProgress(e, fileItem);
            },
            cancelToken: new Axios.CancelToken((c: Canceler) => {
              fileItem._cancel = c;
            }),
          },
        );
        // 检查已上传分片, 支持断点续传，新版文件服务，上传成功后，chunks 为 true，做兼容
        if (data.chunks === true) {
          fileItem.chunkId = fileItem.chunks;
        } else {
          const uploadedChunks = data.chunks && data.chunks.length ? data.chunks : [0];
          const nextChunkId = Math.max(...uploadedChunks) + 1;
          if (fileItem.chunkId < nextChunkId) {
            fileItem.chunkId = nextChunkId;
          } else {
            // 保护：防止接口返回的是相同的 chunkId, 造成死循环
            this.error(new Error('重复上传分片，请检查接口返回的 chunks 数组。'), fileItem);
          }
        }

        if (fileItem.chunkId < fileItem.chunks) {
          loadNextChunk();
        } else {
          this.mergeFileItem(fileItem, data);
          this.success(fileItem);
          this.delayHideProgressBar(fileItem);
        }
      } catch (error) {
        this.error(error, fileItem);
      }
    };
    fileReader.onerror = error => {
      throw error;
    };
    loadNextChunk();
  }

  updateProgress(event: ProgressEvent, fileItem: IFile) {
    const uploadedSize = fileItem.chunkId * fileItem.chunkSize;
    const percent = Math.floor(((event.loaded + uploadedSize) / fileItem.fileSize) * 100);
    fileItem.percent = percent > 0.2 ? percent : 0.2;
    fileItem.loaded = event.loaded;
    this.$emit('progress', fileItem);
  }

  success(fileItem: IFile) {
    this.mergeFileItem(fileItem, {
      fileCategory: FileServer.getFileCategory(fileItem.file), // 后端返回数据存在不准确情况，使用浏览器标准 File
      mimeType: fileItem.file.type, // 后端返回数据存在不准确情况，使用浏览器标准 File
      loaded: fileItem.fileSize,
      percent: 100,
      status: 'done',
    });
    delete fileItem._cancel;
    this.$emit('success', fileItem);
    this.onChange();

    if (this.removeAfterSuccess) {
      const index = this.fileItems.findIndex(f => f.fileKey === fileItem.fileKey);
      if (index >= 0) {
        this.fileItems.splice(index, 1);
      }
    }
  }

  error(error: any, fileItem: IFile) {
    this.mergeFileItem(fileItem, {
      percent: 0,
      status: 'error',
    });
    this.$emit('error', fileItem, error);
    this.$message.error(`「${fileItem.fileName}」上传失败，请重试`);
    this.onChange();
  }

  async remove(fileItem: IFile, index: number) {
    if (fileItem._cancel) {
      fileItem._cancel();
    }
    this.$emit('remove', this.fileItems[index], index);
    this.fileItems.splice(index, 1);
    this.onChange();
  }

  /**
   * 事件参数:
   *  this.fileItems 总的文件列表，包含所有可能的状态文件
   *  statusFiles 按照状态分组的文件列表
   *  allSettled 文件列表是否都已处理完毕，文件状态为 成功 或 失败
   */
  onChange() {
    const todoFiles = this.fileItems.filter((file: IFile) => file.status === 'todo');
    const doingFiles = this.fileItems.filter((file: IFile) => file.status === 'doing');
    const doneFiles = this.fileItems.filter((file: IFile) => file.status === 'done');
    const errorFiles = this.fileItems.filter((file: IFile) => file.status === 'error');

    const allSettled = todoFiles.length === 0 && doingFiles.length === 0 && errorFiles.length === 0;
    const statusFiles = {
      todo: todoFiles,
      doing: doingFiles,
      done: doneFiles,
      error: errorFiles,
    };
    this.$emit('change', this.fileItems, statusFiles, allSettled);
    this.$emit('update:isAllSettled', allSettled); // 同步 isAllSettledd 状态
    this.$emit('statusChange', allSettled); // 文件列表状态
    if (allSettled) {
      this.$emit('allSettle', doneFiles, errorFiles); // 如果都处理完毕，发送事件
    }
  }

  clickFileInput(e: any) {
    if (e.target && e.target.tagName === 'BUTTON') {
      (this.$refs.fileInput as any).click();
    } else {
      const path: HTMLDivElement[] = e.path;
      const triggerIndex = path.findIndex(o => o.classList.contains('trigger-box'));
      const triggerElement = path[triggerIndex - 1];
      if (triggerElement && triggerElement.tagName === 'BUTTON') {
        (this.$refs.fileInput as any).click();
      }
    }
  }

  preview(fileItem: IFile) {
    this.$emit('preview', fileItem);
  }

  delayHideProgressBar(fileItem: IFile) {
    this.mergeFileItem(fileItem, { percent: 99.9 });
    setTimeout(() => {
      this.mergeFileItem(fileItem, { percent: 100 });
    }, 500);
  }
}
</script>

<template lang="pug">
.file-uploader-component
  .trigger-box(@click='clickFileInput', v-loading='isComputing')
    input.file-input(
      ref='fileInput',
      type='file',
      :value='[]',
      :accept='accept',
      :multiple='multiple',
      :disabled='disabled',
      @change='handleFileInputChange'
    )
    slot
      a-button(type='primary', icon='upload', :disabled='disabled')
        | 选择文件
  .file-list(v-if='showList && fileItems.length')
    Attachments(
      :attachments='fileItems',
      :showActions='!disabled',
      @restart='start',
      @remove='remove',
      @preview='preview'
    )
</template>

<style lang="stylus" scoped>
$contentHeight = 36px

.file-uploader-component
  display inline-block
  width 100%
  .trigger-box
    position relative
    display inline-block
    width 100%
    .file-input
      position absolute
      top 0
      left 0
      display inline
      margin 0
      padding 0
      width 0px
      height 0px
      outline none
      border none
      opacity 0
    button
      *
        pointer-events none
  .file-list
    padding-top 10px
</style>
