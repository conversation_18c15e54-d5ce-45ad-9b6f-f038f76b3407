<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import draggable from 'vuedraggable';
import { TaIndexTableConfigInterface } from './TaIndex';

@Component({
  components: {
    draggable,
  },
})
export default class TaIndexList extends Vue {
  // props
  @Prop({ type: Array }) private data!: IObject[];
  @Prop({ type: Object, default: () => ({}) }) private store!: any;
  @Prop({ type: String, default: '暂无数据' }) private emptyText!: string;
  @Prop({ type: Number, default: 1 }) private splitCount!: number;
  // scroll
  @Prop({ type: Boolean, default: false }) private scrollLoading!: boolean;
  // pagination
  @Prop({ type: Boolean, default: false }) private hideOnSinglePage!: boolean;
  @Prop({ type: Boolean, default: false }) private showSizeChanger!: boolean;
  @Prop({ type: String, default: 'middle' }) private paginationSize!: 'middle' | 'small' | 'large';
  // draggable
  @Prop({ type: Boolean, default: false }) private draggable!: boolean;

  get records() {
    return this.data || this.store.records || [];
  }

  set records(val: IObject[]) {
    // this.$emit('change', val);
  }

  get pagination() {
    return {
      total: this.store.totalCount,
      pageSize: this.store.perPage,
      current: this.store.currentPage,
      hideOnSinglePage: this.hideOnSinglePage,
      showSizeChanger: this.showSizeChanger,
      showQuickJumper: this.store.totalPages > 1,
      size: this.paginationSize,
    };
  }
  get loading() {
    return this.store.loading;
  }

  get scrollLoadingDisable() {
    return this.store.loading;
  }

  onPaginationChange(page: number, pageSize: number): void {
    this.$emit('change', page, {}, pageSize, this.scrollLoading);
  }

  loadMore() {
    const page = this.store.currentPage + 1;
    this.$emit('change', page, {}, this.store.perPage, this.scrollLoading);
  }

  endDrag(event: any) {
    const { newIndex, oldIndex } = event;
    this.$emit('draggle', this.records[oldIndex], this.records[newIndex].position);
  }
}
</script>

<template lang="pug">
.ta-index-list
  .scroll-list(
    v-if='scrollLoading'
    v-infinite-scroll='loadMore'
    infinite-scroll-disabled='scrollLoadingDisable'
    infinite-scroll-distance='10'
  )
    TaEmpty(v-if='records.length === 0 && emptyText', :desc='emptyText')
    draggable.group(v-model='records', :disabled='!draggable', @end='endDrag')
      .item(
        v-for='(record, index) in records',
        :key='["record", record.id].join("_")'
        :style='`width: ${100 / splitCount}%`'
      )
        slot(:record='record', :index='index')
    .spin
      a-spin(v-if='loading', size='small')
  .pagination-list(v-else)
    a-skeleton(:loading='loading', active)
      TaEmpty(v-if='records.length === 0 && emptyText', :desc='emptyText')
      draggable.group(v-model='records', :disabled='!draggable', @end='endDrag')
        .item(
          v-for='(record, index) in records',
          :key='["record", record.id].join("_")'
          :style='`width: ${100 / splitCount}%`'
        )
          slot(:record='record', :index='index')
    .list-view__pagination(v-if='!scrollLoading && store.totalCount > 0')
      a-pagination(v-bind='pagination', @showSizeChange='onPaginationChange', @change='onPaginationChange')
</template>

<style lang="stylus">
.ta-index-list
  width 100%
  // min-height 100px
  height 100%
  .scroll-list
    height 100%
    overflow-y auto
    .spin
      padding-top 5px
      height 50px
      display flex
      justify-content center
  .pagination-list
    .group
      overflow-y scroll
    .list-view__pagination
      margin-top 12px
      margin-right 12px
      text-align right
  .group
    display flex
    flex-wrap wrap
</style>
