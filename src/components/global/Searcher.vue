<template lang="pug">
.searcher(:class="{ focus: isFocus }")
  a-icon.prefix-icon(type="search")
  input.search-input(
    ref="searchInput"
    v-model="keyword"
    :placeholder="inputPlaceholder"
    @input="debounceInput"
    @keyup.enter="onSearch"
    @focus="isFocus = true"
    @blur="onBlur"
    clearable)
  a-icon.suffix-icon(
    v-if="keyword"
    type="close-circle"
    @click="resetSearch")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';

@Component
export default class Searcher extends Vue {
  // props
  @Prop({ type: [Array, Object], default: () => ({}) }) private value!: any; // v-model: queryObject，ransack 搜索的 q 对象
  @Prop({ type: Array, default: () => [] }) private variables!: any; // 搜索字段，例如：['name', 'desc']
  @Prop({ type: Array, default: () => [] }) private extraKey!: any; // 额外添加的 ransack 搜索条件，例如：['sell_time_gteq']
  @Prop({ type: String, default: '' }) private initValue!: string; // 默认值
  @Prop({ type: String, default: '请输入关键词' }) private placeholder!: string;
  @Prop({ type: String, default: '检索内容' }) private tips!: string;
  @Prop({ type: String, default: 'or' }) private conjunction!: string; // ransack conjunction
  @Prop({ type: String, default: 'cont_any' }) private predicate!: string; // ransack predicate
  // data
  private keyword: any = '';
  private isFocus: boolean = false;
  private debounceInput: any = this.debounce(this.onSearch, 500);

  get search_key() {
    if (this.variables.length === 0) {
      return '';
    }
    return `${this.variables.join(`_${this.conjunction}_`)}_${this.predicate}`;
  }
  get inputPlaceholder() {
    return this.isFocus ? this.placeholder : this.tips;
  }

  @Watch('initValue')
  public onChildChanged() {
    this.keyword = this.initValue;
    this.onSearch();
  }

  public mounted() {
    if (this.initValue) {
      this.keyword = this.initValue;
      this.onSearch();
    }
  }
  public onSearch() {
    const keywordArray = this.keyword
      ? this.keyword
          .trim()
          .toLocaleLowerCase()
          .replace(/\s{2,}/g, ' ')
          .split(' ')
      : [];
    const extra = this.extraKey.reduce(
      (obj: any, key: string) => ({
        ...obj,
        [key]: keywordArray,
      }),
      {},
    );
    const emitData = { ...this.value, ...extra };

    if (this.search_key) {
      emitData[this.search_key] = keywordArray;
    }
    this.$emit('input', emitData);
    this.$emit('change', emitData, this.keyword, keywordArray);
  }
  public onBlur() {
    if (!this.keyword) {
      this.isFocus = false;
    }
  }
  public resetSearch() {
    this.keyword = '';
    (this.$refs.searchInput as any).focus();
    this.onSearch();
  }
  debounce(fn: (...args: any) => void, wait: number = 100, immediate: boolean = false) {
    let timer: any = null;
    return (...args: any) => {
      if (timer) {
        clearTimeout(timer);
      } else if (immediate) {
        fn(...args);
      }
      timer = setTimeout(() => {
        fn(...args);
      }, wait);
    };
  }
}
</script>

<style lang="stylus" scoped>
.searcher
  display inline-flex
  align-items center
  overflow hidden
  width 100px
  height 32px
  border-radius 3px
  transition width 0.2s ease
  .search-input
    margin 0 6px
    padding 0
    width 100%
    height 20px
    border none
    background-color transparent
    color #383838
    font-size 14px
    line-height 20px
    &:hover, &:focus
      outline none
  .prefix-icon, .suffix-icon
    color #A6A6A6
    font-size 14px
  .suffix-icon
    cursor pointer

.focus
  padding 0px 12px
  width 280px
  background-color #F5F5F5
</style>
