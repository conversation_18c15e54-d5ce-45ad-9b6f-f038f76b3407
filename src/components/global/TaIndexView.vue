<script lang="ts">
import { ActiveStore, RequestConfig } from '@/lib/ActiveStore';
import { Component, Vue, Prop, Watch, Model } from 'vue-property-decorator';
import TaSidebarList from './TaSidebarList.vue';
import {
  IFormModalConfig,
  TaIndexViewConfigInterface,
  TaIndexViewDetailMode,
  TaIndexViewFetchDataOptsInterface,
  TaIndexViewTabInterface,
} from './TaIndex';
import ActiveModel from '@/lib/ActiveModel';
import { merge } from 'lodash';

// slot `${key}_tab` 作为该 tab 下 slot
@Component({
  components: {
    TaSidebarList,
  },
})
export default class TaIndexView<T extends Record<string, any>> extends Vue {
  @Model('onSelect', { type: Array, default: () => [] }) selectedRecords!: T[];
  @Prop({ type: Object }) store!: ActiveStore<T>;
  @Prop({ type: Object, required: true, default: () => ({}) }) private readonly config!: TaIndexViewConfigInterface;
  @Prop({ type: Object, default: () => ({}) }) private readonly requestConfig!: RequestConfig;

  @Prop({ type: Array, default: () => [] }) private readonly tabs!: TaIndexViewTabInterface[];
  // tab标签页的左边间隔
  @Prop({ type: Number, default: () => 24 }) private readonly tabsLeftMargin!: number;
  @Prop({ type: Number, default: () => 60 }) private readonly sidebarWidth!: number;
  @Prop({ type: String, default: '暂无数据' }) private readonly emptyText!: string;

  activeTabKey = '';
  visibleSelection = false;
  visibleDrawer = false;
  visibleDialog = false;
  visibleForm = false;
  visibleImportDialog = false;
  searcherQuery = {};
  temporaryQuery = {};
  selectedRecord: Partial<T> = {};
  formData: Partial<T> = {};
  lastWatchValue: IObject = {};

  get query() {
    return {
      ...this.tabQuery,
      ...this.searcherQuery,
    };
  }

  get storeQuery() {
    return this.activeStore.query;
  }

  get tabQuery() {
    return this.activeTab.query || {};
  }

  get activeTab(): Partial<TaIndexViewTabInterface> {
    return this.tabs.find(i => i.key === this.activeTabKey) || {};
  }

  get activeMode() {
    return this.activeTab.mode || this.config.mode;
  }

  get activeStore() {
    return this.activeTab.store || this.config.store;
  }

  // 使用 tab 单独配置 或 统一配置 -- 开始 --
  get activePerPage() {
    return this.activeTab.perPage || this.config.perPage;
  }

  get activeRecordName() {
    return this.activeTab.recordName || this.config.recordName;
  }

  get activeShowCount() {
    return this.activeTab.showCount || this.config.showCount || false;
  }

  get activeShowSelectionByDefault() {
    return this.activeTab.showSelectionByDefault || this.config.showSelectionByDefault || false;
  }

  get activeShowSelectionSwitch() {
    return this.activeTab.showSelectionSwitch || this.config.showSelectionSwitch || false;
  }

  get activeRowKey() {
    return this.activeTab.rowKey || this.config.rowKey || 'id';
  }

  get activeSearcherSimpleOptions() {
    return this.activeTab.searcherSimpleOptions || this.config.searcherSimpleOptions || null;
  }

  get activeSearcherComplicatedOptions() {
    return this.activeTab.searcherComplicatedOptions || this.config.searcherComplicatedOptions || null;
  }

  // yangyi
  get activeDetailMode(): TaIndexViewDetailMode {
    return this.activeTab.detailConfig?.detailMode || this.config.detailConfig?.detailMode || 'other';
  }

  // yangyi
  get activeDetailWidth(): string {
    return this.activeTab.detailConfig?.detailWidth || this.config.detailConfig?.detailWidth || '80%';
  }

  // yangyi
  get activeModalWidth(): string {
    return this.activeTab.detailConfig?.activeModalWidth || this.config.detailConfig?.activeModalWidth || '80%';
  }

  // yangyi
  get activeFormModalConfig(): IFormModalConfig {
    return this.activeTab.formModalConfig || this.config.formModalConfig || {};
  }

  get activeTemplate() {
    return this.activeTab.template || this.config.template;
  }

  get activeFormDataDecode() {
    return (
      this.activeTab.formDataDecode ||
      this.config.formDataDecode ||
      function(record: T) {
        return record;
      }
    );
  }

  get activeFormDataEncode() {
    return (
      this.activeTab.formDataEncode ||
      this.config.formDataEncode ||
      function(record: T) {
        return record;
      }
    );
  }

  get activeEditStore() {
    return this.activeTab.editStore || this.config.editStore || this.activeStore;
  }

  get activeShowActions() {
    return this.activeTab.showActions || this.config.showActions;
  }

  get activeTableConfig() {
    return this.activeTab.tableConfig || this.config.tableConfig;
  }

  get activeShowPageSizeChanger() {
    return this.activeTab.showPageSizeChanger || this.config.showPageSizeChanger;
  }

  get activeScrollLoading() {
    return this.activeTab.scrollLoading || this.config.scrollLoading;
  }

  get activeSplitCount() {
    return this.activeTab.splitCount || this.config.splitCount;
  }

  get activeShowImport() {
    return this.activeTab.showImport || this.config.showImport;
  }

  get activeShowExport() {
    return this.activeTab.showExport || this.config.showExport;
  }

  get activeImportHeaders() {
    return this.activeTab.importHeaders || this.config.importHeaders;
  }

  get activeExportHeaders() {
    return this.activeTab.exportHeaders || this.config.exportHeaders;
  }

  get activeDraggable() {
    return this.activeTab.draggable || this.config.draggable;
  }
  // 使用 tab 单独配置 或 统一配置 -- 结束 --

  get rowSelection() {
    if (this.visibleSelection) {
      return {
        selectedRowKeys: this.selectedRecords.map(i => i[this.activeRowKey]),
        onChange: (rowKeys: number[], records: T[]) => {
          // onSelect(newValue, oldValue)
          const map: { [key: string]: T } = this.selectedRecords.concat(records).reduce(
            (obj, item) => ({
              ...obj,
              [item[this.activeRowKey]]: item,
            }),
            {},
          );
          const selectedRecords = Object.values(map).filter(o => rowKeys.includes(o[this.activeRowKey]));

          this.$emit('onSelect', selectedRecords, this.selectedRecords);
        },
      };
    } else {
      this.$emit('onSelect', []);
      return {};
    }
  }

  // RansackSearcher 支持的旧版格式
  get oldSearcherOptions() {
    return (this.activeSearcherComplicatedOptions || []).map(i => ({ ...i, value: i.key }));
  }

  get watchValue() {
    return {
      // 切换 store 里的 query
      storeQuery: this.storeQuery,
      // 切换 store
      store: this.activeStore.namespaced,
      // 切换 init
      model: this.activeStore.model,
    };
  }

  get hasInit() {
    return Object.getPrototypeOf(this.activeStore.model).constructor !== ActiveModel;
  }

  @Watch('query', { deep: true })
  handleQueryChange() {
    this.activeStore.SET_QUERY(this.query);
  }

  @Watch('watchValue', { deep: true })
  handleWatchValueChange() {
    if (JSON.stringify(this.watchValue) !== JSON.stringify(this.lastWatchValue)) {
      this.fetchData();
    }
    this.lastWatchValue = this.watchValue;
  }

  get taTabtabs() {
    return this.tabs.map(tab => this.$utils.only(tab, ['key', 'label', 'num']));
  }

  created() {
    if (this.tabs[0]) {
      this.activeTabKey = this.tabs[0].key;
    }

    this.visibleSelection = this.config.showSelectionByDefault || false;
  }

  mounted() {
    this.$emit('mounted');
  }

  fetchData(page = 1, query = {}, perPage = this.activePerPage, opts: TaIndexViewFetchDataOptsInterface = {}) {
    if (!this.hasInit) {
      return;
    }

    this.temporaryQuery = query;

    this.activeStore
      .index({
        shouldAppend: this.activeScrollLoading,
        silence: this.requestConfig.silence || opts.silence,
        page,
        per_page: perPage,
        q: {
          ...query,
          ...this.storeQuery,
        },
      })
      .then(res =>
        this.$emit(
          'onIndex',
          res.data,
          merge(
            {
              q: {
                ...query,
                ...this.storeQuery,
              },
            },
            this.activeStore.model.params,
          ),
        ),
      );
  }

  public slienceRefresh() {
    this.fetchData(this.activeStore.currentPage, {}, this.activeStore.perPage, { silence: true });
  }

  onTabChange() {
    this.visibleSelection = this.config.showSelectionByDefault || false;
    this.$emit('onSelect', []);
    if (this.activeTab.route) {
      this.$router.push(this.activeTab.route);
    }
    this.$emit('tabChange', this.activeTab);
  }

  handlePaginateChange(page: number, query: IObject, perPage: number) {
    this.fetchData(page, query, perPage);
  }

  handleRowClick(record: T) {
    this.selectedRecord = record;
    this.$emit('onShow', record);
    switch (this.activeDetailMode) {
      case 'drawer':
        this.visibleDrawer = true;
        break;
      case 'dialog':
        this.visibleDialog = true;
        break;
      case 'route':
        this.$router.push(`${this.$route.path}/${record.id}`);
        break;
      case 'window':
        if (process.env.VUE_APP_PUBLIC_PATH) {
          const publichPath = process.env.VUE_APP_PUBLIC_PATH.split('/')
            .filter(i => i)
            .join('/');
          window.open(
            publichPath ? `/${publichPath}${this.$route.path}/${record.id}` : `${this.$route.path}/${record.id}`,
          );
        } else {
          window.open(`${this.$route.path}/${record.id}`);
        }
        break;
    }
  }

  onCreate() {
    this.formData = {};
    if (this.activeTemplate) {
      this.visibleForm = true;
    }
    this.$emit('onCreate');
  }

  onEdit(record: T) {
    const rawRecord = record.id ? record : this.selectedRecords[0];

    this.formData = this.activeFormDataEncode(rawRecord);
    if (this.activeTemplate) {
      this.visibleForm = true;
    }

    this.$emit('onUpdate', this.formData);
  }

  create(record: T) {
    const formattedRecord = this.activeFormDataDecode(record);

    return this.activeEditStore
      .create({
        formData: formattedRecord,
        config: this.requestConfig,
      })
      .then(res => {
        this.visibleForm = false;
        this.$message.success('创建成功');
        if (this.activeEditStore !== this.activeStore) {
          this.activeStore.ADD_RECORD(res.data);
        }
        this.$emit('afterCreate');
      })
      .catch(err => {
        this.$message.error('创建失败');
        throw err;
      });
  }

  update(record: T) {
    const formattedRecord = this.activeFormDataDecode(record);

    return this.activeEditStore
      .update({
        formData: formattedRecord,
        config: this.requestConfig,
      })
      .then(() => {
        this.visibleForm = false;
        this.$message.success('更新成功');
        // 滚动加载，获取 show 接口，更新 record，
        // 非滚动加载，直接拉取 index
        if (this.activeScrollLoading) {
          this.activeEditStore.find(record.id).then(res => {
            this.activeStore.UPDATE_RECORD(res.data);
          });
        } else {
          this.slienceRefresh();
        }
        this.$emit('afterUpdate');
      })
      .catch(err => {
        this.$message.error('更新失败');
        throw err;
      });
  }

  onDelete(record: Partial<T> = this.selectedRecords[0]) {
    return this.activeEditStore
      .delete({ id: +(record.id || ''), config: this.requestConfig })
      .then(() => {
        this.$message.success('删除成功');
        if (this.activeEditStore !== this.activeStore) {
          this.activeStore.DELETE_RECORD(record.id as number);
        }
        this.$emit('afterDelete');
      })
      .catch(err => {
        this.$message.error('删除失败');
        throw err;
      });
  }

  onBatchDestroy(records: T[] = this.selectedRecords) {
    this.activeEditStore
      .sendCollectionAction({
        action: 'batch_destroy',
        config: { params: { ids: records.map(record => record.id) } },
      })
      .then(() => {
        this.$message.success('批量删除成功');
        this.fetchData();
      })
      .catch(err => {
        this.$message.error('批量删除失败');
        throw err;
      });
  }

  onCloseDetail() {
    this.visibleDrawer = false;
    if (!this.activeScrollLoading) {
      this.slienceRefresh();
    }
    this.$emit('onCloseDetail', this.selectedRecord);
  }

  onImport() {
    this.visibleImportDialog = true;
  }

  handleDraggle(record: T, position: number) {
    this.activeEditStore
      .update({ formData: { id: record.id, position: position }, config: this.requestConfig })
      .then(() => {
        this.slienceRefresh();
      });
  }

  private onFieldsChange(props: any, fields: any) {
    this.$emit('fieldsChange', props, { ...fields });
  }

  get slotsActions() {
    return {
      onCreate: this.onCreate,
      onDelete: this.onDelete,
      onEdit: this.onEdit,
    };
  }
}
</script>

<template lang="pug">
.ta-index-view
  .ta-index-view-header
    .title
      slot(name='header')
        .ta-index-view-ta-tab(:style='{ marginLeft: `${tabsLeftMargin}px` }')
          TaTab(v-if='tabs && tabs[0]', v-model='activeTabKey', :tabs='taTabtabs', @change='onTabChange')
            template(#tab='{ tab, isActive }')
              .tab.flex-between(
                :class='{ "tab-active": isActive }',
                :style='tab.width ? `width: ${tab.width}px` : null'
              )
                .tab-label {{ tab.label }}
                .num(v-if='activeShowCount && typeof tab.num === "number"')
                  span ·
                  .badge(:style='{ background: tab.background, color: tab.color }')
                    | {{ tab.num }}
    .ta-index-view-actions
      .custom-actions
        slot(name='actions', :record='selectedRecord', :records='selectedRecords')
      .member-actions(v-if='selectedRecords.length === 1 && activeShowActions')
        PopoverConfirm(title='删除', :content='`您确认删除该${activeRecordName}吗？`', @confirm='onDelete')
          IconTooltip(icon='delete', tips='删除')
        IconTooltip(icon='edit', tips='编辑', @click='onEdit')
        slot(name='member-actions', :record='selectedRecords[0]')
      .collection-action(v-if='selectedRecords.length > 1 && activeShowActions')
        PopoverConfirm(title='批量删除', :content='`您确认删除这些${activeRecordName}吗？`', @confirm='onBatchDestroy')
          IconTooltip(icon='delete', tips='批量删除')
        IconTooltip(icon='edit', tips='批量编辑')
        slot(name='collection-actions', :records='selectedRecords', :temporaryQuery='temporaryQuery')
      .import-action(v-if='activeShowImport')
        TaImport(:store='activeEditStore', :headers='activeImportHeaders', @success='fetchData')
      .export-action(v-if='activeShowExport')
        TaExport(:store='activeStore', :headers='activeExportHeaders', :temporaryQuery='temporaryQuery')
      .selection-switch(v-if='activeShowSelectionSwitch && !activeShowSelectionByDefault')
        span.action(v-if='visibleSelection', @click='visibleSelection = false') 取消选择
        span.action(v-else, @click='visibleSelection = true') 批量选择

      .search
        RansackSearcher(
          v-if='activeSearcherComplicatedOptions && activeSearcherSimpleOptions',
          v-model='searcherQuery',
          :options='oldSearcherOptions',
          :variables='activeSearcherSimpleOptions.map((i) => i.key)',
          :tips='`检索${activeRecordName}`',
          :placeholder='`搜索${activeSearcherSimpleOptions.map((i) => i.label).join("、")}`'
        )
        Searcher(
          v-else-if='activeSearcherSimpleOptions',
          v-model='searcherQuery',
          :variables='activeSearcherSimpleOptions.map((i) => i.key)',
          :tips='`检索${activeRecordName}`',
          :placeholder='`搜索${activeSearcherSimpleOptions.map((i) => i.label).join("、")}`'
        )
      .create-action(v-if='activeShowActions')
        TextButton(icon='plus-circle', theme='filled', @click='onCreate')
          | {{ `创建${activeRecordName}` }}
      .custom-actions
        slot(name='right-actions')

  .content
    template(v-if='activeMode === "custom"')
      slot(name='content')
    TaSidebarList(
      v-else,
      :store='activeStore',
      :isTable='activeMode === "table"',
      :sidebarWidth='sidebarWidth',
      :rowKey='activeRowKey',
      :rowSelection='rowSelection',
      :showPageSizeChanger='activeShowPageSizeChanger',
      :tableConfig='activeTableConfig',
      :scrollLoading='activeScrollLoading',
      :splitCount='activeSplitCount',
      :draggable='activeDraggable',
      :emptyText='emptyText',
      @rowClick='handleRowClick',
      @onPaginateChange='handlePaginateChange',
      @draggle='handleDraggle'
    )
      template(#default='{ record, index, isActive }')
        slot(:name='`${activeTab.key}_tab`', :record='record', :index='index', :isActive='isActive')
        slot(
          :name='activeMode === "table" ? "table" : "card"',
          :record='record',
          :index='index',
          :isActive='isActive',
          :actions='slotsActions'
        )
      template(#table_top)
        slot(name='table_top')
      template(#sidebar)
        slot(name='sidebar')

  a-drawer(:visible='visibleDrawer', :closable='false', :width='activeDetailWidth', @close='onCloseDetail')
    slot(name='detail', v-if='visibleDrawer', :record='selectedRecord')
  a-modal(v-model='visibleDialog', :width='activeModalWidth')
    slot(name='detail', v-if='visibleDialog', :record='selectedRecord')

  //- TaFormDialog(
  //-   v-model='visibleForm',
  //-   :title='formData.id ? `编辑${activeRecordName}` : `新建${activeRecordName}`',
  //-   :formData='formData',
  //-   :template='activeTemplate',
  //-   :loading='activeEditStore.loading',
  //-   @update='update',
  //-   @create='create'
  //- )

  //- yangyi
  TaFormModal(
    @create='create',
    @update='update',
    v-if='visibleForm',
    v-model='visibleForm',
    :formData='formData',
    :template='activeTemplate',
    @cancel='visibleForm = false',
    :loading='activeEditStore.loading',
    @fieldsChange='onFieldsChange',
    :title='formData.id ? `编辑${activeRecordName}` : `新建${activeRecordName}`'
  )
</template>

<style lang="stylus" scoped>
.ta-index-view
  height 100%
  .ta-index-view-header
    display flex
    justify-content space-between
    align-items center
    .title
      width 50%
    .ta-index-view-ta-tab
      // padding-bottom 12px
    .tab-active
      .tab-label
        color black
      .badge
        color black
    .tab
      height 48px
      color #808080
      font-size 14px
      line-height 48px
      .num
        display flex
        span
          margin-left 3px
        .badge
          margin auto 0px
          padding 0 4px
          height 23px
          border-radius 50%
          text-align center
          font-size 8px
          line-height 23px
    .ta-index-view-actions
      display flex
      align-items center
      padding-top 2px
      color #A4A4A4
      line-height 30px
      .action
        padding-right 10px
        cursor pointer
      .search, .create-action, .selection-switch, .import-action, .export-action
        margin-left 20px
      .create-action
        margin-top 4px
      .import-action, .export-action
        display flex
        flex-shrink 0
        align-items center
  .content
    height 100%
</style>
