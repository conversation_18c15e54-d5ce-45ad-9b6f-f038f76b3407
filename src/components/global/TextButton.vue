<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';

@Component
export default class TextButton extends Vue {
  @Prop(String) private icon!: string;
  @Prop(String) private rightIcon!: string;
  @Prop(String) private theme!: string;
  @Prop(String) private to!: string;
  @Prop({ type: Boolean, default: false }) private blank!: boolean;
  @Prop({ type: Boolean, default: false }) private disabled!: boolean;
  @Prop({ type: Boolean, default: false }) private loading!: boolean;

  click(e: Event) {
    if (this.disabled || this.loading) {
      return;
    }
    if (this.to) {
      if (this.blank) this.$utils.open(this.to);
      else this.$router.push(this.to);
      return;
    }
    this.$emit('click', e);
  }
}
</script>

<template lang="pug">
button.text-button(@click="click" type="button" :disabled="disabled")
  a-icon.icon(v-if="loading" type="loading")
  a-icon.icon(
    v-if="icon"
    :type="icon"
    :theme="theme")
  span.text-button__text
    slot
  a-icon.icon(
    v-if="rightIcon"
    :type="rightIcon"
    :theme="theme")
</template>

<style lang="stylus" scoped>
.text-button
  padding 0
  outline none
  border none
  background unset
  color #3DA8F5
  display inline-flex
  align-items center
  font-weight 400
  font-size 14px
  line-height 20px
  cursor pointer
  &:last-child
    margin-left 14px
  &:hover
    color darken(#3DA8F5, 20%)
  &:disabled
    color #999999
    cursor not-allowed
  .icon
    margin 0 4px
</style>
