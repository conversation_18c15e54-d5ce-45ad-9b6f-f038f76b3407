<script lang="ts">
import { Component, Vue, Prop, Model } from 'vue-property-decorator';
import { IFormTemplateItem } from '@/interfaces/formTemplate.interface';
import TemplateForm from '@/components/form/TemplateForm.vue';

@Component({
  components: {
    TemplateForm,
  },
})
export default class TaFormDialog extends Vue {
  @Model('input', { type: Boolean, required: true }) visible!: boolean;
  @Prop({ type: String, required: true }) readonly title!: string;
  @Prop({ type: Boolean, default: false, required: true }) readonly loading!: boolean;
  @Prop({ type: Object, default: () => ({}), required: true }) readonly formData!: IObject;
  @Prop({ type: Array, default: () => [], required: true }) readonly template!: IFormTemplateItem[];

  timer: any = null;
  innerDisabled: boolean = false;

  handleCancel() {
    this.$emit('input', false);
  }
  handleOk() {
    if (this.loading) return;
    (this.$refs.form as any).submit({
      success: (formData: any) => {
        this.$emit('submit', formData);
        if (this.formData.id) {
          this.$emit('update', { id: this.formData.id, ...formData });
        } else {
          this.$emit('create', formData);
        }
      },
    });
  }
  onFieldsChange(props: any, fields: any) {
    this.$emit('fieldsChange', props, { ...fields });
  }
}
</script>

<template lang="pug">
MainModal(
  :title="title"
  :value="visible"
  :destroyOnClose="true"
  @change="handleCancel")
  .dialog-form
    slot(name="body")
    TemplateForm(
      ref="form"
      :formData="formData"
      :template="template"
      :showActions="false"
      @fieldsChange="onFieldsChange")
  template(slot="footer")
    .actions
      a-button(key="back" @click="handleCancel" size="large")
        | 取消
      a-button(key="submit" type="primary" :loading="loading" @click="handleOk" size="large" :disabled="loading")
        | 确认
</template>

<style lang="stylus" scoped>
.dialog-form
  padding 16px

.actions
  text-align right
</style>
