<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { IRowSection } from '@/components/global/TaSidebarList';

@Component
export default class TaListCheckboxItem<T extends Record<string, any>> extends Vue {
  @Prop({ type: Number, default: () => 0 }) private readonly index!: number;
  @Prop({ type: Object, required: true }) private readonly record!: T;
  @Prop({ type: String, default: () => 'id' }) rowKey!: string;
  @Prop({ type: Boolean, default: () => false }) private readonly disabled!: boolean;
  @Prop({ type: Boolean, default: () => true }) private readonly showCheckbox!: boolean;

  @Prop({ type: Object, required: true }) private readonly rowSelection!: IRowSection<number, T>;

  private get checked(): boolean {
    if (Array.isArray(this.rowSelection.selectedRowKeys)) {
      const key = this.record[this.rowKey];
      return this.rowSelection.selectedRowKeys.indexOf(key) > -1;
    }

    return false;
  }

  private handleCheckboxChange() {
    this.$emit('change', this.record[this.rowKey], this.record);
  }

  private rowClick() {
    this.$emit('rowClick', this.record);
  }
}
</script>

<template lang="pug">
.ta-list-checkbox-item
  .control(v-if='showCheckbox')
    a-checkbox(:checked='checked', :disabled='disabled', @change='handleCheckboxChange')
  .list-item(@click='rowClick')
    slot(v-bind:record='record', v-bind:index='index', :isActive='checked')
</template>

<style lang="stylus" scoped>
.ta-list-checkbox-item
  display flex
  // padding 5px 0
  .control
    flex 0 0 25px
    align-self center
  .list-item
    flex 1
</style>
