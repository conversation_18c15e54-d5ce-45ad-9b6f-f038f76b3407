import { ActiveStore } from '../../lib/ActiveStore';
import { IFormTemplateItem } from '../../interfaces/formTemplate.interface';

export interface TaIndexViewTabSpecialInterface {
  key: string;
  label: string;
  num?: number;
  background?: string;
  color?: string;
  query?: IObject;
  // tab 切换时进入的 route
  route?: string;
  // tab 宽度
  width?: number;
}

export interface TaIndexViewConfigInterface {
  store: ActiveStore;
  editStore?: ActiveStore;
  perPage?: number;
  mode?: 'table' | 'list' | 'custom';
  // recordName 用于搜索 tips 与 placeholder 显示
  recordName: string;
  // 是否显示 tab 的数字
  showCount?: boolean;
  // 是否默认显示 checkbox
  showSelectionByDefault?: boolean;
  // 是否显示 多选切换
  showSelectionSwitch?: boolean;
  // 是否显示创建、member 方法、collection 方法
  showActions?: boolean;
  // 是否显示每页显示切换
  showPageSizeChanger?: boolean;
  // 是否显示导入
  showImport?: boolean;
  // 是否显示导入
  showExport?: boolean;
  // 是否滚动加载
  scrollLoading?: boolean;
  // 多选时使用的 key，默认为 id
  rowKey?: string;
  // 简单搜索配置
  searcherSimpleOptions?: TaIndexSearcherOptionInterface[];
  // 复杂搜索配置
  searcherComplicatedOptions?: TaIndexSearcherOptionInterface[];
  //  抽屉 弹窗配置 yangyi
  detailConfig?: IDetailConfig;
  // 通用表单的 template
  template?: IFormTemplateItem[];
  // 创建编辑时的格式化函数，解码成 template 需要的格式
  formDataDecode?: Function;
  // 编辑初始化 formData 时的格式化函数，组件内 onEdit 中使用，编码成 template 需要的格式
  formDataEncode?: Function;
  // 表格设置
  tableConfig?: TaIndexTableConfigInterface;
  // 列表视图时，一行分为几块
  splitCount?: number;
  // 导入的 headers
  importHeaders?: TaIndexImportHeader[];
  // 导出的 headers
  exportHeaders?: TaIndexImportHeader[];
  // 通用表单 modal config
  formModalConfig?: IFormModalConfig;
  // 是否支持拖动
  draggable?: boolean;
}

// tab 类型由 tab special 类型 + config 类型
export type TaIndexViewTabInterface = TaIndexViewTabSpecialInterface & Partial<TaIndexViewConfigInterface>;

export interface TaIndexSearcherOptionInterface {
  key: string;
  label: string;
  type: 'number' | 'string';
}

export type TaIndexViewDetailMode = 'drawer' | 'dialog' | 'route' | 'window' | 'other';

// yangyi
export interface IFormModalConfig {
  width?: number | string;
  okText?: string;
  cancelText?: string;
  closable?: boolean;
  maskStyle?: Record<string, any>;
  bodyStyle?: Record<string, any>;
}

// yangyi
export interface IDetailConfig {
  detailMode: TaIndexViewDetailMode;
  detailWidth?: string; // 抽屉宽度
  activeModalWidth?: string; // 弹窗宽度
}

export interface TaIndexTableConfigInterface {
  perPage?: number;
  showSizeChanger?: boolean;
  pageSizeOptions?: string[];
  rowKey?: string;
  rowSelection?: IObject;
  scroll?: IObject;
  rowClassName?: Function | string;
  emptyText?: string;
  size?: string;
  paginationSize?: string;
  bordered?: boolean;
  showHeader?: boolean;
  hideOnSinglePage?: boolean;
}

export interface TaIndexTablePaginationInterface {
  current: number;
  pageSize: number;
  total: number;
}

export interface TaIndexTableSorterInterface {
  order?: string;
  field?: string;
  columnKey?: string;
}

export interface TaIndexViewFetchDataOptsInterface {
  silence?: boolean;
}

export interface TaIndexImportHeader {
  key: string;
  name: string;
}
