<template lang="pug">
.log-item
  .info-box
    .logo
      a-icon(:type="formatIcon(audit.icon)")
    span.desc {{audit.role}} {{audit.username}} {{formatType(audit.type)}}
    span.title  {{audit.name}}
    span(v-if="audit.auditable_type").source (来源：{{formatSource(audit.auditable_type)}})
  .date
    span {{audit.created_at}}
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class LogCell extends Vue {
  // props
  @Prop({ type: Object, default: () => ({}) }) audit!: object;

  public formatIcon(val: string): string {
    const obj: any = {
      create: 'plus-square',
      update: 'form',
      delete: 'delete',
    };
    return obj[val];
  }
  public formatType(val: string) {
    const obj: any = {
      create: '创建',
      update: '更新',
      delete: '删除',
    };
    return obj[val];
  }
  public formatSource(val: string) {
    const obj: any = {
      Department: '组织结构',
      Duty: '职务',
      Title: '职称',
      Teacher: '花名册',
    };
    return obj[val];
  }
}
</script>

<style lang="stylus" scoped>
.log-item
  display flex
  justify-content space-between
  align-items center
  margin-bottom 16px
  padding 5px 0px
  font-size 12px
  line-height 20px
  .info-box
    display flex
    align-items center
    .logo
      width 18px
      height 18px
      color #A6A6A6
    .desc
      margin 0px 8px 0px 16px
      color #A6A6A6
    .source
      margin-left 8px
      color #A6A6A6
  .date
    color #A6A6A6
</style>
