<script lang="ts">
import { Component, Vue, Prop, Model } from 'vue-property-decorator';
import { IFormTemplateItem } from '@/interfaces/formTemplate.interface';
import TemplateFormViewer from '../form/TemplateFormViewer.vue';

@Component({
  components: {
    TemplateFormViewer,
  },
})
export default class TaFormViewDialog extends Vue {
  @Model('input', { type: Boolean, required: true }) value!: boolean;
  @Prop({ type: String, required: true, default: '详情' }) readonly title!: string;
  @Prop({ type: Object, default: () => ({}), required: true }) readonly formData!: IObject;
  @Prop({ type: Array, default: () => [], required: true }) readonly template!: IFormTemplateItem[];
  @Prop({ type: Number, default: 800 }) readonly width!: number;

  get visible() {
    return this.value;
  }
  set visible(v: boolean) {
    this.$emit('input', v);
  }
}
</script>

<template lang="pug">
MainModal(
  :title="title"
  v-model="visible"
  :width="width")
  .form-view
    TemplateFormViewer(
      :formData="formData"
      :template="template")
</template>

<style lang="stylus" scoped>
.form-view
  padding 24px
</style>
