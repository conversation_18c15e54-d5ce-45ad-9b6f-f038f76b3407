<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import audio from '@/assets/icons/file/sound_32.svg';
import video from '@/assets/icons/file/video_32.svg';
import attachment from '@/assets/icons/file/attachment_32.svg';

@Component({
  components: {},
})
export default class FileItem extends Vue {
  fileName: string = '';
  contentType: string = '';
  path: any = attachment;
  iconPath: IObject = {
    audio,
    video,
    attachment,
  };
  typeMap: IObject = {
    image: ['gif', 'jpg', 'jpeg', 'png', 'bmp', 'webp', 'svg'],
    video: ['mp4', 'm3u8', 'rmvb', 'avi', 'swf', '3gp', 'mkv', 'flv'],
    audio: ['mp3', 'wav', 'wma', 'ogg', 'aac', 'flac'],
  };

  @Prop({ type: String }) url!: string;

  @Watch('url', { immediate: true })
  onUrlChange() {
    this.handleUrl();
  }

  handleUrl() {
    this.fileName = this.getFileName();
    const subfix = this.fileName.split('.')[1];
    if (this.typeMap.image.includes(subfix)) {
      this.path = this.url;
    } else if (this.typeMap.video.includes(subfix)) {
      this.path = this.iconPath.video;
    } else if (this.typeMap.audio.includes(subfix)) {
      this.path = this.iconPath.audio;
    } else {
      this.path = this.iconPath.attachment;
    }
  }
  getFileName() {
    const temp = this.url ? this.url.split('?')[0].split('/') : ['图片'];
    return decodeURIComponent(temp[temp.length - 1]);
  }
  showFile() {
    window.open(this.url, '_blank');
  }
}
</script>

<template lang="pug">
.file-item.hover-border(@click="showFile")
  img.file-icon(:src="path" height="32" width="32")
  .name {{ fileName }}
</template>

<style lang="stylus" scoped>
.file-item
  position relative
  display block
  overflow hidden
  padding 6px
  max-width 100%
  border-radius 4px
  text-overflow ellipsis
  white-space nowrap
  cursor pointer
  .file-icon
    display inline-block
    vertical-align middle
    object-fit cover
  .name
    display inline-block
    overflow hidden
    margin-left 8px
    height 32px
    color #383838
    vertical-align middle
    text-overflow ellipsis
    font-size 14px
    line-height 32px
</style>
