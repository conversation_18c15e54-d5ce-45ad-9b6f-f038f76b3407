<template lang="pug">
a-select.menu-select(
  :options="options"
  v-model="currentValue"
  :mode="mode"
  :placeholder="placeholder")
  slot
</template>

<script lang="ts">
import { Component, Vue, Prop, Model } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class MenuSelect extends Vue {
  @Model('change', { type: [String, Array] }) private value!: string | string[];
  @Prop({ type: Array, default: () => [] }) private options!: object[];
  @Prop({ type: String, default: '请选择' }) private placeholder!: string;
  @Prop({ type: String, default: 'default' }) private mode!: string;

  get currentValue() {
    return this.value;
  }
  set currentValue(value: string | string[]) {
    this.$emit('change', value);
  }
}
</script>

<style lang="stylus">
.menu-select
  min-width 80px
  margin 0 10px
  *
    background transparent
    background-color transparent
  .ant-select-selection
    border none
</style>
