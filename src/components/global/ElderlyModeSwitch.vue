<template lang="pug">
.elderly-mode-switch
  a-switch(
    :checked="enabled"
    @change="handleChange"
    checked-children="适老化"
    un-checked-children="标准"
  )
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';

@Component({
  name: 'ElderlyModeSwitch',
})
export default class ElderlyModeSwitch extends Vue {
  enabled = false;

  created() {
    // 从 localStorage 读取状态
    const savedMode = localStorage.getItem('elderly-mode');
    if (savedMode === 'true') {
      this.enabled = true;
      document.body.classList.add('elderly-mode');
    }
  }

  handleChange(checked: boolean) {
    this.enabled = checked;
    if (checked) {
      document.body.classList.add('elderly-mode');
    } else {
      document.body.classList.remove('elderly-mode');
    }
    // 保存状态到 localStorage
    localStorage.setItem('elderly-mode', checked.toString());
  }
}
</script>

<style lang="stylus" scoped>
.elderly-mode-switch
  display inline-block
  margin-right 16px
</style>
