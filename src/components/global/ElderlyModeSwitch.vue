<template lang="pug">
.elderly-mode-switch
  a-switch(
    :checked="enabled"
    @change="handleChange"
    checked-children="适老化"
    un-checked-children="标准"
  )
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import speechService from '@/utils/speechService';

@Component({
  name: 'ElderlyModeSwitch',
})
export default class ElderlyModeSwitch extends Vue {
  enabled = false;

  created() {
    // 从 localStorage 读取状态
    const savedMode = localStorage.getItem('elderly-mode');
    if (savedMode === 'true') {
      this.enabled = true;
      document.body.classList.add('elderly-mode');
      // 适老化模式开启时自动启用语音播报
      this.enableSpeechForElderlyMode();
    }
  }

  handleChange(checked: boolean) {
    this.enabled = checked;
    if (checked) {
      document.body.classList.add('elderly-mode');
      this.enableSpeechForElderlyMode();
      // 播报模式切换信息
      speechService.speak('适老化模式已开启，语音播报功能已启用', { priority: 'high', interrupt: true });
    } else {
      document.body.classList.remove('elderly-mode');
      // 可选择是否关闭语音播报
      speechService.speak('适老化模式已关闭', { priority: 'high', interrupt: true });
    }
    // 保存状态到 localStorage
    localStorage.setItem('elderly-mode', checked.toString());
  }

  private enableSpeechForElderlyMode() {
    // 检查语音播报是否支持
    if (speechService.isSupported()) {
      // 启用语音播报
      speechService.setEnabled(true);

      // 设置适合老年人的语音参数
      speechService.updateSettings({
        rate: 0.8, // 较慢的语速
        volume: 0.9, // 较大的音量
        pitch: 1.0 // 标准音调
      });
    }
  }
}
</script>

<style lang="stylus" scoped>
.elderly-mode-switch
  display inline-block
  margin-right 16px
</style>
