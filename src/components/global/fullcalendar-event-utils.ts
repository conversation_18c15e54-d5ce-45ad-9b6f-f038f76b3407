import { EventInput } from '@fullcalendar/vue';

let eventGuid = 0;

export const INITIAL_EVENTS: EventInput[] = [
  {
    id: createEventId(),
    title: '德国交流',
    start: new Date('2020-11-01').toISOString().replace(/T.*$/, ''), // YYYY-MM-DD of today,
    end: new Date('2021-11-01').toISOString().replace(/T.*$/, ''), // YYYY-MM-DD of today,
  },
  {
    id: createEventId(),
    title: '泰国交流',
    start: new Date('2020-11-01').toISOString().replace(/T.*$/, ''), // YYYY-MM-DD of today,
    end: new Date('2021-11-01').toISOString().replace(/T.*$/, ''), // YYYY-MM-DD of today,
  },
];

export function createEventId() {
  return String(eventGuid++);
}
