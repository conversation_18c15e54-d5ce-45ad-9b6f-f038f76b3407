<script lang="ts">
import { Component, Vue, Prop, Watch, Emit, Model } from 'vue-property-decorator';
import MenuSelect from './MenuSelect.vue';
import { debounce } from 'lodash';
import ClickOutside from 'vue-click-outside';

interface ICondition {
  key: string;
  keyName: string;
  predicate: string;
  predicateName: string;
  predicateType: string; // 确实筛选条件是不是要数组
  type: 'string' | 'number';
  value: string | number;
  valueText: string; // 选择框情况，显示标签
  _canDelete: boolean;
  _isEdit?: boolean;
}

interface IKeyOption {
  label: string;
  value: string;
  type: 'string' | 'number';
  options: Array<{ label: string; value: string }>;
}

@Component({
  components: {
    MenuSelect,
  },
  directives: {
    ClickOutside,
  },
})
export default class RansackSearcher extends Vue {
  // 绑定 query 对象
  @Model('change', { type: Object, default: () => ({}) }) private value!: IObject;
  // 配置菜单固定搜索的 key [{ key: 'amount', name: '金额', type: 'string' }]
  @Prop({ type: Array, default: () => [] }) private options!: IKeyOption[];
  // 默认搜索字段 ['seq', 'name', 'age']
  @Prop({ type: Array, default: () => [] }) private variables!: string[];
  // 默认搜索使用的谓词
  @Prop({ type: String, default: 'cont_any' }) private defaultPredicate!: string;
  @Prop({ type: String, default: '请输入关键词' }) private placeholder!: string;
  @Prop({ type: String, default: '检索内容' }) private tips!: string;

  isDoing: boolean = false;
  isFocus: boolean = false;
  showMenu: boolean = false;
  // default input
  keyword: any = '';
  debounceInput: any = debounce(this.onSearch, 800);
  // condition input
  historyConditions: ICondition[] = [];
  conditions: ICondition[] = [];
  conditionFormData: ICondition | IObject = {
    key: '',
    predicate: '',
    value: '',
  };
  conditionIndex: number = -1;
  selectedKeyOption: IKeyOption | IObject = {};

  get predicateOptions() {
    return this.getPredicateOptions(this.selectedKeyOption.type);
  }
  get inputPlaceholder() {
    return this.isFocus ? this.placeholder : this.tips;
  }

  mounted() {
    this.selectedKeyOption = this.options[0] || {};
    this.conditionFormData = {
      key: this.selectedKeyOption.value,
      predicate: this.getPredicateOptions(this.selectedKeyOption.type)[0].value,
      value: '',
    };
  }
  onSearch() {
    const query: IObject = this.conditions.reduce((q: IObject, c: ICondition) => {
      const key = `${c.key}_${c.predicate}`;
      let value: any = c.value;
      if (c.predicateType === 'array') {
        value = this.parseStringToArray(String(c.value));
      }
      return { ...q, [key]: value };
    }, {});
    if (this.variables.length && this.keyword) {
      const key = this.variables.join('_or_') + `_${this.defaultPredicate}`;
      query[key] = this.parseStringToArray(this.keyword);
    }
    this.$emit('change', query);
  }
  resetSearch() {
    this.conditions = [];
    this.keyword = '';
    this.onSearch();
  }
  setInputFocus() {
    const input = this.$refs.defaultInput as any;
    if (input) {
      input.scrollIntoView();
      input.focus();
    }
  }
  onBackspace() {
    const lastCondition = this.conditions[this.conditions.length - 1];
    if (lastCondition) {
      if (lastCondition._canDelete) {
        this.conditions.pop();
        this.onSearch();
      } else {
        this.$set(lastCondition, '_canDelete', !this.keyword);
      }
    }
  }
  remvoeCondition(cd: ICondition, index: number) {
    this.$delete(this.conditions, index);
    this.onSearch();
    this.setInputFocus();
    this.showMenu = true;
    if (cd.key === this.conditionFormData.key) {
      this.conditionFormData = {};
    }
  }
  onSelectKeyOption(key: string) {
    this.onOpen();
    this.selectedKeyOption = this.options.find((o: IKeyOption) => o.value === key)!;
    this.conditionFormData.predicate = this.getPredicateOptions(this.selectedKeyOption.type)[0].value;
  }
  onSelectPredicate(key: string) {
    const predicateOption = this.predicateOptions.find(o => o.value === key)!;
    this.conditionFormData.predicateType = predicateOption.type;
    this.onOpen();
  }
  addCondition() {
    const { key, predicate, value, valueText } = this.conditionFormData;
    const predicateOption = this.predicateOptions.find((o: IObject) => o.value === predicate)!;
    const conditionFormData: ICondition = {
      key,
      value,
      valueText,
      predicate,
      keyName: this.selectedKeyOption.label,
      predicateName: predicateOption.label,
      predicateType: predicateOption.type,
      type: this.selectedKeyOption.type || 'string',
      _canDelete: false,
      _isEdit: false,
    };
    this.conditions.push(conditionFormData);
    this.historyConditions.push({ ...conditionFormData });
    this.conditionFormData = {};
    this.setInputFocus();
    this.onSearch();
  }
  editCondition(cd: ICondition, index: number) {
    this.conditionFormData = { ...cd, _isEdit: true };
    this.conditionIndex = index;
  }
  updateCondition() {
    // 更新 tag
    const cd = this.conditions[this.conditionIndex]!;
    Object.assign(cd, this.conditionFormData);
    // 更新历史
    const hcd = this.historyConditions.find(o => o.key === cd.key);
    if (hcd) {
      Object.assign(hcd, this.conditionFormData);
    }
    this.conditionFormData = {};
    this.setInputFocus();
    this.onSearch();
  }
  onConditionKeyOptionChange(value: string) {
    const option = this.selectedKeyOption.options.find((o: any) => o.value === value);
    if (option) {
      this.conditionFormData.valueText = option.label;
    }
  }
  useHistroyCondition(cd: ICondition) {
    if (!this.conditions.find(c => c.key === cd.key)) {
      this.conditions.push({ ...cd });
      this.onSearch();
    }
  }
  parseStringToArray(string: string) {
    if (!string) return [];
    const pattern = /[,，;；\s、!@#$%^&*_\-+=《》<>?\\/[\]()（）'"‘’“”]/g;
    const formatString = string
      .replace(pattern, ' ')
      .trim()
      .replace(/\s{2,}/g, ' ');
    return formatString.split(' ');
  }
  onOpen() {
    if (this.isFocus) {
      this.showMenu = true;
      return;
    }
    this.isFocus = true;
    setTimeout(() => {
      this.showMenu = true;
    }, 100);
  }
  onClose() {
    if (this.isDoing) return;
    if (this.keyword || this.conditions.length) {
      this.showMenu = false;
      return;
    }
    this.showMenu = false;
    setTimeout(() => {
      this.isFocus = false;
    }, 50);
  }
  setDoing() {
    this.isDoing = true;
    setTimeout(() => {
      this.isDoing = false;
    }, 200);
  }
  getPredicateOptions(type: 'string' | 'number') {
    if (type === 'number') {
      return [
        { label: '=', value: 'eq', type: 'number' },
        { label: '>', value: 'gt', type: 'number' },
        { label: '>=', value: 'gteq', type: 'number' },
        { label: '<', value: 'lt', type: 'number' },
        { label: '<=', value: 'lteq', type: 'number' },
      ];
    }
    return [
      { label: '包含', value: 'cont_any', type: 'array' },
      { label: '等于', value: 'eq', type: 'string' },
      { label: '不等于', value: 'not_eq', type: 'string' },
    ];
  }
}
</script>

<template lang="pug">
.ransack-searcher(
  v-click-outside="onClose"
  :class="{ 'ransack-searcher-focus': isFocus, 'auto-width-searcher': showMenu }")
  .input-box
    a-icon.prefix-icon(type="search")
    .key-items-wrapper
      .key-items
        a-tag.key-tag(
          v-for="(item, index) in conditions"
          :key="index"
          :closable="true"
          :visible="true"
          color="#A6A6A6"
          @click="editCondition(item, index)"
          @close.stop="remvoeCondition(item, index)")
          span {{ item.keyName }} {{ item.predicateName }} {{ item.valueText || item.value }}
        input.key-input(
          ref="defaultInput"
          v-model="keyword"
          :placeholder="inputPlaceholder"
          @keyup.enter="onSearch"
          @keyup.delete="onBackspace"
          @input="debounceInput"
          @focus="onOpen"
          @onBlur="onClose"
          @click="showMenu = true"
          :size="Math.round(keyword.length * 1.2)"
          type="text"
          clearable)
    a-icon.suffix-icon(
      v-if="conditions.length || keyword"
      type="close-circle"
      @click="resetSearch")

  .condition-box(
    v-if="options.length"
    @click="onOpen"
    :class="{ 'condition-box-open': showMenu }")
    .history
      .history-title(v-if="historyConditions.length")
        | 历史搜索
      .history-condition(@click="useHistroyCondition(cd)" v-for="(cd, i) in historyConditions" :key="i")
        | {{ cd.keyName }} {{ cd.predicateName }} {{ cd.valueText || cd.value }}
    .condition-footer
      a-select.select.key-select(
        v-model="conditionFormData.key"
        :options="options"
        placeholder="搜索项"
        size="small"
        @select="setDoing"
        @change="onSelectKeyOption")
      a-select.select.predicate-select(
        v-model="conditionFormData.predicate"
        :options="predicateOptions"
        placeholder="条件"
        size="small"
        @select="setDoing"
        @change="onSelectPredicate")
      a-select.condition-input(
        v-if="selectedKeyOption.options"
        v-model="conditionFormData.value"
        :options="selectedKeyOption.options"
        placeholder="值"
        size="small"
        @select="setDoing"
        @change="onConditionKeyOptionChange")
      a-input.condition-input(
        v-else
        v-model="conditionFormData.value"
        :type="conditionFormData.type === 'number' ? 'number' : 'string'"
        placeholder="值"
        size="small")
      a-button(
        v-if="conditionFormData._isEdit"
        type="primary"
        size="small"
        @click="updateCondition"
        :disabled="!(conditionFormData.key && conditionFormData.predicate && conditionFormData.value)")
        | 更新
      a-button(
        v-else
        type="primary"
        size="small"
        @click="addCondition"
        :disabled="!(conditionFormData.key && conditionFormData.predicate && conditionFormData.value)")
        | 添加
</template>

<style lang="stylus">
$minWidth = 200px
$maxWidth = 340px

.ransack-searcher
  display inline-block
  // width $minWidth
  transition all 0.2s ease
  position relative
  background-color #ffffff
  .input-box
    display flex
    align-items center
    border-radius 3px
    height 36px
    cursor pointer
    overflow hidden
    position relative
    flex-shrink 0
    background-color inherit
    z-index 100
    padding 0px 10px
    width fit-content
    min-width 100%
    .prefix-icon, .suffix-icon
      color #A6A6A6
      font-size 14px
    .suffix-icon
      cursor pointer
    .key-items-wrapper
      margin 0 8px
      height 24px
      flex-grow 1
      overflow hidden
      flex-shrink 0
      .key-items
        display flex
        align-items center
        // overflow-x scroll
        padding-bottom 30px
        flex-wrap nowrap
        .key-tag
          display inline-block
          background rgba(166,166,166,1)
          border-radius 4px
          padding 0px 6px
          font-size 12px
          font-weight 400
          color rgba(255,255,255,1)
          line-height 20px
          white-space nowrap
          margin-right 2px
        .key-input
          padding 0 2px
          height 24px
          line-height 24px
          border none
          background-color transparent
          color #383838
          font-size 14px
          flex-grow 1
          min-width 100px
          max-width 240px
          &:hover, &:focus
            outline none
  .condition-box
    min-width $maxWidth
    overflow hidden
    position absolute
    top 44px
    left 0px
    background rgba(255,255,255,1)
    box-shadow 0px 7px 21px 0px rgba(0,0,0,0.1)
    border-radius 2px
    z-index 10
    transition all 0.2s ease
    opacity 0
    height 0px
    transform translateY(-100%)
    .history
      padding 8px 0
      .history-title
        padding 8px 16px
        font-size 14px
        font-weight 500
        color rgba(166,166,166,1)
        line-height 20px
      .history-condition
        font-size 14px
        font-weight 400
        color rgba(56,56,56,1)
        line-height 16px
        padding 12px 24px 12px 16px
        white-space nowrap
        text-overflow ellipsis
        cursor pointer
        &:hover
          background #f5f5f5
    .condition-footer
      display flex
      align-items center
      justify-content space-between
      padding 8px 16px 10px
      .select
        color #3DA8F5
        flex-grow 1
        margin-right 4px
        transform translateX(-7px)
        line-height 24px
        .ant-select-selection__placeholder
          height 24px
          line-height 24px
        .ant-select-selection
          border none
      .key-select
        min-width 100px
      .predicate-select
        width 70px
        flex-shrink 0
      .condition-input
        margin-right 4px
        border none
        min-width 80px
  .condition-box-open
    opacity 1
    height fit-content
    transform translateY(0px)

.ransack-searcher-focus
  width $maxWidth
  background-color #F5F5F5

.auto-width-searcher
  width fit-content
  min-width $maxWidth
</style>
