<template lang="pug">
.component-panel
  .header(
    v-if="title || $slots.header || $slots.actions"
    :class="{ 'full-head-line': fullHeadLine, bordered: bordered }")
    slot(name="header")
    .component-panel__title(v-if="title" :class="{ 'title-center': center }")
      | {{ title }}
    .component-panel__actions
      slot(name="actions")
  .content(:class="{ 'content-panel': notCard }")
    slot
  .footer(v-if="$slots.footer")
    slot(name="footer")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class Panel extends Vue {
  @Prop({ type: String, default: '' }) title!: string;
  @Prop({ type: Boolean, default: true }) bordered!: boolean;
  @Prop({ type: Boolean, default: false }) center!: boolean;
  @Prop({ type: <PERSON><PERSON><PERSON>, default: false }) fullHeadLine!: boolean;

  get notCard() {
    return this.title || this.$slots.header || this.$slots.footer;
  }
}
</script>

<style lang="stylus" scoped>
.component-panel
  display flex
  flex-direction column
  width 100%
  border-radius 3px
  background #FFFFFF
  box-shadow 0 2px 3px 0 rgba(0, 0, 0, 0.1)
  &:last-child
    margin-bottom 0 !important
  .header
    position relative
    display flex
    flex-shrink 0
    justify-content space-between
    align-items center
    padding 0 20px
    min-height 48px
    z-index 1
    &:after
      position absolute
      right 20px
      bottom 0px
      left 20px
      height 0px
      background #E5E5E5
      content ''
    .component-panel__title
      flex-grow 1
      color #383838
      font-weight 500
      font-size 16px
    .title-center
      text-align center
    .component-panel__actions
      display inline-flex
      align-items center
  .bordered:after
    height 1px
  .full-head-line
    &:after
      right 0
      left 0
  .content
    width 100%
    height 100%
  .content-panel
    position relative
    overflow-x hidden
    overflow-y auto
    padding 0 20px
    height 100%
  .footer
    position relative
    display flex
    flex-shrink 0
    align-items center
    padding 0 20px
    height 56px
    &:after
      position absolute
      top 0px
      right 0px
      left 0px
      height 1px
      background #E5E5E5
      content ''
</style>
