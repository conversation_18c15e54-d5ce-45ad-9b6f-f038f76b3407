<script lang="ts">
/**
 * 配置文件渲染表单
 * 事件：
 * submit 提交, 参数是 formData
 * cancel 取消
 * 示例 Template:
 * [{
 *   key: 'select_1234',
 *   name: '标题',
 *   accessibility: 'read_and_write',
 *   layout: {
 *     component: 'select',
 *     options: [{ label: '选项一' }, { label: '选项二' }],
 *     type: 'text',
 *     placeholder: '请输入标题',
 *     required: true,
 *     disabled: false,
 *     span: 24,
 *   },
 *   model: {
 *     attr_type: 'string',
 *   },
 * }]
 *
 * component（控件）：
 *   input, textarea, date
 *   time, datetime, radio, checkbox, select, file,
 *   hr,
 *   charge, remission
 *
 * accessibility（访问性）: read_and_write, readonly, hidden
 * attr_type（值类型）: string, number, json, array
 * required（是否必填）: Boolean
 * disabled（是否禁用）: Boolean
 *
 */
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { IFormTemplateItem } from '../../interfaces/formTemplate.interface';
import utils from '@/utils';
import moment, { Moment } from 'moment';
import { set, get } from 'lodash';

@Component
export default class TaTemplateForm extends Vue {
  form: any = null;
  validateFields: any = null;
  modelTypeMap: IObject = {
    string: 'string',
    number: 'number',
    array: 'array',
    json: 'object',
  };

  @Prop({ type: Object, default: () => ({}) }) readonly formData!: IObject;
  @Prop({ type: Array, default: () => [], required: true }) readonly template!: IFormTemplateItem[];
  @Prop({ type: Boolean, default: false }) readonly loading!: boolean;
  @Prop({ type: Boolean, default: false }) readonly disabled!: boolean;
  @Prop({ type: Boolean, default: false }) readonly showActions!: boolean;
  @Prop({ type: Number, default: 24 }) readonly labelCol!: number;
  @Prop({ type: Number, default: 24 }) readonly wrapperCol!: number;
  @Prop({ type: Boolean, default: true }) readonly showLabel!: boolean;
  @Prop({ type: Boolean, default: true }) readonly hideRequiredMark!: boolean;
  @Prop({ type: String, default: 'large' }) readonly size!: boolean;

  @Watch('formData', { deep: true })
  onFormDataChange() {
    this.updateFieldsValue();
  }

  created() {
    this.form = this.$form.createForm(this, {
      onFieldsChange: this.onFieldsChange,
    });
    this.validateFields = this.form.validateFields;
  }

  mounted() {
    this.updateFieldsValue();
  }

  // 更新表单的值
  updateFieldsValue() {
    if (this.form) {
      const fields = this.template.reduce((obj: IObject, item: IFormTemplateItem) => {
        if (item.key) {
          set(obj, item.key, get(this.formData, item.key));
        }
        return obj;
      }, {});
      setTimeout(() => {
        this.form.setFieldsValue(fields);
      }, 100);
    }
  }
  // 表单的 submit 事件，由原生表单触发
  onFormSubmit(e: any) {
    e.preventDefault();
    this.submit();
  }
  getFieldsValue(...args: any[]) {
    return this.form.getFieldsValue(...args);
  }
  submit(options: { success?: any; fail?: any } = {}) {
    this.form.validateFields((err: any, formData: IObject) => {
      if (!err) {
        // 处理时间
        const payload: IObject = Object.entries(formData).reduce(
          (obj: IObject, [key, value]) => ({
            ...obj,
            [key]: value instanceof moment ? (value as Moment).format() : value,
          }),
          {},
        );
        if (this.formData.id) {
          payload.id = this.formData.id;
        }
        this.$emit('submit', payload);
        if (options.success) {
          options.success(payload);
        }
      } else if (options.fail) {
        options.fail(err);
      }
    });
  }
  onFieldsChange(props: any, fields: any) {
    this.$emit('fieldsChange', props, { ...fields });
  }
  getRules(item: IFormTemplateItem) {
    let type = this.modelTypeMap[(item.model as any).attr_type];
    if (['date', 'datetime', 'time', 'wechat_articles'].includes(item.layout.component!)) {
      type = 'object';
    } else if (['checkbox', 'file', 'contacts'].includes(item.layout.component!)) {
      type = 'array';
    }
    return [
      {
        required: item.layout.required,
        message: `请设置${item.name}`,
        type,
      },
    ];
  }
  isDisabled(item: IFormTemplateItem) {
    return item.accessibility === 'readonly' || item.layout.disabled || this.disabled;
  }
  isVisibility(item: IFormTemplateItem) {
    return item.accessibility !== 'hidden';
  }
  getOptions(item: IFormTemplateItem) {
    return (item.layout.options || []).map((o: any) => ({
      label: o.label,
      value: o.value || o.label,
    }));
  }
  isGroupSelect(item: IFormTemplateItem) {
    return item.layout.options && item.layout.options[0] && item.layout.options[0].group;
  }
  getSelectOptions(item: IFormTemplateItem) {
    const options = (item.layout.options || []).map((o: any) => ({
      label: o.label,
      value: o.value || o.label,
      group: o.group,
    }));
    return options[0] && options[0].group ? utils.groupBy(options, 'group') : options;
  }
  filterOption(input: string, option: any) {
    return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  }
  cancel() {
    this.form.resetFields();
    this.$emit('cancel');
  }
  dateTimeNormalize(value: string | Moment) {
    if (!value) return null;
    if (value instanceof moment) {
      return value;
    }
    const mo = moment(value);
    return mo.isValid() ? mo : null;
  }
}
</script>

<template lang="pug">
a-form.form-content(:form='form', @submit='onFormSubmit', :hideRequiredMark='hideRequiredMark')
  .template-form(v-loading='loading')
    slot(name='top')
    a-row(:gutter='16')
      a-col(v-for='(item, index) in formattedTemplates', :key='index', :span='item.layout.span || 24')
        //- hr
        .boundary(v-if='item.layout.component === "hr"')
          | {{ item.name }}
        //- form-item
        a-form-item.form-item(
          v-if='isVisibility(item)',
          :labelCol='{ span: labelCol }',
          :wrapperCol='{ span: wrapperCol }',
          :label='showLabel && item.name'
        )
          //- input
          a-input(
            v-if='item.layout.component === "input" && item.model.attr_type !== "number"',
            v-decorator='[item.key, { rules: getRules(item) }]',
            :name='item.key',
            :type='item.layout.type',
            :disabled='isDisabled(item)',
            :placeholder='item.layout.placeholder || `请输入${item.name}`',
            :size='size',
            autocomplete='off'
          )
          //- textarea
          a-textarea(
            v-else-if='item.layout.component === "textarea"',
            v-decorator='[item.key, { rules: getRules(item) }]',
            :name='item.key',
            :autoSize='{ minRows: 4 }',
            :disabled='isDisabled(item)',
            :placeholder='item.layout.placeholder || `请输入${item.name}`',
            :size='size'
          )
          //- inputNumber
          a-input-number.picker(
            v-else-if='item.layout.component === "input" && item.model.attr_type === "number"',
            v-decorator='[item.key, { rules: getRules(item) }]',
            :name='item.key',
            :disabled='isDisabled(item)',
            :placeholder='item.layout.placeholder || `请输入${item.name}`',
            :min='typeof item.layout.min === "number" ? item.layout.min : 0',
            :max='typeof item.layout.max === "number" ? item.layout.max : Infinity',
            :size='size'
          )
          //- group select
          a-select(
            v-else-if='item.layout.component === "select" && isGroupSelect(item)',
            v-decorator='[item.key, { rules: getRules(item) }]',
            :name='item.key',
            :disabled='isDisabled(item)',
            :placeholder='item.layout.placeholder || `请选择${item.name}`',
            :size='size',
            :filterOption='filterOption',
            showSearch
          )
            a-select-opt-group(v-for='(options, group) in getSelectOptions(item)', :key='group')
              a-select-option(v-for='option in options', :key='option.label', :value='option.value || option.label')
                | {{ option.label }}
          //-  select
          a-select(
            v-else-if='item.layout.component === "select"',
            v-decorator='[item.key, { rules: getRules(item) }]',
            :name='item.key',
            :disabled='isDisabled(item)',
            :options='getSelectOptions(item)',
            :placeholder='item.layout.placeholder || `请选择${item.name}`',
            :size='size',
            :filterOption='filterOption',
            showSearch
          )
          //- radio
          a-radio-group(
            v-else-if='item.layout.component === "radio"',
            v-decorator='[item.key, { rules: getRules(item) }]',
            :name='item.key',
            :disabled='isDisabled(item)',
            :options='getOptions(item)',
            :size='size'
          )
          //- checkbox
          a-checkbox-group(
            v-else-if='item.layout.component === "checkbox"',
            v-decorator='[item.key, { rules: getRules(item) }]',
            :name='item.key',
            :disabled='isDisabled(item)',
            :options='getOptions(item)',
            :size='size'
          )
          //- switch
          a-switch(
            v-else-if='item.layout.component === "switch"',
            v-decorator='[item.key, { rules: getRules(item) }]',
            :disabled='isDisabled(item)',
            :size='size'
          )
          //- date picker
          a-date-picker.picker(
            v-else-if='item.layout.component === "date"',
            v-decorator='[item.key, { rules: getRules(item), normalize: dateTimeNormalize }]',
            valueFormat='YYYY-MM-DD',
            :name='item.key',
            :disabled='isDisabled(item)',
            :placeholder='item.layout.placeholder || `请选择${item.name}`',
            :size='size'
          )
          //- time picker
          a-time-picker.picker(
            v-if='item.layout.component === "time"',
            v-decorator='[item.key, { rules: getRules(item), normalize: dateTimeNormalize }]',
            :name='item.key',
            :disabled='isDisabled(item)',
            :placeholder='item.layout.placeholder || `请选择${item.name}`',
            :size='size'
          )
          //- date-time picker
          a-date-picker.picker(
            v-else-if='item.layout.component === "datetime"',
            v-decorator='[item.key, { rules: getRules(item), normalize: dateTimeNormalize }]',
            :name='item.key',
            :disabled='isDisabled(item)',
            :placeholder='item.layout.placeholder || `请选择${item.name}`',
            :format='item.format || "YYYY-MM-DD HH:mm:ss"',
            :showTime='true',
            :size='size'
          )
          //- uploader
          //- 注意：自定义组件，使用 decorator，绑定的 value prop 不要设置 default, 否则会报错：
          //- `getFieldDecorator` will override `value`,
          //- so please don't set `value and v-model` directly and use `setFieldsValue` to set it
          FileUploader(
            v-else-if='item.layout.component === "file"',
            v-decorator='[item.key, { rules: getRules(item), initialValue: formData[item.key] || [] }]',
            :name='item.key',
            :disabled='isDisabled(item)',
            :multiple='item.layout.multiple',
            :accept='item.layout.accept',
            :size='size'
          )
    //- last child
    slot
  .actions(v-if='showActions')
    slot(name='actions')
      a-button(@click='cancel')
        | 取消
      a-button(ref='submitBtn', type='primary', htmlType='submit', :loading='loading', :disabled='disabled')
        | {{ formData.id ? " 更新 " : " 创建 " }}
</template>

<style lang="stylus" scoped>
.form-content
  position relative
  height 100%
  line-height 1.5
  .template-form
    overflow-x hidden
    overflow-y auto
    box-sizing border-box
    height 100%
    .picker
      min-width 200px
      width 100%
    .boundary
      margin-top 16px
      margin-bottom 8px
      border-bottom 1px solid #eee
      line-height 1.4
    .append
      margin-left 10px
    .option-item
      margin-bottom 8px
  .actions
    z-index 10
    padding 8px 16px
    width 100%
    background #fff
    text-align right
    button
      margin-left 10px
</style>
