<template lang="pug">
.admin-table-component
  .admin-table-component__header(v-if='isSelected')
    slot(name='header')
  a-table.admin-table-component__table(
    ref='table',
    :rowKey='rowKey',
    :dataSource='records',
    :columns='columns',
    :rowSelection='rowSelection',
    :pagination='pagination',
    :bordered='bordered',
    :loading='tableLoading',
    :showHeader='showHeader',
    :customRow='customRow',
    :rowClassName='getRowClassName',
    :scroll='scroll',
    :locale='locale',
    :size='size',
    @change='onChange'
  )
    slot
    slot(name='last')
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class AdminTable extends Vue {
  // props
  @Prop({ type: Array }) private data!: IObject[];
  @Prop({ type: Array }) private columns?: IObject[];
  @Prop({ type: Object, default: () => ({}) }) private store!: any;
  @Prop({ type: Number, default: 1 }) private totalPages!: number;
  @Prop({ type: Number, default: 0 }) private totalCount!: number;
  @Prop({ type: Number, default: 1 }) private currentPage!: number;
  @Prop({ type: Number, default: 10 }) private perPage!: number;
  @Prop({ type: Boolean, default: true }) private showSizeChanger!: boolean;
  @Prop({ type: Array, default: () => ['10', '20', '30', '40', '50', '100', '200', '500', '1000'] })
  private pageSizeOptions!: string[];
  @Prop({ type: String, default: 'id' }) private rowKey!: string;
  @Prop({ type: Object, default: null }) private rowSelection!: IObject;
  @Prop({ type: Object, default: () => ({ x: '100%' }) }) private scroll!: IObject;
  @Prop({ type: [Function, String] }) private rowClassName!: any;
  @Prop({ type: String, default: '暂无数据' }) private emptyText!: string;
  @Prop({ type: String, default: 'middle' }) private size!: string;
  @Prop({ type: String, default: 'middle' }) private paginationSize!: string;
  @Prop({ type: Boolean, default: false }) private bordered!: boolean;
  @Prop({ type: Boolean, default: true }) private showHeader!: boolean;
  @Prop({ type: Boolean, default: null }) private loading!: boolean;
  @Prop({ type: Boolean, default: false }) private isSelected?: boolean;
  @Prop({ type: Boolean, default: false }) private hideOnSinglePage?: boolean;
  @Prop({ type: Boolean, default: false }) private clickable?: boolean;
  // data
  private localPageSize: number | null = null;
  private sorter: IObject = {};
  private queryObject: IObject = { s: [] };

  get records() {
    return this.data || this.store.records || [];
  }
  // pagination
  get current() {
    return this.store.currentPage || this.currentPage;
  }
  get pageSize() {
    return this.localPageSize || this.store.perPage || this.perPage;
  }
  get total() {
    if (this.store.totalCount === -1) {
      // store 未请求
      return this.totalCount || 0;
    } else {
      return this.store.totalCount || this.totalCount;
    }
  }
  get pagination() {
    const totalPages: number = this.store.totalPages || this.totalPages;
    return {
      total: this.total,
      pageSize: this.pageSize,
      current: this.current,
      hideOnSinglePage: this.hideOnSinglePage || this.total < 10,
      showSizeChanger: this.showSizeChanger,
      showQuickJumper: totalPages > 1,
      size: this.paginationSize,
      pageSizeOptions: this.pageSizeOptions,
    };
  }
  get tableLoading() {
    return typeof this.loading === 'boolean' ? this.loading : this.store.loading;
  }
  get locale() {
    return {
      emptyText: this.emptyText,
    };
  }

  public onChange(pagination: IObject, filters: IObject, sorter: IObject): void {
    this.$emit('tableChange', pagination, filters, sorter);

    if (pagination.current !== this.current || pagination.pageSize !== this.pageSize) {
      this.localPageSize = pagination.pageSize;
      this.$emit('change', pagination.current, { ...this.queryObject }, this.pageSize);
      this.$emit('paginate', pagination.current);
    } else if (sorter.order && (this.sorter.order !== sorter.order || this.sorter.field !== sorter.field)) {
      this.sorterChange(sorter);
    } else {
      this.filterChange(filters);
    }
  }

  public async sorterChange(sorter: IObject) {
    this.sorter = sorter;
    let ransackQuerySort = ['id desc'];
    if (sorter.order) {
      ransackQuerySort = [`${sorter.field || sorter.columnKey} ${sorter.order === 'descend' ? 'desc' : 'asc'}`];
      this.queryObject.s = ransackQuerySort;
    } else {
      const index = (this.queryObject.s || []).find((o: any) => o.includes(sorter.field));
      this.queryObject.s.splice(index, 1);
    }

    this.$emit('change', 1, { ...this.queryObject }, this.pageSize);
    this.$emit('sort', { ...this.queryObject });
  }

  public filterChange(filters: IObject): void {
    this.queryObject = {
      ...this.queryObject,
      ...Object.keys(filters || {}).reduce((obj: IObject, key: string) => {
        obj[`${key}_in`] = filters[key];
        return obj;
      }, {}),
    };
    this.$emit('change', 1, { ...this.queryObject }, this.pageSize);
    this.$emit('filter', this.queryObject);
  }

  public customRow(record: object, index: number): object {
    return {
      on: {
        click: () => {
          this.$emit('rowClick', record, index);
        },
        mouseenter: () => {
          this.$emit('rowHover', record, index);
        },
        mouseleave: () => {
          this.$emit('rowHover', {}, null);
        },
      },
    };
  }
  getRowClassName(...args: IObject[]) {
    if (this.rowClassName instanceof Function) {
      return this.rowClassName(...args);
    }
    return this.clickable ? 'click-row' : this.rowClassName;
  }
}
</script>

<style lang="stylus" scoped>
.admin-table-component
  position relative
  width 100%
  .admin-table-component__header
    position absolute
    top 0px
    left 26px
    z-index 9
    display flex
    align-items center
    width calc(100% - 26px)
    height 53px
    background #fff
</style>
