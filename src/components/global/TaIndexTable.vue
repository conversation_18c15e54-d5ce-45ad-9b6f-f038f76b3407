<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import {
  TaIndexTableConfigInterface,
  TaIndexTablePaginationInterface,
  TaIndexTableSorterInterface,
} from '@/components/global/TaIndex';

@Component({
  components: {},
})
export default class TaIndexTable extends Vue {
  @Prop({ type: Array }) private data!: IObject[];
  @Prop({ type: Array }) private paginatedData!: IObject[];
  @Prop({ type: Array }) private columns?: IObject[];
  @Prop({ type: Object, default: () => ({}) }) private store!: any;
  @Prop({ type: Boolean, default: null }) private loading!: boolean;
  @Prop({ type: Object, default: () => ({}) }) private config!: TaIndexTableConfigInterface;

  @Prop({ type: Number, default: 0 }) private totalCount!: number;
  @Prop({ type: Number, default: 1 }) private currentPage!: number;
  @Prop({ type: Number, default: 1 }) private totalPages!: number;

  // data
  private localPageSize: number | null = null;
  private sorter: IObject = {};
  private queryObject: IObject = { s: [] };
  private localData: IObject[] = this.data || this.paginatedData;
  private localTotalCount = this.totalCount;
  private localCurrentPage = this.currentPage;

  get records() {
    return (this.data || this.paginatedData ? this.localData : this.store.records) || [];
  }

  // -- config start --

  get perPage() {
    return this.config.perPage || this.localPageSize || 15;
  }

  get showSizeChanger() {
    return this.config.showSizeChanger;
  }

  get pageSizeOptions() {
    return this.config.pageSizeOptions || ['10', '20', '30', '40', '50', '100', '200', '500', '1000'];
  }

  get rowKey() {
    return this.config.rowKey || 'id';
  }

  get scroll() {
    return this.config.scroll || { x: '100%' };
  }

  get rowClassName() {
    return this.config.rowClassName;
  }

  get emptyText() {
    return this.config.emptyText || '暂无数据';
  }

  get size() {
    return this.config.size || 'middle';
  }

  get paginationSize() {
    return this.config.paginationSize || 'middle';
  }

  get bordered() {
    return this.config.bordered || false;
  }

  get showHeader() {
    return this.config.showHeader || true;
  }

  get hideOnSinglePage() {
    return this.config.hideOnSinglePage || false;
  }

  get rowSelection() {
    return this.config.rowSelection || null;
  }

  // -- config end --

  get haveStore() {
    return !!this.store.currentPage;
  }

  get current() {
    return this.store.currentPage || this.localCurrentPage;
  }

  get total() {
    return this.store.totalCount || this.localTotalCount || 0;
  }

  get pageSize() {
    return this.store.perPage || this.perPage || 15;
  }

  get totoPage() {
    return this.store.totalPages || this.totalPages || 0;
  }

  get pagination() {
    return {
      total: this.total,
      pageSize: this.pageSize,
      current: this.current,
      hideOnSinglePage: this.hideOnSinglePage,
      showSizeChanger: this.showSizeChanger ? true : false,
      showQuickJumper: this.totoPage > 1,
      size: this.paginationSize,
      pageSizeOptions: this.pageSizeOptions,
    };
  }

  get tableLoading() {
    return typeof this.loading === 'boolean' ? this.loading : this.store.loading;
  }

  get locale() {
    return {
      emptyText: this.emptyText,
    };
  }

  @Watch('data', { immediate: true, deep: true })
  handleDataChange() {
    if (this.data) {
      this.localTotalCount = this.data.length;
      this.localData = this.data;
    }
    this.records;
  }

  @Watch('paginatedData', { immediate: true })
  handleLocalDataChange() {
    if (this.paginatedData && this.paginatedData.length > 0) {
      this.localData = this.paginatedData;
    }
  }

  public onChange(pagination: TaIndexTablePaginationInterface, filters: IObject, sorter: TaIndexTableSorterInterface) {
    this.haveStore || this.paginatedData
      ? this.onStoreTableChange(pagination, filters, sorter)
      : this.onDataTableChange(pagination);
  }

  public onDataTableChange(pagination: TaIndexTablePaginationInterface) {
    if (pagination.current !== this.current || pagination.pageSize !== this.pageSize) {
      this.localPageSize = pagination.pageSize;
      this.localCurrentPage = pagination.current;
      this.localData = this.data.slice(
        (this.localCurrentPage - 1) * this.localPageSize,
        this.localCurrentPage * this.localPageSize,
      );
    }
  }

  public onStoreTableChange(
    pagination: TaIndexTablePaginationInterface,
    filters: IObject,
    sorter: TaIndexTableSorterInterface & any,
  ) {
    this.$emit('tableChange', pagination, filters, sorter);
    if (pagination.current !== this.current || pagination.pageSize !== this.pageSize) {
      this.localPageSize = pagination.pageSize;
      this.$emit('change', pagination.current, { ...this.queryObject }, this.localPageSize);
      this.$emit('paginate', pagination.current);
    } else if (
      sorter.order &&
      !sorter.column?.sorter && // 本地 sorter
      (this.sorter.order !== sorter.order || this.sorter.field !== sorter.field)
    ) {
      this.sorterChange(sorter);
    } else if (Object.keys(filters).length > 0) {
      this.filterChange(filters);
    }
  }

  public async sorterChange(sorter: TaIndexTableSorterInterface) {
    this.sorter = sorter;
    let ransackQuerySort = ['id desc'];
    if (sorter.order) {
      ransackQuerySort = [`${sorter.field || sorter.columnKey} ${sorter.order === 'descend' ? 'desc' : 'asc'}`];
      this.queryObject.s = ransackQuerySort;
    } else {
      const index = (this.queryObject.s || []).find((o: any) => o.includes(sorter.field));
      this.queryObject.s.splice(index, 1);
    }

    this.$emit('change', 1, { ...this.queryObject }, this.pageSize);
    this.$emit('sort', { ...this.queryObject });
  }

  public filterChange(filters: IObject) {
    this.queryObject = {
      ...this.queryObject,
      ...Object.keys(filters || {}).reduce((obj: IObject, key: string) => {
        obj[`${key}_in`] = filters[key];
        return obj;
      }, {}),
    };
    this.$emit('change', 1, { ...this.queryObject }, this.pageSize);
    this.$emit('filter', this.queryObject);
  }

  public customRow(record: object, index: number): object {
    return {
      on: {
        click: () => {
          this.$emit('rowClick', record, index);
        },
        dblclick: () => {
          this.$emit('rowDbClick', record, index);
        },
        mouseenter: () => {
          this.$emit('rowHover', record, index);
        },
        mouseleave: () => {
          this.$emit('rowHover', {}, null);
        },
      },
    };
  }

  getRowClassName(...args: IObject[]) {
    if (this.rowClassName instanceof Function) {
      return this.rowClassName(...args);
    }
    return this.rowClassName;
  }
}
</script>

<template lang="pug">
.ta-table-component
  slot(name="header")
  a-table.ta-table-component__table(
    :rowKey="rowKey"
    :dataSource="records"
    :columns="columns"
    :rowSelection="rowSelection"
    :pagination="pagination"
    :bordered="bordered"
    :loading="tableLoading"
    :showHeader="showHeader"
    :customRow="customRow"
    :rowClassName="getRowClassName"
    :scroll="scroll"
    :locale="locale"
    :size="size"
    @change="onChange"
  )
    slot
</template>

<style lang="stylus" scoped>
.ta-table-component
  position relative
  width 100%
  .ta-table-component__header
    position absolute
    top 0px
    left 26px
    z-index 9
    display flex
    align-items center
    width calc(100% - 26px)
    height 53px
    background #fff
  .ta-table-component__table
    margin-bottom 30px
</style>
