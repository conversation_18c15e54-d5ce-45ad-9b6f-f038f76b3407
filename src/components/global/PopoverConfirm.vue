<template lang="pug">
a-popover(
  :visible="localVisible"
  :placement="placement"
  trigger="click"
  @visibleChange="syncVisible")
  template(slot="content")
    .popover-card(:style="{width: width + 'px'}")
      .popover-header
        slot(name="title")
          span {{ title }}
        a-button.close(shape="circle" icon="close" size="small" @click="syncVisible(false, true)")
      .popover-content
        slot(name="content")
          span {{ content }}
      .popover-footer
        slot(name="footer")
          a-button(
            :type="type"
            block
            size="large"
            @click.stop="confirm"
            :loading="loading"
            :disabled="throttleDisabled || disabled")
            | {{ okText }}
  slot
</template>

<script lang="ts">
import { Component, Vue, Prop, Model, Watch, Emit } from 'vue-property-decorator';

@Component
export default class PopoverConfirm extends Vue {
  @Model('change', { type: Boolean }) visible!: boolean;
  @Prop({ type: String, default: '删除' }) title!: string;
  @Prop({ type: String, default: '您确认继续执行此操作吗?' }) content!: string;
  @Prop({ type: String, default: 'bottom' }) placement!: string;
  @Prop({ type: Number, default: 248 }) width!: number;
  @Prop({ type: Boolean, default: false }) private loading!: string;
  @Prop({ type: Boolean, default: false }) private disabled!: string;
  @Prop({ type: String, default: 'click' }) private trigger!: string;
  @Prop({ type: String, default: 'danger' }) private type!: string;
  @Prop({ type: String, default: '确认' }) private okText!: string;
  @Prop({ type: Boolean, default: true }) private autoClose!: boolean;

  localVisible: boolean = false;
  throttleDisabled: boolean = false;

  @Watch('visible')
  onVisibleChange() {
    this.localVisible = this.visible;
  }

  confirm() {
    if (this.throttleDisabled) return;
    this.throttleDisabled = true;

    this.$emit('confirm');
    this.syncVisible(false, true);

    setTimeout(() => {
      this.throttleDisabled = false;
    }, 1000);
  }
  syncVisible(visible: boolean, focus?: boolean) {
    if (visible) {
      this.localVisible = visible;
      return;
    }
    if (this.autoClose || focus) {
      this.localVisible = visible;
      this.$emit('change', visible);
      if (!visible) {
        this.$emit('close');
      }
    }
  }
}
</script>

<style lang="stylus" scoped>
.popover-card
  margin -12px -16px
  width 100%
  .popover-header
    position relative
    padding 15px 0px
    width 100%
    height 50px
    border-bottom 1px #E5E5E5 solid
    text-align center
    font-weight 500
    font-size 16px
    line-height 20px
    .close
      position absolute
      top 15px
      right 12px
      width 20px
      height 20px
      border none
      color #a6a6a6
      line-height 20px
      &:hover
        color #3da8f5
  .popover-content
    padding 16px
    width 100%
  .popover-footer
    padding 12px 16px
    width 100%
    border-top 1px #E5E5E5 solid
</style>
