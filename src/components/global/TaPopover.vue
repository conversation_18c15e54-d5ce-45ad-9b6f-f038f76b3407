<script lang="ts">
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';

@Component
export default class TaPopover extends Vue {
  @Model('change', { type: Boolean, default: false }) visible!: boolean;
  @Prop({ type: String, default: '' }) title!: string;
  @Prop({ type: String, default: '' }) content!: string;
  @Prop({ type: Number, default: 248 }) width!: number;
  @Prop() triggerElement!: HTMLElement;

  @Prop({ type: String, default: 'bottom' }) placement!: string;
  @Prop({ type: Number, default: 10 }) offsetY!: number;
  @Prop({ type: Number, default: 10 }) offsetX!: number;
  @Prop({ type: Boolean, default: false }) backable!: boolean;

  popoverDom: HTMLElement | null = null;
  timer: any = null;
  style: object = {};

  mounted() {
    this.popoverDom = this.$refs.popover as HTMLElement;
  }

  @Watch('visible')
  onVisibleChange() {
    if (this.visible) {
      this.style = { display: this.visible ? 'block' : 'none', visibility: 'hidden' };
      this.$nextTick(() => {
        this.initPopover(this.triggerElement);
      });
    } else {
      this.hide();
    }
  }

  initPopover(domElement: HTMLElement) {
    if (!domElement) return;
    this.popoverDom = this.popoverDom || (this.$refs.popover as HTMLElement);
    if (!this.popoverDom) return;

    const { left, right, top, bottom, height } = domElement.getBoundingClientRect();
    const needHeight = this.popoverDom.clientHeight + this.offsetY;
    const offsetBottom = window.innerHeight - bottom;
    const positionTop = offsetBottom > needHeight ? `${top + height + this.offsetY}px` : `${top - needHeight}px`;
    // TODO: 增加 左右方位 边界兼容情况
    this.style = {
      display: this.visible ? 'block' : 'none',
      width: `${this.width}px`,
      top: positionTop,
      left: `${right - this.width - this.offsetX}px`,
      transform: 'unset',
      visibility: 'visible',
    };
  }

  back() {
    this.$emit('back');
  }
  close() {
    this.hide();
    this.$emit('change', false);
  }
  hide() {
    this.style = { display: 'none', left: '0px', top: '0px', transform: 'translateX(-100%)' };
  }
}
</script>

<template lang="pug">
.ta-popover(:style="style" ref="popover")
  .header
    a-icon.icon.back(type="left" @click="back" v-if="backable")
    .title {{ title }}
    a-icon.icon.close(type="close" @click="close")
  .content
    slot(name="content")
      .desc {{ content }}
  .footer(v-if="$slots.footer")
    slot(name="footer")
</template>

<style lang="stylus" scoped>
.ta-popover-wrapper
  position absolute

.ta-popover
  position fixed
  top 0px
  left 0px
  height fit-content
  z-index 9999
  display none
  width 248px
  border-radius 2px
  background rgba(255, 255, 255, 1)
  box-shadow 0px 7px 21px 0px rgba(0, 0, 0, 0.1)
  overflow hidden
  .header
    position relative
    padding 0 12px
    height 50px
    border-bottom 1px solid #E5E5E5
    color rgba(56, 56, 56, 1)
    font-weight 500
    font-size 16px
    line-height 20px
    .title
      height 49px
      text-align center
      line-height 49px
    .icon
      position absolute
      top 16px
      color #a6a6a6
      cursor pointer
      &:hover
        color #3da8f5
    .close
      right 12px
    .back
      left 12px
  .content
    .desc
      padding 16px
      color rgba(56, 56, 56, 1)
      font-size 14px
      line-height 20px
  .footer
    padding 12px 16px
    border-top 1px solid #E5E5E5
</style>
