<template lang="pug">
Popover(
  :visible="value"
  :placement="placement"
  :width="width"
  :showHeader="true"
  :title="title"
  @change="close")
  .popover-form(slot="main")
    SimpleForm(
      v-model="localForm"
      :templates="template"
      :submitTitle="submitTitle"
      @submit="submit")
  slot
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import SimpleForm from '@/components/SimpleForm.vue';

@Component({
  components: {
    SimpleForm,
  },
})
export default class PopoverForm extends Vue {
  // props
  @Prop({ type: Boolean, default: false }) private value!: boolean;
  @Prop({ type: String, default: 'click' }) private trigger!: string;
  @Prop({ type: String, default: 'topRight' }) private placement!: string;
  @Prop({ type: Object, default: () => ({}) }) private form!: object;
  @Prop({ type: Array, default: () => [] }) private template!: object[];
  @Prop({ type: Number, default: 248 }) private width!: number;
  @Prop({ type: String, default: '创建' }) private title!: string;
  @Prop({ type: String, default: '创建' }) private submitTitle!: string;
  // emit
  @Emit('submit')
  public submit(val: object): object {
    this.$emit('change');
    return val;
  }
  @Emit('input')
  public close(val: any): boolean {
    return val;
  }

  get localForm() {
    return this.form;
  }

  set localForm(val: IObject) {
    this.$emit('changeVal', val);
  }
}
</script>

<style lang="stylus" scoped>
.popover-form
  margin -16px
</style>
