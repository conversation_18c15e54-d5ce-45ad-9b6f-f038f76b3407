<template lang="pug">
a-skeleton(:loading='tableLoading', active)
  .list-view-component(:style='appendStyle')
    TaEmpty(v-if='records.length === 0', :desc='emptyText')
    .item(v-for='(record, index) in records', :key='["record", record.id].join("_")')
      slot(:record='record', :index='index')
    .list-view__pagination(v-if='!hidePage && total > 0')
      a-pagination(v-bind='pagination', @showSizeChange='onPaginationChange', @change='onPaginationChange')
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class TaListView extends Vue {
  // props
  @Prop({ type: Array }) private data!: IObject[];
  @Prop({ type: Object, default: () => ({}) }) private store!: any;
  @Prop({ type: Number, default: 1 }) private totalPages!: number;
  @Prop({ type: Number, default: 0 }) private totalCount!: number;
  @Prop({ type: Number, default: 1 }) private currentPage!: number;
  @Prop({ type: Number, default: null }) private perPage!: number | null;
  @Prop({ type: String, default: '暂无数据' }) private emptyText!: string;
  @Prop({ type: Boolean, default: null }) private loading!: boolean;
  @Prop({ type: Boolean, default: true }) private showSizeChanger!: boolean;
  @Prop({ type: String, default: 'middle' }) private paginationSize!: string;
  @Prop({ type: Boolean, default: false }) private hideOnSinglePage?: boolean;
  @Prop({ type: Boolean, default: false }) private hidePage!: boolean;
  @Prop({ type: String, default: '' }) private appendStyle?: string;
  // data
  private localPageSize: number | null = null;
  private sorter: IObject = {};
  private queryObject: IObject = { s: [] };

  get records() {
    return this.data || this.store.records || [];
  }
  // pagination
  get current() {
    return this.store.currentPage || this.currentPage;
  }
  get pageSize() {
    return this.localPageSize || this.perPage || this.store.perPage;
  }
  get total() {
    return this.store.totalCount || this.totalCount;
  }
  get pages() {
    return this.store.totalPages || this.totalPages;
  }
  get pagination() {
    const totalPages: number = this.store.totalPages || this.totalPages;
    return {
      total: this.total,
      pageSize: this.pageSize,
      current: this.current,
      hideOnSinglePage: this.hideOnSinglePage,
      showSizeChanger: this.showSizeChanger,
      showQuickJumper: totalPages > 1,
      size: this.paginationSize,
    };
  }
  get tableLoading() {
    return typeof this.loading === 'boolean' ? this.loading : this.store.loading;
  }

  onPaginationChange(page: number, pageSize: number): void {
    this.$emit('change', page, { ...this.queryObject }, pageSize);
  }
}
</script>

<style lang="stylus">
.list-view-component
  min-height 100px
  .list-view__pagination
    margin-top 12px
    margin-right 12px
    text-align right
</style>
