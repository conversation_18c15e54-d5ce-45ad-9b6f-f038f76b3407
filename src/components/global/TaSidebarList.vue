<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { ActiveStore } from '@/lib/ActiveStore';
import { IRowSection } from '@/components/global/TaSidebarList';
import { TaIndexTableConfigInterface } from './TaIndex';

@Component
export default class TaSidebarList<T extends Record<string, any>> extends Vue {
  @Prop({ type: Number, default: () => 240 }) private readonly sidebarWidth!: number;
  @Prop({ type: Boolean, default: () => false }) private readonly isTable!: boolean;
  @Prop({ type: Boolean, default: () => true }) private readonly showPageSizeChanger!: boolean;
  // 表格配置
  @Prop({ type: Object, default: () => ({}) }) private readonly tableConfig!: TaIndexTableConfigInterface;
  // 列表配置
  @Prop({ type: Number, default: 1 }) private readonly splitCount!: number;
  // 表格和列表共用的属性
  @Prop({ type: Array, default: () => null }) private readonly data!: null | T[];
  @Prop({ type: Object, default: () => null }) private readonly store!: ActiveStore<T>;
  @Prop({ type: String, default: '暂无数据' }) private readonly emptyText!: string;
  @Prop({ type: Boolean, default: false }) private scrollLoading!: boolean;

  // 如果传入会出现勾选的批量状态
  @Prop({ type: String, default: () => 'id' }) private rowKey!: string;
  @Prop({ type: Object, default: () => null }) private rowSelection!: IRowSection<number, T>;

  @Prop({ type: Boolean, default: false }) private draggable!: boolean;

  private selectedRecords: T[] = [];

  private get hasSidebar() {
    return !!this.$slots['sidebar'];
  }

  // 取值 tableConfig 内值 或 默认 prop 值
  private get activeRowKey() {
    return this.isTable ? this.tableConfig.rowKey || this.rowKey : this.rowKey;
  }

  private get activeRowSelection() {
    return this.isTable ? this.tableConfig.rowSelection || this.rowSelection : this.rowSelection;
  }

  private get activeEmptyText() {
    return this.isTable ? this.tableConfig.emptyText || this.emptyText : this.emptyText;
  }

  private get activeTableConfig() {
    return {
      showSizeChanger: this.showPageSizeChanger,
      ...this.tableConfig,
      rowKey: this.activeRowKey,
      rowSelection: this.activeRowSelection.selectedRowKeys ? this.activeRowSelection : null,
      emptyText: this.activeEmptyText,
    };
  }

  private handleCheckboxChange(rowKey: number, record: T) {
    const existsKeys = Array.isArray(this.activeRowSelection.selectedRowKeys)
      ? this.activeRowSelection.selectedRowKeys
      : [];
    // rowSelection 清空时，清空 selectedRecords
    this.selectedRecords = this.selectedRecords.filter(i => existsKeys.includes(i[this.activeRowKey]));

    const index = existsKeys.indexOf(rowKey);

    if (index === -1) {
      existsKeys.push(rowKey);
      this.selectedRecords.push(record);
    } else {
      existsKeys.splice(index, 1);
      this.selectedRecords.splice(index, 1);
    }

    if (typeof this.activeRowSelection.onChange === 'function') {
      this.activeRowSelection.onChange(existsKeys, this.selectedRecords);
    }
  }

  private handlePaginateChange(page: number, queryParams: Record<string, any>, perPage: number, scrollLoading = false) {
    this.$emit('onPaginateChange', page, queryParams, perPage, scrollLoading);
  }

  private handleRowClick(record: T) {
    this.$emit('rowClick', record);
  }

  private handleDraggle(record: T, position: number) {
    this.$emit('draggle', record, position);
  }
}
</script>

<template lang="pug">
.ta-sidebar-list
  aside(:style='{ flexBasis: `${sidebarWidth}px` }', v-if='hasSidebar')
    slot(name='sidebar')
  .list(:style='`width: calc(100% - ${sidebarWidth}px)`')
    slot(name="table_top")
    TaIndexTable(
      v-if='isTable',
      :data='data',
      :store='store',
      :config="activeTableConfig"
      @rowClick='handleRowClick',
      @change='handlePaginateChange'
    )
      slot
    TaIndexList(
      v-else,
      :data='data',
      :store='store',
      :showSizeChanger='showPageSizeChanger'
      :emptyText='emptyText',
      :scrollLoading='scrollLoading',
      :splitCount='splitCount',
      :draggable='draggable',
      :config="activeTableConfig"
      :hideOnSinglePage='tableConfig.hideOnSinglePage',
      @change='handlePaginateChange',
      @draggle='handleDraggle'
    )
      template(#default='{record, index}')
        template
          TaListCheckboxItem(
            :index='index',
            :record='record',
            :rowKey='rowKey',
            :rowSelection='rowSelection',
            :showCheckbox='!!(rowKey && rowSelection.selectedRowKeys)',
            @rowClick='handleRowClick',
            @change='handleCheckboxChange'
          )
            template(#default='{ record, index, isActive }')
              slot(:record='record', :index='index', :isActive='isActive')
</template>

<style lang="stylus" scoped>
.ta-sidebar-list
  display flex
  height 100%
  aside
    flex-grow 0
    flex-shrink 0
  .list
    flex 1
    height 100%
</style>
