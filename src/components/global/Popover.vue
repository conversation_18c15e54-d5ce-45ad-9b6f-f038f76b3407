<template lang="pug">
a-popover(
  v-model="localVisible"
  trigger="click"
  :placement="placement"
  @visibleChange="visibleChange")
  template(slot="content")
    .popover-card(:style="{width: width + 'px'}")
      slot(name="content")
        .popover-header(v-if="title")
          a-button.back(shape="circle" icon="left" size="small" @click.stop="onBack" v-if="back")
          span {{ title }}
          a-button.close(shape="circle" icon="close" size="small" @click="cancel()")
        .popover-main
          slot(name="main")
            span {{ desc }}
        .popover-footer(v-if="$slots.footer")
          slot(name="footer")
  slot
</template>

<script lang="ts">
import { Component, Vue, Prop, Model, Watch, Emit } from 'vue-property-decorator';

@Component
export default class Popover extends Vue {
  @Model('change', { type: Boolean, default: false }) visible!: boolean;
  @Prop({ type: String, default: '' }) title!: string;
  @Prop({ type: String, default: '' }) desc!: string;
  @Prop({ type: String, default: 'click' }) trigger!: string;
  @Prop({ type: String, default: 'bottom' }) placement!: string;
  @Prop({ type: Number, default: 248 }) width!: number;
  @Prop({ type: Function, default: null }) back!: () => void;

  localVisible: boolean = false;

  @Watch('visible', { immediate: true })
  onValueChange() {
    this.localVisible = this.visible;
  }

  @Emit('change')
  visibleChange(visible: Boolean) {
    this.$emit('onCancel');
    return visible;
  }

  cancel() {
    this.localVisible = false;
    this.visibleChange(false);
    this.$emit('onCancel');
  }

  onBack() {
    if (this.back) {
      this.back();
    }
  }
}
</script>

<style lang="stylus" scoped>
.popover-card
  margin -12px -16px
  width 100%
  .popover-header
    position relative
    padding 15px 0px
    width 100%
    height 50px
    border-bottom 1px #E5E5E5 solid
    text-align center
    font-weight 500
    font-size 16px
    line-height 20px
    .back
      position absolute
      top 15px
      left 12px
      width 20px
      height 20px
      border none
      color #a6a6a6
      line-height 20px
      &:hover
        color #3da8f5
    .close
      position absolute
      top 15px
      right 12px
      width 20px
      height 20px
      border none
      color #a6a6a6
      line-height 20px
      &:hover
        color #3da8f5
  .popover-main
    padding 16px
    width 100%
  .popover-footer
    padding 12px 16px
    width 100%
    border-top 1px #E5E5E5 solid
</style>
