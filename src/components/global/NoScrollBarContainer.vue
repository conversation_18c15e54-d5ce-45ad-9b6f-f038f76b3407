<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component
export default class NoScrollBarContainer extends Vue {}
</script>

<template lang="pug">
.no-scroll-bar-container
  .no-scroll-bar-inner-container
    slot
</template>

<style lang="stylus" scoped>
.no-scroll-bar-container
  height 100%
  position relative
  overflow hidden
  .no-scroll-bar-inner-container
    position absolute
    left 0
    right -18px
    top 0
    height 100%
    overflow auto
    padding-right 18px
</style>
