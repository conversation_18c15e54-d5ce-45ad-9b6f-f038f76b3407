<template lang="pug">
.empty-placeholder
  img(v-if="type === 'school'" src="@/assets/icons/empty/school.png" width="80" height="80")
  img(v-else-if="type === 'table'" src="@/assets/icons/empty/table.png" width="80" height="80")
  img(v-else-if="type === 'survey'" src="@/assets/icons/empty/survey.png" width="80" height="80")
  img(v-else src="@/assets/icons/empty/empty.png" width="80" height="80")
  .desc {{ desc }}
  .tips {{ tips }}
  .action
    slot
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class Empty extends Vue {
  // prop
  @Prop({ type: String, default: '暂无相应内容' }) private desc!: string;
  @Prop({ type: String, default: '' }) private tips!: string;
  @Prop({ type: String, default: 'table' }) private type!: string; // personnel、school、table、survey
}
</script>

<style lang="stylus" scoped>
.empty-placeholder
  padding 60px 0px
  width 100%
  text-align center
  line-height 1
  img
    margin-bottom 10px
  .desc
    margin-bottom 12px
    color #808080
    font-weight 500
    font-size 16px
  .tips
    margin-bottom 22px
    color #A6A6A6
    font-size 14px
  .action
    button
      min-width 100px
      height 40px
</style>
