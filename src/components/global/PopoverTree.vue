<template lang="pug">
Popover(
  :visible="value"
  :placement="placement"
  :width="width")
  template(#main)
    .popover-header
      a-input(v-model="filter" placeholder="检索部门" style="margin: 16px 0px" @change="onChange")
    .popover-main
      a-tree(
        :treeData="departmentTrees")
        .tree-node-column(slot="name" slot-scope="scope" @click="onSelect(scope.id)")
          span(v-if="scope.short_name.indexOf(filter) > -1")
            span {{scope.name.substr(0, scope.name.indexOf(filter))}}
            span(style="color: #1890ff") {{filter}}
            span {{scope.name.substr(scope.name.indexOf(filter) + filter.length)}}
          span(v-else) {{scope.name}}
          a-icon(type="check" v-if="filterSelect(scope.id)")
    .popover-footer
      a-button(size="large" style="margin-right: 8px" @click="close") 取消
      a-button(type="primary" size="large" @click="submit") {{ submitTitle }}
  slot
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class PopoverTree extends Vue {
  // props
  @Prop({ type: Boolean, default: false }) private value!: boolean;
  @Prop({ type: String, default: 'click' }) private trigger!: string;
  @Prop({ type: String, default: 'bottomRight' }) private placement!: string;
  @Prop({ type: Number, default: 248 }) private width!: number;
  @Prop({ type: String, default: '创建' }) private title!: string;
  @Prop({ type: String, default: '确定' }) private submitTitle!: string;
  // data
  private filter: string = '';
  private departmentTrees: any = [];
  private departmentTree: any = {};
  private expandedKeys: string[] = [];
  private selectIds: number[] = [];
  // emit
  @Emit('submit')
  public submit(): number[] {
    this.close();
    return this.selectIds;
  }
  @Emit('input')
  public close(): boolean {
    return false;
  }
  // methods
  public mounted() {
    this.fetchData();
  }
  public async fetchData() {
    const { data } = await this.$models.department.tree();
    this.departmentTrees = this.initTree(data.departments);
    this.expandedKeys = data.departments.map((e: any) => e.id).slice(0, 1);
    this.departmentTree = data.departments[0] || {};
  }
  public initTree(value: any[] = []): any {
    return (value || []).map((item: any) => ({
      ...item,
      title: item.short_name,
      value: item.name,
      key: item.id,
      children: this.initTree(item.children),
      scopedSlots: { title: 'name' },
    }));
  }
  public onChange() {}
  public onSelect(id: number) {
    const isExist: boolean = this.selectIds.some((e: number) => e === id);
    if (isExist) {
      this.selectIds = this.selectIds.filter((e: number) => e !== id);
    } else {
      this.selectIds.push(id);
    }
  }
  public filterSelect(id: number): boolean {
    return this.selectIds.some((e: number) => e === id);
  }
}
</script>

<style lang="stylus" scoped>
.popover-header
  padding 0px 16px

.popover-main
  overflow auto
  padding 0px 8px
  max-height 260px
  .tree-node-column
    display flex
    justify-content space-between
    align-items center
    width 100%

.popover-footer
  padding 12px 16px
  border-top 1px #E5E5E5 solid
  button
    width 104px
</style>
