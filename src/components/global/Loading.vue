<template lang="pug">
.loading-page(v-if="loading")
  a-spin(:spinning="loading" :tip="tip")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class Loading extends Vue {
  @Prop({ type: String, default: '加载中' }) private tip!: string;
  @Prop({ type: Boolean, default: false }) private loading!: boolean;
}
</script>

<style lang="stylus" scoped>
.loading-page
  display flex
  justify-content center
  padding 30px 0px
  width 100%
</style>
