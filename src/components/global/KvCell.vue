<template lang="pug">
.kv-cell(:class="`size-${size}`")
  .key(:style="`width: ${nameWidth}`")
    a-icon.icon(:type="icon" v-if="icon")
    slot(name="name")
      span {{ name }}
  .value
    slot
      span(v-if="value") {{ value }}
      a.link(:href="to" target="_blank" v-if="to")
        | {{ to }}
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class KvCell extends Vue {
  @Prop({ type: String, default: '' }) name!: string;
  @Prop({ type: String, default: '' }) icon!: string;
  @Prop() value!: any;
  @Prop({ type: String }) to!: any;
  @Prop({ type: String, default: 'default' }) size!: any;
  @Prop({ type: String, default: '' }) nameWidth!: string;
}
</script>

<style lang="stylus" scoped>
.kv-cell
  display flex
  margin 10px 0px
  font-size 14px
  line-height 22px
  font-weight 400
  align-items flex-start
  .key
    color rgba(128,128,128,1)
    width 96px
    flex-shrink 0
    padding-right 12px
    display flex
    align-items center
    .icon
      margin-right 4px
      font-size 16px
  .value
    color rgba(56,56,56,1)
    .link
      color rgba(56,56,56,1)
      &:hover
        color #3DA8F5

.size-small
  margin 6px 0
  .key
    width 68px
</style>
