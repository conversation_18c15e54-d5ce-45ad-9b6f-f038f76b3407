<script lang="ts">
import { Component, Vue, Prop, Watch, Model } from 'vue-property-decorator';

@Component
export default class StepToolbar extends Vue {
  @Model('change', { type: [String, Number, Boolean, Array, Object], default: '' }) value!: string;
  @Prop({
    type: String,
    default: 'steps',
    validator(val) {
      return ['tabs', 'steps', 'breadcrumbs'].includes(val);
    },
  })
  mode!: string;
  @Prop({ type: Array, default: () => [] }) steps!: any;
  @Prop({ type: Array, default: () => [] }) breadcrumbs!: any[];
  @Prop({
    type: String,
    default: 'default',
    validator(val) {
      return ['default', 'large', 'small'].includes(val);
    },
  })
  size!: string;
  @Prop({ type: String, default: '180px' }) private titleWidth?: string;
  @Prop({ type: String, default: '' }) private title?: string;
  @Prop({ type: String, default: 'filled' }) private theme?: 'filled' | 'outlined' | 'twoTone';
  @Prop({ type: [String, Boolean], default: 'bordered' }) private bordered?: boolean;
  @Prop({ type: Object, default: () => {} }) private stepStyle?: any;

  private cursorLeft: number = 0;
  private cursorWidth: number = 0;

  get cursorStyle() {
    return {
      width: `${this.steps && this.steps.length ? this.cursorWidth : 0}px`,
      transform: `translate3d(${this.cursorLeft}px, 0px, 0px)`,
    };
  }
  get keys() {
    return this.steps.map((o: any) => o.key);
  }
  get hasLeftPart() {
    return (this.breadcrumbs && this.breadcrumbs.length > 0) || this.title;
  }

  @Watch('value')
  public watchChange() {
    this.initActiveCursor();
  }
  @Watch('steps', { deep: true })
  public stepsChange() {
    this.initActiveCursor();
  }

  public mounted() {
    this.initActiveCursor();
  }
  public initActiveCursor() {
    this.$nextTick(() => {
      const stepTarget = this.$refs.activeStep && (this.$refs.activeStep as any[])[0];
      if (stepTarget) {
        this.cursorLeft = stepTarget.offsetLeft;
        this.cursorWidth = stepTarget.clientWidth;
      }
    });
  }

  clickStep(e: any, step: any) {
    if (!step.disabled) {
      if (step.type === 'route') {
        this.$router.replace({ name: step.key, query: step.query }).catch(() => {});
        this.$emit('change', step.key);
      } else {
        this.$emit('change', step.key);
      }
    }
  }

  public getStepClass(step: any): any {
    const keys = this.steps.map((o: any) => o.key);
    const stepIndex = keys.indexOf(step.key);
    const activeIndex = keys.indexOf(this.value);
    return {
      'step-disabled': step.disabled,
      'step-node': this.mode === 'steps',
      'step-tab': this.mode === 'tabs',
      'step-finish': stepIndex < activeIndex,
      'step-wait': stepIndex > activeIndex,
      'step-active': this.value === step.key,
    };
  }

  private backPage(item: any): void {
    this.$emit('breadcrumbBackPage', item);
  }
}
</script>

<template lang="pug">
.step-toolbar(
  :class="{ 'toolbar__no-padding': !hasLeftPart, [`${this.size}-size`]: true, 'toolbar__no-border': !bordered }"
  :style="stepStyle"
  )
  .step-toolbar__left-part(v-if="hasLeftPart")
    .title(v-if="title")
      | {{ title }}
    a-breadcrumb.breadcrumb(v-if="breadcrumbs && breadcrumbs.length")
      template(slot="separator")
        a-icon(type="right")
      a-breadcrumb-item(v-for="(breadcrumb, index) in breadcrumbs" :key="index" )
        a.ellipsis-text(
          href="javascript:;"
          :style="{ maxWidth: titleWidth }"
          v-if="breadcrumb.url === 'back'"
          @click="$router.back()")
          a-tooltip(:title="breadcrumb.title")
            | {{ breadcrumb.title }}
        router-link.ellipsis-text(:to="breadcrumb.url" :style="{ maxWidth: titleWidth }" v-else-if="breadcrumb.url")
          a-tooltip(:title="breadcrumb.title")
            | {{ breadcrumb.title }}
        span.ellipsis-text.box(:style="{ maxWidth: titleWidth }" @click="backPage(breadcrumb)" v-else)
          a-tooltip(:title="breadcrumb.title")
            span {{ breadcrumb.title }}
  .step-toolbar__steps-container
    slot(name="center")
    .steps(ref="steps")
      .cursor(:style="cursorStyle")
      .step(
        v-for="step in steps"
        :key="step.key"
        :ref="step.key === value ? 'activeStep' : null"
        :class="getStepClass(step)"
        @click="(e) => { clickStep(e, step) }"
      )
        template(v-if="mode === 'steps'")
          a-icon.icon.finish-icon(type="check-circle" :theme="theme")
          a-icon.icon.active-icon(v-if="step.icon" :type="step.icon")
        span {{ step.title }}
        span(v-if="step.count")  · {{step.count}}
  .step-toolbar__right-part
    slot
    slot(name="right")
</template>

<style lang="stylus">
$primaryColor = #3DA8F5
$lineBgColor = #3DA8F5
$textColor = #383838
$textGrayColor = #808080

.step-toolbar
  position relative
  display flex
  padding 0 20px
  width 100%
  height 48px
  border-bottom 1px solid #DDDDDD
  line-height 48px
  .step-toolbar__left-part
    flex-grow 1
    margin-right 20px
    width 100%
    height inherit
    line-height inherit
    .title
      color #000
      font-weight 500
      font-size 16px
    .breadcrumb
      display inline-block
      font-weight 500
      .ellipsis-text
        display inline-block
        overflow hidden
        max-width 140px
        vertical-align text-top
        text-overflow ellipsis
        white-space nowrap
        line-height 1.3
  .step-toolbar__right-part
    display flex
    flex-grow 1
    justify-content flex-end
    align-items center
    margin-left 20px
    width 100%
  .step-toolbar__steps-container
    position relative
    display inline-block
    flex-shrink 0
    margin-right -20px
    margin-left -20px
    height inherit
    white-space nowrap
    .steps
      display inline-block
      height inherit
      line-height inherit
      .cursor
        position absolute
        bottom 0
        width 100%
        height 4px
        background $primaryColor
        transition all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1)
      .step
        display inline-block
        margin 0px 20px
        height inherit
        color $textColor
        font-size 14px
        line-height inherit
        cursor pointer
        .icon
          margin-right 4px
          vertical-align middle
          font-size 16px
          line-height 1
        .finish-icon
          display none
        .active-icon
          display inline-block
        span
          vertical-align middle
      .step-node
        position relative
        margin-right 68px
        margin-left 8px
        color $textColor
        &:after
          position absolute
          top 50%
          right -68px
          z-index 1
          width 60px
          height 1px
          background $primaryColor
          content ''
        &:last-child
          margin-right 8px
          &:after
            display none
      .step-disabled
        cursor auto
      .step-finish
        &:after
          background $primaryColor
        .finish-icon
          display inline-block
          color $primaryColor
        .active-icon
          display none
      .step-active
        &:after
          background $lineBgColor
      .step-wait
        color $textGrayColor
        &:after
          background $lineBgColor
      .step-tab
        font-weight 500
        .finish-icon, .active-icon
          display none

.toolbar__no-padding
  padding 0px
  border none

.toolbar__no-border
  border none

.large-size
  height 56px
  line-height 56px

.small-size
  height 40px
  line-height 40px
</style>
