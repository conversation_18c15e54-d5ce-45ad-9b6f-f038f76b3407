<script lang="ts">
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import FileSaver from 'file-saver';
import FileServer, { IFile } from '@/models/file';
import attachmentIcon from '@/assets/icons/file/attachment_32.svg';
import TaVideo from './TaVideo.vue';
import TaTimer from '@/components/global/TaTimer.vue';
import moment from 'moment';
import Viewer from 'v-viewer';
import mime from 'mime-types';
import pdf from 'vue-pdf';

@Component({
  components: {
    TaVideo,
    TaTimer,
    Viewer,
    pdf,
  },
})
export default class FilePreviewer extends Vue {
  @Model('input', { type: Boolean, default: false }) private visible!: boolean;
  @Prop({ type: String, default: '' }) private title!: string;
  @Prop({ type: Object, default: () => ({}) }) private attachment!: IFile;
  @Prop({ type: <PERSON>olean, default: () => true }) private readonly heidDispalyDownloadButton!: boolean;

  innerVisible: boolean = false;
  fileServer: FileServer = new FileServer();
  attachmentIcon: any = attachmentIcon;
  viewerOptions: object = {
    inline: true,
    navbar: false,
    title: false,
    toolbar: {
      zoomIn: 4,
      zoomOut: 4,
      oneToOne: 4,
      reset: 4,
      prev: 0,
      play: 0,
      next: 0,
      rotateLeft: 4,
      rotateRight: 4,
      flipHorizontal: 4,
      flipVertical: 4,
    },
  };
  videoOptions: any = {
    aspectRatio: '16:9',
  };

  get fileTitle() {
    return this.title || this.attachment.fileName || '文件预览';
  }
  get fileItem() {
    return {
      ...this.attachment,
      url: this.fileServer.getCDNUrl(this.attachment),
      sizeText: FileServer.getSizeText(this.attachment.fileSize),
    };
  }
  get officeTypes() {
    // 注：Word和PowerPoint文档必须小于10M，Excel 必须小于五M
    return [
      'doc',
      'docx',
      'xlsx',
      'xls',
      'pptx',
      'ppt',
      'msword',
      'vnd.openxmlformats-officedocument.wordprocessingml.document',
      'vnd.ms-excel',
      'vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'vnd.ms-powerpoint',
      'vnd.openxmlformats-officedocument.presentationml.presentation',
    ];
  }
  get durationString() {
    if (this.fileItem.duration) {
      return this.$utils.parseSeconds(this.fileItem.duration);
    }
    return '';
  }
  get isOfficeFile() {
    return this.officeTypes.some(type => this.fileItem.fileType.includes(type));
  }

  @Watch('visible', { immediate: true })
  onVisibleChange() {
    if (this.visible) {
      this.innerVisible = true;
      setTimeout(() => {
        if (this.$refs.container) {
          const { clientHeight, clientWidth } = this.$refs.container as any;
          this.videoOptions.aspectRatio = `${clientWidth}:${clientHeight}`;
        }
      }, 50);
    }
  }

  onClose() {
    this.$emit('input', false);
    this.$emit('close');
    setTimeout(() => {
      this.innerVisible = false;
    }, 1000);
  }
  download() {
    FileSaver.saveAs(this.fileItem.url, this.fileItem.fileName);
  }
}
</script>

<template lang="pug">
a-drawer(
  v-if="innerVisible"
  :title="null"
  :visible="visible"
  :zIndex="99999"
  :destroyOnClose="true"
  :mask="true"
  placement="bottom"
  height="calc(100vh - 50px)"
  wrapClassName="file-previewer-wrap"
  @close="onClose")
  .file-preview-container
    .file-previewer-header
      .title.text-ellipsis {{ fileTitle }}
      .actions(v-if="heidDispalyDownloadButton")
        TextButton.action(icon="download" @click="download") 下载
    .file-previewer(ref="container")
      //- Pdf
      //- object.type-file-frame(
      //-   v-if="fileItem.fileType === 'pdf'"
      //-   :data="fileItem.url"
      //-   type="application/pdf")
      //-   .frame-placeholder
      //-     img.file-icon(:src="attachmentIcon")
      //-     .file-tips 浏览器不支持在线预览 PDF 文件，可下载后查看
      //-     a-button(icon="download" type="primary" @click="download")
      //-       | 点击下载
      pdf.type-file-frame(v-if="fileItem.fileType === 'pdf'", :src="fileItem.url")
      //- Video
      .type-file-frame(v-else-if="fileItem.mimeType.includes('video')")
        TaVideo.video-widget(:src="fileItem.url" :options="videoOptions")
      //- Audio
      .type-file-frame(v-else-if="fileItem.mimeType.includes('audio')")
        audio.audio-widget(:src="fileItem.url" controls)
      //- Image
      .type-file-frame(v-else-if="fileItem.mimeType.includes('image')")
        .image-widget(v-viewer="viewerOptions")
          img(:src="fileItem.url" :alt="fileItem.name" v-show="false")
      //- Offices
      iframe.type-file-frame(
        v-else-if="isOfficeFile"
        :src='`https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(fileItem.url)}`'
        width='100%'
        height='100%'
        frameborder='0')
      //- Other
      .type-file-frame(v-else)
        .frame-placeholder
          img.file-icon(:src="attachmentIcon")
          .file-tips 不支持在线预览，请下载后查看
          a-button(icon="download" type="primary" @click="download")
            | 点击下载
    .side-container
      .side-part(v-if="$slots.sideTop")
        slot(name="sideTop" :fileItem="fileItem")
      .side-part
        KvCell(name="大小" nameWidth="48px")
          | {{ fileItem.sizeText }}
        KvCell(name="时长" nameWidth="48px" v-if="fileItem.duration")
          | {{ durationString }}
        KvCell(name="尺寸" nameWidth="48px" v-if="fileItem.width")
          | {{ fileItem.width }} x {{ fileItem.height }}
      .side-part(v-if="$slots.sideBottom")
        slot(name="sideBottom" :fileItem="fileItem")
</template>

<style lang="stylus">
.file-previewer-wrap
  height 100%
  .ant-drawer-mask
    opacity 0.6 !important
  .ant-drawer-wrapper-body
    height 100%
  .ant-drawer-content-wrapper
    height 100%
    padding 0 50px 50px
    .ant-drawer-close-x
      font-size 20px
    .ant-drawer-body
      padding 0
      height 100%
      .file-preview-container
        display flex
        height 100%
        padding-top 56px
        position relative
        .file-previewer-header
          position absolute
          top 0
          left 0
          width 100%
          height 56px
          padding-right 70px
          padding-left 16px
          display flex
          align-items center
          justify-content space-between
          color #333
          border-bottom 1px solid RGBA(224, 224, 224, 1.00)
          .actions
            flex-shrink 0
            .action
              margin-left 20px
              color #333333
              &:hover
                color #000000
                font-weight bold
        .file-previewer
          width 100%
          height 100%
          background #333
          .type-file-frame
            width 100%
            height 100%
            display flex
            align-items center
            justify-content center
            position relative
            overflow auto
            .frame-placeholder
              font-size 16px
              color #fff
              text-align center
              .file-icon
                width 60px
                margin-bottom 20px
              .file-tips
                margin-bottom 30px
            .audio-widget
              width 500px
            .image-widget
              width 100%
              height 100%
            .video-widget
              max-width 100%
              max-height 100%
        .side-container
          width 300px
          flex-shrink 0
          height 100%
          border-left 1px solid RGBA(224, 224, 224, 1.00)
          padding 10px 20px
          overflow auto
          .side-part
            border-bottom 1px solid #e8e8e8
            position relative
            overflow hidden
            &:last-child
              border-bottom none
</style>
