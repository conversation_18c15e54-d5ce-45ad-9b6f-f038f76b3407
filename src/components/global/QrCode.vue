<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import QRCode, { QRCodeErrorCorrectionLevel } from 'qrcode';

@Component
export default class QrCode extends Vue {
  @Prop({ type: String }) data!: string;
  @Prop({ type: Number, default: 6 }) scale!: number;
  @Prop({ type: String, default: 'M', validator: l => ['L', 'Q', 'M', 'H'].indexOf(l) > -1 })
  level!: QRCodeErrorCorrectionLevel;
  @Prop({ type: String, default: '#fff' }) background!: string;
  @Prop({ type: String, default: '#000' }) foreground!: string;
  @Prop({ type: Number, default: 0 }) margin!: number;

  mounted() {
    this.draw();
    ['data', 'scale', 'level', 'background', 'foreground'].forEach(key => {
      this.$watch(key, this.draw);
    });
  }

  draw() {
    QRCode.toCanvas(
      this.$refs.qrcode,
      this.data || '',
      {
        errorCorrectionLevel: this.level,
        margin: this.margin,
        scale: this.scale,
        color: {
          dark: this.foreground,
          light: this.background,
        },
      },
      error => {
        if (!error) {
          const element = this.$refs.qrcode as HTMLElement;
          element.style.height = '';
          element.style.width = '';
        }
      },
    );
  }
}
</script>

<template lang="pug">
canvas(ref="qrcode")
</template>

<style lang="stylus" scoped>
canvas
  width 100%
  height auto
</style>
