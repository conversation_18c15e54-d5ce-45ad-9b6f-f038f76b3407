<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component
export default class FileInput extends Vue {
  @Prop({ type: String }) accept!: string;
  @Prop({ type: Boolean, default: false }) multiple!: boolean;
  @Prop({ type: Boolean, default: false }) disabled!: boolean;
  @Prop({ type: Boolean, default: false }) loading!: boolean;

  handleFileInputChange(e: any) {
    this.$emit('change', [...e.target.files]);
  }

  clickFileInput(e: any) {
    if (this.loading) return;
    if (e.target && e.target.tagName === 'BUTTON') {
      (this.$refs.fileInput as any).click();
      e.stopPropagation();
    } else {
      const path: HTMLDivElement[] = e.path;
      const triggerIndex = path.findIndex(o => o.classList && o.classList.contains('file-input-wrapper'));
      const triggerElement = path[triggerIndex - 1];
      if (triggerElement && triggerElement.tagName === 'BUTTON') {
        (this.$refs.fileInput as any).click();
      }
      e.stopPropagation();
    }
  }
}
</script>

<template lang="pug">
.file-input-wrapper(@click="clickFileInput")
  input.file-input(
    ref="fileInput"
    type="file"
    :value="[]"
    :accept="accept"
    :multiple="multiple"
    :disabled="disabled"
    @click.stop=""
    @change="handleFileInputChange")
  slot
    a-button(type="primary" icon="upload" :disabled="disabled" :loading="loading")
      | 选择文件
</template>

<style lang="stylus" scoped>
.file-input-wrapper
  position relative
  display inline-block
  .file-input
    position absolute
    top 0
    left 0
    display inline
    margin 0
    padding 0
    width 0px
    height 0px
    outline none
    border none
    opacity 0
  button
    *
      pointer-events none
</style>
