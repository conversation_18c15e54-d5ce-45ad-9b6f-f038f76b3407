<script lang="ts">
import { Component, Vue, Prop, Model } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class CurrencyInput extends Vue {
  @Model('change', { type: [Number, String] }) value!: number;
  @Prop({ type: Boolean, default: false }) disabled!: boolean;
  @Prop({ type: Number, default: Infinity }) max!: number;
  @Prop({ type: Number, default: 0 }) min!: number;
  @Prop({ type: Number, default: 2 }) precision!: number;
  @Prop({ type: [Number, String], default: 1 }) step!: number;
  @Prop({ type: String }) placeholder!: string;
  @Prop({ type: String }) name!: string;

  set number(value: number) {
    const val = Number(value);
    if (val || val == 0) {
      if (val <= this.min) {
        this.$emit('change', this.min);
      } else if (val >= this.max) {
        this.$emit('change', this.max);
      } else {
        this.$emit('change', val);
      }
    } else {
      this.$emit('change', null);
    }
  }

  get number() {
    return this.value;
  }

  blur() {
    (this.$refs.input as any).blur();
  }
  focus() {
    (this.$refs.input as any).focus();
  }
  parseInput(value: any) {
    return value.replace(/\$\s?|(,*)/g, '');
  }
  formatInput(value: any) {
    const val = Number(value);
    if (val) {
      if (val <= this.min) {
        return `${this.min}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      } else if (val >= this.max) {
        return `${this.max}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      }
      return `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else if (val == 0) {
      return 0;
    }
    return null;
  }
}
</script>

<template lang="pug">
a-input-number(
  ref='input',
  v-model.number='number',
  :name='name',
  :max='max',
  :min='min',
  :disabled='disabled',
  :placeholder='placeholder',
  :formatter='formatInput',
  :parser='parseInput'
)
</template>

<style lang="stylus" scoped></style>
