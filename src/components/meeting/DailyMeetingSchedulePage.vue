<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import semesterModel, { ISemester } from '@/models/teaching/semester';
import WeekSelector from '@/components/teaching/WeekSelector.vue';
import TeacherRoomStore from '@/store/modules/conference/teacher_room.store';
import { TeacherActivityMeeting } from '@/models/conference/teacher_activity_meeting';
import { ITeacherRoom } from '@/models/conference/teacher_room';
import ActivityMeetingCard from '@/components/conference/ActivityMeetingCard.vue';
import { IActivityMeeting } from '@/models/conference/activity_meeting';

@Component({
  components: {
    WeekSelector,
    ActivityMeetingCard,
  },
})
export default class DailyMeetingSchedulePage extends Vue {
  @Prop({ type: String, default: '一日会议安排' }) title!: string;
  @Prop({ type: Boolean, default: true }) rightActionVisible!: boolean;
  @Prop({ type: Object, default: () => ({}) }) private store!: any;
  @Prop({ type: Array, default: () => ['pending', 'unreviewed', 'reviewed'] }) stateAry!: string[];

  query: IObject = {};
  semester: ISemester = {};
  meetings: IObject[] = [];
  roomToMeetings: object = {};
  loading: boolean = false;
  mode: string = 'daily';
  options: any[] = [
    { label: '日历视图', value: 'daily' },
    { label: '列表视图', value: 'list' },
  ];
  theDate: string = this.$moment().format('YYYY-MM-DD');
  times: string[] = [
    '8:00',
    '9:00',
    '10:00',
    '11:00',
    '12:00',
    '13:00',
    '14:00',
    '15:00',
    '16:00',
    '17:00',
    '18:00',
    '19:00',
    '20:00',
    '21:00',
    '22:00',
  ];
  indexToWeekday = ['周日', '周一', '周二', '周三', '周四', '周五', '周六', '周日'];
  rooms: ITeacherRoom[] = [];
  semesterBeginOn = '';

  mounted() {
    this.fetchCurrentSemester();
    this.fetchMeetingRooms();
    this.fetchMeetings();
  }

  get roomStore() {
    return TeacherRoomStore;
  }

  get week() {
    return Math.floor(this.$moment(this.theDate).diff(this.$moment(this.semesterBeginOn)) / (3600 * 24 * 7 * 1000)) + 1;
  }

  async fetchCurrentSemester() {
    const role = this.$store.getters.currentRole;
    const { data } = await semesterModel.setRole(role).current();
    this.semesterBeginOn = data.begin_on as string;
  }

  async fetchMeetings() {
    this.loading = true;
    await this.store.fetch({
      page: 1,
      per_page: 999999,
      q: {
        date_eq: this.theDate,
        state_in: this.stateAry,
        ...this.query,
      },
    });
    this.meetings = this.store.records;
    this.loading = false;
    // this.meetings = [
    //   {
    //     id: 68,
    //     created_at: '2020-04-08T15:39:21.000+08:00',
    //     updated_at: '2020-04-09T13:11:45.000+08:00',
    //     date: '2020-05-18',
    //     title: '专题会议专题会议专题会议专题会议专题会议',
    //     desc: null,
    //     meeting_room_id: 1,
    //     begin_time: '2020-04-08T15:00:00.000+08:00',
    //     end_time: '2020-04-08T16:00:00.000+08:00',
    //     reserver_type: 'Teacher',
    //     reserver_id: 83,
    //     meta: { topic: null, files: [], meeting_type: null },
    //     type: 'Meeting::ActivityMeeting',
    //     location: null,
    //     school_id: 1,
    //     published: false,
    //     limit_count: null,
    //     meeting_room_name: '503测试',
    //     state: null,
    //     is_over: true,
    //     moderator_names: ['计大威'],
    //     user_names: ['贾璐'],
    //     is_moderator: true,
    //     is_joined: false,
    //   },
    // ];
    this.roomToMeetings = {};
    this.meetings.forEach(meeting => {
      (this.roomToMeetings as any)[meeting.meeting_room_name] = (
        (this.roomToMeetings as any)[meeting.meeting_room_name] || []
      ).concat(meeting);
    });
  }

  onShow(meeting: IObject) {
    this.$emit('show', meeting);
  }

  changeDate(num: number) {
    this.theDate = this.$moment(this.theDate)
      .add(num, 'days')
      .format('YYYY-MM-DD');
    this.fetchMeetings();
  }

  async fetchMeetingRooms() {
    const params = {
      page: 1,
      per_page: 999999,
    };
    await this.roomStore.fetch(params);
    this.rooms = this.roomStore.records;
    this.rooms.push({
      id: undefined,
      name: '其他',
    });
  }

  fillEmpty(meetings: IActivityMeeting[]) {
    const initStartInt = parseInt(this.times[0].replace(':', ''));
    const initEndInt = parseInt(this.times[this.times.length - 1].replace(':', ''));
    var timeIntPoint: number[] = [initStartInt, initEndInt];
    var timeIntToLength = {};

    const timeIntToLengthAry = meetings.forEach(meeting => {
      var beginInt = parseInt(this.$moment(meeting.begin_time).format('HHmm'));
      var endInt = parseInt(this.$moment(meeting.end_time).format('HHmm'));
      if (beginInt % 100 === 30) {
        beginInt = beginInt + 20;
      }
      if (endInt % 100 === 30) {
        endInt = endInt + 20;
      }
      timeIntPoint.push(beginInt);
      timeIntPoint.push(endInt);
      (timeIntToLength as any)[beginInt] = {
        length: endInt - beginInt,
        data: meeting,
      };
    });

    timeIntPoint = this.unique(timeIntPoint).sort((a, b) => a - b);
    const result = timeIntPoint.map((startTimeInt, index) => {
      if ((timeIntToLength as any)[startTimeInt as any] !== undefined) {
        return {
          width: `${(((timeIntToLength as any)[startTimeInt as any] as any).length / 100) * 137}px`,
          empty: false,
          data: ((timeIntToLength as any)[startTimeInt as any] as any).data,
        };
      } else {
        const endTimeInt = (timeIntPoint as any)[index + 1] as any;
        const length = ((endTimeInt as any) - (startTimeInt as any)) as any;
        const width = (length / 100) * 137;

        return {
          width: `${width}px`,
          empty: true,
          data: {
            date: this.theDate,
            // 用100进制，在 Show/New 页面转换
            startTimeInt,
            endTimeInt,
          },
        };
      }
    });
    return result.splice(0, result.length - 1);
  }

  unique(arr: any[]) {
    for (var i = 0; i < arr.length; i++) {
      for (var j = i + 1; j < arr.length; j++) {
        if (arr[i] === arr[j]) {
          //第一个等同于第二个，splice方法删除第二个
          arr.splice(j, 1);
          j--;
        }
      }
    }
    return arr;
  }

  bookMeeting(room: ITeacherRoom, data: object) {
    this.$emit('click-empty', room, data);
    // this.$router.push({
    //   path: '',
    //   query: {
    //     roomId: room.id as any,
    //     roomName: room.name,
    //     from: '日视图',
    //     startTime: (data as any).startTime,
    //     length: (data as any).length,
    //   },
    // });
  }
}
</script>

<template lang="pug">
.daily-meeting-schedule
  TaTitleHeader.toolbar(title="预约会议")
    template(slot="actions")
      a-button-group.date-buttons
        a-button(@click="changeDate(-1)")
          a-icon(type="left")
        a-button.date-botton(ghost)
          span.date {{ this.$moment(theDate).format('MM-DD') }}
        a-button(@click="changeDate(+1)")
          a-icon(type="right")
      .week-info 第 {{ week }} 周
      .week-info {{ indexToWeekday[this.$moment(theDate).day()] }}
    .right-actions(v-show="rightActionVisible")
        Searcher(
          v-model="query"
          :variables="['title']"
          tips="检索会议"
          @change="fetchMeetings")
        slot(name="right-actions")
          MenuSelect.menu-select(
            :options="options"
            v-model="mode")

  .vertical-scroll-body(v-loading='loading', v-show="mode === 'daily'")
    .col-header
      .col-time(v-for="time in times")
        .time-text {{ time }}
        .square-shell
          .square

    .col-body
      .raw-header
        .raw-room(v-for="room in rooms")
          .room-name {{room.name}}
      .body-shell
        .body
          .row(v-for="room in rooms")
            //- | {{ fillEmpty(roomToMeetings[room.name] || []) }}
            .cell(v-for="cellStyle in fillEmpty(roomToMeetings[room.name] || [])" :style="cellStyle")
              ActivityMeetingCard(
                v-show="cellStyle.empty === false"
                :record="cellStyle.data"
                :roomVisiable="false"
                :tagVisible="false"
                :deleteVisible="true"
                @refresh='fetchMeetings'
              )
              a-tooltip(v-show="cellStyle.empty === true" title="预约会议" trigger="hover" arrowPointAtCenter)
                .empty-card(:record="cellStyle.data" @click="bookMeeting(room, cellStyle.data)")
                  .plus-sign +



</template>

<style lang="stylus" scoped>
.date-buttons
  margin 20px 0px 20px 40px
  .date-botton
    margin 0 2px 0 2px
  .date
    color black

.week-info
  padding-left 10px
.right-actions
  .menu-select
    margin-top -10px

.daily-meeting-schedule
  height 100%
  .vertical-scroll-body
    overflow auto
    width 100%
    height 100%
    .col-header
      position sticky
      top 0px
      z-index 1
      display flex
      justify-content flex-start
      margin-left 108px
      width 2055px
      height 40px
      border 1px solid #E8E8E8
      border-radius 4px 4px 0 0
      background-color #FFFFFF
      .col-time
        margin-bottom 0px
        width 137px
        height 100%
        border-width 1px
        border-style solid
        border-color #fff #E8E8E8 #fff #fff
        .time-text
          text-align center
          line-height 35px
        .square-shell
          display flex
          justify-content center
          .square
            width 0
            height 0
            border-width 2px
            border-style solid
            border-color #3DA8F5 #E8E8E8 #E8E8E8 #E8E8E8
    .col-body
      display flex
      justify-content flex-start
      width 2055px
      .fig-leaf
        position absolute
        z-index 2
        margin-top -40px
        width 105px
        height 100%
        background-color white
      .raw-header
        position sticky
        left 0px
        z-index 3
        margin-right 13px
        width 100px
        height 400px
        .raw-room
          margin 0px 0px 6px -1px
          width 96px
          height 145px
          border-radius 4px
          background-color #EDF7FF
          color #3DA8F5
          // text-align center
          // line-height 145px
          display flex
          justify-content center
          align-items center
          .room-name
            width 80%
            text-align center

      .body-shell
        width 100%
        .body
          width 100%
          border 1px solid #E8E8E8
          .row
            margin-bottom 6px
            padding-left 68px
            width 100%
            height 145px
            background-color #F5F5F5
            .cell
              position relative
              z-index 0
              display block
              float left
              overflow hidden
              padding 2px
              height 100%
              .empty-card
                display table
                display flex
                justify-content center
                align-items center
                height 100%
                .plus-sign
                  display none
                  color #A6A6A6
                  font-size 40px
                &:hover
                  background-color #E5E5E5
                  .plus-sign
                    display block
</style>
