<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import MeetingSchedule from '@/components/meeting/MeetingSchedule.vue';
import semesterModel, { ISemester } from '@/models/teaching/semester';
import WeekSelector from '@/components/teaching/WeekSelector.vue';
import ActivityMeetingCard from '@/components/conference/ActivityMeetingCard.vue';

@Component({
  components: {
    MeetingSchedule,
    WeekSelector,
    ActivityMeetingCard,
  },
})
export default class WeekMeetingSchedule extends Vue {
  @Prop({ type: String, default: '一周会议安排' }) title!: string;
  @Prop({ type: Object, default: () => ({}) }) private store!: any;
  @Prop({ type: Object, default: () => ({}) }) private moreQuery!: any;
  @Prop({ type: Boolean, default: false }) recordChangeFlag!: any;
  @Prop({ type: Boolean, default: true }) rightActionVisible!: boolean;
  @Prop({ type: Array, default: () => ['pending', 'unreviewed', 'reviewed'] }) stateAry!: string[];

  query: IObject = {};
  semester: ISemester = {};
  week: number = 1; // 当前周
  startDate: string = '';
  endDate: string = '';
  meetings: IObject[] = [];
  loading: boolean = false;
  mode: string = 'schedule';
  options: any[] = [
    { label: '日历视图', value: 'schedule' },
    { label: '模块视图', value: 'list' },
  ];

  @Watch('recordChangeFlag', { immediate: true }) onRecordChangeFlagChange() {
    this.fetchMeetings();
  }

  created() {
    this.fetchCurrentSemester();
  }

  async fetchCurrentSemester() {
    const role = this.$store.getters.currentRole;
    const { data } = await semesterModel.setRole(role).current();

    this.semester = data;
    this.week = data.current_week || 0;
    this.fetchMeetings();
  }
  async fetchMeetings() {
    if (!this.semester.begin_on) return;

    this.startDate = this.$moment(this.semester.begin_on)
      .add((this.week - 1) * 7, 'days')
      .format('YYYY-MM-DD');
    this.endDate = this.$moment(this.semester.begin_on)
      .add(this.week * 7 - 1, 'days')
      .format('YYYY-MM-DD');

    this.store.init({
      params: {
        ...this.moreQuery,
        q: {
          s: ['date asc', 'begin_time asc'],
          date_lteq: this.endDate,
          date_gteq: this.startDate,
          state_in: this.stateAry,
        },
      },
    });
    await this.store.index({
      page: 1,
      per_page: 999999,
      q: {
        ...this.query,
      },
    });
    this.meetings = this.store.records;
  }

  onShow(meeting: IObject) {
    this.$emit('show', meeting);
  }
}
</script>

<template lang="pug">
.week-meetings
  TaTitleHeader.toolbar(:title='title')
    template(slot='actions')
      WeekSelector(v-model='week', :max='semester.weeks_count', @change='fetchMeetings')
    .right-actions(v-show='rightActionVisible')
      Searcher(v-model='query', :variables='["title"]', tips='检索会议', @change='fetchMeetings')
      slot(name='right-actions')
        MenuSelect(:options='options', v-model='mode')
        TaExport(:store='store')
          | 导出本周
        TaExport(:store='store', :exportAll='true')
          | 导出全部
  .schedule-content
    slot(:meetings='meetings', :loading='loading')
      MeetingSchedule(v-show='mode === \'schedule\'', :meetings='meetings', :startDate='startDate', @show='onShow')
        template(#card-more='{ meeting }')
          slot(name='card-more', :meeting='meeting')
      .meetings(v-show='mode === \'list\'')
        .meeting(v-for='meeting in meetings', @click='onShow(meeting)')
          ActivityMeetingCard(:record='meeting', :deleteVisible='true', @refresh='fetchMeetings')
            template(#more='{ meeting }')
              slot(name='card-more', :meeting='meeting')
</template>

<style lang="stylus" scoped>
.week-meetings {
  padding-bottom: 10px;
  height: 100%;

  .toolbar {
    .right-actions {
      display: inline-flex;
      align-items: center;

      .select {
        width: 100px;
      }
    }
  }

  .schedule-content {
    padding: 10px 0 0;
    height: calc(100% - 58px);
  }
}

.meetings {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;

  .meeting {
    margin: 4px;
    width: 30%;
  }
}
</style>
