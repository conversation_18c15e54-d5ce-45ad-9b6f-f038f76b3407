<script lang="ts">
/**
 * 会议室日历组件
 */
import { Component, Vue, Prop } from 'vue-property-decorator';
import MeetingScrollTable from '@/components/meeting/MeetingScrollTable.vue';
import { IScrollTableRecord, IScrollTableRecordItem } from '../teaching/scrollTablle.interface';
import ActivityMeetingCard from '@/components/conference/ActivityMeetingCard.vue';
import { IActivityMeeting, ActivityMeeting } from '@/models/conference/activity_meeting';
import moment from 'moment';

@Component({
  components: {
    MeetingScrollTable,
    ActivityMeetingCard,
  },
})
export default class MeetingSchedule extends Vue {
  @Prop({ type: Array, default: () => [] }) meetings!: IObject[];
  @Prop({ type: Boolean, default: false }) loading!: boolean;
  @Prop({ type: String, default: '' }) startDate!: string;

  columns: IObject[] = [
    { index: 1, title: '上午' },
    { index: 2, title: '下午' },
    { index: 3, title: '晚上' },
  ];

  get tableRecords(): IScrollTableRecord[] {
    const dateToMeetings: object = {};
    this.meetings.forEach(meeting => {
      (dateToMeetings as any)[meeting.date] = ((dateToMeetings as any)[meeting.date] || []).concat(meeting);
    });
    if (this.startDate.length > 1) {
      return [0, 1, 2, 3, 4, 5, 6].map(index => {
        const date = this.$moment(this.startDate)
          .add(index, 'days')
          .format('YYYY-MM-DD');
        const day = this.$moment(date).format('MM月DD日');
        const week = this.$moment(date).format('dddd');
        return {
          key: date,
          head: { date: day, week: week },
          items: (((dateToMeetings as any)[date] || []) as IActivityMeeting[]).map(meeting => {
            return {
              start_unit: this.getOffestUnit(meeting.begin_time || ''),
              end_unit: this.getOffestUnit(meeting.end_time || ''),
              meeting: meeting,
            };
          }),
        };
      });
    } else {
      return [];
    }
    // 只需要写一个方法，把 meeting 数据转换为下面的结构即可
    // return [
    //   {
    //     key: '2020-04-01', // 必填
    //     head: {
    //       date: '4月1日',
    //       week: '周一',
    //     },
    //     items: [
    //       {
    //         start_unit: 1, // 必填
    //         end_unit: 1, // 必填
    //         meeting: {}, // 其他属性随意
    //       },
    //       {
    //         start_unit: 1, // 必填
    //         end_unit: 1, // 必填
    //         meeting: {}, // 其他属性随意
    //       },
    //     ],
    //   },
    //   {
    //     key: '2020-04-02',
    //     head: {
    //       date: '4月2日',
    //       week: '周二',
    //     },
    //     items: [
    //       {
    //         start_unit: 2,
    //         end_unit: 2,
    //         meeting: {},
    //       },
    //     ],
    //   },
    //   {
    //     key: '2020-04-03',
    //     head: {
    //       date: '4月3日',
    //       week: '周三',
    //     },
    //     items: [
    //       {
    //         start_unit: 3,
    //         end_unit: 3,
    //         meeting: {},
    //       },
    //     ],
    //   },
    // ];
  }

  getOffestUnit(time: string) {
    const timeStr = parseInt(this.$moment(time).format('HHmm'));
    if (timeStr > 1600) {
      return 3;
    } else {
      if (timeStr > 1200) {
        return 2;
      } else {
        return 1;
      }
    }
  }

  getColHeadStyle(index: number) {
    const styles: IObject = { borderBottom: '1px solid #e8e8e8', borderRight: '1px solid rgba(232, 232, 232, 0.3)' };
    if (index === 0) {
      return { ...styles, background: '#68B4EB' };
    }
    if (index === 1) {
      return { ...styles, background: '#F9AF36' };
    }
    return { ...styles, background: '#2F4169' };
  }
  getCellStyle(index: number) {
    switch (index) {
      case 0:
        return {
          borderLeft: '1px dashed #68B4EB',
          borderRight: '1px dashed #68B4EB',
          borderBottom: '1px solid #e8e8e8',
        };
      case 1:
        return {
          borderRight: '1px dashed #F9AF36',
          borderBottom: '1px solid #e8e8e8',
        };
      case 2:
        return {
          borderRight: '1px dashed #2F4169',
          borderBottom: '1px solid #e8e8e8',
        };
      default:
        return {
          borderRight: '1px dashed #e8e8e8',
          borderBottom: '1px solid #e8e8e8',
        };
    }
  }
  onShow(item: IScrollTableRecordItem) {
    this.$emit('show', item.meeting);
  }
}
</script>

<template lang="pug" scoped>
MeetingScrollTable.component-meeting-schedule(:records="tableRecords" :columns="columns" :loading="loading")
  template(#colHead="{ data, colIndex }")
    .col-head(:style="getColHeadStyle(colIndex)")
      .title {{ data.title }}
  template(#rowHead="{ data }")
    .week-day
      .date {{ data.week }}
      .date {{ data.date }}
  template(#grid-cell="{ colIndex }")
    .grid-cell(:style="getCellStyle(colIndex)")
  template(#default="{ data }")
    .meeting-cell(@click="onShow(data)")
      ActivityMeetingCard.meeting-component(
       :record="data.meeting" roomVisiable=true
      )
        template(#more="{ meeting }")
          slot(name="card-more" :meeting="meeting")
</template>

<style lang="stylus">
.component-meeting-schedule
  .grid-cell
    width 100%
    height 100%
    border-right 1px dashed rgba(232, 232, 232, 1)
  .col-head
    padding 10px 6px
    height 100%
    border-right 1px solid rgba(232, 232, 232, 0.3)
    color #fff
    .title
      text-align center
      letter-spacing 0
      font-weight 500
      // font-size 14px
      line-height 20px
  .week-day
    padding 20px 0
    width 100px
    text-align center
    .date
      margin-bottom 4px
      color rgba(128, 128, 128, 1)
      font-weight 500
      // font-size 14px
      line-height 20px
  .meeting-cell
    padding 6px
</style>
