<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { informTemplate } from './informTemplate';
import InformNewsStore from '@/store/modules/inform/news.store';
import InformAdviseStore from '@/store/modules/inform/advise.store';
import newsStore from '@/store/modules/inform/news.store';
import adviseStore from '@/store/modules/inform/advise.store';
import TemplateForm from '@/components/form/TemplateForm.vue';
import FileServer from '@/models/file';

@Component({
  components: {
    TemplateForm,
  },
})
export default class InformForm extends Vue {
  @Prop({ type: Object, default: () => ({}) }) store!: IObject;
  @Prop({ type: String, default: '', required: true }) informZh!: string;
  @Prop({ type: String, default: '', required: true }) informType!: string;
  @Prop({ type: String, default: '', required: true }) routeKey!: string;

  template: IObject[] = informTemplate;
  formData: IObject = {};

  get breadcrumbs() {
    const bc = [
      { title: '校园综合服务平台', url: '/education/home' },
      { title: this.informZh, url: `/portal/${this.routeKey}` },
    ];
    if (this.$route.params.id) {
      bc.push({ title: `编辑${this.informZh}`, url: '' });
    } else {
      bc.push({ title: `创建${this.informZh}`, url: '' });
    }
    return bc;
  }

  get isEdit() {
    return !!this.$route.params.id;
  }

  mounted() {
    if (this.isEdit) {
      this.fetchData();
    }
  }

  async fetchData() {
    const { data } = await this.store.find(this.$route.params.id);
    this.formData = {
      ...this.formData,
      ...data,
    };
  }

  onUpdate() {
    (this.$refs as any).form.submit({
      success: async (formData: IObject) => {
        if (this.$route.query.mod_id) {
          formData.inform_mod_id = this.$route.query.mod_id;
        }
        if ((typeof formData.cover_image as any) === 'array') {
          formData.cover_image = new FileServer().getThumbnailUrl(formData.cover_image[0]);
        }
        await this.store
          .update(formData)
          .then(() => {
            this.$message.success('保存成功');
            this.$router.go(-1);
          })
          .catch((err: any) => {
            this.$message.error('保存失败', err);
          });
      },
    });
  }

  onCreate() {
    (this.$refs as any).form.submit({
      success: async (formData: IObject) => {
        if (this.$route.query.mod_id) {
          formData.inform_mod_id = this.$route.query.mod_id;
        }
        if ((typeof formData.cover_image as any) === 'array') {
          formData.cover_image = new FileServer().getThumbnailUrl(formData.cover_image[0]);
        }
        await this.store
          .create(formData)
          .then(() => {
            this.$message.success('保存成功');
            this.$router.go(-1);
          })
          .catch((err: any) => {
            this.$message.error('保存失败', err);
          });
      },
    });
  }
}
</script>

<template lang="pug">
.container
  StepToolbar(mode='breadcrumbs' :breadcrumbs="breadcrumbs")
  .background
    .body
      TemplateForm.form(:template="template" ref="form" :formData="formData")
  .footer
    .actions
      a-button(type='primary' @click="() => { isEdit ? onUpdate() : onCreate() }") {{ isEdit ? '保存' : '保存为草稿' }}

</template>

<style lang="stylus" scoped>
.container
  width 100%
  height 100%
  .background
    display flex
    justify-content center
    margin 11px 60px 0 60px
    width 100%
    height 100%
    background-color white
    .body
      overflow-y scroll
      margin-top 34px
      width 60%
      height 100%
      .form
        margin-bottom 60px
  .footer
    position fixed
    bottom 0
    z-index 999
    display flex
    justify-content center
    align-items center
    width 100%
    height 60px
    border-top 1px solid #D8D8D8
    background-color white
    .actions
      display flex
      justify-content flex-end
      width 60%
</style>
