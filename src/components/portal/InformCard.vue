<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class InformCard extends Vue {
  @Prop({ type: Object, default: () => ({ title: '' }) }) record!: IObject;
  @Prop({ type: String, default: '' }) title!: string;

  richTextFormat(value: string) {
    value = value.replace(/<\/?.+?>/g, '');
    value = value.replace(/\s+/g, '');
    value = value.replace(/&nbsp/g, '');
    return value;
  }
}
</script>

<template lang="pug">
  .inform-card
    .header.flex-between
      p {{ title }}
    .body(v-if="record.title")
      .title {{ record.title }}
      .content.two-line {{ richTextFormat(record.content) }}
      .footer-group.flex-between
        .time {{ $moment(record.publish_time).format('YYYY-MM-DD') }}
        .action
          slot(name="action" :record="record")
    Empty(v-else type="survey")
</template>

<style lang="stylus" scoped>
.inform-card
  margin-top 20px
  border 1px solid rgba(0, 0, 0, 0.05)
  border-radius 4px
  .header
    padding 16px 20px
    border-bottom 1px solid #E5E5E5
  .body
    padding 18px 20px
    .title
      margin-bottom 10px
      font-weight 500
      font-size 16px
      line-height 24px
    .content
      margin-bottom 10px
      color #666666
      font-weight 400
      font-size 14px
      line-height 20px
    .time
      color #B2B2B2
      font-weight 500
      font-size 14px
      line-height 20px
  .empty
    padding 18px 20px
</style>
