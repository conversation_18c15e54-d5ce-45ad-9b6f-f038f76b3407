<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import InformNewsStore from '@/store/modules/inform/news.store';
import InformAdviseStore from '@/store/modules/inform/advise.store';
import InformModStore from '@/store/modules/inform/mod.store';
import { IMod } from '@/models/inform/mod';
import FileServer from '@/models/file';
import InformPaginationList from './InformPaginationList.vue';

@Component({
  components: {
    InformPaginationList,
  },
})
export default class InformMine extends Vue {
  tabs: IObject[] = [];
  tabKey: string = '';
  perPage: number = 5;
  top: IObject = {};
  menus: IObject[] = [
    { label: '全部', value: 'all' },
    { label: '审核中', value: 'unreviewed' },
    { label: '已发布', value: 'reviewed' },
    { label: '草稿箱', value: 'draft' },
  ];
  activeMenuKey: string[] = ['all'];

  @Prop({ type: String, default: '', required: true }) modType!: string;
  @Prop({ type: Object, default: () => ({}), required: true }) store!: any;
  @Prop({ type: String, default: '', required: true }) informZh!: string;
  @Prop({ type: String, default: '', required: true }) informType!: string;
  @Prop({ type: String, default: '', required: true }) routeKey!: string;

  get breadcrumbs() {
    return [
      { title: '校园综合服务平台', url: '/education/home' },
      { title: this.informZh, url: `/portal/${this.routeKey}` },
      { title: '我创建的', url: '' },
    ];
  }

  async mounted() {
    await this.fetchMods();
    this.fetchData();
  }

  get modStore() {
    return InformModStore;
  }

  get stateQuery() {
    switch (this.activeMenuKey[0]) {
      case 'all':
        return {};
      case 'unreviewed':
        return { state_eq: '审核中' };
      case 'reviewed':
        return { state_eq: '已发布' };
      case 'draft':
        return { state_eq: '草稿箱' };
    }
    return {};
  }

  async fetchMods() {
    const { data } = await this.modStore.fetch({ q: { school_id_eq: this.$store.state.currentUser.school_id } });
    const mods = (data.inform_mods as IMod[]).filter(mod => mod.type === this.modType);
    this.tabs = mods.map(mod => ({ key: `${mod.id}`, title: mod.title }));
    this.tabKey = mods[0] ? `${mods[0].id}` : '';
  }

  fetchData(page = 1, query = {}, perPage = 15) {
    this.store.fetch({
      page,
      per_page: this.perPage,
      q: {
        inform_mod_id_eq: +this.tabKey,
        creator_id_eq: this.$store.state.currentUser.id,
        creator_type_eq: 'Teacher',
        ...this.stateQuery,
        ...query,
      },
    });
  }

  changeTab(key: number) {
    this.fetchData();
  }

  onShow(record: IObject) {
    this.$router.push(`/portal/${this.routeKey}/${record.id}`);
  }

  async onDelete(record: IObject) {
    await this.store.delete(record.id);
    this.fetchData(this.store.currentPage);
  }

  onEdit(record: IObject) {
    this.$router.push(`/portal/${this.routeKey}/${record.id}/edit`);
  }

  onCreate() {
    this.$router.push(`/portal/${this.routeKey}/create?mod_id=${this.tabKey}`);
  }

  onMenuChange(val: string[]) {
    this.activeMenuKey = val;
    this.fetchData();
  }

  stateTagClass(record: IObject) {
    switch (record.state) {
      case '已发布':
        return 'blue-tag';
      case '草稿箱':
        return 'gray-tag';
      case '未发布':
        return 'gray-tag';
      case '审核中':
        return 'green-tag';
    }
  }
}
</script>

<template lang="pug">
.inform-mine
  StepToolbar(:breadcrumbs='breadcrumbs', :steps='tabs', mode='tabs', v-model='tabKey', @change='changeTab')
  .background
    a-menu.menu(
      :selectedKeys='activeMenuKey',
      @selectChange='onMenuChange',
      mode='inline',
      :default-selected-keys='["all"]'
    )
      a-menu-item(v-for='menu in menus', :key='menu.value')
        span {{ menu.label }}
    InformPaginationList.list(:store='store', :informZh='informZh', :onShow='onShow', @change='fetchData')
      template(#rightTop='{ record }')
        .state-tag(:class='stateTagClass(record)') {{ record.state }}
      template(#rightBottom='{ record }')
        .actions
          .text-gray.edit(@click.stop='onEdit(record)') 编辑
          .text-gray.delete(@click.stop='onDelete(record)') 删除
</template>

<style lang="stylus" scoped>
.inform-mine
  width 100%
  height 100%
  .background
    display flex
    overflow scroll
    margin 16px 60px 52px 60px
    padding 10px 24px 26px 24px
    height 85%
    background-color white
    .menu
      width 240px
      height 200px
      border 1px solid rgba(0, 0, 0, 0.1)
    .list
      margin-left 40px

.delete
  padding 0 10px

.state-tag
  display flex
  justify-content center
  align-items center
  width 52px
  height 20px
  border 1px solid #3DA8F5
  border-radius 4px
  color white
  font-size 14px

.green-tag
  background #50B18C

.gray-tag
  background #D8D8D8

.blue-tag
  background #3DA8F5
</style>
