<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import InformList from './InformList.vue';

@Component({
  components: {
    InformList,
  },
})
export default class InformPaginationList extends Vue {
  @Prop({ type: Object, default: () => ({}) }) store!: any;
  @Prop({ type: String, default: '', required: true }) informZh!: string;

  query: IObject = {};

  change(page = 1, query = {}, perPage = 15) {
    this.$emit('change', page, { ...query, ...this.query }, perPage);
  }

  onShow(record: IObject) {
    this.$emit('onShow', record);
  }
}
</script>

<template lang="pug">
.container
  .left-part
    TaTitleHeader.header(:title='`${informZh}列表`')
      Searcher(
        v-model='query',
        :initValue='$route.query.catalog',
        :variables='["title", "catalog", "tags_name"]',
        :tips='`检索${informZh}`',
        @change='change(1)'
      )
    .content-contianer
      InformList(:records='store.records', @onShow='onShow')
        template(#rightTop='{ record }')
          slot(name='rightTop', :record='record')
        template(#rightBottom='{ record }')
          slot(name='rightBottom', :record='record')

    .pagination
      a-pagination(
        showQuickJumper,
        size='large',
        :current='store.currentPage',
        :defaultPageSize='store.perPage',
        :total='store.totalCount',
        @change='change'
      )
</template>

<style lang="stylus" scoped>
.container
  padding-right 4%
  width 100%
  .header
    margin-bottom -15px
  .content-contianer
    padding 10px 0
</style>
