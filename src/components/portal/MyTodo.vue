<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { IInstance, InstanceType } from '../../models/bpm/instance';
import { IAdminTofu } from '../../models/tofu/adminApp';
import InstanceStore from '@/store/modules/bpm/instance.store';
import InstanceDetailDialog from '../bpm/InstanceDetailDialog.vue';

@Component({
  components: {
    InstanceDetailDialog,
  },
})
export default class PortalMyTodo extends Vue {
  @Prop({ type: Array, default: () => [], required: true }) private tofuInfo!: any;

  typeToInfoMap: IObject = {};
  visible: boolean = false;
  selectedInstance: IInstance = {};

  get store() {
    return InstanceStore;
  }

  get map() {
    return {
      'Bpm::Instance': '业务流程申请',
      'Ems::CourseSetInstance': '课程教学平台',
      'Exam::ActivityInstance': '考试',
      'Finance::LoanVoucherInstance': '财务报销',
      'Finance::ProjectInstance': '财务报销',
      'Finance::VoucherInstance': '财务报销',
      'Hr::ModificationInstance': '人事管理',
      'Meeting::ApplicationFormInstance': '会议室预约',
      'Wechat::Instance': '微信矩阵管理',
      'Studying::Welcome::Instance': '迎新',
      'Teaching::ScheduleCourseInstance': '教学计划申请',
    };
  }

  fetchInstances() {
    this.store.fetch({
      per_page: 5,
      q: { approving: [this.$store.state.currentUser.id, 'Teacher'] },
    });
  }

  @Watch('tofuInfo')
  tofuInfoChange() {
    Object.keys(this.map).forEach(key => {
      const name = (this.map as IObject)[key];
      const info = this.tofuInfo.filter((tofu: IAdminTofu) => tofu.name === name)[0] || {};
      if (info.name || info.image) {
        this.typeToInfoMap[key] = {
          name: info.name,
          image: info.image,
        };
      }
    });
  }

  mounted() {
    this.fetchInstances();
  }

  getInfo(type: string) {
    return this.typeToInfoMap[type] || {};
  }

  more() {
    window.open(`${process.env.VUE_APP_PUBLIC_PATH}portal/all_instances`);
  }

  showInstance(instance: IInstance) {
    this.selectedInstance = instance;
    this.visible = true;
  }
}
</script>

<template lang="pug">
.portal-my-todo
  TaTitleHeader.title(title='我的待办')
  .buttons
    .review.flex-between
      .left-part.flex
        .image-circle
          img(src='@/assets/icons/portal/review_blue.png')
        p 审批
      .right-part
        .text-circle {{ store.totalCount }}
  .instances
    .instance(v-for='instance in store.records', @click='showInstance(instance)')
      img.tofu-image(v-if='getInfo(instance.type).image', :src='getInfo(instance.type).image')
      img.tofu-image(v-else, src='@/assets/icons/portal/tofu_placeholder.png')
      .content
        .tofu-info.flex-between
          .tofu-title.text-gray(v-if='getInfo(instance.type).name') {{ getInfo(instance.type).name }}
          .tofu-title.text-gray(v-else) 业务流程申请
          .time.text-gray {{ $moment(instance.created_at).format("MM-DD HH:mm") }}
        .body {{ instance.workflow_name }}
    .more
      TextButton.text-gray(icon='ellipsis', @click='more') 更多
  InstanceDetailDialog(title='待办详情', v-model='visible', :instanceId='selectedInstance.id')
</template>

<style lang="stylus" scoped>
.portal-my-todo
  margin 10px
  .buttons
    margin 10px 0
    .right-part
      .text-circle
        width 20px
        height 20px
        border-radius 50%
        background-color white
        color #3DA8F5
        text-align center
        font-weight 500
        font-size 12px
        line-height 20px
    .review
      padding 10px 12px
      width 100%
      height 48px
      border-radius 8px
      background-color #3DA8F5
      color white
      .image-circle
        display flex
        justify-content center
        align-items center
        margin-right 6px
        width 28px
        height 28px
        border-radius 14px
        background white
        -moz-border-radius 14px
        -webkit-border-radius 14px
        img
          width 14px
          height 14px
  .instances
    .instance
      display flex
      padding 16px 15px 16px 0
      border-bottom 1px solid #E5E5E5
      cursor pointer
      &:hover
        margin-left 10px
        transition 0.5s
      .tofu-image
        margin-right 4px
        width 20px
        height 20px
      .content
        width 100%
        .tofu-info
          width 100%
          .tofu-title
            font-size 14px
  .more
    margin 16px 0
    .text-gray
      margin 0
</style>
