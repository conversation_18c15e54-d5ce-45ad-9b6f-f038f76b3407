import { IFormTemplateItem } from '@/interfaces/formTemplate.interface';
export const informTemplate: IFormTemplateItem[] = [
  {
    key: 'title',
    name: '标题',
    layout: {
      component: 'input',
      placeholder: '请输入标题',
      required: true,
    },
    model: {
      attr_type: 'string',
    },
  },
  {
    key: 'tag_list',
    name: '添加标签',
    layout: {
      component: 'tag',
      placeholder: '请选择',
      required: false,
      options: [],
    },
    model: {
      attr_type: 'array',
    },
  },
  {
    key: 'publish_time',
    name: '发布时间',
    layout: {
      component: 'date',
      required: true,
      placeholder: '请选择发布时间',
    },
    model: {
      attr_type: 'string',
    },
  },
  {
    key: 'cover_image',
    name: '封面图',
    layout: {
      component: 'image',
      required: false,
    },
    model: {
      attr_type: 'array',
    },
  },
  {
    key: 'catalog',
    name: '分类',
    layout: {
      component: 'input',
      placeholder: '请输入分类',
      required: true,
    },
    model: {
      attr_type: 'string',
    },
  },
  {
    key: 'platform',
    name: '可见平台',
    layout: {
      component: 'checkbox',
      required: true,
      options: [
        { label: '首页', value: '首页' },
        { label: '统一门户', value: '统一门户' },
        { label: '学生-首页', value: '学生-首页' },
        { label: '学生-统一门户', value: '学生-统一门户' },
      ],
    },
    model: {
      attr_type: 'array',
    },
  },
  {
    key: 'content',
    name: '编辑内容',
    layout: {
      component: 'rich_text',
      placeholder: '请选中',
      required: true,
    },
    model: {
      attr_type: 'string',
    },
  },
];
