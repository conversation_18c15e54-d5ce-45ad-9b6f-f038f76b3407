<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import InformNewsStore from '@/store/modules/inform/news.store';
import InformAdviseStore from '@/store/modules/inform/advise.store';
import InformModStore from '@/store/modules/inform/mod.store';
import { IMod } from '@/models/inform/mod';
import FileServer from '@/models/file';
import InformCard from './InformCard.vue';
import InformPaginationList from './InformPaginationList.vue';
import { log } from 'util';
import infoStore from '@/store/modules/info.store';

@Component({
  components: {
    InformCard,
    InformPaginationList,
  },
})
export default class InformIndex extends Vue {
  tabs: IObject[] = [];
  tabKey: string = '';
  perPage: number = 5;
  top: IObject = {};

  @Prop({ type: String, default: '', required: true }) modType!: string;
  @Prop({ type: Object, default: () => ({}), required: true }) store!: any;
  @Prop({ type: Object, default: () => ({}), required: true }) topStore!: any;
  @Prop({ type: String, default: '', required: true }) informZh!: string;
  @Prop({ type: String, default: '', required: true }) informType!: string;
  @Prop({ type: String, default: '', required: true }) routeKey!: string;

  get breadcrumbs() {
    return [
      { title: '校园综合服务平台', url: '/education/home' },
      { title: this.informZh, url: '' },
    ];
  }

  async mounted() {
    await infoStore.findAndSetRolePermits();
    await this.fetchMods();
    this.fetchData(1, { catalog_eq: this.$route.query.catalog }, 15);
    this.fetchTop();
  }

  get modStore() {
    return InformModStore;
  }

  get haveTeacherAuth() {
    return this.$utils.hasPermission('portal', 'teacher') || this.haveAdminAuth;
  }

  get haveAdminAuth() {
    return this.$utils.hasPermission('portal', 'admin');
  }

  async fetchMods() {
    const platform_cont = this.haveAdminAuth
      ? {}
      : this.$store.state.currentUser.type === 'Teacher'
      ? { platform_cont: '统一门户-教师' }
      : { platform_cont: '统一门户-学生' };
    const { data } = await this.modStore.fetch({
      q: { school_id_eq: this.$store.state.currentUser.school_id, ...platform_cont },
    });
    const mods = (data.inform_mods as IMod[]).filter(mod => mod.type === this.modType);
    this.tabs = mods.map(mod => ({ key: `${mod.id}`, title: mod.title }));
    this.tabKey = this.$route.query.tabKey ? `${this.$route.query.tabKey}` : mods[0] ? `${mods[0].id}` : '';
  }

  fetchData(page = 1, query = {}, perPage = 15) {
    this.store.fetch({
      page,
      per_page: this.perPage,
      q: { inform_mod_id_eq: +this.tabKey, state_eq: '已发布', ...query },
    });
  }

  async fetchTop() {
    await this.topStore.fetch({
      per_page: 1,
      q: { inform_mod_id_eq: +this.tabKey, top_true: true, state_eq: '已发布' },
    });
    this.top = this.topStore.records[0] || {};
  }

  changeTab(key: number) {
    this.fetchData();
    this.fetchTop();
  }

  async moveToTop(record: IObject) {
    await this.store.update({ id: record.id, top: true });
    this.fetchData(this.store.currentPage);
    this.fetchTop();
  }

  async cancelTop(record: IObject) {
    await this.store.update({ id: record.id, top: false });
    this.fetchData(this.store.currentPage);
    this.fetchTop();
  }

  onShow(record: IObject) {
    const publicPath = process.env.VUE_APP_PUBLIC_PATH;
    window.open(`${window.location.origin}${publicPath}portal/${this.routeKey}/${record.id}`);
  }

  async onDelete(record: IObject) {
    await this.store.delete(record.id);
    this.fetchData(this.store.currentPage);
    this.fetchTop();
  }

  onCreate() {
    this.$router.push(`/portal/${this.routeKey}/create?mod_id=${this.tabKey}`);
  }

  onMine() {
    this.$router.push(`/portal/${this.routeKey}/mine`);
  }

  onReview() {
    this.$router.push(`/portal/instances?type=${this.informType}`);
  }
}
</script>

<template lang="pug">
.inform-index
  StepToolbar(:breadcrumbs='breadcrumbs', :steps='tabs', mode='tabs', v-model='tabKey', @change='changeTab')
  .background
    .left-part
      InformPaginationList(:store='store', :informZh='informZh', @onShow='onShow', @change='fetchData')
        template(#rightTop='{ record }')
          .is-top.text-primary(v-if='record.top') 置顶
        template(#rightBottom='{ record }', v-if='haveAdminAuth')
          .flex-between
            PopoverConfirm(
              title="删除"
              content=`您确认删除吗？`
              @confirm="onDelete(record)"
            )
              .text-gray.delete(@click.stop="") 删除
            .text-gray.to-top(@click.stop='moveToTop(record)', v-if='!record.top') 置顶
    .right-part(v-if='haveTeacherAuth')
      .actions
        .action(@click='onCreate')
          .new
            a-icon(type='highlight', theme='filled', :style='{ color: "#3DA8F5" }')
          .text {{ `新建${informZh}` }}
        .action(@click='onMine')
          .created
            a-icon(type='switcher', theme='filled', :style='{ color: "#FFB252" }')
          .text 我创建的
        .action(@click='onReview')
          .review
            a-icon(type='profile', theme='filled', :style='{ color: "#E5E5E5" }')
          .text 待我审核
      .top
        InformCard(:title='`置顶${informZh}`', :record='top')
          template(#action='{ record }')
            .text-gray.cancel-top(@click.stop='cancelTop(record)') 取消置顶
</template>

<style lang="stylus" scoped>
.inform-index
  width 100%
  height 100%
  .background
    display flex
    overflow scroll
    margin 11px 60px 52px 60px
    padding 10px 24px 26px 24px
    height 85%
    background-color white
    .left-part
      padding-right 4%
      width 100%
      .header
        margin-bottom -15px
      .content-contianer
        padding 10px 0
    .right-part
      width 40%
      height 116px
      .actions
        display flex
        justify-content space-around
        padding 20px 23px 36px 23px
        border 1px solid rgba(0, 0, 0, 0.05)
        .action
          cursor pointer
          .new, .created, .review
            display flex
            justify-content center
            align-items center
            height 43px
          .text
            color #999999
            font-weight 500
            font-size 14px
          &:hover
            .text
              color #333333

.action
  padding 0 10px

.delete
  padding 0 10px

.to-top
  padding-right 10px

.is-top
  width 34px
  height 18px
  border 1px solid rgba(61, 168, 245, 1)
  border-radius 4px
  text-align center
  font-size 10px
  line-height 18px

.cancel-top
  cursor pointer
</style>
