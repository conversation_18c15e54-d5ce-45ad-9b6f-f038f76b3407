<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import FileServer from '../../models/file';

@Component({
  components: {},
})
export default class InformList extends Vue {
  @Prop({ type: Array }) records!: IObject[];
  @Prop({ type: Boolean, default: true }) showCatalog!: boolean;

  parseImage(inform: IObject) {
    return inform.cover_image && typeof inform.cover_image === 'object' && inform.cover_image[0]
      ? new FileServer().getDownloadUrl(inform.cover_image[0])
      : '';
  }

  onShow(inform: IObject) {
    this.$emit('onShow', inform);
  }
}
</script>

<template lang="pug">
.inform-list
  .headline(v-for='inform in records', :key='inform.id', @click='onShow(inform)')
    .header
      .title.one-line {{ inform.title }}
      .actions
        slot(name='rightTop', :record='inform')
    .inform-part1
      img.image(v-if='parseImage(inform)', :src='parseImage(inform)', height='60', width='100')
      .content.three-line {{ inform.content }}
    .inform-part2.flex-between
      .left.gray
        span {{ `浏览 ${inform.view_count}` }}
        span.tag(v-if='inform.catalog && showCatalog') {{ `【${inform.catalog}】` }}
        span.tag {{ `标签 ${inform.tag_list.map((tag) => `#${tag}`).join(" ")}` }}
      .time
        .actions
          slot(name='rightBottom', :record='inform')
        .gray {{ $moment(inform.publish_time).format("YYYY-MM-DD") }}
  slot
</template>

<style lang="stylus" scoped>
.inform-list
  .headline
    display block
    padding 20px 0 22px 0
    border-bottom 1px solid #E8E8E8
    &:last-child
      border-bottom 0px solid white
    .header
      display flex
      justify-content space-between
      .actions
        display flex
      .title
        margin-bottom 14px
        color black
        font-weight 500
        font-size 16px
        line-height 20px
    &:hover
      cursor pointer
      .title
        color rgba(16, 130, 204, 1)
        text-decoration underline
      .inform-part2
        .time
          .actions
            display flex
    .inform-part1
      display flex
      margin-bottom 14px
      height 60px
    .image
      margin-right 12px
    .content
      color #5F5F5F
      font-weight 400
      font-size 14px
      line-height 21px
    .time
      display flex
      min-height 12px
      font-size 12px
      line-height 12px
      .actions
        display none
    .inform-part2
      min-height 12px
      font-size 12px
      line-height 12px
      .tag
        margin-left 16px

.gray
  color #A6A6A6
</style>
