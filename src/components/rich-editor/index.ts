import RichEditor from './RichEditor.vue';

const EditorInstance = RichEditor as any;
EditorInstance.install = (Vue: any, options: IObject) => {
  if (options) {
    EditorInstance.props.type.default = options.type || '';
    EditorInstance.props.editors.default = () => options.editors || {};
    EditorInstance.props.plugins.default = () => options.plugins || [];
  }

  Vue.component('RichEditor', EditorInstance);
};

export default EditorInstance;
