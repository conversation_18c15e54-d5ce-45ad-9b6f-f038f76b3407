<script lang="ts">
import { Component, Vue, Prop, Model } from 'vue-property-decorator';
import CKEditor from '@ckeditor/ckeditor5-vue';
import UploadAdapter from './UploadAdapter';
import DocumentEditor from '@ckeditor/ckeditor5-build-decoupled-document';
import '@/assets/styles/editor.css';
import '@ckeditor/ckeditor5-build-decoupled-document/build/translations/zh-cn';

@Component({
  components: {
    ckeditor: CKEditor.component,
  },
})
export default class RichEditor extends Vue {
  @Model('change', { type: String, default: '' }) value!: string;
  @Prop({ type: String, default: 'document' }) type!: string; // 当前使用的编辑器类型
  @Prop({
    type: Object,
    default: () => ({
      document: DocumentEditor,
    }),
  })
  editors!: IObject; // 多类型编辑器配置
  @Prop({ type: Array, default: () => [] }) plugins!: any[];
  @Prop({ type: Boolean, default: false }) disabled!: boolean;
  @Prop({ type: Number, default: null }) height!: number;
  @Prop({ type: String, default: '请输入内容' }) placeholder!: string;
  @Prop({
    type: Array,
    default: () => [
      'heading',
      'bold',
      'italic',
      'underline',
      'strikethrough',
      'highlight',
      '|',
      'numberedList',
      'bulletedList',
      'blockQuote',
      'alignment',
      '|',
      'link',
      'imageUpload',
      'insertTable',
    ],
  })
  toolbar!: string[];

  instance: any = null;
  focus: boolean = false;

  get editorConfig() {
    return {
      language: 'zh-cn',
      placeholder: this.placeholder,
      extraPlugins: this.plugins,
      toolbar: this.toolbar,
      heading: {
        options: [
          { model: 'paragraph', view: 'p', title: '段落', class: 'ck-heading_paragraph' },
          { model: 'heading1', view: 'h1', title: '一级标题', class: 'ck-heading_heading1' },
          { model: 'heading2', view: 'h2', title: '二级标题', class: 'ck-heading_heading2' },
          { model: 'heading3', view: 'h3', title: '三级标题', class: 'ck-heading_heading3' },
          { model: 'heading4', view: 'h4', title: '四级标题', class: 'ck-heading_heading4' },
        ],
      },
    };
  }
  get editorStyle() {
    return !this.height ? {} : { height: `${this.height}px` };
  }

  created() {
    if (!this.editors[this.type]) {
      throw new Error(
        `The "editors" prop should have the value of prop "type". See more editor builds:
https://ckeditor.com/docs/ckeditor5/latest/examples/builds/classic-editor.html`,
      );
    }
  }
  onReady(editor: any) {
    setTimeout(() => {
      const toolbarContainer = this.$refs.toolbar as Element;
      toolbarContainer.appendChild(editor.ui.view.toolbar.element);
      this.instance = editor;
      this.$emit('ready', editor);
      this.installImageUploaderPlugin();
    }, 50);
  }
  installImageUploaderPlugin() {
    this.instance.plugins.get('FileRepository').createUploadAdapter = (loader: any) =>
      new UploadAdapter(loader, {
        headers: this.$store.getters.getFileAuthHeader,
      });
  }
  onInput(value: string) {
    this.$emit('change', value);
    this.$emit('input', value);
  }
}
</script>

<template lang="pug">
.rich-ckeditor-wrapper
  .rich-editor-content(v-if="!focus" @click="focus = true")
    .ck-content(v-html="value || '点击开始编辑内容'")
    a-icon.edit-content-icon(type="edit")
  .rich-ckeditor-component(v-else)
    .toolbar(ref="toolbar")
    ckeditor.editor(
      :style="editorStyle"
      :value="value || ''"
      :disabled="disabled"
      :editor="editors[type]"
      :config="editorConfig"
      @input="onInput"
      @ready="onReady")
</template>

<style lang="stylus">
.ck-content
  max-height 75vh
  overflow auto
.rich-editor-content
  padding 10px 16px 10px 32px
  cursor pointer
  position relative
  border 1px solid rgb(232, 232, 232)
  border-radius 3px
  .edit-content-icon
    position absolute
    left 10px
    top 14px
    color #aaa
.rich-ckeditor-component
  display flex
  flex-direction column
  max-width 100%
  width 100%
  // max-height 15vh
  border-radius 3px
  border 1px solid rgb(232, 232, 232)

  .toolbar
    flex-shrink 0
    border-bottom 1px solid rgb(232, 232, 232)
  .editor
    box-sizing border-box
    padding 0px 10px
    min-height 100px
    width 100%
    border-radius 0
</style>
