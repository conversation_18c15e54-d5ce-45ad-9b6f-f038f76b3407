<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class ObjectBlock extends Vue {
  @Prop({ type: String, default: '记录' }) name!: string;
  @Prop({ type: String }) title!: string;
  @Prop({ type: String }) desc!: string;
  @Prop({ type: Boolean }) active!: boolean;
  @Prop({ type: Boolean, default: true }) showActions!: boolean;

  onClick(...args: any) {
    this.$emit('click', ...args);
  }
  onDelete() {
    this.$emit('delete');
  }
  onEdit() {
    this.$emit('edit');
  }
}
</script>

<template lang="pug">
.object-block(@click="onClick" :class="{ 'block-active': active }")
  .title.flex-between
    .name {{ title }}
    .actions(@click.stop="" v-if="showActions")
      IconTooltip(icon="edit" tips="编辑" @click="onEdit")
      PopoverConfirm(
        title="删除"
        :content="`确认要删除该${name}吗？`"
        placement="bottomRight"
        @confirm="onDelete")
        IconTooltip(icon="delete" tips="删除")
  .info(v-if="desc")
    | {{ desc }}
  slot
</template>

<style lang="stylus" scoped>
.object-block
  padding 10px
  background #FAFAFA
  border-radius 3px
  margin-bottom 10px
  &:hover
    box-shadow 0px 0px 4px 0px rgba(0,0,0,0.1)
    background #fff
    cursor pointer
    .title
      color #3DA8F5
      .actions
        display block
  .flex-between
    display flex
    align-items center
    justify-content space-between
  .title
    font-size 14px
    font-weight 500
    color rgba(56,56,56,1)
    line-height 20px
    .name
      width 100%
      text-overflow ellipsis
      overflow hidden
      white-space nowrap
    .actions
      flex-shrink 0
      display none
      height 20px
      line-height 20px
  .info
    font-size 12px
    color #999
    margin-top 6px
    &:last-child
      margin-bottom 0

.block-active
  background #edf7ff
  .title
    color #3da8f5
  &:hover
    box-shadow none
    background #edf7ff
    cursor normal
    .title
      color #3da8f5
      .actions
        display block
</style>
