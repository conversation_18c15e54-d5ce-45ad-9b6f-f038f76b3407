<template lang="pug">
.assessment-page
  SideSlot(title="考核")
    .header
      .cell
        a-dropdown
          a-button(size="large")
            span {{ formatStateTitle() }}
            a-icon(type="down")
          a-menu(slot="overlay" style="width: 200px; padding: 4px 0px;")
            a-menu-item(
              v-for="(operation, key) in operations"
              :key="key + 1"
              style="padding: 12px 20px"
              :style="{borderTop: key === 3 ? '1px #E8E8E8 solid' : ''}"
              @click="onFilter(operation.value)")
              .flex-between
                span {{ operation.label }}
                a-icon(type="check" style="color: #A6A6A6;" v-if="entryState === operation.value")
        Searcher(
          v-model="queryObject"
          :variables="['teacher_name', 'teacher_code']"
          placeholder="输入姓名、工号")
      .flex-end
        a-tooltip(title="查看附件" v-if="attachments.length")
          .attachment(@click="visibleAttachment = true")
            a-icon(type="link")
            span 附件{{ attachments.length }}
        .tag(:class="{'done': scoreEntry.score_count === scoreEntry.total_entry_count}")
          template(v-if="scoreEntry.score_count === scoreEntry.total_entry_count")
            a-icon(type="info-circle" theme="filled" style="color: #6DC37D; font-size: 16px")
            span 已考核 {{ scoreEntry.score_count }} 人
          template(v-else)
            a-icon(type="exclamation-circle" theme="filled" style="color: #3DA8F5; font-size: 16px")
            span 已评 {{ scoreEntry.score_count }} 人，
            span 待评 {{ Number(scoreEntry.total_entry_count) - Number(scoreEntry.score_count) }} 人
    .menus(slot="side")
      .menu-item(
        v-for="(menu, index) in scoreEntries"
        :key="index"
        :class="{'menu-item-active': scoreEntry.id === menu.id}"
        @click="fetchEntry(menu.id)")
        .name {{ menu.teacher && menu.teacher.name }}
        .type(v-if="menu.access_score && menu.access_score.state === 'done'")
          span.text-success 已评
          span(style="margin-left: 6px") | {{ menu.access_score.score }} 分
        .type.text-primary(v-else) 待评
    .content
      .info-box(v-if="scoreEntry.teacher && scoreEntry.teacher.id")
        .name.flex-between
          span {{ scoreEntry.teacher.name }}
          span(v-if="scoreEntry.access_score && scoreEntry.access_score.score") {{ scoreEntry.access_score.score }} 分
        .company-info
          span(v-if="entryMeta.total") 总体自评：{{ entryMeta.total }}
          span 工号：{{ scoreEntry.teacher.code }}
          span 部门： {{ scoreEntry.teacher.department_name }}
          span(v-if="entryMeta.duty") 职务：{{ entryMeta.duty }}
        .company-info(v-if="entryMeta.work")
          span 从事或分管工作：{{ entryMeta.work }}
      .notification
        span 在 {{ activity.meta && activity.meta.assessment_start_at | format('YYYY/MM/DD') }} 00:00
        span -{{ activity.meta && activity.meta.assessment_end_at | format('YYYY/MM/DD')}}
        span 23:59「考核阶段」内可对考核结果进行修改。
      .collapse
        .collapse-panel(v-for="(item, index) in questionSets")
          .panel-header
            span {{ item.title }}
            span {{ item.questions.length }}
          .panel-middle
            .question-item(v-for="(question, key) in item.questions" :key="key")
              .question-header
                span(style="color: #FF4F3E") *
                span {{ question.title }}
              .question-middle
                template(v-if="finishScore && !visibleEdit")
                  strong.text-primary 分值：{{ scoreMeta[question.id] || 0 }} 分
                template(v-else)
                  a-input-number(
                    v-model="scoreMeta[question.id]"
                    size="large"
                    :min="0"
                    :max="100"
                    placeholder="分值， 满分100")
        .collapse-panel
          .panel-header.flex-between
            span 总体评价
            span(v-if="visibleEdit && scoreEntry.score_config !== 'normal'") 必填
          .panel-middle(style="padding: 10px 0px")
            template(v-if="finishScore && !visibleEdit")
              strong.text-primary 分值：{{ totalScore || 0 }} 分
            template(v-else)
              a-input-number(
                v-model="totalScore"
                size="large"
                :min="0"
                :max="100"
                :disabled="scoreEntry.score_config === 'normal'"
                placeholder="分值， 满分100")
      .submit
        template(v-if="entryMeta.attachments && entryMeta.attachments.documents")
          a-button(size="large", @click='visibleSelfEvalutionAttachment = true') 自评附件
        template(v-if="finishScore")
          template(v-if="visibleEdit")
            a-button(size="large" @click="visibleEdit = false") 取消修改
            a-button(type="primary" size="large" @click="onSubmit()") 提交修改
          template(v-else)
            a-button(type="primary" size="large" @click="visibleEdit = true") 修改考核
        template(v-else)
          a-button(type="primary" size="large" @click="onSubmit()") 提交

  a-modal(
    title="查看附件"
    v-model="visibleAttachment"
    width="600px"
    :maskClosable="false"
    :footer="false")
    Attachments(
      style="height: 500px"
      :attachments="attachments")

  a-modal(
    title="查看自评附件"
    v-model="visibleSelfEvalutionAttachment"
    width="600px"
    :maskClosable="false"
    :footer="false"
  )
    Attachments(
      style="height: 500px",
      :downloadable='false',
      :attachments="entryMeta.attachments ? entryMeta.attachments.documents : []",
    )
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import SideSlot from '@/components/evaluation/SideSlot.vue';
import Attachments from '@/components/global/Attachments.vue';
import entrySetStore from '@/store/modules/access/entry.store';
import scoreStore from '@/store/modules/access/score.store';

@Component({
  components: {
    SideSlot,
    Attachments,
  },
})
export default class AssessmentTemplate extends Vue {
  @Prop({ type: Object, default: {} }) private activity?: any;
  private queryObject: object = {};
  private questionSets: any[] = [];
  private scoreEntries: any = [];
  private scoreEntry: any = {};
  private scoreMeta: any = {};
  private entryMeta: any = {};
  private totalScore: any = '';
  private entryState: string = 'all';
  private visibleEdit: boolean = false;
  private documents: any[] = [];
  private visibleAttachment: boolean = false;
  private visibleSelfEvalutionAttachment: boolean = false;

  get operations() {
    return [
      {
        label: '全部',
        value: 'all',
      },
      {
        label: '已评',
        value: 'score',
      },
      {
        label: '待评',
        value: 'noscore',
      },
    ];
  }
  get finishScore() {
    return this.scoreEntry.score_count === this.scoreEntry.total_entry_count;
  }
  get attachments() {
    const { documents } = this.activity.attachments || { documents: [] };
    return (documents || []).concat(this.documents);
  }

  @Watch('queryObject')
  public queryChange() {
    this.fetchData();
  }

  @Watch('scoreMeta', { deep: true })
  public watchChange() {
    if (this.scoreEntry.score_config === 'normal') {
      const values = Object.values(this.scoreMeta);
      const finish = (values || []).filter((e: any) => typeof e === 'number');
      const res = (finish || []).reduce((sum: number, score: any) => sum + Number(score), 0);
      this.totalScore = res > 0 ? (res / finish.length).toFixed(1) : 0;
    }
  }

  public mounted() {
    this.fetchData();
  }

  public async fetchData() {
    const params = {
      page: 1,
      per_page: 1000,
      parentId: this.activity.id,
      type: this.entryState,
      q: {
        ...this.queryObject,
      },
    };
    const { data } = await entrySetStore.score_entries(params);
    this.scoreEntries = data.access_entries;
    const { documents } = data.attachments || { documents: [] };
    this.documents = documents || [];
    const scoreEntry = data.access_entries[0] || {};
    if (scoreEntry.id) {
      this.fetchEntry(scoreEntry.id);
    }
  }

  public async fetchEntry(id: number) {
    const { data } = await entrySetStore.score_entry(id);
    this.visibleEdit = !(data.access_score && data.access_score.state === 'done');
    this.scoreEntry = data;
    this.entryMeta = data.entry_meta || {};
    const { score, score_meta } = data.access_score;
    this.totalScore = score || '';
    this.questionSets = data.exam || [];
    this.scoreMeta = (data.exam || []).reduce((object: any, set: any) => {
      const res = (set.questions || []).reduce((obj: any, question: any) => {
        obj[question.id] = (score_meta || {})[question.id] || '';
        return obj;
      }, {});
      return {
        ...object,
        ...res,
      };
    }, {});
  }

  public async onSubmit() {
    const values = Object.values(this.scoreMeta);
    const finish = (values || []).every((e: any) => Number(e) >= 0);
    if (this.scoreEntry.score_config !== 'leader' && !finish) {
      this.$message.warning('评分项不能为空！');
      return;
    }
    if (!this.totalScore) {
      this.$message.warning('总体评价不能为空或者0');
      return;
    }
    try {
      const obj = {
        parentId: this.scoreEntry.id,
        score: this.totalScore || '',
        score_meta: this.scoreMeta,
      };
      await scoreStore.update_score(obj);
      this.fetchData();
      this.$message.success('提交成功！');
    } catch (error) {
      this.$message.error('提交失败！');
    }
    this.visibleEdit = false;
  }

  public onFilter(val: string) {
    this.entryState = val;
    this.fetchData();
  }
  public formatStateTitle() {
    return (this.operations.find((e: any) => e.value === this.entryState) || { label: '全部' }).label;
  }
}
</script>

<style lang="stylus" scoped>
.assessment-page
  position relative
  width 100%
  height 100%
  .header
    position absolute
    top 0px
    left 0px
    display flex
    justify-content space-between
    align-items center
    width 100%
    height 51px
    background #fff
    .cell
      display flex
      align-items center
    button
      margin-left 20px
      padding 10px 40px 10px 0px
      border none
    .attachment
      display flex
      align-items center
      margin-right 20px
      font-weight 500px
      cursor pointer
      span
        margin-left 4px
        color #424344
        font-size 12px
        line-height 20px
    .tag
      display flex
      align-items center
      margin-right 20px
      padding 0px 8px
      height 26px
      border-radius 4px
      background #EDF7FF
      color #3DA8F5
      span
        margin-left 4px
        font-size 12px
        line-height 20px
    .done
      background #F0F9F2
      color #75C940
  .menus
    z-index 2
    overflow auto
    width 100%
    height 100%
    .menu-item
      padding 18px 48px 18px 20px
      border-bottom 1px #E5E5E5 solid
      border-left 4px #fff solid
      color #383838
      cursor pointer
      &:hover
        opacity 0.8
      .name
        font-size 14px
        line-height 20px
      .type
        margin-top 8px
        color #A6A6A6
        font-size 14px
        line-height 12px
    .menu-item-active
      border-left 4px #3DA8F5 solid
      color #3DA8F5
  .content
    overflow auto
    padding 18px 18px 100px
    width 100%
    height 100%
    .info-box
      padding 18px 16px
      width 100%
      border-radius 4px
      background #3DACF4
      .name
        color #fff
        font-weight 500
        font-size 16px
        line-height 20px
      .company-info
        margin-top 6px
        color #fff
        font-size 14px
        line-height 20px
        span
          margin-right 40px
    .notification
      margin 16px 0px
      padding 6px 8px
      width 100%
      border-radius 4px
      background #EDF7FF
      color #3DA8F5
      font-size 14px
      line-height 20px
    .collapse
      width 100%
      .collapse-panel
        margin-top 20px
        width 100%
        .panel-header
          padding 12px 16px
          width 100%
          background #EDF7FF
          color #3DA8F5
          font-size 14px
          line-height 20px
        .panel-middle
          background #fff
          .question-item
            width 100%
            .question-header
              padding 20px 0px 10px
              color rgba(38, 38, 38, 0.85)
              font-weight 500
              font-size 14px
              line-height 20px
            .question-middle
              margin-bottom 20px
              color #A6A6A6
              font-weight 500
              font-size 14px
              line-height 20px
  .submit
    position absolute
    bottom 0px
    left 200px
    z-index 1
    display flex
    justify-content flex-end
    box-sizing border-box
    padding 10px 16px
    width calc(100% - 200px)
    border-top 1px #E5E5E5 solid
    background #fff
    button
      width 100px

.ant-input
  padding 0px
  width 724px
  border none
  background none

.ant-input-number-lg
  width 100%
</style>
