<template lang="pug">
.page
  template(v-if="visibleDetail")
    Entry(v-model="value" :entryId="entryId" @change="(e) => { visibleDetail = e }")
  Entries(v-model="value" @click="onShow" v-show="!visibleDetail")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import Entries from '@/components/evaluation/temp/Entries.vue';
import Entry from '@/components/evaluation/temp/Entry.vue';

@Component({
  components: {
    Entries,
    Entry,
  },
})
export default class DetailTemplate extends Vue {
  @Prop({ type: Object, default: () => ({}) }) private value?: any;
  private visibleDetail: boolean = false;
  private entryId?: any = '';

  public onShow(val: any) {
    this.entryId = val.id;
    this.visibleDetail = true;
  }
}
</script>

<style lang="stylus" scoped>
.page
  width 100%
  height 100%
</style>
