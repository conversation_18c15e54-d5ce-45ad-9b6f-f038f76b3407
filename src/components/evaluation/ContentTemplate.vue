<template lang="pug">
.page
  SideSlot(title="考核内容" sideWidth="280px" activeMenuLabel="考核项")
    .header(slot="extension" v-if="question_set.id")
      PopoverForm(
        v-model="editVisible"
        trigger="click"
        placement="top"
        :form="form"
        :template="templates"
        :width="248"
        title="设置"
        submitTitle="确认"
        @change="(val) => { editVisible = val }"
        @submit="submit")
        TextButton(icon="setting" @click="onEdit").edit 设置
      TextButton(icon="plus-circle" theme="filled" @click="addQuestion") 添加评分项
    .menus(slot="side")
      PopoverForm(
        v-model="createVisible"
        trigger="click"
        placement="top"
        :form="form"
        :template="templates"
        :width="248"
        title="新建考核项"
        submitTitle="创建"
        @change="(val) => { createVisible = val }"
        @submit="submit")
        a-dropdown(:trigger="['click']")
          a-button(icon="plus-circle").create 创建考核项
          a-menu(slot="overlay" style="width: 200px; padding: 4px 0px;")
            a-menu-item(
              v-for="(item, key) in types"
              :key="key + 1"
              style="padding: 12px 20px"
              @click="onCreate(item.value)") {{ item.label }}
      .menu-item(
        v-for="(menu, index) in questionSetStore.records"
        :key="index"
        :class="{'menu-item-active': menu.id === question_set.id}"
        @click="onSelect(menu, index)")
        .name {{ menu.title }}
        .type {{ menu.type === 'Access::QuestionSet::System' ? '给定项' : '自评项' }}
        .more
          a-dropdown(placement="bottomCenter")
            a-icon(type="ellipsis")
            a-menu(slot="overlay" style="width: 200px; padding: 4px 0px;")
              a-menu-item(
                v-for="(operation, key) in operations"
                :key="key + 1"
                style="padding: 12px 20px"
                :style="{borderTop: key === 2 ? '1px #E8E8E8 solid' : ''}"
                :disabled="(index === 0 && key === 0) || (index === questionSetStore.records.length -1 && key === 1)"
                @click="onChange(operation.value, menu)")
                span
                  a-icon(:type="operation.icon" style="color: #A6A6A6; margin-right: 6px")
                  span {{ operation.label }}
    .content
      Question(:questions="questions" @refresh="fetchQuestions" @cancel="cancelQuestion")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import SideSlot from '@/components/evaluation/SideSlot.vue';
import Question from '@/components/evaluation/temp/Question.vue';
import questionSetStore from '@/store/modules/access/question_set.store';
import questionStore from '@/store/modules/access/question.store';

@Component({
  components: {
    SideSlot,
    Question,
  },
})
export default class ContentTemplate extends Vue {
  @Prop({ type: Object, default: () => ({}) }) private value?: any;
  private question_set: any = {
    title: '',
    state: '',
    weighting: '',
  };
  private questions: any[] = [];
  private form: any = {
    title: '',
    state: '',
    weighting: '',
  };
  private createVisible: boolean = false;
  private editVisible: boolean = false;
  get questionSetStore() {
    return questionSetStore || {};
  }
  get questionStore() {
    return questionStore || {};
  }
  get types() {
    return [
      {
        label: '给定项',
        value: 'Access::QuestionSet::System',
      },
      {
        label: '自评项',
        value: 'Access::QuestionSet::Normal',
      },
    ];
  }
  get templates() {
    return this.form.type === 'Access::QuestionSet::Normal'
      ? [
          {
            key: 'title',
            label: '自评项',
            widgetType: 'text',
            widget: 'input',
            placeholder: '请输入考核项',
          },
          {
            key: 'max_question_count',
            label: '工作内容数量',
            widgetType: 'number',
            widget: 'input',
            placeholder: '请输入最大数量',
          },
          {
            key: 'max_letter_count',
            label: '工作内容字数',
            widgetType: 'number',
            widget: 'input',
            max: 500,
            placeholder: '请输入最大字数',
          },
        ]
      : [
          {
            key: 'title',
            label: '给定项',
            widgetType: 'text',
            widget: 'input',
            placeholder: '请输入考核项',
          },
        ];
  }
  get operations() {
    return [
      {
        label: '选项上移',
        value: 'Up',
        icon: 'drag',
      },
      {
        label: '选项下移',
        value: 'Down',
        icon: 'drag',
      },
      {
        label: '删除',
        value: 'Delete',
        icon: 'delete',
      },
    ];
  }

  public mounted() {
    questionStore.changeNamespace('admin');
    this.fetchData();
  }

  public async fetchData(page: number = 1) {
    const params = {
      page,
      per_page: 10,
      parentId: this.value.id,
    };
    const { data } = await questionSetStore.fetchByParent(params);
    this.question_set = {
      ...data.access_question_sets[0],
      ...(data.access_question_sets[0] && data.access_question_sets[0].meta),
    };
    if (this.question_set.id) {
      this.fetchQuestions();
    }
  }

  public async fetchQuestions(page: number = 1) {
    const params = {
      page,
      per_page: 10,
      parentId: this.question_set.id,
    };
    const { data } = await questionStore.fetchByParent(params);
    this.questions = data.access_questions;
  }

  public onCreate(val: string) {
    this.form =
      val === 'Access::QuestionSet::Normal'
        ? {
            title: '',
            type: val,
            max_question_count: 12,
            max_letter_count: 30,
          }
        : {
            title: '',
            type: val,
          };
    this.createVisible = true;
  }

  public onEdit() {
    const { meta } = this.question_set;
    this.form =
      this.question_set.type === 'Access::QuestionSet::Normal'
        ? {
            ...this.question_set,
            max_question_count: meta.max_question_count ? meta.max_question_count : 12,
            max_letter_count: meta.max_letter_count ? meta.max_letter_count : 30,
          }
        : {
            ...this.question_set,
          };
    this.editVisible = true;
  }

  public submit(val: any) {
    if (val.id) {
      this.update(val);
    } else {
      this.create(val);
    }
  }

  public async create(val: any) {
    try {
      const obj = {
        ...val,
        meta: {
          max_question_count: val.max_question_count,
          max_letter_count: val.max_letter_count,
        },
        parentId: this.value.id,
      };
      const { data } = await questionSetStore.createByParent(obj);
      this.question_set = {
        ...data,
        ...data.meta,
      };
      this.questions = [];
      this.$message.success('创建成功！');
    } catch (error) {
      this.$message.error('创建失败！');
    }
  }

  public async update(val: any) {
    try {
      const obj = {
        ...val,
        meta: {
          max_question_count: val.max_question_count,
          max_letter_count: val.max_letter_count,
        },
        parentId: this.value.id,
      };
      await questionSetStore.update(obj);
      this.fetchData();
      this.$message.success('操作成功！');
    } catch (error) {
      this.$message.error('操作失败！');
    }
  }

  public onDelete(id: number) {
    this.$confirm({
      title: '确定要继续执行此操作吗?',
      content: '该操作是不可逆的',
      okText: '确定',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        this.delete(id);
      },
      onCancel: () => {},
    });
  }

  public async delete(id: number) {
    try {
      await questionSetStore.delete(id);
      this.fetchData();
      this.$message.success('操作成功！');
    } catch (error) {
      this.$message.error('操作失败！');
    }
  }

  public onSelect(val: any) {
    if (this.question_set !== val.id) {
      this.question_set = val;
      this.fetchQuestions();
    }
  }

  public onChange(type: string, val: any) {
    if (type === 'Delete') {
      this.onDelete(val.id);
    } else if (type === 'Up') {
      const obj: any = {
        id: val.id,
        position: +val.position - 1,
      };
      this.update(obj);
    } else if (type === 'Down') {
      const obj: any = {
        id: val.id,
        position: +val.position + 1,
      };
      this.update(obj);
    }
  }
  // question
  public addQuestion() {
    const val: any = {
      title: '',
      meta: '',
      score: '',
      parentId: this.question_set.id,
    };
    this.questions.push(val);
  }

  public cancelQuestion(index: number) {
    this.questions.splice(index, 1);
  }
}
</script>

<style lang="stylus" scoped>
.page
  margin 0px -20px
  height 100%
  .menus
    overflow auto
    width 100%
    height 100%
    .create
      display flex
      align-items center
      margin 0px
      padding 0px 20px
      width 100%
      height 56px
      border none
      border-bottom 1px #E5E5E5 solid
      color #808080
      &:hover
        color #3DA8F5
    .menu-item
      position relative
      padding 18px 48px 18px 20px
      border-bottom 1px #E5E5E5 solid
      border-left 4px #fff solid
      color #383838
      cursor pointer
      &:hover
        opacity 0.8
      .name
        font-size 14px
        line-height 20px
      .type
        margin-top 8px
        color #A6A6A6
        font-size 14px
        line-height 12px
      .more
        position absolute
        top 0px
        right 0px
        display flex
        justify-content center
        align-items center
        width 48px
        height 100%
    .menu-item-active
      border-left 4px #3DA8F5 solid
      color #3DA8F5
  .content
    width 100%
    height 100%

.edit
  color #A6A6A6
  &:hover
    color #3DA8F5
</style>
