<template lang="pug">
.page
  .page-top
    .title  考核统计
    IconTooltip(tips="导出Excel")
      a-button.export(slot="custom" size="small" v-loading="exportLoading" @click="expertExcel")
        a-icon(type="download")
        span 导出
  .page-middle
    .content-top
      .chart-item(v-for="(item, index) in statistics" :key="index")
        template(v-if="item.type === 'divider'")
          .divider
        template(v-else)
          .key {{ item.label }}
          span.value {{ item.value }}
          span.unit 位
    .content-middle
      a-row(:gutter="20")
        a-col(:span="12")
          a-card(title="各维度平均分")
            G2Histogram(:charData="scopeHistogramData" id="H1")
        a-col(:span="12")
          a-card(title="各考核项平均分")
            G2Histogram(:charData="qstsHistogramData" id="H2")
        a-col(:span="12" v-for="(type, index) in types" :key="index")
          a-card(:title="type.title")
            .list
              a-row(:gutter="20")
                a-col(:span="12" v-for="(item, key) in type.ranks" :key="key")
                  .cell
                    .order(:class="{'first': key === 0, 'second': key === 1, 'third': key === 2}") {{ key + 1 }}
                    a-tooltip(:title="item.department_name")
                      .name {{ item.teacher_name }}
                    .score {{ item.score }}分
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import FileSaver from 'file-saver';
import G2Histogram from '@/components/evaluation/temp/G2Histogram.vue';
import activityStore from '@/store/modules/access/activity.store';

@Component({
  components: {
    G2Histogram,
  },
})
export default class StatisticalTemplate extends Vue {
  @Prop({ type: Object, default: () => ({}) }) private value?: any;
  private statistics: any[] = [];
  private scopeHistogramData: any[] = [];
  private qstsHistogramData: any[] = [];
  private scopes: any[] = [];
  private qsts: any[] = [];
  private exportLoading: boolean = false;
  get config() {
    return [
      {
        label: '被考核人',
        key: 'total_entry_count',
      },
      {
        label: '考核人',
        key: 'total_score_count',
      },
      {
        type: 'divider',
      },
      {
        label: '已自评',
        key: 'total_access_count',
      },
      {
        label: '未自评',
        key: 'total_no_access_count',
      },
      {
        type: 'divider',
      },
      {
        label: '优秀',
        key: 'total_good_count',
      },
      {
        label: '称职',
        key: 'total_competent_count',
      },
      {
        label: '基本称职',
        key: 'total_qualified_count',
      },
      {
        label: '不称职',
        key: 'total_bad_count',
      },
    ];
  }
  get types() {
    return this.scopes.concat(this.qsts);
  }

  public mounted() {
    this.fetchData();
    this.fetchRanks();
  }

  public async fetchData() {
    const { data } = await activityStore.statistics(this.value.id);
    const res = {
      ...data,
      total_no_access_count: Number(data.total_entry_count) - Number(data.total_access_count),
    };
    this.statistics = this.config.map((item: any) => ({
      ...item,
      value: res[item.key],
    }));
    this.scopeHistogramData = (data.scopes || []).map((item: any) => ({
      name: item.name,
      score: item.avg_score || 0,
    }));
    this.qstsHistogramData = (data.qsts || []).map((item: any) => ({
      name: item.title,
      score: item.avg_score || 0,
    }));
  }

  public async fetchRanks() {
    const { data } = await activityStore.ranks(this.value.id);
    this.scopes = (data.scopes || []).map((item: any) => ({
      ...item,
      title: item.name,
    }));
    this.qsts = data.qsts || [];
  }

  public async expertExcel() {
    try {
      this.exportLoading = true;
      const { data } = await activityStore.export(this.value.id);
      FileSaver.saveAs(data.url, data.url.split('/').pop());
      this.exportLoading = false;
    } catch (error) {
      this.exportLoading = false;
      this.$message.error('导出失败');
    }
  }
}
</script>

<style lang="stylus" scoped>
.page
  width 100%
  height 100%
  .page-top
    display flex
    justify-content space-between
    align-items center
    width 100%
    height 56px
    border-bottom 1px #e8e8e8 solid
    .title
      color #383838
      font-weight 500
      font-size 16px
      line-height 20px
    .export
      display flex
      align-items center
      margin-right 16px
      border none
      color #808080
      font-weight 500
      &:hover
        color #3da8f5
  .page-middle
    width 100%
    .content-top
      display flex
      justify-content space-between
      align-items center
      padding 40px 100px
      .divider
        margin 0px 30px
        width 1px
        height 40px
        background #E8E8E8
      .chart-item
        .key
          margin-bottom 12px
          color #A6A6A6
          font-weight 500
          font-size 12px
          line-height 14px
        .value
          color #383838
          font-weight 500
          font-size 30px
          font-family DINCond-Medium, DINCond
          line-height 30px
        .unit
          margin-left 4px
          font-size 12px
          line-height 18px
          colro #808000
    .content-middle
      width 100%
      .ant-card
        margin-bottom 20px
        width 100%
        .list
          overflow auto
          margin -24px
          padding 0px 24px 24px
          height 200px
          .cell
            display flex
            align-items center
            padding 16px 0px
            width 100%
            border-bottom 1px #E8E8E8 solid
            white-space nowrap
            .order
              width 20px
              height 20px
              border-radius 50%
              background #EDF7FF
              color #3DA8F5
              text-align center
              font-size 12px
              line-height 20px
            .first
              background #EBCC7A
              color #fff
            .second
              background #CFD3D8
              color #fff
            .third
              background #DBAC88
              color #fff
            .name
              margin 0px 32px 0px 42px
              width 80px
              color #383838
              font-size 14px
              line-height 20px
              cursor pointer
            .score
              color #808080
              font-size 14px
              line-height 20px
</style>
