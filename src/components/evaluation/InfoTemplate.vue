<template lang="pug">
.page
  a-form(layout="vertical")
    a-form-item(label="考核标题（必填）")
      a-input(
        v-model="value.name"
        size="large"
        :disabled="disabled"
        placeholder="请输入考核标题")
    a-form-item(label="考核时间（必填）")
      a-range-picker(
        size="large"
        style="width:440px"
        :disabled="disabled"
        :value="defaultDate"
        @change="changeDate")
    a-form-item(label="自评阶段（可选）")
      a-range-picker(
        size="large"
        style="width:440px"
        :disabled="disabled"
        :value="selfDefaultDate"
        @change="selfDateRange")
    a-form-item(label="考核阶段（必填）")
      a-range-picker(
        size="large"
        style="width:440px"
        :disabled="disabled"
        :value="assessmentDefaultDate"
        @change="assessmentDateRange")
    a-form-item(label="统计阶段（必填）")
      a-range-picker(
        size="large"
        style="width:440px"
        :disabled="disabled"
        :value="statisticsDefaultDate"
        @change="statisticsDateRange")
    a-form-item(label="考核内容（必填）")
      a-textarea(
        v-model="value.body"
        size="large"
        :disabled="disabled"
        placeholder="请输入考核内容")
    a-form-item(label="考核附件")
      template(v-if="disabled")
        Attachments(
          :attachments="documents")
      template(v-else)
        FileUploader(
          ref="uploader"
          :value="value.attachments.documents"
          @change="onSuccess")
          a-button(type="primary" size="large") 上传附件
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import Attachments from '@/components/global/Attachments.vue';

@Component({
  components: {
    Attachments,
  },
})
export default class InfoTemplate extends Vue {
  @Prop({ type: Object, default: () => ({}) }) private value?: any;
  @Prop({ type: String, default: 'Form' }) private infoType?: string;
  private attachments: object[] = [];
  get disabled() {
    return this.infoType === 'Show';
  }
  get documents() {
    return this.value.attachments && this.value.attachments.documents ? this.value.attachments.documents : [];
  }
  // 考核时间
  get defaultDate() {
    return this.value.start_at && this.value.end_at
      ? [
          this.$moment(this.$moment(this.value.start_at), 'YYYY-MM-DD'),
          this.$moment(this.$moment(this.value.end_at), 'YYYY-MM-DD'),
        ]
      : null;
  }
  // 自评阶段
  get selfDefaultDate() {
    const meta = this.value.meta || {};
    return meta.self_start_at && meta.self_end_at
      ? [
          this.$moment(this.$moment(meta.self_start_at), 'YYYY-MM-DD'),
          this.$moment(this.$moment(meta.self_end_at), 'YYYY-MM-DD'),
        ]
      : null;
  }
  // 考核阶段
  get assessmentDefaultDate() {
    const meta = this.value.meta || {};
    return meta.assessment_start_at && meta.assessment_end_at
      ? [
          this.$moment(this.$moment(meta.assessment_start_at), 'YYYY-MM-DD'),
          this.$moment(this.$moment(meta.assessment_end_at), 'YYYY-MM-DD'),
        ]
      : null;
  }
  // 统计阶段
  get statisticsDefaultDate() {
    const meta = this.value.meta || {};
    return meta.statistics_start_at && meta.statistics_end_at
      ? [
          this.$moment(this.$moment(meta.statistics_start_at), 'YYYY-MM-DD'),
          this.$moment(this.$moment(meta.statistics_end_at), 'YYYY-MM-DD'),
        ]
      : null;
  }

  public changeDate(date: any, dateString: string[]) {
    this.$emit('changeDate', dateString[0], dateString[1]);
  }

  public selfDateRange(date: any, dateString: string[]) {
    this.value.meta = {
      ...this.value.meta,
      self_start_at: dateString[0],
      self_end_at: dateString[1],
    };
  }

  public assessmentDateRange(date: any, dateString: string[]) {
    this.value.meta = {
      ...this.value.meta,
      assessment_start_at: dateString[0],
      assessment_end_at: dateString[1],
    };
  }

  public statisticsDateRange(date: any, dateString: string[]) {
    this.value.meta = {
      ...this.value.meta,
      statistics_start_at: dateString[0],
      statistics_end_at: dateString[1],
    };
  }

  public onSuccess(fileItems: any[]) {
    this.value.attachments.documents = fileItems;
  }
}
</script>

<style lang="stylus" scoped>
.page
  padding 26px 0px
  width 100%
  .ant-form
    margin 0px auto
    width 440px
  .ant-input-number-lg
    width 440px
</style>
