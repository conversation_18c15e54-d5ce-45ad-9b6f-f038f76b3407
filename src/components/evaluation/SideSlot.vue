<template lang="pug">
.component-page
  .left-box(:style="{ minWidth: sideWidth }")
    .side-top
      slot(name='side-header')
        span {{ title }}
    .side-middle
      slot(name="side")
        .side-menu-item(
          v-for="(item, index) in menus"
          :key="index"
          :class="{'active-item': activeKey === item.value}"
          @click="onMenu(item)")
          a-icon(:type="item.icon" style="color: #808080")
          span {{ item.label }}
  .right-box
    .content-top
      slot(name="header")
        label {{ activeMenuLabel || activeMenu.label }}
      slot(name="extension")
    slot
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class SideSlot extends Vue {
  @Prop({ type: String, default: '' }) private title?: string;
  @Prop({ type: Array, default: () => [] }) private menus?: any;
  @Prop({ type: String, default: '' }) private activeKey?: string;
  @Prop({ type: String, default: '' }) private activeMenuLabel?: string;
  @Prop({ type: String, default: '200px' }) private sideWidth?: string;
  private activeMenu: any = {};
  public mounted() {
    this.activeMenu = this.menus[0] || {};
  }

  public onMenu(val: any) {
    this.activeMenu = val;
    this.$emit('change', val);
  }
}
</script>

<style lang="stylus" scoped>
.component-page
  display flex
  width 100%
  height 100%
  .left-box
    padding-top 52px
    min-width 200px
    min-height 100%
    border-right 1px #DDD solid
    .side-top
      float left
      margin-top -52px
      padding 0px 20px
      width 100%
      height 52px
      border-bottom 1px #DDD solid
      color #808080
      font-weight 500
      font-size 16px
      line-height 52px
    .side-middle
      width 100%
      height 100%
      .side-menu-item
        display flex
        align-items center
        padding 16px 20px
        color #808080
        font-weight 500
        font-size 14px
        cursor pointer
        span
          margin-left 8px
      .active-item
        background #F5F5F5
        color #383838
  .right-box
    padding-top 52px
    width 100%
    height 100%
    .content-top
      display flex
      justify-content space-between
      align-items center
      float left
      margin-top -53px
      padding 0px 20px 0px 24px
      width 100%
      height 53px
      border-bottom 1px #DDD solid
      label
        color #383838
        font-weight 500
        font-size 16px
</style>
