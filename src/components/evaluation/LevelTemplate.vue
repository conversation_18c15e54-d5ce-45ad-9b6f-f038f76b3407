<template lang="pug">
.page
  SideSlot(title="考核维度" sideWidth="280px")
    template(slot="header")
      StepToolbar(v-model="tabIndex" :steps="tabs" mode="tabs")
    template(slot="extension" v-if="scope.id")
      PopoverForm(
        :value="editVisible"
        trigger="click"
        placement="top"
        :form="form"
        :template="templates"
        :width="248"
        title="设置"
        submitTitle="确认"
        @change="(val) => { editVisible = val }"
        @submit="submit")
        TextButton(icon="setting" @click="onEdit").edit 设置
    .menus(slot="side")
      PopoverForm(
        :value="createVisible"
        trigger="click"
        placement="top"
        :form="form"
        :template="templates"
        :width="248"
        title="新建考核维度"
        submitTitle="创建"
        @change="(val) => { createVisible = val }"
        @submit="submit")
        TextButton(icon="plus-circle" @click="onCreate" v-if="remaining > 0").create 创建考核项
      .menu-item(
        v-for="(menu, index) in scopeStore.records"
        :key="index"
        :class="{'menu-item-active': scope.id === menu.id}"
        @click="onSelect(menu, index)")
        .name {{ menu.name }}
        .type 占权重{{ menu.weight }}%
        .more
          a-dropdown(placement="bottomCenter")
            a-icon(type="ellipsis")
            a-menu(slot="overlay" style="width: 200px; padding: 4px 0px;")
              a-menu-item(
                v-for="(operation, key) in operations"
                :key="key + 1"
                style="padding: 12px 20px"
                :style="{borderTop: key === 2 ? '1px #E8E8E8 solid' : ''}"
                :disabled="(index === 0 && key === 0) || (index === scopeStore.records.length -1 && key === 1)"
                @click="onChange(operation.value, menu)")
                span
                  a-icon(:type="operation.icon" style="color: #A6A6A6; margin-right: 6px")
                  span {{ operation.label }}
      .tag(v-if="remaining > 0")
        a-icon(type="exclamation-circle" theme="filled" style="color: #3DA8F5")
        span 还缺评分占比 {{ remaining }}%
    .right-content(v-if="scope.id")
      template(v-if="tabIndex === 'Teacher'")
        template(v-if="scope.type === 'Access::SelectScope'")
           SelectEntry(v-model='value' :scopeId="scope.id")
        template(v-else)
          UserSelector(
            ref="memberSelector"
            :userIds="scope.teacher_ids"
            @change="changeTeacherId")
      template(v-else)
        .module-title 维度附件
        template(v-if="value.stage >= 2")
          Attachments(
            :attachments="attachments")
          Empty(v-if="attachments.length === 0")
        template(v-else)
          FileUploader(
            ref="uploader"
            :value="attachments"
            @change="onSuccess")
            a-button(type="primary" size="large") 上传附件
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import SideSlot from '@/components/evaluation/SideSlot.vue';
import UserSelector from '@/components/hr/UserSelector.vue';
import Attachments from '@/components/global/Attachments.vue';
import scopeStore from '@/store/modules/access/scope.store';
import teacherStore from '@/store/modules/access/teacher.store';
import SelectEntry from './temp/SelectEntry.vue';

@Component({
  components: {
    SideSlot,
    UserSelector,
    Attachments,
    SelectEntry,
  },
})
export default class LevelTemplate extends Vue {
  @Prop({ type: Object, default: () => ({}) }) private value?: any;
  private tabIndex: string = 'Teacher';
  private scopes: any[] = [];
  private scope: any = {
    teacher_ids: [],
  };
  private form: any = {
    name: '',
    weight: '',
  };
  private teachers: any[] = [];
  private createVisible: boolean = false;
  private editVisible: boolean = false;
  get scopeStore() {
    return scopeStore || {};
  }
  get tabs() {
    return [
      {
        title: '考核人',
        key: 'Teacher',
      },
      {
        title: '附件',
        key: 'Attachment',
      },
    ];
  }
  get templates() {
    return [
      {
        key: 'name',
        label: '考核维度',
        widgetType: 'text',
        widget: 'input',
        placeholder: '请输入考核维度',
      },
      {
        key: 'weight',
        label: '占权重(%)',
        widgetType: 'text',
        widget: 'input',
        placeholder: '请输入占权重',
      },
      {
        key: 'type',
        label: '考核人类型',
        widgetType: 'text',
        widget: 'select',
        placeholder: '请选择',
        options: [
          {
            label: '固定分配',
            value: '',
          },
          {
            label: '自行选择',
            value: 'Access::SelectScope',
          },
        ],
      },
      {
        key: 'score_config',
        label: '直接设置总分',
        widgetType: 'text',
        widget: 'select',
        placeholder: '请选择',
        options: [
          {
            label: '是',
            value: 'leader',
          },
          {
            label: '否-总分不与单项关联',
            value: 'manual',
          },
          {
            label: '否-总分求平均分',
            value: 'normal',
          },
        ],
      },
    ];
  }
  get operations() {
    return [
      {
        label: '选项上移',
        value: 'Up',
        icon: 'drag',
      },
      {
        label: '选项下移',
        value: 'Down',
        icon: 'drag',
      },
      {
        label: '删除',
        value: 'Delete',
        icon: 'delete',
      },
    ];
  }
  get attachments() {
    const { attachments } = this.scope;
    return attachments && attachments.documents ? attachments.documents : [];
  }
  get remaining() {
    const weightSum = (scopeStore.records || []).reduce(
      (sum: number, item: any) => Number(sum) + Number(item.weight),
      0,
    );
    return 100 - Number(weightSum);
  }

  public mounted() {
    this.fetchData();
  }

  public async fetchData(page: number = 1) {
    const params = {
      page,
      per_page: 10,
      parentId: this.value.id,
    };
    const { data } = await scopeStore.fetchByParent(params);
    this.scope = data.access_scopes[0] || { teacher_ids: [] };
  }

  public onCreate() {
    this.form = {
      name: '',
      weight: '',
      teacher_ids: [],
    };
    this.createVisible = true;
  }

  public onEdit() {
    this.form = { ...this.scope };
    this.editVisible = true;
  }

  public submit(val: any) {
    if (val.id) {
      this.update(val);
    } else {
      this.create(val);
    }
  }

  public async create(val: any) {
    try {
      const obj = {
        ...val,
        parentId: this.value.id,
      };
      const { data } = await scopeStore.createByParent(obj);
      this.scope = data;
      this.$message.success('创建成功！');
    } catch (error) {
      this.$message.error('创建失败！');
    }
  }

  public async update(val: any) {
    try {
      await scopeStore.update(val);
      this.fetchData();
      this.$message.success('更新成功！');
    } catch (error) {
      this.$message.error('更新失败！');
    }
  }

  public onDelete(id: number) {
    this.$confirm({
      title: '确定要继续执行此操作吗?',
      content: '该操作是不可逆的',
      okText: '确定',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        this.delete(id);
      },
      onCancel: () => {},
    });
  }

  public async delete(id: number) {
    try {
      await scopeStore.delete(id);
      this.fetchData();
      this.$message.success('操作成功！');
    } catch (error) {
      this.$message.error('操作失败！');
    }
  }

  public changeTeacherId(ids: number[]) {
    const scope: any = {
      id: this.scope.id,
      teacher_ids: ids,
    };
    scopeStore.update(scope);
    this.scope.teacher_ids = ids;
  }

  public async onChange(type: string, val: any) {
    if (type === 'Delete') {
      this.onDelete(val.id);
    } else if (type === 'Up') {
      const obj: any = {
        id: val.id,
        position: +val.position - 1,
      };
      await scopeStore.update(obj);
      this.fetchData();
    } else if (type === 'Down') {
      const obj: any = {
        id: val.id,
        position: +val.position + 1,
      };
      await scopeStore.update(obj);
      this.fetchData();
    }
  }

  public onSelect(val: any) {
    if (this.scope.id !== val.id) {
      this.scope = {
        ...val,
        teacher_ids: val.teacher_ids || [],
      };
      (this.$refs.memberSelector as any).reset();
    }
  }

  public async onSuccess(fileItems: any[]) {
    const scope: any = {
      id: this.scope.id,
      attachments: {
        documents: fileItems,
      },
    };
    await scopeStore.update(scope);
    this.fetchData();
  }
}
</script>

<style lang="stylus" scoped>
.page
  margin 0px -20px
  height 100%
  .menus
    overflow auto
    width 100%
    height 100%
    .create
      margin 0px
      padding 18px 20px
      width 100%
      border-bottom 1px #E5E5E5 solid
      color #808080
      text-align left
      &:hover
        color #3DA8F5
    .tag
      margin 12px 14px
      padding 4px 8px
      border-radius 4px
      background #F2FBFF
      span
        margin-left 2px
        color #3DA8F5
        font-size 12px
        line-height 20px
    .menu-item
      position relative
      padding 18px 48px 18px 20px
      border-bottom 1px #E5E5E5 solid
      border-left 4px #fff solid
      color #383838
      cursor pointer
      &:hover
        opacity 0.8
      .name
        font-size 14px
        line-height 20px
      .type
        margin-top 8px
        color #A6A6A6
        font-size 14px
        line-height 12px
      .more
        position absolute
        top 0px
        right 0px
        display flex
        justify-content center
        align-items center
        width 48px
        height 100%
    .menu-item-active
      border-left 4px #3DA8F5 solid
      color #3DA8F5
  .right-content
    padding 0px 20px
    width 100%
    height 100%
    .module-title
      margin 12px 0px
      color #808080
      font-size 14px
      line-height 20px

.edit
  width 60px
  color #A6A6A6
  text-align right
  &:hover
    color #3DA8F5
</style>
