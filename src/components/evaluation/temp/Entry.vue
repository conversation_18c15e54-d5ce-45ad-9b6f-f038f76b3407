<template lang="pug">
NoScrollBarContainer
  .entry-component-container
    .page-top
      .top-left
        a-breadcrumb
          template(slot="separator")
            a-icon(type="right")
          a-breadcrumb-item(@click.native="onBack" style="cursor: pointer") 考核明细
          a-breadcrumb-item {{ entry.teacher && entry.teacher.name }}
      .top-center
        StepToolbar(
          v-model="stepIndex"
          :steps="steps"
          mode="tabs"
          :bordered="true")
      .top-right
        template(v-if="stepIndex === 'Statistics'")
          a-button.export(size="small" @click="onPrint")
            a-icon(type="printer")
            span 预览打印
        template(v-if="stepIndex === 'Detail'")
    .page-middle
      template(v-if="stepIndex === 'Statistics'")
        EntryChart(:entry.sync="entry")
      template(v-else-if="stepIndex === 'Detail'")
        EntryDetail(:entry.sync="entry")
      template(v-else-if="stepIndex === 'NoScore'")
        .card(v-for="(scope, index) in scopes" :key="index")
          a-row(:gutter="20")
            a-col(:span="3")
              label {{ scope.name }}
            a-col(:span="21")
              template(v-if="scope.names.length")
                a-tag(v-for="(name, key) in scope.names" :key="key" color="blue") {{ name }}
                span.text-gray 共{{ scope.names.length }}人
              template(v-else)
                label.text-gray 暂无未考核人
      template(v-else-if="stepIndex === 'Content'")
        EntryContent(:entry.sync="entry")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import EntryChart from '@/components/evaluation/temp/EntryChart.vue';
import EntryDetail from '@/components/evaluation/temp/EntryDetail.vue';
import EntryContent from '@/components/evaluation/temp/EntryContent.vue';
import entryStore from '@/store/modules/access/entry.store';

@Component({
  components: {
    EntryChart,
    EntryDetail,
    EntryContent,
  },
})
export default class Entry extends Vue {
  @Prop({ type: Object, default: () => ({}) }) private value?: any;
  @Prop({ type: Number, default: '' }) private entryId!: number;
  private stepIndex: string = 'Statistics';
  private scopes: any[] = [];
  get steps() {
    return this.value.hasSelf
      ? [
          {
            title: '考核统计',
            key: 'Statistics',
          },
          {
            title: '评分详情',
            key: 'Detail',
          },
          {
            title: '未考核人',
            key: 'NoScore',
          },
          {
            title: '自评内容',
            key: 'Content',
          },
        ]
      : [
          {
            title: '考核统计',
            key: 'Statistics',
          },
          {
            title: '未考核人',
            key: 'NoScore',
          },
          {
            title: '评分详情',
            key: 'Detail',
          },
        ];
  }
  get entry() {
    return entryStore.record || {};
  }

  public mounted() {
    entryStore.find(this.entryId);
    this.fetchData();
  }

  public async fetchData(page: number = 1) {
    const params = {
      page,
      per_page: 100,
      parentId: this.entryId,
    };
    const { data } = await entryStore.unactive_teachers(params);
    this.scopes = data.scopes;
  }

  public onPrint() {
    window.open(`${process.env.VUE_APP_PUBLIC_PATH}access/activities/${this.entryId}/print`);
  }

  public onBack() {
    this.$emit('change', false);
  }
}
</script>

<style lang="stylus" scoped>
.entry-component-container
  .page-top
    display flex
    justify-content space-between
    align-items center
    width 100%
    border-bottom 1px #e8e8e8 solid
    position sticky
    top 0
    z-index 1000
    background #fff
    .top-center
      width 300px
    .top-left
      width 200px
    .top-right
      width 200px
      .export
        display flex
        align-items center
        float right
        border none
        color #808080
        font-weight 500
        &:hover
          color #3da8f5
  .page-middle
    margin-top 20px
    .card
      margin-bottom 12px
      border-radius 3px
      border 1px solid #E8E8E8
      padding 8px 20px
      label
        font-size 14px
        color #383838
        line-height 40px
      .ant-tag
        margin 6px 8px 6px 0px
</style>
