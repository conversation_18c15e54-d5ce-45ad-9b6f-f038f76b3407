<template lang="pug">
.component-page
  .page-top
    .module
      .module-header
        a-icon(type="caret-down" theme="filled")
        span 个人基本信息
      .module-middle(style="padding: 10px 0px")
        a-form
          a-form-item(label="职务")
            a-input(v-model="entryMeta.duty" size="large" disabled)
          a-form-item(label="从事或分管工作")
            a-input(v-model="entryMeta.work" size="large" disabled)
  .page-middle
    .module(v-for="(item, index) in questionSets")
      .module-header
        a-icon(type="caret-down" theme="filled")
        span {{ item.title }}
      .module-middle
        .question-item(v-for="(question, key) in item.questions" :key="key")
          span {{ key + 1}}、 {{ question.title }}
  .page-bottom
    strong 总体评价
    a-radio-group(v-model="entryMeta.total" disabled)
      a-radio.radio(v-for="(item, index) in evaluations" :key="index" :value="item") {{ item }}
  .page-bottom.bottom
    strong 附件
    template(v-if='entryMeta.attachments && entryMeta.attachments.documents')
      Attachments.attachments(:attachments="entryMeta.attachments.documents")
    template(v-else)
      p 暂无
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class EntryContent extends Vue {
  @Prop({ type: Object, default: () => ({}) }) private entry?: any;
  get entryMeta() {
    return this.entry.entry_meta || {};
  }
  get questionSets() {
    return (this.entry.exam || []).filter((e: any) => e.type === 'Access::QuestionSet::Normal');
  }
  get evaluations() {
    return ['优秀', '良好', '一般', '较差'];
  }

  public mounted() {
    this.fetchData();
  }

  public fetchData() {}
}
</script>

<style lang="stylus" scoped>
.component-page
  padding 0px 200px
  width 100%
  height 100%
  .page-top
    width 100%
  .page-middle
    width 100%
  .bottom
    padding-bottom 200px
  .page-bottom
    padding-top 20px
    p, .attachments
      margin-top 10px
    .ant-radio-group
      display flex
      flex-wrap nowrap
      margin-top 10px
      border 1px solid #E8E8E8
      border-radius 3px
      .radio
        margin 0px
        width 100%
        border-left 1px #E8E8E8 solid
        color rgba(38, 38, 38, 0.65)
        text-align center
        font-size 14px
        line-height 48px
        cursor pointer
        &:first-child
          border none

.module
  margin-top 20px
  width 100%
  .module-header
    padding 12px 16px
    width 100%
    background #EDF7FF
    color #3DA8F5
    font-size 14px
    line-height 20px
    span
      margin-left 4px
  .module-middle
    background #fff
    .question-item
      margin-top 20px
      padding 10px
      width 100%
      border 1px #e8e8e8 solid
      border-radius 4px
      background #eee
      span
        color #383838
        font-size 14px
        line-height 20px

.ant-input
  width 100%
  background #eee
  color #383838
</style>
