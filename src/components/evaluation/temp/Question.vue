<template lang="pug">
.question-page
  Empty(type="school" v-if="questions.length === 0")
  .question-item(v-for="(item, index) in questions" :key="index")
    .top
      .question-key 内容{{ index + 1}}：
      .question-value
        a-textarea(
          v-model="item.title"
          size="large"
          :maxlength="textLength"
          :autoSize="{ minRows: 2, maxRows: 8 }"
          :placeholder="placeholder"
          v-if="!item.id || questionId === item.id")
        span(v-else) {{ item.title }}
      .more(v-if="item.id && questionId !== item.id")
        a-dropdown(placement="bottomCenter")
          a-icon(type="ellipsis")
          a-menu(slot="overlay" style="width: 200px; padding: 4px 0px;")
            a-menu-item(
              v-for="(operation, key) in operations"
              :key="key + 1"
              style="padding: 12px 20px"
              :style="{borderTop: key === 3 ? '1px #E8E8E8 solid' : ''}"
              :disabled="(index === 0 && key === 0) || (index === questions.length -1 && key === 1)"
              @click="onChange(operation.value, item)")
              span
                a-icon(:type="operation.icon" style="color: #A6A6A6; margin-right: 6px")
                span {{ operation.label }}
    .bottom(v-if="!item.id || questionId === item.id")
      a-button(type="primary" @click="onSubmit(item)" :disabled="!item.title") {{ item.id ? '更新' : '创建'}}
      a-button(@click="onCancel(item, index)") 取消
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import questionStore from '@/store/modules/access/question.store';

@Component({
  components: {},
})
export default class Question extends Vue {
  @Prop({ type: Array, default: () => [] }) private questions?: any;
  @Prop({ type: Number, default: null }) private textLength?: number;
  private questionId: any = null;
  get questionStore() {
    return questionStore || {};
  }
  get operations() {
    return [
      {
        label: '选项上移',
        value: 'Up',
        icon: 'drag',
      },
      {
        label: '选项下移',
        value: 'Down',
        icon: 'drag',
      },
      {
        label: '编辑',
        value: 'Edit',
        icon: 'form',
      },
      {
        label: '删除',
        value: 'Delete',
        icon: 'delete',
      },
    ];
  }
  get placeholder() {
    return this.textLength ? `请输入内容, ${this.textLength || ''}字以内` : '请输入内容';
  }

  /**
   * name
   */
  public onSubmit(val: any) {
    if (val.id) {
      this.update(val);
    } else {
      this.create(val);
    }
  }

  public async create(val: any) {
    try {
      await questionStore.createByParent(val);
      this.$emit('refresh');
      this.$message.success('创建成功！');
    } catch (error) {
      this.$message.error('创建失败！');
    }
  }

  public async update(val: any) {
    try {
      await questionStore.update(val);
      this.$emit('refresh');
      this.questionId = '';
      this.$message.success('操作成功！');
    } catch (error) {
      this.$message.error('操作失败！');
    }
  }

  public onDelete(id: number) {
    this.$confirm({
      title: '确定要继续执行此操作吗?',
      content: '该操作是不可逆的',
      okText: '确定',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        this.delete(id);
      },
      onCancel: () => {},
    });
  }

  public async delete(id: number) {
    try {
      await questionStore.delete(id);
      this.$emit('refresh');
      this.$message.success('操作成功');
    } catch (error) {
      this.$message.error('操作失败！');
    }
  }

  public onChange(type: string, val: any) {
    val.position = val.position || 1;
    if (type === 'Delete') {
      this.onDelete(val.id);
    } else if (type === 'Up') {
      const obj: any = {
        id: val.id,
        position: val.position - 1,
      };
      this.update(obj);
    } else if (type === 'Down') {
      const obj: any = {
        id: val.id,
        position: val.position + 1,
      };
      this.update(obj);
    } else if (type === 'Edit') {
      this.questionId = val.id;
    }
  }

  public async onCancel(val: any, index: number) {
    this.questionId = '';
    if (index >= 0 && !val.id) {
      this.$emit('cancel', index);
    }
  }
}
</script>

<style lang="stylus" scoped>
.question-page
  overflow auto
  padding 20px
  width 100%
  height 100%
  .question-item
    margin-bottom 12px
    padding 0px 16px
    width 100%
    border 1px solid #E8E8E8
    border-radius 4px
    background #FAFAFA
    .top
      position relative
      display flex
      padding 14px 0px
      .more
        position absolute
        top 0px
        right 0px
        width 48px
        height 48px
        text-align center
        line-height 48px
    .bottom
      display flex
      justify-content flex-end
      padding 4px 0px 14px
      width 100%

.question-key
  min-width 60px
  width 60px
  color #808080
  font-size 14px
  line-height 20px

.question-value
  width 100%
  color #383838
  font-size 14px
  line-height 20px

.ant-input
  padding 0px
  width 100%
  border none
  background none
</style>
