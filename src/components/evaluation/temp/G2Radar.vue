<template lang="pug">
.g2(:id="id")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import G2 from '@antv/g2';
import { DataView } from '@antv/data-set';

@Component({
  components: {},
})
export default class G2Radar extends Vue {
  @Prop({ type: Array, default: () => [] }) private charData: any;
  @Prop({ type: String, default: '' }) private id?: string;
  @Prop({ type: Number, default: 600 }) private width?: number;
  @Prop({ type: Number, default: 230 }) private boxHeight?: number;
  @Prop({ type: Boolean, default: true }) private forceFit?: boolean;
  @Prop({ type: Boolean, default: false }) private animate?: boolean;
  @Prop({ type: Boolean, default: false }) private showTitle?: boolean;
  @Prop({ type: Array, default: () => ['#3DA8F5', '#6DC37D'] })
  private lineColors?: string[];
  @Prop({ type: Array, default: () => ['#3DA8F5', '#6DC37D'] })
  private pointColors?: string[];
  @Prop({ type: Array, default: () => ['#C4E4FC', '#D3EDD8'] })
  private areaColors?: string[];
  // data
  private chart: any = null;
  @Watch('charData')
  public watchChange() {
    this.chart.destroy();
    this.drawChart();
  }

  public mounted() {
    this.drawChart();
  }
  public drawChart() {
    const data = new DataView().source(this.charData);
    data.transform({
      type: 'fold',
      fields: ['个人得分', '平均分'],
      key: 'type',
      value: 'score',
    });
    this.chart = new G2.Chart({
      container: this.id,
      width: this.width,
      height: this.boxHeight,
      forceFit: this.forceFit,
      animate: this.animate,
      padding: [20, 20, 40, 20],
    });
    this.chart.source(data, {
      score: {
        min: 0,
        max: 100,
      },
    });
    this.chart.coord('polar', {
      radius: 0.8,
    });
    this.chart.axis('name', {
      line: null,
      tickLine: null,
      grid: {
        lineStyle: {
          lineDash: null,
        },
        hideFirstLine: false,
      },
    });
    this.chart.axis('score', {
      line: null,
      tickLine: null,
      grid: {
        type: 'polygon',
        lineStyle: {
          lineDash: null,
        },
      },
    });
    this.chart.legend('type', {
      marker: 'circle',
      offset: 30,
    });
    this.chart
      .line()
      .position('name*score')
      .color('type', this.lineColors)
      .tooltip(false)
      .size(2);
    this.chart
      .point()
      .position('name*score')
      .color('type', this.pointColors)
      .shape('circle')
      .size(4)
      .tooltip(false)
      .style({
        stroke: '#fff',
        lineWidth: 1,
        fillOpacity: 1,
      });
    this.chart
      .area()
      .position('name*score')
      .color('type', this.areaColors)
      .tooltip('type*score', (type: any, score: any) => {
        const val = `${score}分`;
        return {
          name: type,
          value: val,
        };
      });
    this.chart.render();
  }
}
</script>

<style lang="stylus" scoped>
::-webkit-scrollbar
  display none

html, body
  overflow hidden
  margin 0
  height 100%

.g2
  width 100%
  height 100%
</style>
