<template lang="pug">
.score-page
  AdminTable(
    :data="scoreStore.scores"
    :totalCount="scoreStore.totalCount"
    :currentPage="scoreStore.currentPage"
    :totalPages="scoreStore.totalPages"
    :perPage="scoreStore.perPage"
    :showHeader="true"
    :hideOnSinglePage="true"
    :scroll="{ x: scrollWidth }"
    :loading="scoreStore.loading"
    @paginate="onPaginate")
    a-table-column(align="center" title="考核人" :width="100")
      template(slot-scope="scope")
        .text-ellipsis {{ scope.order }}
    //- a-table-column(title="考核人")
      template(slot-scope="scope")
        a-tooltip
          template(slot="title")
            p 姓名：{{ scope.teacher.name }}
            p 工号：{{ scope.teacher.code }}
            P 部门：{{ scope.teacher.department_name }}
          span {{ scope.teacher.name }}
    a-table-column(title="全部维度" :width="100")
      template(slot-scope="scope")
        .text-ellipsis {{ scopes[scope.access_scope_id] }}
    a-table-column(title="总分" align="center" :width="100")
      template(slot-scope="scope")
        .text-ellipsis(v-if="scope.score") {{ scope.score }}分
        span(v-else) -
    a-table-column(
      v-for="(question, index) in questions"
      :key="index + 10"
      key="title"
      align="center")
      a-tooltip(:title="question.title" slot="title")
        .text-ellipsis.text-header-title {{ question.title }}
      template(slot-scope="scope")
        span(v-if="scope.score_meta[question.id]") {{ scope.score_meta[question.id] }}分
        span(v-else) -
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import scoreStore from '@/store/modules/access/score.store';
import questionStore from '@/store/modules/access/question.store';
import question from '@/models/access/question';
import question_set from '@/models/access/question_set';

@Component({
  components: {},
})
export default class EntryDetail extends Vue {
  @Prop({ type: Object, default: () => ({}) }) private entry?: any;
  scopes: any = {};
  questions: any[] = [];
  scrollWidth: number = 1000;

  get scoreStore() {
    return scoreStore || {};
  }

  public mounted() {
    this.fetchData();
  }

  public async fetchData(page: number = 1) {
    const params = {
      page,
      per_page: 10,
      parentId: this.entry.id,
      q: {
        s: ['access_scope_id asc'],
      },
    };
    const { data } = await scoreStore.fetchByParent(params);
    this.questions = (data.exam || []).reduce((res: any[], set: any) => {
      const questions = set.questions || [];
      return res.concat(questions);
    }, []);
    this.scrollWidth = this.questions.length * 100 + 300;
    this.scopes = data.scopes || {};
  }

  public onPaginate(page: number) {
    this.fetchData(page);
  }
}
</script>

<style lang="stylus" scoped>
.score-page
  width 100%
  height 100%
  background #fff
  .text-header-title
    width 84px
</style>
