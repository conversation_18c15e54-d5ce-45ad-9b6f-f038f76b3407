<template lang="pug">
.score-page
  AdminTable(
    :data="teachers"
    :totalCount="data.total_count"
    :currentPage="data.current_page"
    :totalPages="data.total_pages"
    :perPage="12"
    :showHeader="true"
    :hideOnSinglePage="true"
    @filter="onFilter"
    @paginate="onPaginate")
    a-table-column(dataIndex="name" title="考核人")
    a-table-column(dataIndex="code" title="工号")
    a-table-column(dataIndex="department_name" title="部门")
    a-table-column(title="所属维度" key="access_scope_id" :filters="scopeFilters" width="100px")
      template(slot-scope="scope")
        span {{ scopes[scope.access_scope_id] }}
    a-table-column(dataIndex="wait_number" align="center" title="待评")
    a-table-column(dataIndex="score_number" align="center" title="已评")
    a-table-column(dataIndex="total_number" align="center" title="总数")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import activityStore from '@/store/modules/access/activity.store';

@Component({
  components: {},
})
export default class ScoreInfo extends Vue {
  @Prop({ type: Object, default: () => ({}) }) private value?: any;
  @Prop({ type: Object, default: () => ({}) }) private queryObject?: any;
  private teachers: any[] = [];
  private scopes: any = {};
  private data: any = {};
  get scopeFilters() {
    const keys = Object.keys(this.scopes);
    return (keys || []).map((key: any) => ({
      text: this.scopes[key],
      value: key,
    }));
  }

  @Watch('queryObject', { deep: true })
  public watchChange() {
    this.fetchData();
  }
  public mounted() {
    this.fetchData();
  }

  public async fetchData(page: number = 1) {
    const params = {
      page,
      per_page: 12,
      parentId: this.value.id,
      q: {
        ...this.queryObject,
      },
    };
    const { data } = await activityStore.score_teachers(params);
    this.data = data;
    this.teachers = data.teachers.map((item: any) => ({
      ...item,
      ...item.teacher,
    }));
    this.scopes = data.scopes;
  }

  public onPaginate(page: number) {
    this.fetchData(page);
  }

  public onFilter(val: IObject = {}) {
    Object.assign(this.queryObject, val);
    this.fetchData();
  }
}
</script>

<style lang="stylus" scoped>
.score-page
  width 100%
  height 100%
  background #fff
  border-top 1px #e6e6e6 solid
</style>
