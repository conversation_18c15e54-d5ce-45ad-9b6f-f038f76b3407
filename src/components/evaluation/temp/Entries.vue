<template lang="pug">
.component-page
  .page-header
    StepToolbar(
      v-model="tabIndex"
      :steps="tabs"
      mode="tabs"
      :bordered="true")
    Searcher(
      v-model="queryObject"
      :variables="['teacher_name', 'teacher_code', 'teacher_departments_name']"
      placeholder="输入姓名、工号、部门")
  .page-middle
    template(v-if="tabIndex === 'Entry'")
      table
        tr
          td(v-for="(item, index) in titles" :key="index" :width="item.width")
            span {{ item.label }}
            template(v-if="item.label === '自评状态'")
              a-dropdown(placement="bottomCenter")
                a-icon(type="filter" style="color: #808080")
                a-menu(slot="overlay" style="width: 140px; padding: 4px 0px;")
                  a-menu-item(
                    v-for="(item, key) in states"
                    :key="key + 1"
                    style="padding: 12px 20px"
                    @click="filterState(item.value)")
                    .flex-between
                      span {{ item.label }}
                      a-icon(type="check" style="color: #A6A6A6" v-if="item.value === state")
            template(v-if="item.label === '考核等级'")
              a-dropdown(placement="bottomRight")
                a-icon(type="filter" style="color: #808080")
                a-menu(slot="overlay" style="width: 140px; padding: 4px 0px;")
                  a-menu-item(
                    v-for="(item, key) in levels"
                    :key="key + 1"
                    style="padding: 12px 20px"
                    @click="filterLevel(item.value)")
                    .flex-between
                      span {{ item.label }}
                      a-icon(type="check" style="color: #A6A6A6" v-if="item.value === level")
        tr(v-if="entryStore.entries.length === 0")
          td.text-center(colspan="9") 暂无内容
        tr.cell(v-for="(entry, index) in entryStore.entries" :key="index" @click="onShow(entry)")
          td.black(width="200px")
            p 姓名：{{ entry.teacher.name }}
            p 工号：{{ entry.teacher.code }}
            p 部门：{{ entry.teacher.department_name }}
          td(width="100px" v-if="value.hasSelf")
            span.text-primary(:class="{'text-success': entry.selfAssessment }")
              | {{ entry.selfAssessment ? '已自评' : '待自评' }}
          td(width="300px")
            template(v-if="value.stage >= 1")
              p(v-for="(scope, key) in entry.scopes" :key="key")
                span {{scope.name }}：{{ scope.score }}分，
                span 已评：{{ scope.score_number }}人，
                span 待评：{{ scope.wait_number }}人，
                span(v-if="scope.score_number") 当前排名：第{{ scope.rownum }}名
            template(v-else) -
          td(width="200px")
            template(v-if="value.stage >= 1")
              p(v-if="entry.score") {{ entry.score }}分
              p 已评：{{ entry.score_number || 0 }}人， 待评：{{ entry.wait_number || 0 }}人
              p(v-if="entry.score_number") 排名：第{{ entry.rownum }}名
            template(v-else) -
          td(width="100px")
            template(v-if="value.stage >= 1")
              span {{ initLevel(entry.level) }}
              a-dropdown(placement="bottomRight")
                a-button(@click.stop="() => {}")
                  span 评鉴
                  a-icon(type="down" style="color: #3da8f5")
                a-menu(slot="overlay" style="width: 140px; padding: 4px 0px;")
                  a-menu-item(
                    v-for="(operation, key) in operations"
                    :key="key + 1"
                    style="padding: 12px 20px"
                    @click="onChange(entry, operation.value)")
                    .flex-between
                      span {{ operation.label }}
                      a-icon(type="check" style="color: #A6A6A6" v-if="operation.value === entry.level")
            template(v-else) -
      .pagination
        a-pagination(
          showQuickJumper
          showSizeChanger
          :current="entryStore.currentPage"
          :pageSizeOptions="['10', '20', '30', '40', '50', '100']"
          :defaultPageSize="perPage"
          :total="entryStore.totalCount"
          @showSizeChange="onShowSizeChange"
          @change="onPagination")
    template(v-else)
      ScoreInfo(v-model="value" :queryObject="queryObject")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import ScoreInfo from '@/components/evaluation/temp/ScoreInfo.vue';
import entryStore from '@/store/modules/access/entry.store';

@Component({
  components: {
    ScoreInfo,
  },
})
export default class Entries extends Vue {
  @Prop({ type: Object, default: () => ({}) }) private value?: any;
  private perPage: number = 10;
  private queryObject: object = {};
  private state: string = '';
  private level: string = '';
  private tabIndex: string = 'Entry';
  get tabs() {
    return [
      {
        title: '被考核人',
        key: 'Entry',
      },
      {
        title: '考核人',
        key: 'Score',
      },
    ];
  }
  get titles() {
    return this.value.hasSelf
      ? [
          {
            label: '被考核人信息',
            width: '200px',
          },
          {
            label: '自评状态',
            width: '100px',
          },
          {
            label: '各项维度分数',
            width: '300px',
          },
          {
            label: '总分',
            width: '200px',
          },
          {
            label: '考核等级',
            width: '100px',
          },
        ]
      : [
          {
            label: '被考核人信息',
            width: '200px',
          },
          {
            label: '各项维度分数',
            width: '400px',
          },
          {
            label: '总分',
            width: '200px',
          },
          {
            label: '考核等级',
            width: '100px',
          },
        ];
  }
  get operations() {
    return [
      {
        label: '优秀',
        value: 'good',
      },
      {
        label: '称职',
        value: 'competent',
      },
      {
        label: '基本称职',
        value: 'qualified',
      },
      {
        label: '不称职',
        value: 'bad',
      },
    ];
  }
  get entryStore() {
    return entryStore || {};
  }
  get states() {
    return [
      {
        label: '全部',
        value: '',
      },
      {
        label: '待自评',
        value: false,
      },
      {
        label: '已自评',
        value: true,
      },
    ];
  }
  get levels() {
    return [
      {
        label: '全部',
        value: '',
      },
      {
        label: '优秀',
        value: 'good',
      },
      {
        label: '称职',
        value: 'competent',
      },
      {
        label: '基本称职',
        value: 'qualified',
      },
      {
        label: '不称职',
        value: 'bad',
      },
    ];
  }
  @Watch('queryObject')
  public queryChange() {
    this.fetchData();
  }
  @Watch('value', { immediate: true, deep: true })
  public watchChange() {
    if (this.value.id) {
      this.fetchData();
    }
  }

  public fetchData(page: number = 1) {
    const stateObj =
      ({
        true: {
          total_and_question_count_not_null: true,
          question_count_gt: 0,
        },
        false: {
          total_or_question_count_not_null: false,
          // question_count_eq: 0,
        },
      } as any)[this.state] || {};
    const params = {
      page,
      per_page: this.perPage,
      parentId: this.value.id,
      q: {
        ...this.queryObject,
        ...stateObj,
        level_eq: this.level,
      },
    };
    entryStore.fetchByParent(params);
  }

  public async onChange(val: any, level: string) {
    const obj: any = {
      id: val.id,
      level,
    };
    await entryStore.update(obj);
    this.fetchData();
    this.$message.success('操作成功！');
  }

  // extends
  public onPagination(page: number) {
    this.fetchData(page);
  }

  public onShowSizeChange(current: number, pageSize: number) {
    this.perPage = pageSize;
    this.fetchData();
  }

  public onShow(val: any) {
    this.$emit('click', val);
  }

  public filterState(val: string) {
    this.state = val;
    this.fetchData();
  }

  public filterLevel(val: string) {
    this.level = val;
    this.fetchData();
  }

  public initLevel(val: string) {
    const res: any = this.operations.find((e: any) => e.value === val);
    return res.label || '--';
  }
}
</script>

<style lang="stylus" scoped>
.component-page
  padding-bottom 20px
  width 100%
  .page-header
    display flex
    justify-content space-between
    align-items center
    width 100%
  .page-middle
    width 100%
    height 100%
    color #808080
    font-size 14px
    line-height 20px
    table, tr
      width 100%
      td
        padding 10px
        min-width 100px
        border 1px #e8e8e8 solid
    .cell
      cursor pointer
      &:hover
        background #f8f8f8
      .black
        color #383838
      button
        display block
        margin-top 14px
        padding 0px
        border none
        background none
        color #3da8f5
    .pagination
      padding-top 20px
      width 100%
      text-align right
</style>
