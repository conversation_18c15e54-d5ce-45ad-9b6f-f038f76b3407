<template lang="pug">
.page
  .page-top
    .name.flex-between
      span {{ teacher.name }}
      span(v-if="entry.id")
        span {{ initLevel(entry.level) }}
        a-divider(type="vertical")
        span {{ entry.score }} 分
    .company-info
      span(v-if="entryMeta.total") 总体自评：{{ entryMeta.total }}
      span 工号：{{ teacher.code }}
      span 部门： {{ teacher.department_name }}
      span(v-if="entryMeta.duty") 职务：{{ entryMeta.duty }}
      p(v-if="entryMeta.work") 从事或分管工作： {{ entryMeta.work }}
      .config
        a-dropdown(placement="bottomRight")
          a-button
            label 评鉴
            a-icon(type="down" style="color: #fff")
          a-menu(slot="overlay" style="width: 140px; padding: 4px 0px;")
            a-menu-item(
              v-for="(operation, key) in operations"
              :key="key + 1"
              style="padding: 12px 20px"
              @click.stop="onChange(operation.value)")
              .flex-between
                span {{ operation.label }}
                a-icon(type="check" style="color: #A6A6A6" v-if="entry.level === operation.value")
  .page-middle
    a-row(:gutter="20")
      a-col(:span="12")
        a-card(title="各维度比分")
          a-row(:gutter="20")
            a-col(:span="6")
              .scope-item(v-for="(item, index) in scopes" :key="index" v-if="index < 3")
                .key {{ item.name }}
                span.value {{ item.score || 0 }}
                span.unit 分
                span.order 排名{{ item.rownum }}
            a-col(:span="18")
              G2Radar(:charData="scopeRadarData" id="R1")
      a-col(:span="12")
        a-card(title="各考核项比分")
          a-row(:gutter="20")
            a-col(:span="6")
              .scope-item(v-for="(item, index) in qsts" :key="index" v-if="index < 3")
                .key {{ item.title }}
                span.value {{ item.score || 0 }}
                span.unit 分
                span.order 排名{{ item.rownum }}
            a-col(:span="18")
              G2Radar(:charData="qstRadarData" id="R2")
      a-col(:span="12" v-for="(level, index) in levels" :key="index" v-if="level.tickets && level.tickets.length")
        a-card(:title="level.name")
          G2Pie(:charData="level.tickets" :id="'P' + index")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import G2Radar from '@/components/evaluation/temp/G2Radar.vue';
import G2Pie from '@/components/evaluation/temp/G2Pie.vue';
import entryStore from '@/store/modules/access/entry.store';

@Component({
  components: {
    G2Radar,
    G2Pie,
  },
})
export default class EntryChart extends Vue {
  @Prop({ type: Object, default: () => ({}) }) private entry?: any;
  get teacher() {
    return this.entry.teacher || {};
  }
  get entryMeta() {
    return this.entry.entry_meta || {};
  }
  get operations() {
    return [
      {
        label: '优秀',
        value: 'good',
      },
      {
        label: '称职',
        value: 'competent',
      },
      {
        label: '基本称职',
        value: 'qualified',
      },
      {
        label: '不称职',
        value: 'bad',
      },
    ];
  }
  get scopes() {
    return this.entry.scopes || [];
  }
  get qsts() {
    return this.entry.qsts || [];
  }
  get levels() {
    const selfLevel = {
      name: '个人工作内容',
      ...this.entry.total_level,
    };
    (this.entry.levels || []).unshift(selfLevel);
    return (this.entry.levels || []).map((level: any) => ({
      ...level,
      tickets: (level.tickets || []).map((item: any) => ({
        name: this.initLevel(item.level),
        count: item.number || 0,
        percent: (Number(item.number) / Number(level.total_number)) * 100,
      })),
    }));
  }
  get scopeRadarData() {
    return (this.scopes || []).map((scope: any) => ({
      name: scope.name,
      个人得分: +scope.score || 0,
      平均分: +scope.avg_score || 0,
    }));
  }
  get qstRadarData() {
    return (this.qsts || []).map((questionSet: any) => ({
      name: questionSet.title,
      个人得分: +questionSet.score || 0,
      平均分: +questionSet.avg_score.toFixed(2) || 0,
    }));
  }

  public mounted() {
    this.fetchData();
  }

  public fetchData() {}

  public async onChange(level: string) {
    const obj: any = {
      id: this.entry.id,
      level,
    };
    await entryStore.update(obj);
    this.$message.success('操作成功！');
  }

  public initLevel(val: string) {
    const res: any = this.operations.find((e: any) => e.value === val);
    return res.label || '--';
  }
}
</script>

<style lang="stylus" scoped>
.page
  width 100%
  height 100%
  .page-top
    position relative
    margin 20px 0px
    padding 18px 16px
    width 100%
    border-radius 4px
    background #3DACF4
    .name
      color #fff
      font-weight 500
      font-size 16px
      line-height 20px
    .company-info
      margin-top 6px
      color #fff
      font-size 14px
      line-height 20px
      span
        margin-right 40px
    .config
      position absolute
      right 16px
      bottom 10px
      button
        display block
        padding 0px 2px
        border none
        background none
        color #fff
        font-size 14px
  .page-middle
    width 100%
    .ant-card
      margin-bottom 20px
      width 100%
      .scope-item
        margin-bottom 24px
        width 100%
        .key
          overflow hidden
          margin-bottom 8px
          width 100%
          color #A6A6A6
          text-overflow ellipsis
          white-space nowrap
          font-weight 500
          font-size 12px
          line-height 14px
        .value
          color #383838
          font-weight 500
          font-size 30px
          font-family DINCond-Medium, DINCond
          line-height 30px
        .unit
          margin 0px 4px
          font-size 12px
          line-height 18px
          colro #808000
        .order
          padding 4px 6px
          border-radius 4px
          background #EDF7FF
          color #3DA8F5
          white-space nowrap
          font-size 12px
          line-height 12px
</style>
