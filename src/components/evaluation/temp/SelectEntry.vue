<template lang="pug">
.component-page
  .page-middle
    Searcher.search(
      v-model='queryObject',
      :variables='["teacher_name", "teacher_code", "teacher_departments_name"]',
      placeholder='输入姓名、工号、部门'
    )
    table
      tr
        th(width='200px') 工号
        th(width='200px') 姓名
        th(width='200px') 部门
        th 操作
      tr(v-if='entryStore.entries.length === 0')
        td.text-center(colspan='9') 暂无内容
      tr.cell(v-for='(entry, index) in entryStore.entries', :key='index')
        td {{ entry.teacher.code }}
        td {{ entry.teacher.name }}
        td {{ entry.teacher.department_name }}
        td
          a-button(@click='onShow(entry.id)') 分配考核人
    .pagination
      a-pagination(
        showQuickJumper,
        showSizeChanger,
        :current='entryStore.currentPage',
        :pageSizeOptions='["10", "20", "30", "40", "50", "100"]',
        :defaultPageSize='perPage',
        :total='entryStore.totalCount',
        @showSizeChange='onShowSizeChange',
        @change='onPagination'
      )
    UserSelectorDialog(
      title='分配考核人',
      v-model='visible',
      :userIds='userIds',
      :multiple='true',
      @selectUsers='changeTeacherId'
    )
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import ScoreInfo from '@/components/evaluation/temp/ScoreInfo.vue';
import entryStore from '@/store/modules/access/entry.store';
import UserSelectorDialog from '@/components/hr/UserSelectorDialog.vue';
import { ITeacher } from '@/models/teacher';
import { paramCase } from 'change-case';
import { IEntry } from '@/models/access/entry';

@Component({
  components: {
    ScoreInfo,
    UserSelectorDialog,
  },
})
export default class SelectEntry extends Vue {
  @Prop({ type: Object, default: () => ({}) }) private value?: any;
  @Prop({ type: Number }) private scopeId?: number;
  private perPage: number = 10;
  private queryObject: object = {};
  private state: string = '';
  private level: string = '';
  private visible: boolean = false;
  public userIds: number[] = [];
  private currentEntry: IEntry | IObject = {};

  get entryStore() {
    return entryStore || {};
  }
  @Watch('queryObject')
  public queryChange() {
    this.fetchData();
  }
  @Watch('value', { immediate: true, deep: true })
  public watchChange() {
    if (this.value.id) {
      this.fetchData();
    }
  }

  public fetchData(page: number = 1) {
    const stateObj =
      ({
        true: {
          total_and_question_count_not_null: true,
          question_count_gt: 0,
        },
        false: {
          total_or_question_count_not_null: false,
          // question_count_eq: 0,
        },
      } as any)[this.state] || {};
    const params = {
      page,
      per_page: this.perPage,
      parentId: this.value.id,
      q: {
        ...this.queryObject,
        ...stateObj,
        level_eq: this.level,
      },
    };
    entryStore.fetchByParent(params);
  }

  public async onChange(val: any, level: string) {
    const obj: any = {
      id: val.id,
      level,
    };
    await entryStore.update(obj);
    this.fetchData();
    this.$message.success('操作成功！');
  }

  // extends
  public onPagination(page: number) {
    this.fetchData(page);
  }

  public onShowSizeChange(current: number, pageSize: number) {
    this.perPage = pageSize;
    this.fetchData();
  }

  public async onShow(val: number) {
    // this.$emit('click', val);
    await this.entryStore.find(val);
    this.currentEntry = entryStore.record;
    this.userIds =
      this.currentEntry.scope_teacher_ids.find((item: IObject) => this.scopeId === item.id)?.teacher_ids || [];
    this.visible = true;
  }

  public changeTeacherId(teachers: ITeacher[]) {
    const teacherIds = teachers.map((teacher: IObject) => teacher.id);
    const params = {
      entryId: this.currentEntry.id,
      access_scope_id: this.scopeId,
      teacher_ids: teacherIds,
    };
    this.entryStore.batch_update_scores(params);
  }
}
</script>

<style lang="stylus" scoped>
.component-page {
  padding-bottom: 20px;
  width: 100%;

  .page-middle {
    margin-top: 10px;
    width: 100%;
    height: 100%;
    color: #808080;
    font-size: 14px;
    line-height: 20px;

    .search {
      margin-bottom: 10px;
    }

    table, tr {
      width: 100%;

      td, th {
        padding: 10px;
        min-width: 100px;
        border: 1px #e8e8e8 solid;
      }
    }

    .cell {
      cursor: pointer;

      &:hover {
        background: #f8f8f8;
      }

      .black {
        color: #383838;
      }

      button {
        display: block;
        margin-top: 14px;
        padding: 0px;
        border: none;
        background: none;
        color: #3da8f5;
      }
    }

    .pagination {
      padding-top: 20px;
      width: 100%;
      text-align: right;
    }
  }
}
</style>
