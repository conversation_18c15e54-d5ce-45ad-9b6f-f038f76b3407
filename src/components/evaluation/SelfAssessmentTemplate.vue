<template lang="pug">
.question-page
  .notification
    span 在 {{ activity.meta && activity.meta.self_start_at | format('YYYY/MM/DD') }} 00:00
    span -{{ activity.meta && activity.meta.self_end_at | format('YYYY/MM/DD')}} 23:59「自评阶段」内可对自评结果进行修改。
  a-collapse.collapse(:activeKey="activeKey" :bordered="false")
    a-collapse-panel(key="0"
      style="background:#EDF7FF;border-radius: 1px;margin-bottom: 16px;border: 0;overflow: hidden")
      .panel-header(slot="header") 基本信息
      .panel-middle
        a-form
          a-form-item(label="职务")
            a-input(v-model="entryMeta.duty" size="large" placeholder="请输入你的职务" @blur="onChange")
          a-form-item(label="从事或分管工作")
            a-input(v-model="entryMeta.work" size="large" placeholder="请输入你从事或分管工作" @blur="onChange")
    a-collapse-panel(
      v-for="(item, index) in questionSets"
      :key="`${index + 1}`"
      style="background:#EDF7FF;border-radius: 1px;margin-bottom: 16px;border: 0;overflow: hidden")
      .panel-header(slot="header")
        span {{ item.title }}
        strong(v-if="item.max_question_count") {{ item.questions.length }}/{{ item.max_question_count }}
      .panel-middle
        Question(
          :questions="item.questions"
          :textLength="item.max_letter_count"
          @refresh="onChange"
          @cancel="(key) => { item.questions.splice(key, 1) }")
      .panel-bottom(
        @click="addQuestion(item, index)"
        v-if="item.max_question_count ? item.max_question_count > item.questions.length : true")
        a-icon(type="plus" style="color: #3DA8F5")
        span 添加工作内容
  .evaluation
    span(style="color: #FF4F3E") *
    strong 总体评价
    a-radio-group(v-model="entryMeta.total" @change="onChange")
      a-radio.radio(v-for="(item, index) in evaluations" :key="index" :value="item")
        p {{ item }}
  .evaluation
    span(style="color: #FF4F3E") *
    strong 附件
    .file-uploader
      FileUploader(
        accept='application/pdf'
        :value="entryMeta.attachments.documents"
        @change="onFileUploaderChange"
      )
        a-button(type="primary" size="large") 上传附件

</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import Question from '@/components/evaluation/temp/Question.vue';
import entrySetStore from '@/store/modules/access/entry.store';
import questionStore from '@/store/modules/access/question.store';

@Component({
  components: {
    Question,
  },
})
export default class SelfAssessmentTemplate extends Vue {
  @Prop({ type: Object, default: null }) private activity?: any;
  private questionSets: any[] = [];
  private entryMeta: any = {
    total: '',
    question_count: '',
    duty: '',
    work: '',
    attachments: { documents: [] },
  };
  private evaluation: string = '';
  private entry: any = {};
  get evaluations() {
    return ['优秀', '良好', '一般', '较差'];
  }
  get activeKey() {
    const { length } = this.questionSets;
    return [...Array(length + 1)].map((e, index) => `${index}`);
  }

  public mounted() {
    questionStore.changeNamespace('teacher');
    this.fetchData();
  }

  public async fetchData() {
    const { data } = await entrySetStore.entry(this.activity.id);
    this.entry = data;
    this.entryMeta = data.entry_meta || this.entryMeta;
    const questionSets = (data.exam || []).filter((e: any) => e.type === 'Access::QuestionSet::Normal');
    this.questionSets = (questionSets || []).map((item: any) => ({
      ...item,
      ...item.meta,
    }));
  }

  public async update() {
    try {
      const obj = {
        parentId: this.activity.id,
        entry_meta: {
          ...this.entryMeta,
          question_count: (this.questionSets || []).reduce((sum: number, item: any) => {
            const res = sum + item.questions.length;
            return res;
          }, 0),
        },
      };
      await entrySetStore.update_entry(obj);
    } catch (error) {
      this.$message.error('操作失败！');
    }
  }

  public onChange(val: any) {
    this.update();
  }

  // question
  public addQuestion(val: any, index: number) {
    const obj: any = {
      title: '',
      meta: '',
      score: '',
      parentId: val.id,
    };
    this.questionSets[index].questions.push(obj);
  }

  onFileUploaderChange(fileItems: any[]) {
    if (this.entryMeta) {
      if (!this.entryMeta.attachments) {
        this.entryMeta.attachments = { documents: [] };
      }
      this.entryMeta.attachments.documents = fileItems;
      this.update();
    }
  }
}
</script>

<style lang="stylus" scoped>
.question-page
  overflow auto
  padding 20px
  width 100%
  .notification
    margin-bottom 16px
    padding 6px 8px
    width 100%
    border-radius 4px
    background #EDF7FF
    color #3DA8F5
    font-size 14px
    line-height 20px
  .collapse
    width 100%
    .panel-header
      display flex
      justify-content space-between
      align-items center
      padding 12px 16px
      width 100%
      color #3DA8F5
      font-size 14px
      line-height 20px
    .panel-middle
      background #fff
      .ant-form
        padding 20px
        .ant-input
          padding 9px 11px
          width 100%
          border 1px solid #E8E8E8
    .panel-bottom
      margin-top 20px
      padding 10px 0px
      width 100%
      border-radius 4px
      background #F5F5F5
      text-align center
      cursor pointer
      &:hover
        opacity 0.8
      span
        margin-left 2px
        color #3DA8F5
        font-size 14px
        line-height 20px
  .evaluation
    padding 20px 0px
    border-top 1px #E9E9E9 solid
    .file-uploader
      padding-top 10px
    .ant-radio-group
      display flex
      flex-wrap nowrap
      margin 8px 0px 0px 8px
      border 1px solid #E8E8E8
      border-radius 3px
      .radio
        margin 0px
        padding 12px 0px 10px 8px
        width 100%
        height 68px
        border-left 1px #E8E8E8 solid
        text-align center
        cursor pointer
        &:first-child
          border none
        p
          margin 6px 8px 0px 0px
          color rgba(38, 38, 38, 0.65)
          font-size 14px
          line-height 20px
  .question-item
    margin-bottom 12px
    padding 0px 16px
    width 100%
    border 1px solid #E8E8E8
    border-radius 4px
    background #FAFAFA
    .top
      position relative
      display flex
      padding 14px 0px
      .more
        position absolute
        top 0px
        right 0px
        width 48px
        height 48px
        text-align center
        line-height 48px
    .bottom
      display flex
      justify-content flex-end
      padding 4px 0px 14px
      width 100%

.question-key
  min-width 60px
  width 60px
  color #808080
  font-size 14px
  line-height 20px

.question-value
  color #383838
  font-size 14px
  line-height 20px

.ant-input
  padding 0px
  width 666px
  border none
  background none
</style>
