<script lang="ts">
import { Component, Vue, Prop, Watch, Provide, ProvideReactive } from 'vue-property-decorator';
import EventBus from './EventBus';
import ZoomBox from './ZoomBox.vue';
import FlowTreeBole from './FlowTreeBole.vue';
import FlowPoint from './FlowPoint.vue';
import Transformer from './Transformer';
import { IPlaceMenuTemplate } from './types';
import { IWorkflow } from '@/models/bpm/workflow';
import { defaultCore, defaultMenuPlaces } from '@/models/bpm/defaultValues';

@Component({
  components: {
    ZoomBox,
    FlowTreeBole,
    FlowPoint,
  },
})
export default class FlowTree extends Vue {
  public transformer: any = null;
  public rootNode: IObject = {};

  @Prop({ type: Object, default: () => defaultCore, required: true }) readonly core!: object; // 流程图数据
  @Prop({ type: Array, default: () => defaultMenuPlaces }) readonly places!: IPlaceMenuTemplate[]; // 菜单配置
  @Prop({ type: Object, default: () => ({}) }) readonly activeNode!: object; //  当前激活 node
  @Prop({ type: Boolean, default: false }) readonly disabled!: boolean; // 是否可编辑
  @Prop({ type: Boolean, default: true }) readonly showStartPoint!: boolean;
  @Prop({ type: Boolean, default: true }) readonly showEndPoint!: boolean;
  @Prop({ type: Boolean, default: false }) readonly showStartNode!: boolean;
  @Prop({ type: Boolean, default: false }) readonly showEndNode!: boolean;

  // flowTree 组件的 props, 注入到递归组件中
  @Provide() rootProps = {
    places: this.places,
    activeNode: this.activeNode,
  };

  @Watch('core', { immediate: true, deep: true })
  onCoreChange(newVal: any) {
    if (newVal && this.transformer) {
      const valueStr = JSON.stringify(newVal);
      const coreStr = JSON.stringify(this.transformer.core);
      if (valueStr !== coreStr) {
        this.reloadTree();
      }
    }
  }

  mounted() {
    this.reloadTree();

    this.removeEvents();

    EventBus.$on('addNode', this.addNode);
    EventBus.$on('addRoute', this.addRoute);
    EventBus.$on('removeNode', this.removeNode);
    EventBus.$on('addCondition', this.addCondition);
    EventBus.$on('clickNode', this.clickNode);
    EventBus.$on('nodeChange', this.nodeChange);
  }

  beforeDestroy() {
    this.removeEvents();
  }

  removeEvents() {
    EventBus.$off('addNode');
    EventBus.$off('addRoute');
    EventBus.$off('removeNode');
    EventBus.$off('addCondition');
    EventBus.$off('clickNode');
    EventBus.$off('nodeChange');
  }

  reloadTree() {
    if (!this.core) {
      return;
    }
    const { places, tree } = JSON.parse(JSON.stringify(this.core));
    if (places.length > 0 && tree.length > 0) {
      this.transformer = new Transformer({
        nodes: places,
        associations: tree,
      });
      this.rootNode = this.transformer.renderTree(this);
    }
  }

  clickNode(node: any) {
    const nodeCopy = Transformer.cloneNode(node);
    this.$emit('select', node, nodeCopy);
  }
  nodeChange() {
    this.transformer.deconstructNode();
    this.emitChange();
  }
  addNode(node: any, type: string, extra: any) {
    const { places } = this.transformer.core;
    const newNodeInfo = extra || {
      name: '新建节点',
      type,
      options: {},
    };
    // 加上新节点后，重新渲染树
    const context = this.transformer.addNode(node, newNodeInfo);
    this.rootNode = context.treeNode;
    this.$emit('change', this.transformer.core);
    this.$emit('addNode', context.newNode, type);
    this.emitChange();
  }
  addRoute(node: any) {
    this.rootNode = this.transformer.addRoute(node).treeNode;
    this.emitChange();
  }
  removeNode(node: any) {
    this.rootNode = this.transformer.removeNode(node).treeNode;
    this.emitChange();
  }
  addCondition(node: any) {
    this.rootNode = this.transformer.addCondition(node).treeNode;
    this.emitChange();
  }
  emitChange() {
    this.$emit('change', this.transformer.core);
  }
  getCore() {
    return this.transformer.deconstructNode().core;
  }
}
</script>

<template lang="pug">
ZoomBox
  slot(name="start")
    FlowPoint(type="start" v-if="showStartPoint")
  FlowTreeBole(
    v-model="rootNode"
    :key="rootNode.seq"
    :activeNode="activeNode"
    :disabled="disabled"
    :showStartNode="showStartNode"
    :showEndNode="showEndNode"
    :menuItems="places")
  slot(name="end")
    FlowPoint(type="end" v-if="showEndPoint")
</template>

<style lang="stylus" scoped></style>
