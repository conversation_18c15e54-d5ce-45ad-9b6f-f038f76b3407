<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component
export default class PlaceCondition extends Vue {
  @Prop({ type: Object, default: () => ({ type: '' }) }) readonly node!: IObject;

  expsOperatorMap: object = {
    'in?': '属于',
    'eql?': '等于',
    'include?': '包含',
    '&': '任意包含',
  };

  getJsonValue(value: any) {
    try {
      return JSON.parse(value);
    } catch (error) {
      return value;
    }
  }
}
</script>

<template lang="pug">
.node-item
  span(v-if="node.condition.is_default")
    | 默认条件
  span(v-else-if="node.condition.rules.some(o => !!o.key)")
    .rule(v-for="(rule, index) in node.condition.rules" :key="index")
      //- 多选项条件
      template(v-if="rule.key.includes('checkbox')")
        .rule-item
          span {{ rule.key_name }}
          a-tag(color="blue") {{ expsOperatorMap[rule.exps[0].opt] }}
          span(v-for="(val, index) in JSON.parse(rule.exps[0].val)" :key="index")
            | {{ index + 1 }}. {{ val }}；
      //- 字符型条件
      template(v-else-if="rule.exps && rule.exps.length > 0")
        .rule-item(v-if="rule.exps[0].opt === 'in?'")
          span {{ rule.key_name }}
          a-tag(color="blue") {{ expsOperatorMap[rule.exps[0].opt] }}
          span(v-for="(val, index) in JSON.parse(rule.exps[0].val)" :key="index")
            | {{ index + 1 }}. {{ val }}；
        .rule-item(v-else)
          span {{ rule.key_name }}
          a-tag(color="blue")
            | {{ expsOperatorMap[rule.exps[0].opt] }}
          span {{ getJsonValue(rule.exps[0].val) }}
      //- 发起人角色条件
      template(v-if="rule.key === 'role'")
        .rule-item(v-if="rule.roles.length > 1")
          span 第 {{ rule.roles[0].level }} 级学院{{ rule.roles[0].manager ? '主管' : '员工' }}
          a-tag(color="blue") {{ rule.roles[0].opt === '>=' ? '≤' : '<' }}
          span {{ rule.key_name }}
          a-tag(color="blue") {{ rule.roles[1].opt }}
          span 第 {{ rule.roles[1].level }} 级学院{{ rule.roles[1].manager ? '主管' : '员工' }}
        .rule-item(v-else)
          span {{ rule.key_name }}
          a-tag(color="blue") {{ rule.roles[0].opt }}
          span 第 {{ rule.roles[0].level }} 级学院{{ rule.roles[0].manager ? '主管' : '员工' }}
      //- 数值型条件
      template(v-else-if="rule.opts && rule.opts.length > 0")
        .rule-item(v-if="rule.opts.length > 1")
          span {{ rule.opts[0].val }}
          a-tag(color="blue") {{ rule.opts[0].opt === '>=' ? '≤' : '<' }}
          span {{ rule.key_name }}
          a-tag(color="blue") {{ rule.opts[1].opt }}
          span {{ rule.opts[1].val }}
        .rule-item(v-else)
          span {{ rule.key_name }}
          a-tag(color="blue") {{ rule.opts[0].opt === '=' ? '==' : rule.opts[0].opt }}
          span {{ rule.opts[0].val }}
  span.placeholder(v-else)
    | 请设置条件
</template>

<style lang="stylus"></style>
