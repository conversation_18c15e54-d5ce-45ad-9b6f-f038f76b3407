<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component
export default class PlacePoint extends Vue {
  @Prop({ type: Object, default: () => ({ type: '' }) }) readonly node!: IObject;

  get levelsMap() {
    return this.$store.state.levelsMap;
  }
}
</script>

<template lang="pug">
.place-point
  .node-item(v-if="node.options.name")
    div(style="margin-bottom: 4px;")
      span 指标：
      span {{ node.options.name }}
    div
      span 层级：
      span {{ node.options.level && levelsMap[node.options.level].label }}
    div(v-if="node.options.key_name")
      span 属性：
      span {{ node.options.key_name }}
    div(v-if="node.options.layer_key_name")
      span 层级关联属性：
      span {{ node.options.layer_key_name }}
  .node-item(v-else)
    | 请定义采集规则
</template>

<style lang="stylus"></style>
