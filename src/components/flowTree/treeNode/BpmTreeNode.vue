<script lang="ts">
import { Component, Vue, Prop, Model, Inject } from 'vue-property-decorator';
import { TransitionTypes, PlaceTypes } from '@/models/bpm/workflow';
import { IPlaceMenuTemplate } from '../types';
// place
import PlaceCondition from './places/Condition.vue';
import PlacePoint from './places/Point.vue';

@Component({
  name: 'BpmTreeNode',
  components: {
    PlaceCondition,
    PlacePoint,
  },
})
export default class BpmTreeNode extends Vue {
  inputVisible: boolean = false;

  @Model('change', { type: Object, default: () => ({ type: '' }) }) readonly node!: IObject;
  @Prop({ type: Boolean, default: false }) readonly disabled!: boolean;
  @Prop({ type: Array, default: () => [] }) readonly places!: IPlaceMenuTemplate[];
  @Prop({ type: Boolean, default: false }) readonly active!: boolean;

  get TransitionTypes() {
    return TransitionTypes;
  }
  get PlaceTypes() {
    return PlaceTypes;
  }
  get safeNode(): IObject {
    return {
      ...this.node,
      type: this.node.type || '',
      transition_type: this.node.transition_type || '',
    };
  }
  get nodeClass() {
    const transitionType = this.safeNode.transition_type || '';
    return {
      'approval-node': transitionType.startsWith('Transitions::Approval'),
      'notify-node': transitionType.startsWith('Transitions::Notify'),
      'route-node': this.safeNode.kind === 'condition',
      'start-node': this.safeNode.type === PlaceTypes.Start,
      'self-node': transitionType === TransitionTypes.ApprovalSponsorSelf,
      'end-node': this.safeNode.type === PlaceTypes.End,
      'wechat-node': transitionType.startsWith('Wechat::Transitions'),
      'callback-node':
        transitionType.includes('Callback') || transitionType.includes('Formula') || transitionType.includes('Point'),
      'disabled-node': this.disabled,
    };
  }

  onClick(...args: any[]) {
    if (!this.disabled) {
      this.$emit('click', ...args);
    }
  }
  onEditName() {
    if (this.disabled) {
      return;
    }
    this.inputVisible = true;
    this.$nextTick(() => {
      (this.$refs.input as any).select();
    });
  }
  onNameChange() {
    this.$emit('change', this.node);
  }
  getTeacherNames(options: any, defaultValue: any) {
    const users = options ? options.users || options.teachers || [] : [];
    if (users.length > 0) {
      return users.map((o: any) => o.name || o.teacher_name).join('、');
    }
    return defaultValue;
  }
  removeNode() {
    this.$emit('remove', this.node);
  }
  timeNumberToString(num: number) {
    return this.$moment(String(Number(num)).padStart(4, '0'), 'HH:mm').format('HH:mm');
  }
}
</script>

<template lang="pug">
.node(:class="nodeClass")
  //- 结束节点
  template(v-if="safeNode.type === 'Places::EndPlace'")
    .title(@click.stop="onEditName")
      input(
        ref="input"
        v-show="inputVisible"
        autofocus
        @blur="inputVisible = false"
        @keyup.enter="inputVisible = false"
        @change="onNameChange"
        v-model="node.name")
      span(v-show="!inputVisible")
        | {{ node.name }}
    .content
      .node-item
        | 流程结束
  //- 指标节点
  template(v-else-if="safeNode.transition_type.includes('Point')")
    .title
      span 指标采集
      .close-btn
        a-icon(type="close-circle" @click.stop="removeNode" theme="filled")
    .content(@click="onClick")
      PlacePoint(:node="safeNode")
  //- 条件节点
  template(v-else-if="node.kind === 'condition'")
    .title
      div 第 {{ node.condition.index + 1 }} 个条件
      .close-btn
        a-icon(type="close-circle" @click.stop="removeNode" theme="filled")
    .content(@click="onClick")
      PlaceCondition(:node="safeNode")
  //- 功能回调
  template(v-else-if="safeNode.transition_type.includes('Callback')")
    .title(@click.stop="onEditName")
      a-icon.icon(type="team")
      input(
        ref="input"
        v-show="inputVisible"
        autofocus
        @blur="inputVisible = false"
        @keyup.enter="inputVisible = false"
        @change="onNameChange"
        v-model="node.name")
      span(v-show="!inputVisible")
        | {{ node.name }}
      .close-btn(v-if="!disabled")
        a-icon(type="close-circle" @click.stop="removeNode" theme="filled")
    .content(@click="onClick")
      //- 功能回调
      template(v-if="safeNode.transition_type === TransitionTypes.FlowableCallback")
        .node-item(v-if="node.options.name")
          span {{ node.options.name }}
        span.placeholder(v-else)
          | 请选择
      //- api回调
      template(v-if="safeNode.transition_type === TransitionTypes.ApiCallback")
        .node-item(v-if="node.options.url")
          a-tag(color="blue") {{ node.options.method }}
          span {{ node.options.url }}
        span.placeholder(v-else)
          | 请设置
      //- 服务方法回调
      template(v-if="safeNode.transition_type === TransitionTypes.FunctionCallback")
        .node-item(v-if="node.options.function")
          .node-line
            span 类名：
            a-tag(color="blue") {{ node.options.class_name }}
          .node-line
            span 方法名：
            a-tag(color="blue") {{ node.options.function }}
        span.placeholder(v-else)
          | 请设置
      template(v-if="safeNode.transition_type === TransitionTypes.Formula")
        .node-item(v-if="node.options.formula_view")
          .node-line
            a-tag(color="blue") {{ node.options.formula_view }}
        span.placeholder(v-else)
          | 请设置
  //- 微信文章节点
  template(v-else-if="safeNode.transition_type.includes('Wechat')")
    .title(@click.stop="onEditName")
      a-icon.icon(type="team")
      input(
        ref="input"
        v-show="inputVisible"
        autofocus
        @blur="inputVisible = false"
        @keyup.enter="inputVisible = false"
        @change="onNameChange"
        v-model="node.name")
      span(v-show="!inputVisible")
        | {{ node.name }}
      .close-btn(v-if="!disabled")
        a-icon(type="close-circle" @click.stop="removeNode" theme="filled")
    .content(@click="onClick")
      .node-item(v-if="safeNode.transition_type === TransitionTypes.WechatDirectPublish")
        span 直接发布：
        span {{ timeNumberToString(safeNode.options.enable_begin_at) }} ~
        span  {{ timeNumberToString(safeNode.options.enable_end_at) }}
      .node-item(v-else-if="safeNode.transition_type === TransitionTypes.WechatTimedPublish")
        span 定时发布
  //- 审批节点 & 抄送节点
  template(v-else)
    .title(@click.stop="onEditName")
      a-icon.icon(type="team")
      input(
        ref="input"
        v-show="inputVisible"
        autofocus
        @blur="inputVisible = false"
        @keyup.enter="inputVisible = false"
        @change="onNameChange"
        v-model="node.name")
      span(v-show="!inputVisible")
        | {{ node.name }}
      .close-btn(v-if="!(disabled || safeNode.type === PlaceTypes.Start || safeNode.type === PlaceTypes.End)")
        a-icon(type="close-circle" @click.stop="removeNode" theme="filled")
    //- 审批节点
    .content(@click="onClick" v-if="safeNode.transition_type.startsWith('Transitions::Approval')")
      //- 学院主管
      .node-item(v-if="safeNode.transition_type === TransitionTypes.ApprovalLevelManager")
        span(v-if="safeNode.options.level")
          | 第 {{ safeNode.options.level }} 级{{ safeNode.options.manager ? '主管' : '员工' }}
      //- 部门主管
      .node-item(v-else-if="safeNode.transition_type === TransitionTypes.ApprovalSpecifyManager")
        span(v-if="safeNode.options.department_name")
          | {{ safeNode.options.department_name }}
      //- 科研分管领导
      .node-item(v-else-if="safeNode.transition_type === TransitionTypes.ApprovalResearchManager")
        span
          | 科研分管领导
      //- 直接主管
      .node-item(v-else-if="safeNode.transition_type === TransitionTypes.ApprovalDirectManager")
        span(v-if="safeNode.options.manager")
          | 直接主管
      //- 发起人自己
      .node-item(v-else-if="safeNode.transition_type === TransitionTypes.ApprovalSponsorSelf")
        span 发起人自己
      //- 发起人自选
      .node-item(v-else-if="safeNode.transition_type === TransitionTypes.ApprovalSponsorSelect")
        span 发起人自选
      //- 发起人自选
      .node-item(v-else-if="safeNode.transition_type === TransitionTypes.ApprovalFlowableRole")
        span {{ safeNode.options.role }}
      //- 审批人自选
      .node-item(v-else-if="safeNode.transition_type === TransitionTypes.ApprovalSelect")
        a-tag(color="blue" v-if="safeNode.options.mode === 'and'") 会签
        a-tag(color="blue" v-if="safeNode.options === 'or'") 或签
        | 审批人自选
      //- 指定成员 (新版)
      .node-item(v-else-if="safeNode.transition_type.startsWith('Transitions::Approval::User')")
        a-tag(color="blue" v-if="safeNode.transition_type === 'Transitions::Approval::User'") 指派一人
        a-tag(color="blue" v-if="safeNode.transition_type === 'Transitions::Approval::UserAny'") 或签
        a-tag(color="blue" v-if="safeNode.transition_type === 'Transitions::Approval::UserAll'") 会签
        | {{ getTeacherNames(safeNode.options, '请指定成员') }}
      //- 指定成员 (老版本兼容)
      .node-item(v-else-if="safeNode.transition_type.startsWith('Transitions::Approval::Teacher')")
        a-tag(color="blue" v-if="safeNode.transition_type === 'Transitions::Approval::Teacher'") 指派一人
        a-tag(color="blue" v-if="safeNode.transition_type === 'Transitions::Approval::TeacherAny'") 任意一人
        | {{ getTeacherNames(safeNode.options, '请指定成员') }}
    //- 抄送节点
    .content(@click="onClick" v-else-if="safeNode.transition_type.startsWith('Transitions::Notify')")
      //- 学院主管
      .node-item(v-if="safeNode.transition_type === TransitionTypes.NotifyLevelManager")
        span(v-if="safeNode.options.level")
          | 第 {{ safeNode.options.level }} 级{{ safeNode.options.manager ? '主管' : '员工' }}
      //- 部门主管
      .node-item(v-else-if="safeNode.transition_type === TransitionTypes.NotifySpecifyManager")
        span(v-if="safeNode.options.department_name")
          | {{ safeNode.options.department_name }}
      //- 直接主管
      .node-item(v-else-if="safeNode.transition_type === TransitionTypes.NotifyDirectManager")
        span(v-if="safeNode.options.manager")
          | 直接主管
      //- 抄送人
      .node-item(v-else-if="safeNode.transition_type === TransitionTypes.NotifyTeacher")
        span {{ getTeacherNames(safeNode.options, '请指定成员') }}
      .node-item(v-else-if="safeNode.transition_type === TransitionTypes.NotifySponsorSelect")
        span 发起人自选
      //- 分配角色
      .node-item(v-else-if="safeNode.transition_type === TransitionTypes.NotifyFlowableRole")
        span {{ safeNode.options.role }}
    //- 开始结束
    .content(@click="onClick" v-else)
      //- 开始节点
      .node-item(v-if="safeNode.type === PlaceTypes.Start")
        template(v-if="safeNode.options && safeNode.options.res_type === 'Student'")
          | 所有学生
        template(v-else)
          | {{ getTeacherNames(safeNode.options, '所有教师') }}
      //- 结束节点
</template>

<style lang="stylus" scoped>
.node
  position relative
  margin 0 auto
  min-height 80px
  width 260px
  border-radius 4px
  background-color #ffffff
  color #383838
  cursor pointer
  &:after
    position absolute
    top 0
    right 0
    bottom 0
    left 0
    z-index 2
    border 1px solid transparent
    border-radius 4px
    box-shadow 0 2px 5px 0 rgba(0, 0, 0, 0.1)
    content ''
    transition all 0.1s cubic-bezier(0.645, 0.045, 0.355, 1)
    pointer-events none
  &:hover, &:focus, &:active
    &:after
      box-shadow 0 4px 15px 0 rgba(0, 0, 0, 0.2)
    .title .close-btn
      display block
  .title
    position relative
    display flex
    align-items center
    padding 4px 32px 4px 14px
    width 100%
    height 32px
    border-radius 4px 4px 0 0
    background-color inherit
    color inherit
    text-align left
    font-size 15px
    line-height 24px
    &:hover
      span
        text-decoration underline
    img.icon
      margin-right 6px
      width 18px
      height 18px
    span
      display block
      flex-grow 1
      overflow hidden
      padding 0 6px
      text-overflow ellipsis
      white-space nowrap
    input
      flex-grow 1
      padding 0 6px
      height 24px
      outline none
      border none
      border-radius 3px
      filter brightness(80%)
      background-color inherit
      color inherit
      line-height 24px
    .close-btn
      position absolute
      top 8px
      right 10px
      z-index 100
      display none
      border-radius 50%
      color #fff
      text-align center
      font-size 16px
      line-height 16px
      opacity 0.6
      cursor pointer
  .content
    position relative
    padding 14px
    background-color #fff
    color #383838
    font-size 14px
    .node-item
      overflow hidden
      text-overflow ellipsis
      .node-line
        margin-bottom 6px
        &:last-child
          margin-bottom 0
      .placeholder
        position relative
        padding-right 20px
        color #A6A6A6
        &:after
          position absolute
          top 50%
          right 6px
          width 8px
          height 8px
          border-top 1px solid #A6A6A6
          border-right 1px solid #A6A6A6
          content ''
          transform translateY(-50%) rotateZ(45deg)
      .rule
        display -webkit-box
        overflow hidden
        margin-bottom 5px
        color #333
        font-size 12px
        -webkit-box-orient vertical
        -webkit-line-clamp 2
        span
          padding 0 4px
        .options
          margin-bottom 0
          padding-left 24px

.approval-node
  .title
    background #F29851
    color #fff

.notify-node
  .title
    background #1890ff
    color #fff

.route-node
  .title
    background #f5f5f5
    color #15bc83
    .close-btn
      color #aaa

.start-node, .self-node
  .title
    background #15bc83
    color #fff

.end-node
  .title
    background #E03D29
    color #fff

.wechat-node
  .title
    background #75C940
    color #fff

.callback-node
  .title
    background #36455A
    color #fff

.disabled-node
  cursor unset
  &:hover
    &:after
      border 1px solid transparent
      box-shadow 0 2px 5px 0 rgba(0, 0, 0, 0.1)
  .title:hover
    span
      text-decoration none
</style>
