<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';

@Component
export default class ZoomBox extends Vue {
  rate: number = 100;
  min: number = 30;

  get scale() {
    return this.rate / 100;
  }

  zoomOut() {
    if (this.rate > this.min) {
      this.rate -= 10;
    }
  }
  zoomIn() {
    this.rate += 10;
  }
}
</script>

<template lang="pug">
.flow-tree-zoom-box-wrapper
  .zoom
    .zoom-btn.zoom-out(@click="zoomOut")
    span.rate {{ rate }}%
    .zoom-btn.zoom-in(@click="zoomIn")
  .scale-box-wrapper
    .scale-box(:style="{ transform: `scale(${scale})` }")
      slot
</template>

<style lang="stylus" scoped>
.flow-tree-zoom-box-wrapper
  position absolute
  top 0
  right 0
  bottom 0
  left 0
  .zoom
    position absolute
    top 20px
    right 40px
    z-index 10
    display flex
    justify-content space-between
    align-items center
    width 125px
    height 40px
    background #f5f5f7
    .zoom-btn
      width 30px
      height 30px
      background #fff
      background-size 100%
      background-repeat no-repeat
      color #c1c1cd
      cursor pointer
    .zoom-in
      background-image url('~@/assets/images/flow-tree/plus.png')
    .zoom-out
      background-image url('~@/assets/images/flow-tree/minus.png')
    .rate
      color #191f25
      font-size 12px
  .scale-box-wrapper
    height 100%
    width 100%
    overflow auto
    background-color #f5f5f7
    .scale-box
      position relative
      display flex
      flex-wrap wrap
      justify-content center
      align-items flex-start
      padding 60px 0
      min-width min-content
      width 100%
      background-color #f5f5f7
      transform scale(1)
      transform-origin 0px 0px 0px
</style>
