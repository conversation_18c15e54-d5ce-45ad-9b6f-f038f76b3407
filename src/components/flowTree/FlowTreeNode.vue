<script lang="ts">
import { Component, Vue, Prop, Model, Inject } from 'vue-property-decorator';
import BpmTreeNode from './treeNode/BpmTreeNode.vue';

@Component({
  components: {
    BpmTreeNode,
  },
})
export default class FlowTreeNode extends Vue {
  @Model('change', { type: Object, default: () => ({ type: '' }) }) readonly node!: IObject;
  @Prop({ type: Boolean, default: false }) readonly disabled!: boolean;
  @Prop({ type: Boolean, default: false }) readonly active!: boolean;

  // 流程图 root 组件
  @Inject('rootProps') rootProps!: IObject;

  // 菜单可以点击的节点配置
  get places() {
    return this.rootProps.places || [];
  }

  change() {
    this.$emit('change', this.node);
  }
  clickNode() {
    this.$emit('click', this.node);
  }
  removeNode() {
    this.$emit('remove', this.node);
  }
}
</script>

<template lang="pug">
BpmTreeNode(
  v-bind="$props"
  :places="places"
  @remove="removeNode"
  @click="clickNode"
  @change="change")
</template>
