<script lang="ts">
import { Component, Vue, Prop, Inject } from 'vue-property-decorator';
import { IPlaceMenuTemplate } from './types';

@Component
export default class FlowTreeAddButton extends Vue {
  private visible: boolean = false;

  @Prop({ type: Object, default: () => ({}) }) private node!: object;
  @Prop({ type: Array, default: () => [] }) private menuItems!: IPlaceMenuTemplate[];

  handleAction(menu: IPlaceMenuTemplate) {
    if (menu.isRoute) {
      this.addRoute();
    } else {
      this.addNode(menu);
    }
  }
  addNode(action: IPlaceMenuTemplate) {
    const extra = {
      name: action.name,
      type: action.type,
      options: {},
      ...action.payload,
    } as IObject;
    if (action.transition_type) {
      extra.transition_type = action.transition_type;
    }
    this.$emit('addNode', this.node, action.type, extra);
    this.visible = false;
  }
  addRoute() {
    this.$emit('addRoute', this.node);
    this.visible = false;
  }
}
</script>

<template lang="pug">
.add-node-btn
  a-popover(trigger="click" placement="rightTop" v-model="visible")
    a-button.btn(icon="plus")
    template(slot="content")
      .menu-box
        .triangle
          .triggle-icon
        .column(v-for="(item, index) in menuItems")
          .action(@click="handleAction(item)")
            img(:src="item.icon" v-if="item.icon")
            .info
              .name {{ item.name }}
              .desc {{ item.desc }}
</template>

<style lang="stylus" scoped>
.add-node-btn
  position relative
  padding 28px 0 28px
  text-align center
  user-select none
  .btn
    position relative
    padding 0
    width 30px
    height 30px
    outline none
    border none
    border-radius 50%
    background #3296fa
    box-shadow 0 2px 4px 0 rgba(0, 0, 0, 0.1)
    color #ffffff
    cursor pointer
    transition all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1)
    &:hover
      box-shadow 0 13px 27px 0 rgba(0, 0, 0, 0.1)
      transform scale(1.1)

.menu-box
  position relative
  display flex
  flex-wrap wrap
  margin -12px -16px
  padding 14px
  min-height 160px
  width 360px
  border-radius 3px
  background rgba(255, 255, 255, 1)
  box-shadow 0px 7px 21px 0px rgba(0, 0, 0, 0.1)
  .triangle
    position absolute
    top 8px
    left -7px
    overflow hidden
    width 7px
    height 14px
    .triggle-icon
      width 9.8px
      height 9.8px
      background #fff
      box-shadow 0px 7px 21px 0px rgba(0, 0, 0, 0.1)
      transform rotateZ(45deg) translate(4px, -1px)
  .column
    flex 0 0 50%
    .action
      display flex
      align-items center
      margin 6px
      padding 10px 12px
      border 1px solid rgba(229, 229, 229, 1)
      border-radius 3px
      background rgba(255, 255, 255, 1)
      cursor pointer
      &:hover
        border-color #3da8f5
      img
        margin-right 12px
        width 22px
        height 22px
      .name
        margin-bottom 2px
        color rgba(56, 56, 56, 1)
        font-weight 500
        font-size 13px
        line-height 16px
      .desc
        color rgba(166, 166, 166, 1)
        font-size 12px
        line-height 16px
</style>
