<script lang="ts">
import { Component, Vue, Prop, Model } from 'vue-property-decorator';
import FlowTree from './FlowTree.vue';

const defaultCore = {
  tree: [{ source: { id: null, seq: 'start' }, target: { id: null, seq: 'end' } }],
  places: [
    { id: null, name: '开始', seq: 'start', type: 'Place', transition_type: 'Transition' },
    { id: null, name: '结束', seq: 'end', type: 'Place' },
  ],
};

@Component({
  components: {
    FlowTree,
  },
})
export default class FlowTreeViewer extends Vue {
  @Model('input', { type: Boolean, default: false }) readonly value!: boolean;
  @Prop({ type: Object, default: () => ({ core: defaultCore }) }) readonly workflow!: IObject;

  get visible() {
    return this.value;
  }
  set visible(value) {
    this.$emit('input', value);
  }

  close() {
    this.$emit('input', false);
  }
}
</script>

<template lang="pug">
a-modal(
  v-model="visible"
  title="流程图概览"
  :footer="null"
  :width="960"
  @onCancel="close")
  .flow-tree-container
    FlowTree(
      v-if="workflow.core"
      :core="workflow.core"
      :showStartPoint="false"
      :showStartNode="true"
      :disabled="true")
</template>

<style lang="stylus" scoped>
.flow-tree-container
  position relative
  height 550px
</style>
