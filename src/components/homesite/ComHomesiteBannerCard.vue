<script lang="ts">
import FileServer from '@/models/file';
import { IHomesiteBanner } from '@/types/model';
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class ComHomesiteBannerCard extends Vue {
  @Prop({ type: Object, required: true }) banner!: IHomesiteBanner;

  get image() {
    return new FileServer().getThumbnailUrl(this.banner.image.attachments[0] || {}, 688, 250);
  }
}
</script>

<template lang="pug">
.com-homesite-banner-card
  .image
    img(:src='image')
  .title {{ banner.title }}
</template>

<style lang="stylus" scoped>
.com-homesite-banner-card
  margin-top 10px
  width 95%
  box-shadow 0 2px 4px 0 rgba(0, 0, 0, 0.1)
  .image
    width 100%
    position relative
    padding-top 40%
    height 0
    overflow hidden
    img
      position absolute
      top 0
      width 100%
  .title
    padding 20px 10px
    color #000000
    font-size 14px
    min-height 61px
</style>
