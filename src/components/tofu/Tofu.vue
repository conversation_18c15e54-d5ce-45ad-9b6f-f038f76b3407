<script lang="ts">
import { Component, Vue, Prop, Model } from 'vue-property-decorator';
import { ITofu, TofuApp } from '@/models/tofu/app';
import { TofuStar } from '../../models/tofu/star';
@Component({
  components: {},
})
export default class Tofu extends Vue {
  @Prop({ type: Object, default: {}, required: true }) private tofu!: ITofu;
  @Prop({ type: Boolean, default: false, required: false }) private starVisible!: boolean;
  @Prop({ type: Boolean, default: false, required: false }) private subscript!: boolean;
  @Prop({ type: Boolean, default: false, required: false }) private orderVisible!: boolean;
  @Prop({ type: Boolean, default: false, required: false }) private websiteImage!: boolean;

  clickTofu() {
    this.$emit('clickTofu', this.tofu);
  }

  updateTofu() {
    this.$emit('update', this.tofu);
  }
}
</script>

<template lang="pug">
.tofu
  .shell
    .right-top
      slot(name="rightTop" :tofu="tofu")
    .images
      .main-image-shell
        .website-image(v-if="websiteImage")
          img(:src="tofu.image")
        .main-image(v-else)
          img(:src="tofu.image || require('../../assets/icons/portal/tofu_placeholder.png')")
          .subscript(v-if="subscript && tofu.top_tofu_image !== null")
            img(:src="tofu.top_tofu_image || require('../../assets/icons/portal/tofu_placeholder.png')")
        .instance-count(v-if="tofu.instance_count !== null && tofu.instance_count !== 0")
          .count {{ tofu.instance_count }}
    .titles
      .one-title(v-if="tofu.top_tofu_name === undefined || tofu.top_tofu_name === null")
        .title {{ tofu.name }}
      .two-title(v-else)
        .title {{ tofu.name }}
        .sub-title.title {{ tofu.top_tofu_name }}
  .hover-shell(@click="clickTofu")
    .order-buttom.text-gray(v-if="orderVisible")
      a-tooltip
        template(#title)
          | 拖拽调整顺序
        a-icon(type="border-outer")
    //- .click-buttom
    //-   .gary-buttom(ghost) 前往
    .right-top-hidden
      slot(name="rightTopHidden" :tofu="tofu")
</template>

<style lang="stylus" scoped>
.tofu
  position relative
  padding-top 100%
  width 130px
  height 130px
  border 1px solid #E5E5E5
  border-radius 4px
  &:hover
    .hover-shell
      display block
  .shell
    position absolute
    top 0
    left 0
    width 100%
    height 100%
    .right-top
      position absolute
      top 5%
      right 8%
      z-index 999
    .images
      display flex
      justify-content center
      height 58%
      .main-image-shell
        position relative
        display flex
        justify-content center
        align-items center
        padding-top 10%
        width 40%
        height 100%
        .main-image
          position relative
          width 30px
          height 30px
          img
            width 100%
          .subscript
            position absolute
            right -5px
            bottom -3px
            margin 0
            width 14px
            height 14px
            border 3px solid white
            border-radius 20%
            img
              z-index 99
              width 100%
        .website-image
          display flex
          justify-content center
          align-items center
          width 30px
          height 30px
          border-radius 60%
          background red
          -moz-border-radius 60%
          -webkit-border-radius 60%
        .instance-count
          position absolute
          top 20%
          right -20%
          width 20px
          border-radius 10px
          background-color #F95051
          color white
          text-align center
          line-height 20px
          -moz-border-radius 10px
    .titles
      height 42%
      .title, .one-title
        display flex
        justify-content center
        min-height 20px
        height 100%
        text-align center
      .one-title
        padding-top 10%
      .two-title
        padding-top 3%
        .sub-title
          color #A6A6A6
  .hover-shell
    position absolute
    top 0
    left 0
    display none
    width 100%
    height 100%
    // background-color #38424D
    // opacity 0.7
    .order-buttom
      position absolute
      top 5%
      left 8%
      width 100px
      // color white
    .click-buttom
      display flex
      justify-content center
      align-items center
      width 100%
      height 100%
      color white
      .gary-buttom
        padding 6px 10px
        border 1px solid rgba(166, 166, 166, 1)
        border-radius 4px
        background-color rgba(255, 255, 255, 0.1)
        font-size 16px
        cursor pointer
    .right-top-hidden
      position absolute
      top 5%
      right 8%
</style>
