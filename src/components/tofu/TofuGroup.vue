<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import MenuLayout from '@/layouts/MenuLayout.vue';
import instance, { InstanceType } from '@/models/bpm/instance';
import { ITofu, TofuApp } from '@/models/tofu/app';
import Tofu from '@/components/tofu/Tofu.vue';
import BaseStore from '../../store/BaseStore';
import draggable from 'vuedraggable';

@Component({
  components: {
    Tofu,
    draggable,
  },
})
export default class TofuGroup extends Vue {
  @Prop({ type: Object, default: () => ({}), required: true }) private store!: any;
  @Prop({ type: Object, default: () => ({}), required: false }) private query!: any;
  // 排除掉的 url Tofu，比如在统一门户不显示统一门户这个豆腐块
  @Prop({ type: Array, default: () => [], required: false }) private exceptUrls!: (string | undefined)[];

  // Tofu
  @Prop({ type: Boolean, default: false, required: false }) private starVisible!: boolean;
  @Prop({ type: Boolean, default: false, required: false }) private subscript!: boolean;
  @Prop({ type: Boolean, default: false, required: false }) private editVisible!: boolean;
  @Prop({ type: Boolean, default: false, required: false }) private orderVisible!: boolean;

  tofus: ITofu[] = [];

  get tofuIds() {
    return this.tofus.map(tofu => tofu.id as number);
  }

  mounted() {
    this.fetchTofus();
  }

  async fetchTofus() {
    const { data } = await this.store.fetch({
      per_page: 999999,
      ...this.query,
    });
    this.tofus = this.store.records.filter((record: ITofu) => !this.exceptUrls.includes(record.url));
    if (this.editVisible) {
      this.tofus.push({ id: -1, isEdit: true });
    }
  }

  clickTofu(tofu: ITofu) {
    this.$emit('clickTofu', tofu);
  }

  clickCreateTofu() {
    this.$emit('clickCreateTofu');
  }

  endDrag(event: any) {
    const { newIndex } = event;
    const tofu = this.tofus[newIndex];
    this.store.update({ id: tofu.id, position: newIndex + 1 });
  }

  onChoose() {
    const doc = document.getElementById('tofu-1'); // tofu.id = -1
    (doc as any).draggable = false;
  }
}
</script>

<template lang="pug">
.tofu-group
  .tofus
    draggable.draggable(v-model='tofus', @end='endDrag', :disabled='!orderVisible', @choose='onChoose')
      transition-group.group
        .tofu-shell(v-for='tofu in tofus', :key='tofu.id', :id='`tofu${tofu.id}`')
          .edit(v-if='tofu.isEdit', @click='clickCreateTofu')
            .plus-shell
              .plus +
              .plus-text 添加应用
          Tofu.tofu(
            v-else,
            :tofu='tofu',
            :starVisible='starVisible',
            :subscript='subscript',
            :orderVisible='orderVisible',
            @clickTofu='clickTofu',
            @fresh='fetchTofus'
          )
            template(#rightTop='{ tofu }')
              slot(name='rightTop', :tofu='tofu')
            template(#rightTopHidden='{ tofu }')
              slot(name='rightTopHidden', :tofu='tofu')
</template>

<style lang="stylus" scoped>
.tofu-group
  width 100%
  .tofus
    .draggable
      width 100%

.group
  display flex
  flex-wrap wrap
  justify-content wrap
  .tofu-shell
    margin 7px
    cursor pointer
    &:hover
      margin-top -1px
      transition 0.1s
    .tofu
      &:hover
        box-shadow 0px 2px 4px -1px rgba(0, 0, 0, 0.1)
  .edit
    display flex
    justify-content center
    align-items center
    width 130px
    height 130px
    border 1px dashed #E5E5E5
    background-color #FAFAFA
    cursor pointer
    .plus-shell
      color #A6A6A6
      .plus
        text-align center
        font-weight 120
        font-size 40px
</style>
