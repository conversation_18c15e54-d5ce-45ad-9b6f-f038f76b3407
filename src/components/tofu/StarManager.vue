<script lang="ts">
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import { ITofu } from '@/models/tofu/app';

@Component({
  components: {},
})
export default class StarManager extends Vue {
  @Model('input', { type: Boolean, default: false }) visible!: boolean;
  @Prop({ type: Array, default: [], required: true }) private apps!: ITofu[];

  selectedStarIds: number[] = [];

  @Watch('apps')
  updateSelectedStarIds() {
    this.setSelectedStarIds();
  }

  setSelectedStarIds() {
    const result: number[] = [];
    this.apps.forEach(app => {
      if (app.stared) {
        result.push(app.id as number);
      }
      (app.subs || []).forEach(subApp => {
        if (subApp.stared) {
          result.push(subApp.id as number);
        }
      });
    });
    this.selectedStarIds = result;
  }

  cancel() {
    this.$emit('input', false);
  }

  confirm() {
    this.$emit('confirm', this.selectedStarIds);
    this.$emit('input', false);
  }

  cancelStar(app: ITofu) {
    this.selectedStarIds.splice(
      this.selectedStarIds.findIndex(id => id === (app.id as number)),
      1,
    );
  }

  addStar(app: ITofu) {
    this.selectedStarIds.push(app.id as number);
  }
}
</script>

<template lang="pug">
.star-manager
  a-modal(
      title='标星管理' :visible="visible"
      @cancel="cancel"
    )
      template(#footer)
        a-button(key="cancel" @click="cancel") 取消
        a-button(key="save" type="primary" @click="confirm") 保存
      .shell
        a-collapse.college(:bordered="false")
          a-collapse-panel.college-panel(v-for="app in apps" :show-arrow="false" :key="app.id" )
            template(#header="props")
              .panel
                .panel-left
                  img.panel-img(v-if="app.image" :src="app.image")
                  img.panel-img(v-else src="@/assets/icons/portal/tofu_placeholder.png")
                  span.panel-text {{ app.name }}
                  a-icon(type="down" v-if="app.subs.length !== 0" :rotate="props.isActive ? 180 : 0")
                .panel-right
                  .stared(v-if="selectedStarIds.indexOf(app.id) !== -1")
                    span.text-primary.action(@click.stop="") 已添加
                    span.text-gray.action(@click.stop="cancelStar(app)") 取消标星
                  a-button.text-primary.button(v-else @click.stop="addStar(app)")
                    | 添加
                    
            .empty-panel(v-if="app.subs.length === 0")
            a-collapse-panel.college-panel(v-else v-for="subApp in app.subs" :show-arrow="false" :key="subApp.id")
              template(#header)
                .panel
                  .panel-left
                    img.panel-img(v-if="subApp.image" :src="subApp.image")
                    img.panel-img(v-else src="@/assets/icons/portal/tofu_placeholder.png")
                    span.panel-text {{ subApp.name }}
                  .panel-right.sub-panel-right
                    .stared(v-if="selectedStarIds.indexOf(subApp.id) !== -1")
                      span.text-primary.action(@click.stop="") 已添加
                      span.text-gray.action(@click.stop="cancelStar(subApp)") 取消标星
                    a-button.text-primary.button(v-else @click.stop="addStar(subApp)")
                      | 添加


</template>

<style lang="stylus" scoped>
.shell
  overflow scroll
  height 450px

.college
  background-color white
  .college-panel
    padding 10px 0
    .empty-panel
      height 0
  .panel
    display flex
    justify-content space-between
    .panel-right
      margin-right -30px
      .stared
        margin 5px 0
      .action
        padding 0 10px
        border-right 1px solid #E5E5E5
        &:last-child
          padding-right 0
          border none
      .button
        border 1px solid #3DA8F5
        border-radius 4px
    .sub-panel-right
      margin-right -47px
  .panel-img
    margin-right 13px
    width 21px
    height 21px
  .panel-text
    margin-right 13px
</style>
