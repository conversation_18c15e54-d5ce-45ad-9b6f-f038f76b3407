<script lang="ts">
import { Component, Vue, Prop, Model } from 'vue-property-decorator';
import MenuLayout from '@/layouts/MenuLayout.vue';
import instance, { InstanceType } from '@/models/bpm/instance';
import { ITofu, TofuApp } from '@/models/tofu/app';
import Tofu from '@/components/tofu/Tofu.vue';
import BaseStore from '../../store/BaseStore';
import draggable from 'vuedraggable';
import TofuGroup from '@/components/tofu/TofuGroup.vue';

@Component({
  components: {
    Tofu,
    draggable,
    TofuGroup,
  },
})
export default class TofuManage extends Vue {
  @Prop({ type: Object, default: () => ({}), required: true }) private store!: any;
  @Prop({ type: Array, default: () => [], required: true }) private template!: any;
  @Prop({ type: Object, default: () => ({}), required: false }) private query!: any;

  // Tofu
  @Prop({ type: Boolean, default: false, required: false }) private starVisible!: boolean;
  @Prop({ type: Boolean, default: false, required: false }) private subscript!: boolean;
  @Prop({ type: Boolean, default: false, required: false }) private editVisible!: boolean;
  @Prop({ type: Boolean, default: false, required: false }) private orderVisible!: boolean;

  tofus: ITofu[] = [];
  selectedTofu: ITofu = {};
  formData: ITofu = {};
  dialogVisible: boolean = false;

  // mounted() {
  //   this.fetchTofus();
  // }

  // async fetchTofus() {
  //   const { data } = await this.store.fetch(this.query);
  //   this.tofus = this.store.records;
  //   this.tofus.push({ id: -1, isEdit: true });
  // }

  clickTofu(tofu: ITofu) {
    this.$emit('clickTofu', tofu);
  }

  endDrag(event: any) {
    const { newIndex } = event;
    const tofu = this.tofus[newIndex];
    this.store.update({ id: tofu.id, position: newIndex + 1 });
  }

  onChoose() {
    const doc = document.getElementById('tofu-1'); // tofu.id = -1
    (doc as any).draggable = false;
  }

  showEdit(tofu: ITofu) {
    this.dialogVisible = true;
    this.selectedTofu = tofu;
    this.formData = tofu;
  }

  showCreate() {
    this.dialogVisible = true;
    this.formData = {};
  }

  handleCancel() {
    this.selectedTofu = {};
    this.dialogVisible = false;
  }

  create(payload: ITofu) {
    this.$emit('handlePayload', payload);
    this.store
      .create(payload)
      .then(() => {
        this.$message.success('创建成功');
        (this.$refs.group as any).fetchTofus();
      })
      .catch((error: any) => this.$message.error('创建失败'));
    this.dialogVisible = false;
  }

  update(payload: ITofu) {
    this.$emit('handlePayload', payload);
    this.store
      .update(payload)
      .then(() => {
        this.$message.success('更新成功');
        (this.$refs.group as any).fetchTofus();
      })
      .catch((error: any) => this.$message.error('更新失败'));
    this.dialogVisible = false;
  }

  destroy(tofu: ITofu) {
    this.store
      .delete(tofu.id as number)
      .then(() => {
        this.$message.success('删除成功');
        (this.$refs.group as any).fetchTofus();
      })
      .catch((error: any) => this.$message.error('删除失败'));
  }
}
</script>

<template lang="pug">
.tofu-website-manager
    .tofus
      TofuGroup(
        ref="group"
        :store="store" :editVisible="editVisible" :orderVisible="orderVisible"
        :websiteImage="true" :query="query"
        @clickTofu="clickTofu"
        @clickCreateTofu="showCreate"
      )
        template(#rightTopHidden="{ tofu }")
          a-dropdown.dropdown(:trigger="['click']")
            a.setting.text-gray(@click.stop="e => e.preventDefault()")
              a-icon(type="setting")
            a-menu(slot="overlay")
              a-menu-item.icon-group(@click="showEdit(tofu)")
                a-icon(type="edit")
                .icon-text 编辑
              a-menu-item.icon-group(@click="destroy(tofu)")
                a-icon(type="delete")
                .icon-text 删除
    TaFormDialog(
      :title="selectedTofu.id ? '编辑平台' : '添加平台'"
      :visible="dialogVisible"
      :loading="store.loading"
      :template="template"
      :formData="formData"
      @create="create"
      @update="update"
      @input="handleCancel"
    )
</template>

<style lang="stylus" scoped>
.tofu-website-manager
  .tofus
    .draggable
      width 100%

.group
  display flex
  flex-wrap wrap
  justify-content wrap
  .tofu-shell
    margin 1%
    width 18%
  .edit
    display flex
    justify-content center
    align-items center
    height 100%
    border 1px dashed #E5E5E5
    background-color #FAFAFA
    cursor pointer
    .plus-shell
      color #A6A6A6
      .plus
        text-align center
        font-weight 120
        font-size 40px
</style>
