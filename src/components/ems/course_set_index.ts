import { Component, Vue, Prop } from 'vue-property-decorator';
import BaseStore from '@/store/BaseStore';
import { ICourseSet } from '@/models/ems/course_set';

@Component({
  components: {},
})
export default class EmsCourseSetTable extends Vue {
  @Prop({ type: Object, default: () => ({}) }) private store!: BaseStore;

  change(...args: any) {
    this.$emit('change', ...args);
  }

  onShow(record: ICourseSet) {
    this.$emit('show', record);
  }
}
