<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import BaseStore from '@/store/BaseStore';
import { ICourseSet } from '@/models/ems/course_set';

@Component({
  components: {},
})
export default class EmsCourseSetTable extends Vue {
  @Prop({ type: Object, default: () => ({}) }) private store!: BaseStore;

  change(...args: any) {
    this.$emit('change', ...args);
  }

  onShow(record: ICourseSet) {
    this.$emit('show', record);
  }
}
</script>

<template lang="pug">
AdminTable(
  :store="store"
  :bordered="true"
  rowClassName="click-row"
  @rowClick="onShow"
  @change="change")
  a-table-column(title="课程名称" dataIndex="name" :width="200" fixed="left")
  a-table-column(title="课程代码" dataIndex="code" :width="130")
  a-table-column(title="学分" dataIndex="credits" :width="70" align="center")
  a-table-column(title="学时" dataIndex="period" :width="70" align="center")
  a-table-column(title="培养层次" dataIndex="education" :width="80" align="center")
  a-table-column(title="课程种类" dataIndex="category" :width="90" align="center")
  a-table-column(title="课程类型" dataIndex="course_type" :width="140")
  a-table-column(title="考试方式" dataIndex="exam_mode" :width="80" align="center")
  a-table-column(title="相关共享" dataIndex="paste_major_count" :width="80" align="center")
  a-table-column(title="所属目录" dataIndex="course_dir_name" :width="80" align="center")
  a-table-column(title="所属学院" dataIndex="department_name" :width="80" align="center")
  a-table-column(title="所属项目" dataIndex="project" :width="80" align="center")
  a-table-column(title="课时统计" dataIndex="course_hours" :width="80" align="center")
  a-table-column(title="状态" dataIndex="enabled" :width="70" fixed="right")
    template(slot-scope="enabled")
      span.text-success(v-if="enabled")
        | 使用中
      span.text-gray(v-else)
        | 已停用
</template>

<style lang="stylus" scoped></style>
