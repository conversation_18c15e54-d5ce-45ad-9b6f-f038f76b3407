<template lang="pug">
.tab-page
  .tabs
    .tab(v-for="(tab, index) in tabs" :key="index"
    :class="{'tab-active': tabIndex === tab.value}"
    @click="onTab(tab)") {{ tab.label }}
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class CenterTabs extends Vue {
  @Prop({ type: String, default: 'faculty' }) private tabIndex?: string;
  private tabs: object[] = [
    {
      label: '学院',
      value: 'faculty',
      path: '/dorm/faculties',
    },
    {
      label: '宿舍楼',
      value: 'dormitory',
      path: '/dorm/dormitories',
    },
  ];

  public onTab(val: any) {
    if (this.tabIndex !== val.value) {
      this.$router.replace(val.path);
    }
  }
}
</script>

<style lang="stylus" scoped>
.tab-page
  position fixed
  top 50px
  left 0px
  z-index 1000
  width 100%
  height 48px
  border-top 1px rgba(0, 0, 0, 0.1) solid
  background #fff
  box-shadow 0 4px 7px 0 rgba(0, 0, 0, 0.1)
  .tabs
    display flex
    justify-content space-between
    align-items center
    margin 0px auto
    width 130px
    .tab
      padding 13px 0px 12px
      border-bottom 2px #fff solid
      color #A6A6A6
      font-weight 500
      font-size 14px
      line-height 20px
      cursor pointer
    .tab-active
      border-bottom 2px #3DA8F5 solid
      color #383838
</style>
