<template lang="pug">
.member-page(:style="{paddingBottom: paddingBottom + 'px'}")
  AdminTable(
    :data="data"
    :totalCount="totalCount"
    :currentPage="currentPage"
    :perPage="perPage"
    :showHeader="showHeader"
    :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
    @tableChange="tableChange")
    a-table-column(dataIndex="code" title="学号")
    a-table-column(title="姓名")
      template(slot-scope="scope")
        span.title {{ scope.name }}
    a-table-column(title="性别")
      template(slot-scope="scope")
        span {{ scope.sex }}
    a-table-column(title="系")
      template(slot-scope="scope")
        span {{ scope.faculty }}
    a-table-column(title="专业")
      template(slot-scope="scope")
        span {{ scope.major }}
    a-table-column(title="年级")
      template(slot-scope="scope")
        span {{ scope.grade }}
    a-table-column(title="班级")
      template(slot-scope="scope")
        span {{ scope.class }}
  template(v-if="data.length === 0")
    Empty(type="table")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class SelectStudent extends Vue {
  // props
  @Prop({ type: Array, default: [] }) private data?: object[];
  @Prop({ type: Number, default: 0 }) private totalCount?: number;
  @Prop({ type: Number, default: 0 }) private currentPage?: number;
  @Prop({ type: Number, default: 10 }) private perPage?: number;
  @Prop({ type: Array, default: () => [] }) private selectedRowKeys?: number[];
  @Prop({ type: Boolean, default: true }) private showHeader?: number;
  @Prop({ type: Boolean, default: false }) private isSlot?: number;
  @Prop({ type: Number, default: 0 }) private paddingBottom?: number;
  private visibleConfirm: boolean = false;
  @Emit('change')
  public onSelectChange(keys: number[]): number[] {
    return keys;
  }

  public tableChange(pagination: object, filters: object, sorter: object) {
    this.$emit('tableChange', pagination, filters, sorter);
  }
}
</script>

<style lang="stylus" scoped>
.member-page
  overflow auto
  width 100%
  height 100%
  tr:hover
    .title
      color #3DA8F5
      font-size 14px
    .department
      cursor pointer
    .actions
      display inline
  .actions
    display none
</style>
