<template lang="pug">
.bunk-page(:class="{'selected': bunk.selected || selected}")
  .logo
    a-tooltip
      template(slot='title') 点击选择
      img.icon-18(src="@/assets/icons/dorm/bunked_blue.png" v-if="bunk.selected || selected")
      img.icon-18(src="@/assets/icons/dorm/bunk_unknown_gray.png" v-else-if="bunk.state === 'doing'")
      img.icon-18(src="@/assets/icons/dorm/bunked_green.png" v-else-if="bunk.state === 'done'")
      img.icon-18(src="@/assets/icons/dorm/bunking.png" v-else-if="bunk.state === 'empty'")
  Popover(
    v-model="visibleMore"
    placement="bottom"
    title="床位菜单")
    .popover(slot="main")
      .popover-item(
        v-for="(item, index) in operations"
        :key="index"
        @click="onAction(item.action)")
        a-icon(:type="item.icon")
        span {{ item.label }}
    IconTooltip.more(icon="ellipsis" tips="更多操作" @click="visibleMore=!visibleMore")
  .name
    span(:style="{color: formatColor(bunk.state)}")
      span(v-if="bunk.name") {{ bunk.name }}
      span(v-else).none -

  //-  指派学院
  a-modal(
    v-model="visibleModalCollege"
    :width="480")
    .modal-row(slot="title")
      .title 分配至学院
    .modal-row(slot="footer").footer
      a-button(type="primary" size="large" style="width: 100px" @click="confirmCollege") 确认
    .modal-middle
      .college-cell(v-for="(college, index) in colleges" :key="index")
        span {{ college.name }}
        a-icon(type="check")

  //- 指派学生
  MainModal(
    v-model="visibleModalMember"
    :width="800")
    .modal-row(slot="title")
      .title 分配至学生
      .search
        Searcher(v-model="queryObject" :variables="['name']" tips="检索学生" placeholder="检索学生")
    .modal-row(slot="footer").footer
      TextButton(icon="user-add") 自动分配
      a-button(type="primary" size="large" style="width: 100px" @click="confirmStudent") 确认
    SelectStudent(
      :data="students"
      :showHeader="true"
      :isSlot="false"
      :paddingBottom="24"
      :selectedRowKeys.sync="selectedStudentIds"
      @change="changeSelect")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import SelectStudent from './SelectStudent.vue';

@Component({
  components: {
    SelectStudent,
  },
})
export default class BunkItem extends Vue {
  @Prop({ type: Object, default: () => ({}) }) private bunk?: any;
  @Prop({ type: Boolean, default: false }) private selected?: boolean;
  // data
  private queryObject: object = {};
  private students: object[] = [];
  private selectedStudentIds: number[] = [];
  private visibleMore: boolean = false;
  private visibleModalMember: boolean = false;
  private visibleModalCollege: boolean = false;
  private operations: object[] = [
    {
      label: '更换床位',
      icon: 'block',
      action: 'replace',
    },
    {
      label: '清空床位',
      icon: 'delete',
      action: 'empty',
    },
    {
      label: '分配至学生',
      icon: 'solution',
      action: 'student',
    },
    {
      label: '分配至学院',
      icon: 'file-search',
      action: 'college',
    },
    {
      label: '移动至未分配',
      icon: 'export',
      action: 'move',
    },
    {
      label: '删除床位',
      icon: 'delete',
      action: 'delete',
    },
  ];
  private colleges: object[] = [
    {
      name: '法学院',
    },
    {
      name: '计算机学院',
    },
    {
      name: '外语学院',
    },
    {
      name: '通信学院',
    },
    {
      name: '汉语学院',
    },
  ];

  @Watch('queryObject')
  public onChildChanged() {
    this.fetchStudents();
  }

  public fetchStudents() {}

  public changeSelect(val: number[]) {
    this.selectedStudentIds = val;
  }

  public confirmCollege(val: string) {
    this.visibleModalCollege = false;
  }

  public confirmStudent(val: string) {
    this.visibleModalMember = false;
  }

  public onAction(val: string) {
    this.visibleMore = false;
    switch (val) {
      case 'replace':
        break;
      case 'empty':
        break;
      case 'student':
        this.visibleModalMember = true;
        break;
      case 'college':
        this.visibleModalCollege = true;
        break;
      case 'move':
        break;
      case 'delete':
        break;
      default:
        break;
    }
  }

  public formatColor(val: string) {
    if (this.selected) {
      return '#3DA8F5';
    }
    switch (val) {
      case 'done':
        return '#383838';
      case 'selected':
        return '#3DA8F5';
      default:
        return '#808080';
    }
  }
}
</script>

<style lang="stylus" scoped>
.bunk-page
  position relative
  padding-top 6px
  width 100%
  text-align center
  &:hover
    background #f5f5f5
    .more
      display inline
  .logo
    margin 0px auto
    width 20px
    text-align center
    cursor pointer
    padidng 6px 0px 4px
  .more
    position absolute
    top 0px
    right 0px
    display none
    width 20px
    height 20px
    border none
    background none
    color #A6A6A6
    line-height 20px
    &:hover
      color #3DA8F5
  .name
    overflow hidden
    width 100%
    text-overflow ellipsis
    white-space nowrap
    font-size 12px
    line-height 20px
    .none
      font-weight 500

.selected
  background #f5f5f5
  color #3DA8F5

.popover
  padding 4px 16px
  .popover-item
    display flex
    align-items center
    width 100%
    height 40px
    border none
    cursor pointer
    &:hover
      opacity 0.6
    &:last-child
      border-top 1px #e5e5e5 solid
    span
      margin-left 12px
      line-height 20px

.footer
  display flex
  justify-content flex-end
  align-items center
  button
    margin-left 16px

.middle
  padding 0px 16px 10px

.college-cell
  display flex
  justify-content space-between
  align-items center
  padding 0px 16px
  width 100%
  height 40px
  line-height 16px
  &:hover
    background #F5F5F5
    cursor pointer
  &:first-child
    margin-top 8px
  &:last-child
    margin-bottom 8px
</style>
