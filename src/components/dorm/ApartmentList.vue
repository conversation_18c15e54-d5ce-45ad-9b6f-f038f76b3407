<template lang="pug">
a-card
  template(slot="cover")
    .card-header
      .title
        a-icon(type="caret-down" theme="filled" style="color: #ccc;")
        span(style="margin-left: 8px") 一层
        span &nbsp;&nbsp;· 12/200
      IconTooltip(icon="ellipsis" tips="更多操作" style="color: #A6A6A6")
    .card-middle
      a-checkbox-group(@change="onChange")
        a-row(:gutter="16")
          a-col(:span="4" v-for="(item, index) in list" :key="index").group-item
            span(style="margin-left: 4px")
              a-checkbox(:value="item.code") {{ item.code }}
            a-row(:gutter="8").box
              a-col(:span="12" v-for="(bunk, key) in bunks" :key="key").bunk
                a-popover(trigger="focus")
                  template(slot="content")
                    p sdad
                    p asdasd
                  BunkItem(:bunk.sync="bunk" :selected="checkoutSelected(item.code)")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import BunkItem from '@/components/dorm/BunkItem.vue';

@Component({
  components: {
    BunkItem,
  },
})
export default class ApartmentList extends Vue {
  @Prop({ type: Array, default: () => [] }) private list?: object[];
  private bunks: object[] = [];
  private selectIds: number[] = [];

  mounted() {
    window.setTimeout(() => {
      this.bunks = [
        {
          name: '',
          state: 'doing',
          selected: false,
        },
        {
          name: 'ALEX',
          state: 'done',
          selected: true,
        },
        {
          name: '',
          state: 'empty',
          selected: false,
        },
        {
          name: 'DATE',
          state: '',
          selected: false,
        },
      ];
    }, 0);
  }

  public onChange(val: number[]) {
    this.selectIds = val;
    this.$emit('checkbox', val);
  }

  public checkoutSelected(id: number): boolean {
    return this.selectIds.some((e: number) => e === id);
  }
}
</script>

<style lang="stylus" scoped>
.ant-card
  border none
  box-shadow 0px 1px 2px 0px rgba(0, 0, 0, 0.1)
  .card-header
    display flex
    justify-content space-between
    align-items center
    padding 16px 20px
    border-bottom 1px #E5E5E5 solid
    line-height 20px
    .title
      color #808080
  .card-middle
    padding 12px 16px 4px
    width 100%
    .group-item
      padding 10px 0px 8px
      &:hover
        background #F5F5F5
    .box
      display flex
      flex-wrap wrap
      margin-top 10px
      padding 6px 6px 10px
      width 100%
      border 1px rgba(0, 0, 0, 0.08) solid
      border-radius 2px
      background #fff
      .bunk
        margin-top 4px
        width 50%
        height 50px

.ant-checkbox-group
  width 100%
</style>
