<script lang="ts">
import { ITemplate } from '@/models/comm/template';
import { ITermPlan } from '@/models/teaching/term_plan';
import Joined from '@/views/conference/teacher/activities/mine/Joined.vue';
import { Component, Vue, Prop } from 'vue-property-decorator';
import { TaIndexViewTabInterface } from '../global/TaIndex';

@Component({
  components: {},
})
export default class comtemplatetable extends Vue {
  @Prop({ type: Object, default: () => {} }) private store!: IObject;
  @Prop({ type: String, default: () => '' }) private readonly title!: string;

  state: string = '';
  private visibleForm: boolean = false;
  private form: IObject = {};
  private tabs: TaIndexViewTabInterface[] = [
    { label: '', key: 'execute', num: 0, background: '', color: '', mode: 'table' },
  ];

  get config() {
    return {
      store: this.store,
      recordName: '模板',
      searcherSimpleOptions: [
        {
          key: 'title',
          label: '模板名称',
          type: 'string',
        },
      ],
    };
  }

  mounted() {
    this.fetchData();
  }

  fetchData() {}

  private onShow(value: ITemplate): void {
    this.$emit('onShow', value);
  }

  private addTemplate(): void {
    this.form.id = 0;
    this.form.title = '';
    this.form.type = 'Template::Practice';
    this.visibleForm = true;
  }

  private onEdit(scope: ITemplate) {
    this.visibleForm = true;
    this.form = JSON.parse(JSON.stringify(scope));
  }

  private onDelete(id: number): void {
    this.$confirm({
      title: '确定要继续执行此操作吗?',
      okText: '确定',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        await this.store.delete(id);
        this.$message.success('删除成功');
      },
      onCancel: () => {},
    });
  }

  private async updateTemplate(): Promise<void> {
    this.visibleForm = false;
    await this.store.update(this.form);
    this.$message.success('修改成功');
  }

  private async createTemplate(): Promise<void> {
    this.visibleForm = false;
    await this.store.create(this.form);
    this.$message.success('添加成功');
  }

  private submit() {
    this.form.id ? this.updateTemplate() : this.createTemplate();
  }
}
</script>

<template lang="pug">
.com-template-table
  TaIndexView(:config='config', :tabs='tabs', @onShow='onShow')
    template(#header)
      span.title {{ title }}
    template(#right-actions)
      TextButton(@click='addTemplate', icon='plus-circle', theme='filled') 添加模板
    template(#table)
      a-table-column(title='模板名称', dataIndex='title', ellipsis='true', :width='5')
      a-table-column(title='模板项数量', dataIndex='info_count', ellipsis='true', :width='2')
      a-table-column(title='模板项内容', ellipsis='true', :width='5')
        template(slot-scope='scope')
          span(v-for='item in scope.info_titles') {{ item }},
      a-table-column(title='创建时间', ellipsis='true', :width='2')
        template(slot-scope='scope')
          span {{ $moment(scope.created_at).format("YYYY-MM-DD") }}
      a-table-column(title=' ', ellipsis='true', align='right', :width='2')
        template(slot-scope='scope')
          .actions
            IconTooltip(icon='form', tips='编辑', @click.stop='onEdit(scope)')
            IconTooltip(icon='delete', tips='删除', @click.stop='onDelete(scope.id)')
  a-modal(
    :title='form.id ? "更新模板" : "新建模板"',
    :visible='visibleForm',
    :width='460',
    @cancel='visibleForm = false',
    @ok='submit'
  )
    a-form
      a-form-item(label='模板名称')
        a-input(v-model='form.title', size='large', placeholder='请输入模板名称')
</template>

<style lang="stylus" scoped>
.com-template-table
  tr:hover, .actions
    display block !important
.title
  font-size 18px
  color #383838
  font-weight 500
.actions
  display none
.activeClass
  color #adadad
</style>
