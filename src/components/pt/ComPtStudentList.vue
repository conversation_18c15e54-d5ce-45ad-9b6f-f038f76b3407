<script lang="ts">
import { IPractice } from '@/models/pt/practice';
import { Component, Vue, Prop } from 'vue-property-decorator';
import { TaIndexViewTabInterface } from '../global/TaIndex';

@Component({
  components: {},
})
export default class comptstudentlist extends Vue {
  @Prop({ type: Number }) private readonly sourceId!: number;
  @Prop({ type: Array, default: () => [] }) private readonly parents!: [];
  @Prop({ type: String, default: () => '' }) private readonly title!: string;
  @Prop({ type: Object, default: () => {} }) private readonly store!: IObject;

  private practice: any = {};
  private visibleForm: boolean = false;
  private tabs: TaIndexViewTabInterface[] = [
    { label: '', key: 'execute', num: 0, background: '', color: '', mode: 'table' },
  ];

  get config() {
    return {
      store: this.store,
      recordName: '学生',
      showExport: true,
      showImport: true,
      searcherSimpleOptions: [
        {
          label: '姓名',
          type: 'string',
          key: 'student_name',
        },
        {
          label: '学号',
          type: 'string',
          key: 'student_code',
        },
      ],
    };
  }
  // 获取用户信息，用作后续的权限判断
  get currentUser() {
    return this.$store.state.currentUser;
  }
  // 是否存在于指导教师的列表，用作权限判断
  isGuideTeacher(nested_infos: any) {
    return nested_infos.guide_teachers.includes(this.currentUser.name);
  }
  // 自定义小数点后取值方法,返回的值还是number类型
  roundFun(value: number, n: number) {
    return Math.round(value * Math.pow(10, n)) / Math.pow(10, n);
  }
  get id() {
    return this.$route.params.id;
  }

  mounted() {
    this.store.init({ parents: this.parents });
  }
  // 根据当前url判断是否显示一些内容
  get nowPath() {
    if (this.$route.path.split('/').pop() === 'practivity_replies') {
      return true;
    }
    return false;
  }
  private edit(scope: IPractice): void {
    this.practice = JSON.parse(JSON.stringify(scope));
    this.visibleForm = true;
  }

  private async submit(): Promise<void> {
    await this.store.update(this.practice);
    this.visibleForm = false;
  }

  private getCompanyStatus(val: string): string {
    return val === 'scored' ? '待评分' : '待提交';
  }

  private getThesisStatus(val: string): string {
    return val === 'pending' ? '待提交' : '待评分';
  }
  // 把答辩任务的url也替换到学生详情页里去
  private onShow({ id }: IPractice): void {
    if (this.nowPath) {
      this.$router.push({
        name: 'pts_teacher_activities_show_practices_show_basicInfo',
        params: { practiceId: String(id), from: 'practivity_replies' },
      });
    } else {
      this.$router.push({
        name: 'pts_teacher_activities_show_practices_show_basicInfo',
        params: { practiceId: String(id) },
      });
    }
    this.$emit('onShow', id);
  }

  private stateFilter(state: string): string {
    return state === 'completed' ? '已完成' : state === 'starting' ? '进行中' : '未开始';
  }
}
</script>

<template lang="pug">
.com-pt-student-list
  TaIndexView(:config='config', :tabs='tabs', @onShow='onShow', :tabsLeftMargin='0')
    template(#header)
      span.title {{ title }}
    template(#table)
      a-table-column(title='名字', dataIndex='student.name', :width='5')
      a-table-column(title='学号', dataIndex='student.code', :width='5')
      a-table-column(title='开始时间', :width='5')
        template(slot-scope='scope')
          span {{ $moment(scope.start_at).format("YYYY-MM-DD") }}
      a-table-column(title='结束时间', :width='5')
        template(slot-scope='scope')
          span {{ $moment(scope.end_at).format("YYYY-MM-DD") }}
      a-table-column(title='学院', :width='5')
        template(slot-scope='scope')
          span {{ scope.student.college_name }}
      a-table-column(title='专业', :width='5')
        template(slot-scope='scope')
          span {{ scope.student.major_name }}
      a-table-column(title='班级', :width='5')
        template(slot-scope='scope')
          span {{ scope.student.adminclass_name }}
      a-table-column(title='辅导老师', :width='5')
        template(slot-scope='scope')
          a-tooltip(v-for='item in scope.nested_info.guide_teachers', :title='item', :key='item')
            .text-ellipsis(style='width: 100px') {{ item || "待分配" }}
      a-table-column(title='实习周记(学生)', align='center', :width='8')
        template(slot-scope='scope')
          a-tooltip
            template(slot='title')
              p 已提交：{{ scope.normal_report_count }}
              p 迟交：{{ scope.late_report_count }}
            span.text-primary {{ scope.normal_report_count }}/
            span.text-warning {{ scope.late_report_count }}/
            span.text-gray {{ scope.total_report_count }}
      a-table-column(title='实习周记得分', align='center', :width='8')
        template(slot-scope='scope')
          span {{ roundFun((scope.nested_score.report_avg_score),0) }}分
      //- a-table-column(title='待评分', align='center', :width='8')
        template(slot-scope='scope')
          a-tooltip
            template(slot='title')
              p 已评审：{{ scope.score_report_count }}
            span.text-success {{ scope.score_report_count }}/
            span.text-gray {{ scope.score_report_count + scope.published_report_count }}
      a-table-column(title='企业评价', :width='5')
        template(slot-scope='scope')
          span {{ roundFun(scope.nested_score.company_score,0) }}分
      a-table-column(title='实习报告', :width='5')
        template(slot-scope='scope')
          span {{ roundFun(scope.nested_score.thesis_score,0) }}分
      a-table-column(title='过程成绩', :width='5')
        template(slot-scope='scope')
          span {{ roundFun(scope.nested_score.course_score,0) }}分
      a-table-column(title='答辩分数', :width='5')
        template(slot-scope='scope')
          span {{ roundFun(scope.nested_score.reply_score,0) }}分
      a-table-column(title='总评成绩', :width='5')
        template(slot-scope='scope')
          span {{ roundFun(scope.nested_score.total_score,0) }}分
      a-table-column(title='最近签到', :width='5')
        template(slot-scope='scope')
          .column {{ scope.last_register_at ? $moment(scope.last_register_at).format("MM月DD日 HH:mm") : "-" }}
      a-table-column(v-if='!nowPath', title=' ', :width='2')
        template(slot-scope='scope')
          IconTooltip(icon='edit', tips='修改时间', @click.stop='edit(scope)')
  //- edit modal
  a-modal(title='修改时间', :visible='visibleForm', :width='460', @cancel='visibleForm = false', @ok='submit')
    a-form
      a-form-item(label='开始时间')
        a-date-picker(
          :value='$tools.formatDefaultDate(practice.start_at)',
          placeholder='选择日期',
          allowClear,
          size='large',
          style='width: 100%',
          @change='(date, dateString) => { practice.start_at = dateString; }'
        )
      a-form-item(label='结束时间')
        a-date-picker(
          :value='$tools.formatDefaultDate(practice.end_at)',
          placeholder='选择日期',
          allowClear,
          size='large',
          style='width: 100%',
          @change='(date, dateString) => { practice.end_at = dateString; }'
        )
</template>

<style lang="stylus" scoped>
.com-pt-student-list
  width 100%
  padding 20px
  border-radius 5px
  background #ffffff
.title
  font-size 18px
  color #383838
  font-weight 500
.point-box
  display flex
  justify-content space-between
  margin 0px auto
  width 30px
  .point
    width 6px
    height 6px
    border-radius 50%
    background #CCCCCC
</style>
