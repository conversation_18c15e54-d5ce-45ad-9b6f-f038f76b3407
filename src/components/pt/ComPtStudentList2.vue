<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { TaIndexViewTabInterface } from '../global/TaIndex';
import { replyTemplate } from '@/views/pts/teacher/activity/replies/template';
import { IPtReplies } from '@/types/model';
import TaIndexView from '../global/TaIndexView.vue';
import { PtTeacherReplies } from '@/models/pt/teacher/replies';
@Component({
  components: {},
})
export default class comptstudentlist2 extends Vue {
  @Prop({ type: Number }) private readonly sourceId!: number;
  @Prop({ type: Array, default: () => [] }) private readonly parents!: [];
  @Prop({ type: String, default: () => '' }) private readonly title!: string;
  @Prop({ type: Object, default: () => {} }) private readonly store!: IObject;

  private tabs: TaIndexViewTabInterface[] = [
    { label: '', key: 'execute', num: 0, background: '', color: '', mode: 'table' },
  ];
  template = replyTemplate;
  // 记录当前点击的是哪条学生的答辩，用作后续计算使用
  clickRecord = {};
  get config() {
    return {
      store: this.store,
      recordName: '学生',
      showExport: true,
      showImport: true,
      searcherSimpleOptions: [
        { label: '姓名', type: 'string', key: 'student_name' },
        { label: '学号', type: 'string', key: 'student_code' },
      ],
      template: this.template,
      detailConfig: {
        detailMode: 'drawer',
        detailWidth: '50%',
      },
    };
  }
  // 获取用户信息，用作后续的权限判断
  get currentUser() {
    return this.$store.state.currentUser;
  }
  // 是否存在于指导教师的列表，用作权限判断
  isGuideTeacher(nested_infos: any) {
    return nested_infos.guide_teachers.includes(this.currentUser.name);
  }
  // 计算显示总分
  calculateTotalScore(scope: any) {
    let score = 0;
    score += scope.nested_score['company_score'] * 0.4;
    score += scope.nested_score['reply_score'] * 0.2;
    score += scope.nested_score['report_avg_score'] * 0.2;
    score += scope.nested_score['thesis_score'] * 0.2;
    return this.roundFun(score, 2);
  }
  // 自定义小数点后取值方法,返回的值还是number类型
  roundFun(value: number, n: number) {
    return Math.round(value * Math.pow(10, n)) / Math.pow(10, n);
  }
  get id() {
    return this.$route.params.id;
  }

  mounted() {
    this.fetchData();
  }
  async fetchData() {
    await this.store.init({ parents: this.parents });
  }
  // 判断登录者是不是该学生的答辩老师，只有答辩老师才能改学生的答辩成绩
  isReplyTeacher(record: any) {
    const { id } = this.$store.state.currentUser;
    if (record.reply_teachers_ids.includes(id)) {
      return true;
    }
    return false;
  }
  // taindexview里得到正在点击的record,用作后续计算分数使用
  handleRowClick(record: IPtReplies) {
    this.clickRecord = record;
  }
  // 更新答辩的得分
  async update(formData: IPtReplies) {
    await new PtTeacherReplies().update({
      id: formData.id,
      json_attributes: { ...formData.json_attributes },
    });
    (this.$refs.taIndexView as TaIndexView<IPtReplies>).visibleDrawer = false;
    await this.store.index();
  }
}
</script>

<template lang="pug">
.com-pt-student-list
  TaIndexView(ref='taIndexView', :config='config', :tabs='tabs', :tabsLeftMargin='0', @onShow='handleRowClick')
    template(#header)
      span.title {{ title }}
    template(#table)
      a-table-column(title='名字', dataIndex='student.name', :width='5')
      a-table-column(title='学号', dataIndex='student.code', :width='5')
      a-table-column(title='开始时间', :width='5')
        template(slot-scope='scope')
          span {{ $moment(scope.start_at).format("YYYY-MM-DD") }}
      a-table-column(title='结束时间', :width='5')
        template(slot-scope='scope')
          span {{ $moment(scope.end_at).format("YYYY-MM-DD") }}
      a-table-column(title='学院', :width='5')
        template(slot-scope='scope')
          span {{ scope.student.college_name }}
      a-table-column(title='专业', :width='5')
        template(slot-scope='scope')
          span {{ scope.student.major_name }}
      a-table-column(title='班级', :width='5')
        template(slot-scope='scope')
          span {{ scope.student.major_name }}
      a-table-column(title='辅导老师', :width='5')
        template(slot-scope='scope')
          a-tooltip(v-for='item in scope.nested_info.guide_teachers', :title='item', :key='item')
            .text-ellipsis(style='width: 100px') {{ item || "待分配" }}
      a-table-column(title='实习周记(学生)', align='center', :width='8')
        template(slot-scope='scope')
          a-tooltip
            template(slot='title')
              p 已提交：{{ scope.normal_report_count }}
              p 迟交：{{ scope.late_report_count }}
            span.text-primary {{ scope.normal_report_count }}/
            span.text-warning {{ scope.late_report_count }}/
            span.text-gray {{ scope.total_report_count }}
      a-table-column(title='实习周记得分', align='center', :width='8')
        template(slot-scope='scope')
          span {{ Number(scope.nested_score.report_avg_score) }}分
      a-table-column(title='企业评价', :width='5')
        template(slot-scope='scope')
          span {{ Number(scope.nested_score.company_score) }}分
      a-table-column(title='实习报告', :width='5')
        template(slot-scope='scope')
          span {{ Number(scope.nested_score.thesis_score) }}分
      a-table-column(title='答辩', :width='5')
        template(slot-scope='scope')
          span {{ Number(scope.nested_score.reply_score) }}分
      a-table-column(title='总分', :width='5')
        template(slot-scope='scope')
          span {{ calculateTotalScore(scope) }}分
      a-table-column(title='最近签到', :width='5')
        template(slot-scope='scope')
          .column {{ scope.last_register_at ? $moment(scope.last_register_at).format("MM月DD日 HH:mm") : "-" }}
    template(#detail='{ record }')
      TaTemplateFormShow(
        recordName='答辩详情',
        :record='record.reply',
        :template='template',
        :disabled='isReplyTeacher(record)',
        @update='update'
      )
        template(#content)
          .info_cell
            .key
              span 学院
            .value
              span {{ record.student.college_name }}
            .key
              span 专业
            .value
              span {{ record.student.major_name }}
          .info_cell
            .key
              span 姓名
            .value
              span {{ record.student.name }}
            .key
              span 性别
            .value
              span {{ record.student.sex }}
          .info_cell
            .key
              span 班级
            .value
              span {{ record.student.adminclass_name }}
            .key
              span 学号
            .value
              span {{ record.student.code }}
          .info_cell
            .key
              span 顶岗实习单位
            .value
              span {{ record.company }}
            .key
              span 岗位
            .value
              span {{ record.job }}
</template>

<style lang="stylus" scoped>
.com-pt-student-list
  width 100%
  padding 20px
  border-radius 5px
  background #ffffff
.title
  font-size 18px
  color #383838
  font-weight 500
.point-box
  display flex
  justify-content space-between
  margin 0px auto
  width 30px
  .point
    width 6px
    height 6px
    border-radius 50%
    background #CCCCCC
.info_cell
  display flex
  line-height 20px
  .key
    display flex
    align-items center
    width 20%
    margin 10px 0
  .value
    display flex
    margin 10px 0
    flex-wrap wrap
    width 30%
    color #383838
</style>
