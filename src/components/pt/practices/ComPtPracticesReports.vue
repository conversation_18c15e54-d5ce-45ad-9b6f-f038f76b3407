<script lang="ts">
import { TaIndexViewTabInterface } from '@/components/global/TaIndex';
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class ComPtPracticesReports extends Vue {
  @Prop({ type: String, default: '' }) prop!: string;
  @Prop({ type: Object, default: () => {} }) private store!: IObject;
  @Prop({ type: Array, default: () => {} }) private parents!: IObject;

  state: string = '';

  private tabs: TaIndexViewTabInterface[] = [
    { label: ' ', key: 'published', num: 0, background: '', color: '', mode: 'table' },
  ];
  get config() {
    return {
      store: this.store,
      recordName: '周记',
      searcherSimpleOptions: [
        {
          lable: '周记',
          key: 'windex',
          type: 'string',
        },
      ],
    };
  }

  private onShow(val: any): void {
    val.state === 'pending'
      ? this.$message.warning('暂无周记信息！')
      : this.$router.push({ path: `${this.$route.path}/${val.id}` });
  }

  mounted() {
    this.store.init({ parents: this.parents, params: { q: { type_eq: 'Report::Practice' } } });
  }
}
</script>

<template lang="pug">
.ComPtPracticesReports
  TaIndexView(:tabs="tabs" :config="config"  @onShow="onShow")
    template(#header)
      span.title 实习周记
    template(#table)
      a-table-column(title="周" dataIndex="windex" align="center")
        template(slot-scope="scope")
          span {{ scope && `第 ${scope} 周` || "-"}}
      a-table-column(title="开始时间" dataIndex="start_at" align="center")
      a-table-column(title="结束时间" dataIndex="end_at" align="center")
      a-table-column(title="状态" dataIndex="state" align="center")
        template(slot-scope="scope")
          span(:class="{scored: scope === 'scored', state: scope !=='scored'}") 
            | {{ scope === 'scored' ? '已评分' : scope === 'pending' ? '待上传' : '未评分' }}
      a-table-column(title="老师打分" dataIndex="score" align="center")
        template(slot-scope="scope")
          span {{ scope && parseInt(scope)|| "-" }}
      a-table-column(title="评论数" dataIndex="comment_count" align="center")
        template(slot-scope="scope")
          span {{ scope || "-"}}
      a-table-column(title="提交时间" dataIndex="published_at" align="center")
</template>

<style lang="stylus" scoped>
.ComPtPracticesReports
  padding 20px 20px
.scored
  color #6DC37D
.state
  color #808080
.title
  color #383838
  font-size 16px
  font-weight 500
</style>
