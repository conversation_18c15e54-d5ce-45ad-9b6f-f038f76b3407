<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

import G2Pie from '@/components/pt/G2Pie.vue';
import ComPtItem from '@/components/pt/statistics/ComPtItem.vue';
import ComPtHeader from '@/components/pt/statistics/ComPtHeader.vue';
import { TaIndexViewTabInterface } from '@/components/global/TaIndex';

@Component({
  components: { ComPtItem, ComPtHeader, G2Pie },
})
export default class ComPtPraticesRegisters extends Vue {
  @Prop({ type: String, default: '' }) prop!: string;
  @Prop({ type: Array, default: () => [] }) private parents!: [];
  @Prop({ type: String, default: () => '' }) private roles!: string;
  @Prop({ type: Number, default: () => '' }) private practicesId!: number;
  @Prop({ type: Object, default: () => {} }) private registersStore!: IObject;
  @Prop({ type: Object, default: () => {} }) private practicesStore!: IObject;

  private register: any = {};
  private tabs: TaIndexViewTabInterface[] = [
    { label: '', key: 'published', num: 0, background: '', color: '', mode: 'table' },
  ];
  get config() {
    return {
      store: this.registersStore,
      recordName: '',
      searcherSimpleOptions: [
        {
          lable: '地址',
          key: 'address',
          type: 'string',
        },
        {
          label: '打卡时间',
          key: 'created_at',
          type: 'string',
        },
      ],
      tableConfig: { bordered: true },
    };
  }

  get basePie(): Array<{ state: string; key?: string; amount: number; percent: number }> {
    return [
      {
        state: '已打卡',
        key: 'done',
        amount: this.register.done,
        percent: this.register.done,
      },
      {
        state: '未打卡',
        amount: this.register.undo,
        percent: this.register.total,
      },
    ];
  }

  mounted() {
    this.fetchData();
  }

  private async fetchData() {
    this.registersStore.init({ parents: this.parents });
    this.practicesStore.init({ parents: [{ type: this.roles }] });
    let { data } = await this.practicesStore.find(this.practicesId);
    this.register = data.statistics.register;
  }
}
</script>

<template lang="pug">
.ComPtPraticesRegisters
  .header
    ComPtHeader(title="打卡详情" :basePie="basePie")
      template
        ComPtItem(label="应打卡" unit="次" :num='register.total')
        ComPtItem(label="已打卡" unit="次" :num='register.done')
        ComPtItem(label="未打卡" unit="次" :num='register.undo')
        .G2Pie(v-if="register.total || 0")
          G2Pie(
            id="A1",
            unit="次"
            :charData="basePie"
            :colors="['#1FA0FF', '#CDCBCE']",
          )
  .table
    TaIndexView(
      :tabs="tabs"
      :config="config"
    )
      template(#table)
        a-table-column(title="日期" align="center")
          template(slot-scope="scope")
            span {{ $moment(scope.created_at).format("YYYY/MM/DD") }}
        a-table-column(title="打卡时间" dataIndex="created_at" align="center")
          template(slot-scope="scope")
            span {{ scope && $moment(scope).format("HH:mm") === "00:00"? "-" : $moment(scope).format("HH:mm") }}
        a-table-column(title="打卡地点" dataIndex="address" align="center")
          template(slot-scope="scope")
            span {{ scope || "-" }}
        a-table-column(title="打卡状态" dataIndex="state" align="center")
          template(slot-scope="scope")
            span(:class="{done: scope === 'done'}") {{ scope === 'done' ? '已打卡' : '未打卡' }}
</template>

<style lang="stylus" scoped>
.ComPtPraticesRegisters
  width 100%
  padding 0 15px
  background #ffffff
  .table
    margin-top 25px
  .header
    height 300px
    .G2Pie
      width 25%
.done
  color #6DC37D
</style>
