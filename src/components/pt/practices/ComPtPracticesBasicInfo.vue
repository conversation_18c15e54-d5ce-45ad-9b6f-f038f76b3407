<script lang="ts">
import Joined from '@/views/conference/teacher/activities/mine/Joined.vue';
import { copyFileSync } from 'fs';
import { any } from 'lodash/fp';
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class ComPtPracticesBasicInfo extends Vue {
  @Prop({ type: Object, default: () => {} }) private store!: IObject;
  @Prop({ type: Number, default: () => 0 }) private readonly practiceId!: number;

  private data: any = [];
  get dataKeys() {
    return [
      {
        label: '姓名',
        key: 'name',
      },
      {
        label: '学号',
        key: 'code',
      },
      {
        label: '学院',
        key: 'college_name',
      },
      {
        label: '专业',
        key: 'major_name',
      },
      {
        label: '班级',
        key: 'adminclass_name',
      },
      {
        label: '实习安排',
        key: 'title',
      },
      {
        label: '开始日期',
        key: 'start_at',
      },
      {
        label: '结束日期',
        key: 'end_at',
      },
      {
        label: '指导老师',
        key: 'guide_teachers',
      },
      {
        label: '实习企业',
        key: 'company',
      },
      {
        label: '实习城市',
        key: 'city',
      },
      {
        label: '实习地址',
        key: 'address',
      },
      {
        label: '实习岗位',
        key: 'job',
      },
    ];
  }

  get students(): any {
    return this.store.record.student;
  }

  created() {}

  mounted() {
    this.fetchData();
  }

  private async fetchData(): Promise<void> {
    this.store.init();
    let { data } = await this.store.find(this.practiceId);
    this.data = JSON.parse(JSON.stringify(this.dataKeys));
    this.data.forEach((item: any) => {
      if (item.key in Object.assign(data, data.student)) {
        if (item.key === 'start_at' || item.key === 'end_at') {
          item.value = this.conversionTime(data[item.key]);
        }
        if (item.key === 'guide_teachers') {
          item.value = this.getTeacherName(data[item.key]);
        } else {
          item.value = data[item.key];
        }
      }
    });
  }

  private conversionTime(val: string): string {
    return this.$moment(val).format('YYYY-MM-DD');
  }

  // 获取老师 name
  private getTeacherName(val: Array<any>): string {
    return val
      .map((item: any) => {
        return item.name;
      })
      .join(',');
  }
}
</script>

<template lang="pug">
.ComPtPracticesBasicInfo
  Panel.Panel(title="基本信息")
    template
      Cell(v-for="(item, index) in data" :key="index" :label="item.label")
         template(slot="value")
          span {{ item.value }}
</template>

<style lang="stylus" scoped>
.ComPtPracticesBasicInfo
  .Panel
    min-height 700px
    height 100%
</style>
