<script lang="ts">
import { IActivity } from '@/models/access/activity';
import { isValidDate } from '@fullcalendar/vue';
import { values } from 'lodash';
import { toUnicode } from 'punycode';
import { Component, Vue, Prop } from 'vue-property-decorator';
import { TaIndexViewTabInterface } from '../global/TaIndex';

@Component({
  components: {},
})
export default class compttable extends Vue {
  @Prop({ type: Object, default: () => {} }) private store!: IObject;
  @Prop({ type: String, default: () => '' }) private readonly title!: string;
  @Prop({ type: Boolean, default: () => true }) private readonly createButtonShowState!: boolean;

  private visibleForm: boolean = false;
  private activity: IActivity = {};
  private state: string = '';
  private menu: any = [
    {
      state: 'pending',
      lable: '未开始',
    },
    {
      state: 'starting',
      lable: '进行中',
    },
    {
      state: 'completed',
      lable: '已完成  ',
    },
    {
      state: 'canceled',
      lable: '已取消',
    },
  ];
  private tabs: TaIndexViewTabInterface[] = [
    { label: '', key: 'execute', num: 0, background: '', color: '', mode: 'table' },
  ];

  get routeTitle(): string {
    return this.$route.meta.title;
  }

  get config() {
    return {
      store: this.store,
      recordName: '实习',
      searcherSimpleOptions: [
        {
          key: 'name',
          label: '实习名称',
          type: 'string',
        },
      ],
    };
  }

  private onShow(value: any): void {
    this.$emit('onShow', value);
  }

  private onEdit(val: IActivity): void {
    this.state = '修改';
    this.activity = val;
    this.visibleForm = true;
  }

  private onDelete(id: number) {
    this.$confirm({
      title: '确定要继续执行此操作吗?',
      okText: '确定',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        this.store.delete(id);
      },
      onCancel: () => {},
    });
  }

  private addActivity(): void {
    this.state = '创建';
    this.activity = {};
    this.visibleForm = true;
  }

  private async createActivity(): Promise<void> {
    await this.store.create(this.activity);
    this.$message.success('添加成功');
    this.visibleForm = false;
  }

  private async updateActivity(): Promise<void> {
    await this.store.update(this.activity);
    this.$message.success('修改成功');
    this.visibleForm = false;
  }

  private async changeState(state: string, id: number): Promise<void> {
    await this.store.update({ state, id });
    this.$message.success('状态修改成功');
  }

  private submit(): void {
    this.activity.id ? this.updateActivity() : this.createActivity();
  }

  private stateFilter(state: string): string {
    return state === 'completed' ? '已完成' : state === 'starting' ? '进行中' : '未开始';
  }
}
</script>

<template lang="pug">
.com-pt-table
  TaIndexView(:config="config" :tabs="tabs" @onShow="onShow")
    template(#header)
      span.title {{ title }}
    template(#right-actions v-if="createButtonShowState")
      TextButton( @click="addActivity" icon="plus-circle" theme="filled") 添加活动
    template(#table)
      a-table-column(title='活动名称', dataIndex="name" ellipsis="true" :width='5')
      a-table-column(title='开始时间', ellipsis="true" :width='4')
        template(slot-scope="scope")
          span {{ $moment(scope.start_at).format("YYYY-MM-DD") }}
      a-table-column(title='结束时间', ellipsis="true" :width='4')
        template(slot-scope="scope")
          span {{ $moment(scope.end_at).format("YYYY-MM-DD") }}
      a-table-column(title='状态', ellipsis="true" :width='2')
        template(slot-scope="scope")
          span {{ stateFilter(scope.state) }}
      a-table-column(title='',  ellipsis="true" align="right" :width='2' v-if="routeTitle !== '实习管理' ")
        template(slot-scope="scope")
          .active
            a-dropdown(:trigger="['click']")
              IconTooltip(icon="tool" tips="设置状态")
              a-menu(slot="overlay")
                a-menu-item(
                  v-for='item in menu' 
                  :key="item.label"
                  @click="changeState(item.state, scope.id)"
                  :class="{activeClass: item.state === scope.state}" 
                )
                  span {{ item.lable }}
            IconTooltip(icon="form" tips="编辑" @click.stop="onEdit(scope)")
            IconTooltip(icon="delete" tips="删除" @click.stop="onDelete(scope.id)")
  a-modal(
    :title="activity.id ? '更新活动' : '新建活动'"
    :visible="visibleForm"
    :width="460"
    @cancel="visibleForm = false"
    @ok="submit")
    a-form
      a-form-item(label="活动名称")
        a-input(v-model="activity.name" size="large" placeholder="请输入活动名称")
      a-form-item(label="开始时间")
        a-date-picker(
          v-model="activity.start_at"
          placeholder="选择日期"
          allowClear
          size="large"
          style="width: 100%"
          @change="(date, dateString) => { activity.start_at = dateString }")
      a-form-item(label="结束时间")
        a-date-picker(
          v-model="activity.end_at"
          placeholder="选择日期"
          allowClear
          size="large"
          style="width: 100%"
          @change="(date, dateString) => { activity.end_at = dateString }")
</template>

<style lang="stylus" scoped>
.com-pt-table
  tr:hover
    .active
      display block !important
.title
  font-size 18px
  color #383838
  font-weight 500
.active
  display none
.activeClass
  color #adadad
</style>
