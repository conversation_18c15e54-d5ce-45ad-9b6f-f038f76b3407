<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import ComPtCommitItem from '@/components/pt/statistics/ComPtCommitItem.vue';
import { IComment } from '@/models/comment';
import { IFile } from '@/models/file';
import { commUserCommentsStore } from '@/store/modules/pt/teacher/comments.store';

@Component({
  components: { ComPtCommitItem },
})
export default class ComReportsIndex extends Vue {
  @Prop({ type: Object, default: () => {} }) private reportsStore!: IObject;
  @Prop({ type: Object, default: () => {} }) private commentsStore!: IObject;

  private user: IObject = {};
  private comments: any = [];
  private visible: boolean = false;
  private files: Array<IFile> = [];
  private score: number | string = '';
  private commentContext: string = '';

  get reportsId(): number {
    return +this.$route.params.reportsId;
  }

  get activitiesId(): number {
    return +this.$route.params.activitiesId;
  }

  get userCommentsStore() {
    return commUserCommentsStore;
  }

  get breadcrumbs() {
    return [
      { title: '实习周记', url: `/pts/teacher/activities/${this.activitiesId}/console/reports` },
      { title: this.user.name },
    ];
  }

  mounted() {
    this.reportsStore.init();
    this.userCommentsStore.init({ parents: [{ type: 'user' }] });
    this.commentsStore.init({ parents: [{ type: 'reports', id: this.reportsId }] });
    this.fetchData();
  }

  private async fetchData() {
    await this.reportsStore.find(this.reportsId);
    let { data } = await this.commentsStore.index();
    this.comments = data.comments || '';
    this.user = this.reportsStore.record.user;
    this.score = this.reportsStore.record.score || 0;
    this.files = this.reportsStore.record.attachments.files || [];
  }

  edit() {
    this.visible = true;
  }
  shutDown() {
    if (this.visible) {
      this.visible = false;
    }
  }

  private async deleteComment({ id }: IComment): Promise<void> {
    await this.commentsStore.delete(id);
    let { data } = await this.commentsStore.index();
    this.comments = data.comments;
  }

  private async addComment(): Promise<void> {
    this.userCommentsStore
      .create({
        body: this.commentContext,
        commentable_type: 'Report',
        commentable_id: this.reportsStore.record.id,
      })
      .then(() => {
        this.commentContext = '';
        this.$message.success('成功发布评论');
        this.fetchData();
      });
  }

  private async onScore(): Promise<void> {
    await this.reportsStore.update({ id: this.reportsStore.record.id, score: this.score, state: 'scored' });
    this.$message.success('分数修改成功');
    this.visible = false;
  }
}
</script>

<template lang="pug">
.com-reports-index
  .title 
    StepToolbar.toolbar(:value='$route.name', :breadcrumbs.sync='breadcrumbs', :border='true', mode='tabs')
  .attachments-file
    .label {{ `第${reportsStore.record.windex || " "}周` }}
    Attachments(:attachments='files || []')
  .content
    .label 内容
    .context(v-html='reportsStore.record.body')
  .score-box
    .label 评分
    .score(v-if='!visible')
      span {{ (score && parseInt(score)) || " " }} 分
      IconTooltip(tips='修改', icon='edit', @click='edit')
    .a-input-number(v-if='visible')
      a-input-number(
        v-model='score',
        size='large',
        :min='0',
        :max='100',
        style='width: 120px; height: 40px; margin-right: 8px',
        @keyup.enter='onScore',
        @blur='onScore',
        v-if='visible'
      )
      strong(v-else) {{ score }}
      template(v-if='visible')
        a-button(type='primary', size='large', @click.stop='onScore()') 确认
  .commit
    .label 评论 {{ comments.length || "" }}
    .context(v-for='item in comments', :key='item.id')
      ComPtCommitItem(:item='item', @deleteComment='deleteComment')
  .add-commit
    a-textarea(placeholder='请输入评价', v-model='commentContext', :rows='6')
    .btn
      a-button(type='primary', :disabled='commentContext === ""', @click='addComment') 发布
</template>

<style lang="stylus" scoped>
.com-reports-index {
  padding: 0 20px 20px 20px;
  word-break: break-all;

  .title {
    height: 50px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    border-bottom: 1px solid #e5e5e5;

    .breadcrumb {
      cursor: pointer;
      font-weight: 500;
    }
  }

  .content, .score-box, .attachments-file, .commit, .add-commit {
    margin: 0 auto;
    width: 70%;
    padding: 20px 0;
    border-bottom: 1px solid #e5e5e5;

    .label {
      font-weight: bolder;
      margin-bottom: 5px;
    }
  }

  .score-box {
    padding: 40px 0;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }

  .a-input-number {
    z-index: 10;
  }

  // .attachments-file
  .commit {
    border: none;

    .context {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
    }
  }

  .add-commit {
    .btn {
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      margin-top: 20px;
    }
  }
}
</style>
