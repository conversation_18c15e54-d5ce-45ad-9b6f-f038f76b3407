<script lang="ts">
import { privateDecrypt } from 'crypto';
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class ComCompaniesIndex extends Vue {
  @Prop({ type: Array, default: [] }) private readonly parents!: [];
  @Prop({ type: Object, default: () => {} }) private companiesStore!: IObject;
  @Prop({ type: Number, default: () => 0 }) private readonly companiesId!: number;

  private files: any = [];
  private score: any = null;
  private attachments: any = {};
  private student: IObject = {};
  private visibleScore: boolean = false;

  get steps() {
    return [
      {
        title: '上传附件',
        value: 'uploader',
      },
      {
        title: '老师评分',
        value: 'score',
      },
    ];
  }

  get role(): string {
    return this.$tools.getRole();
  }

  get currentStep(): number {
    return this.files.length > 0 ? 1 : 0;
  }

  get activitiesId(): number {
    return +this.$route.params.activitiesId;
  }

  get company_info(): IObject {
    return this.companiesStore.record.company_info;
  }

  create() {
    this.fetchData();
  }

  async mounted() {
    this.fetchData();
  }

  public async onScore(): Promise<void> {
    await this.companiesStore.update({
      id: this.companiesStore.record.id,
      practice_infos_attributes: [{ id: this.company_info.id, score: this.score }],
    });
    await this.companiesStore.find(this.companiesId);
    this.$message.success('分数修改成功');
    this.visibleScore = false;
  }

  get pathName(): any {
    return this.$route.name;
  }

  get breadcrumbs(): any {
    return [
      { title: '企业评价', url: `/pts/teacher/activities/${this.activitiesId}/companies` },
      { title: this.student.name },
    ];
  }

  private async fetchData(): Promise<void> {
    this.companiesStore.init({ parents: this.parents });
    await this.companiesStore.find(this.companiesId);
    this.score = this.company_info.score || 0;
    this.attachments = this.company_info.attachments;
    this.files = this.companiesStore.record.company_info.files || [];
    this.student = this.companiesStore.record.student || {};
  }
  // 判断登录者是不是指导教师，可以更改评分
  get currentUserIsGuideTeacher() {
    const { id } = this.$store.state.currentUser;
    const guideTeacherIds = this.companiesStore.record.guide_teacher_ids;
    if (guideTeacherIds.includes(id)) {
      return true;
    }
    return false;
  }
}
</script>
// 企业评价详情

<template lang="pug">
.container
  .title(v-if='pathName === "pts_teacher_activities_show_statistics_companies_index"')
    StepToolbar.toolbar(:value='$route.name', :breadcrumbs.sync='breadcrumbs')
  .title.companies-title(v-else)
    template
      |
      | 企业评价
  .steps
    a-steps(direction='vertical', :current='currentStep')
      a-step(v-for='(item, index) in steps', :key='index', :title='item.title')
        template(slot='description')
          .step-item(v-if='item.value === "uploader"')
            template
              Empty(desc='暂无附件信息...', style='padding: 0px', v-if='files.length === 0')
              Attachments(:attachments='files')
          .step-item(v-if='item.value === "score" && currentStep === 1')
            template(v-if='role === "student"')
              strong {{ +company_info.score > 0 ? "得分：" + company_info.score + " 分" : "等待评分" }}
            .score(v-else) 
              a-input-number(
                v-model='score',
                size='large',
                :min='0',
                :max='100',
                style='width: 120px; height: 40px; margin-right: 8px',
                @keyup.enter='onScore',
                @blur='onScore',
                v-if='visibleScore'
              )
              strong(v-else)
                | {{ +company_info.score > 0 ? "得分：" + company_info.score + " 分" : "等待评分" }}
              template(v-if='visibleScore')
                a-button(type='primary', size='large', @click.stop='onScore()') 确认
              template(v-else-if='currentUserIsGuideTeacher')
                IconTooltip(icon='edit', tips='评分', @click='visibleScore = true')
</template>

<style lang="stylus" scoped>
.container
  .steps
    padding 20px 0
    .step-item
      padding 20px 0
      min-height 150px
  .companies-title
    height 50px
    width 100%
    color #383838
    font-size 16px
    font-weight 500
    line-height 50px
    border-bottom 1px solid #e5e5e5
</style>
