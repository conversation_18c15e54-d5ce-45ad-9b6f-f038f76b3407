<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import ComPtCommitItem from '@/components/pt/statistics/ComPtCommitItem.vue';
import { IComment } from '@/models/comment';
import score from '@/models/access/score';

@Component({
  components: { ComPtCommitItem },
})
export default class ComThesesIndex extends Vue {
  @Prop({ type: String, default: '' }) prop!: string;
  @Prop({ type: Number, default: () => '' }) private sourceId!: number;
  @Prop({ type: String, default: () => '' }) private sourceName!: string;
  @Prop({ type: Object, default: () => {} }) private thesesStore!: IObject;
  @Prop({ type: Object, default: () => {} }) private commentsStore!: IObject;
  @Prop({ type: Object, default: () => {} }) private userCommentsStore!: IObject;

  state: string = '';
  private key: string = 'theses';
  private directories: any = [];
  private comments: any = [];
  private thesis: any = [];
  private tabs: any = [
    { title: '实习报告', key: 'theses' },
    { title: '评论', key: 'comment' },
  ];
  private students: any = {};

  get activitiesId(): number {
    return +this.$route.params.activitiesId;
  }

  get breadcrumbs() {
    return [{ title: '实习报告', url: `/pts/teacher/activities/${this.activitiesId}/theses` }, { title: '陈丞' }];
  }

  get infos() {
    return [
      {
        label: '姓名',
        key: 'name',
      },
      {
        label: '学号',
        key: 'code',
      },
      {
        label: '专业',
        key: 'major_name',
      },
      {
        label: '班级',
        key: 'adminclass_name',
      },
    ].map((item: any) => ({
      ...item,
      value: this.students[item.key] || '-',
    }));
  }

  create() {
    this.getTheses();
  }

  mounted() {
    this.setStoreInit();
    this.getComments();
    this.getTheses();
  }
  private setStoreInit(): void {
    this.thesesStore.init({ parents: [{ type: 'teacher' }] });
    this.commentsStore.init({ parents: [{ type: this.sourceName, id: this.sourceId }] });
  }

  private async getComments(): Promise<void> {
    let { data } = await this.commentsStore.index();
    this.comments = data.comments;
  }

  private async getTheses(): Promise<void> {
    let { data } = await this.thesesStore.find(this.sourceId);
    this.thesis = data.thesis.infos;
    this.students = data.student;
    this.directories = this.thesis.map((item: any) => ({
      title: item.title,
      children: this.$tools.formatDirectory(item.body),
    }));
  }

  private async deleteComment({ id }: IComment): Promise<void> {
    await this.commentsStore.delete(id);
  }

  fetchData() {}
  private change(key: string) {
    this.key = key;
  }
}
</script>

<template lang="pug">
.ComThesesIndex
  StepToolbar.toolbar(
    v-model='key',
    mode='breadcrumbs',
    :steps='tabs',
    :border='true',
    :breadcrumbs.sync='breadcrumbs',
    @change='change'
  )
    template(slot='right')
      IconTooltip(icon='printer', tips='预览')
  .context-view
    .thsese-view(v-if='key === "theses"')
      template
        .pannel-top
          .title {{ thesis.title }}
          .info-box
            .cell(v-for='(info, index) in infos', :key='index')
              | {{ info.label }}：{{ info.value }}
        .pannel-middle
          strong 目录
          dl(style='margin-top: 4px')
            dt(v-for='(dt, index) in directories', :key='index') {{ index + 1 }}、{{ dt.title }}
              dd(v-for='(dd, key) in dt.children', :key='key', style='margin-left: 22px')
                .flex.ck-content
                  span {{ index + 1 }}.{{ key + 1 }}、
                  span(v-html='dd.title')
                dt(v-for='(item, ind) in dd.children', :key='ind', style='margin-left: 36px')
                  .flex.ck-content
                    span {{ index + 1 }}.{{ key + 1 }}.{{ ind + 1 }}、
                    span(v-html='item.title')
        .module(v-for='(item, index) in theses', :key='index')
          .module-top
            strong {{ item.title }}
            //- template(v-if="item.body")
              span(:class="+item.similarity > 30 ? 'text-danger' : 'text-success'") 查重率 {{ +item.similarity }}%
          .module-middle
            .ck-content(v-html='item.body')
            p(v-if='!item.body') 待完善
    .comments-view(v-else)
      .context(v-for='item in comments') 
        ComPtCommitItem(:item='item', :key='item.id', @deleteComment='deleteComment')
</template>

<style lang="stylus" scoped>
.ComThesesIndex {
  width: 100%;
  min-height: 500px;
  background: #ffffff;
  padding: 0 20px 20px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;

  .context-view {
    width: 75%;
    height: 100%;
    display: flex;
    // margin 0 auto
    padding: 20px 0;
    flex-direction: column;
    align-items: center;
    // justify-content center
    border: 1px solid #3333;

    .pannel-top {
      .info-box {
        display: flex;
        flex-direction: row;
      }
    }
  }

  .comments-view {
    width: 75%;
    height: 100%;
    margin: 0 auto;
    padding: 20px 0;
  }
}
</style>
