<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class ComPtItem extends Vue {
  @Prop({ type: String, default: () => '份' }) private readonly unit!: string;
  @Prop({ type: Number, default: () => 0, required: true }) private readonly num!: number;
  @Prop({ type: String, default: () => '', required: true }) private readonly label!: string;
}
</script>

<template lang="pug">
.container
  .label {{ label }}
  span.num {{ num }}
  span.unit {{ unit }}
</template>

<style lang="stylus" scoped>
.container
  .label
    color #a6a6a6
    font-size 12px
    font-weight 500
  .num
    color #383838
    font-size 30px
    font-weight 500
    font-family DINCond-Medium, DINCond
  .unit
    color #808080
    font-size 12px
    margin-left 5px
    font-weight  400
</style>
