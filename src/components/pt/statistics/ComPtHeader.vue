<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class ComPtHeader extends Vue {
  @Prop({ type: String, default: () => '' }) title!: string;
  @Prop({ type: Array, default: () => [] }) basePie!: [];
}
</script>

<template lang="pug">
.ComPtHeader
  .title  
    span {{ title }}
    slot(name="title")
  .context
    .box
      slot
      //- slot(name="contextRight")
    slot(name="contextRight")
</template>

<style lang="stylus" scoped>
.ComPtHeader
  width 100%
  height  100%
  display flex
  flex-direction column
  // padding 0 15px
  .title
    height 50px
    width 100%
    color #383838
    font-size 16px
    font-weight 500
    line-height 50px
    border-bottom 1px solid #e5e5e5
  .context
    flex 1
    display flex
    align-items center
    flex-direction row
    justify-content center
    // padding 10px 0
    .G2
      flex 1
      width 40%
    .box
      width 100%
      height 70px
      display flex
      align-items center
      flex-direction row
      align-content center
      justify-content space-around
      padding 0 40px
      .label
        color #a6a6a6
        font-size 12px
        font-weight 500
      .num
        color #383838
        font-size 30px
        font-weight 500
        font-family DINCond-Medium, DINCond
      .unit
        color #808080
        font-size 12px
        margin-left 5px
        font-weight  400
</style>
