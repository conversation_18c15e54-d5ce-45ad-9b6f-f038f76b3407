<script lang="ts">
import { TaIndexViewTabInterface } from '@/components/global/TaIndex';
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
// 打卡情况
@Component({
  components: {},
})
export default class ComPtPtRegistersTable extends Vue {
  @Prop({ type: Number }) private readonly sourceId!: number;
  @Prop({ type: Object, default: () => {} }) private store!: IObject;
  @Prop({ type: Array, default: () => [] }) private readonly source!: [];
  @Prop({ type: String, default: () => null }) private readonly time!: string;
  @Prop({ type: String, default: () => '' }) private readonly sourceName!: string;

  private key: string = '';
  private tabs: TaIndexViewTabInterface[] = [{ label: '', key: ' ', num: 0, background: '', color: '', mode: 'table' }];

  @Watch('time')
  onchangeTime() {
    this.store.init({
      parents: this.source,
      params: { parentId: this.sourceId, date: this.time },
    });
  }

  get config() {
    return {
      store: this.store,
      recordName: '',
      searcherSimpleOptions: [
        {
          label: '姓名',
          type: 'string',
          key: 'user_of_Student_type_name',
        },
        {
          label: '学号',
          type: 'string',
          key: 'user_of_Student_type_code',
        },
      ],
      tableConfig: { bordered: true },
    };
  }

  mounted() {
    this.store.init({
      parents: this.source,
      params: { parentId: this.sourceId, date: this.time },
    });
  }

  private handleRequset({ info }: any): void {
    this.$emit('statistics', info);
  }
}
</script>
// 打卡情况

<template lang="pug">
.ComPtPtRegistersTable
  TaIndexView(:config="config" :tabs="tabs"  :tabsLeftMargin='0',  @onIndex="handleRequset")
    template(#table)
      a-table-column(title="姓名" dataIndex="student.name" align="center")
      a-table-column(title="学号" dataIndex="student.code" align="center")
      a-table-column(title="专业" dataIndex="student.major_name" align="center")
      a-table-column(title="班级" dataIndex="student.adminclass_name" align="center")
      a-table-column(title="打卡时间" align="center")
        template(slot-scope="scope")
          span {{ scope.state==="undo" ? "-" : $moment(scope.created_at).format('HH:mm') }}
      a-table-column(title="打卡地点" dataIndex="address" align="center")
      a-table-column(title="打卡状态" dataIndex="state" align="center")
        template(slot-scope="scope")
          span(:class="{done: scope === 'done'}") {{ scope === 'done' ? '已打卡' : '未打卡' }}
</template>

<style lang="stylus" scoped>
.done
  color #6DC37D
</style>
