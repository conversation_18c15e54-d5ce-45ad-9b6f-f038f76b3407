<script lang="ts">
import { TaIndexViewTabInterface } from '@/components/global/TaIndex';
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class ComPtWorkbenchTable extends Vue {
  @Prop({ type: Number }) private readonly sourceId!: number;
  @Prop({ type: Object, default: () => {} }) private store!: IObject;
  @Prop({ type: Array, default: () => [] }) private readonly source!: [];
  @Prop({ type: String, default: () => '' }) private readonly sourceName!: string;
  @Prop({ type: Array, default: () => [] }) private readonly searcherSimpleOptions!: [];

  private key: string = 'published';
  private tabs: TaIndexViewTabInterface[] = [
    { label: '待评分', key: 'published', num: 0, background: '', color: '', mode: 'table' },
    { label: '已评分', key: 'scored', num: 0, background: '', color: '', mode: 'table' },
    { label: '未提交', key: 'pending', num: 0, background: '', color: '', mode: 'table' },
  ];

  get title(): string {
    return this.$route.meta.title;
  }
  get config() {
    return {
      store: this.store,
      recordName: '',
      searcherSimpleOptions: this.searcherSimpleOptions,
    };
  }

  mounted() {
    this.setStroeInit();
  }

  private handleRequset({ info }: any): void {
    this.$emit('statistics', info);
  }

  private onShow(val: any): void {
    this.key === 'pending'
      ? this.$message.warning('暂无周记信息！')
      : this.$router.push({ path: `${this.$route.path}/${val.id}` });
  }

  private async tabChange(val: TaIndexViewTabInterface): Promise<void> {
    this.key = val.key;
    this.setStroeInit();
  }

  private getState(scope: any): string {
    return scope.state == 'pending'
      ? '未提交'
      : scope.state == 'published' && scope.late == 'yes'
      ? '迟交'
      : '正常提交';
  }

  private async setStroeInit(): Promise<void> {
    await this.store.init({ parents: this.source, params: { q: { state_eq: this.key }, parentId: this.sourceId } });
  }
}
</script>
// 实习周记table

<template lang="pug">
.ComPtWorkbenchTable
  TaIndexView(
    :tabs="tabs"
    :config="config"
    :tabsLeftMargin='0',
    @onShow="onShow"
    @tabChange="tabChange"
    @onIndex="handleRequset"
  )
    template(#table)
      a-table-column(title="姓名" dataIndex="student.name" align="center")
      a-table-column(title="学号" dataIndex="student.code" align="center")
      a-table-column(title="周" dataIndex="windex" align="center")
        template(slot-scope="scope")
          span {{ `第${scope}周` }}
      a-table-column(title="开始时间" dataIndex="start_at" align="center")
      a-table-column(title="结束时间" dataIndex="end_at" align="center")
      a-table-column(title="提交状态" align="center")
        template(slot-scope="scope")
          span(v-if="scope.state == 'pending'") 未提交
          span.text-warning(v-else-if="scope.late == 'yes'") 迟交
          span.text-success(v-else-if="scope.late == 'no'") 正常提交
      a-table-column(title="提交时间" dataIndex="published_at" align="center" v-if="key !== 'pending' ")
      a-table-column(title="打分", align="center")
        template(slot-scope="scope")
          span {{ scope.score && `${parseInt(+scope.score)}分` || "-"  }}
      a-table-column(title="评论" dataIndex="comment_count" align="center")
        template(slot-scope="scope")
          span {{ scope || "-" }}
</template>

<style lang="stylus" scoped>

.scored
  color #eb9e05 !important
.pedding
  color #808080 !important
</style>
