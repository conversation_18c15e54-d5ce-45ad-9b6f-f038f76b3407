<script lang="ts">
import { IComment } from '@/models/comment';
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class ComPtCommitItem extends Vue {
  @Prop({ type: Object, default: () => {} }) private readonly item!: IObject;

  private deleteComment(val: IComment): void {
    this.$confirm({
      title: '确定要继续执行此操作吗?',
      content: '该操作是不可逆的',
      okText: '确定',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        this.$emit('deleteComment', this.item);
      },
      onCancel: () => {},
    });
  }
}
</script>

<template lang="pug">
.ComPtCommitItem
  .content
    .top
      .left
        a-avatar(style="width: 30px; height: 30px;") {{ item.user_name }}
        strong.name {{ item.user_name }}
      .right {{ $moment(item.updated_at).format("MM-DD hh:mm:ss") }}
    .intermediate {{item.body }}
    .bottom
      IconTooltip(tips="删除" icon="delete" @click="deleteComment")
</template>

<style lang="stylus" scoped>
.ComPtCommitItem
  .content
    display flex
    flex-direction column
    width 100%
    // height 80px
    display flex
    border-bottom 1px solid #e5e5e5
    .top
      width 100%
      height 40px
      display flex
      align-items center
      flex-direction row
      justify-content space-between
      .left
        .name
          font-size 14px
          line-height 22px
          color rgba(0, 0, 0, 0.65)
          margin-left 20px
      .right
        color  #808080
        font-size 14px
  .content:hover
    // height 100px
    .bottom
      display block
  .intermediate
    color #adadad
    padding-left 50px
    word-break break-all
    white-space normal
    text-overflow ellipsis
  .bottom
    display none
    margin-top 5px
    padding-left 50px
</style>
