<script lang="ts">
import { IReport } from '@/models/pt/report';
import { Component, Vue, Prop } from 'vue-property-decorator';
import { TaIndexViewTabInterface } from '@/components/global/TaIndex';
import { includes } from 'lodash-es';

@Component({
  components: {},
})
export default class ComPtCompaniesTable extends Vue {
  @Prop({ type: Number }) private readonly sourceId!: number;
  @Prop({ type: Object, default: () => {} }) private store!: IObject;
  @Prop({ type: Array, default: () => [] }) private readonly source!: [];
  @Prop({ type: String, default: () => '' }) private readonly sourceName!: string;
  @Prop({ type: Array, default: () => [] }) private readonly searcherSimpleOptions!: [];

  private info: any = {};
  private key: string = 'published';
  private tabs: TaIndexViewTabInterface[] = [
    { label: '待评分', key: 'published', num: 0, background: '', color: '', mode: 'table' },
    { label: '已评分', key: 'scored', num: 0, background: '', color: '', mode: 'table' },
    { label: '未提交', key: 'pending', num: 0, background: '', color: '', mode: 'table' },
  ];

  get config() {
    return {
      store: this.store,
      recordName: '',
      searcherSimpleOptions: this.searcherSimpleOptions,
    };
  }

  get title(): string {
    return this.$route.meta.title;
  }

  created() {
    // 因为企业评价和实习报告使用的相同的组件，但是搜索的key不同，这里做个区分
    if (this.$route.path.includes('companies')) {
      this.tabs = [
        { label: '待评分', key: 'submited', num: 0, background: '', color: '', mode: 'table' },
        { label: '已评分', key: 'scored', num: 0, background: '', color: '', mode: 'table' },
        { label: '未提交', key: 'pending', num: 0, background: '', color: '', mode: 'table' },
      ];
      // 默认选中第一个tab页面
      this.key = this.tabs[0].key;
    }
  }
  async mounted() {
    await this.setStoreInit();
  }

  private tabChange(val: TaIndexViewTabInterface): void {
    this.key = val.key;
    this.setStoreInit();
  }

  private onShow(val: IReport): void {
    this.$emit('onShow', val, this.key);
  }

  private handleRequset({ info }: any): void {
    this.$emit('statistics', info);
  }

  private getState(state: string): string {
    return state === 'pending' ? '未提交' : state === 'scored' ? '迟到' : '提交';
  }

  private setStoreInit(): void {
    this.store.init({ parents: this.source, params: { q: { state_eq: this.key }, parentId: this.sourceId } });
  }
}
</script>
// 企业评论 实习报告

<template lang="pug">
.ComPtCompaniesTable
  TaIndexView(
    :tabs='tabs',
    :config='config',
    @onShow='onShow',
    @tabChange='tabChange',
    @onIndex='handleRequset',
    :tabsLeftMargin='0'
  )
    template(#table)
      a-table-column(title='姓名', dataIndex='student.name', align='center')
      a-table-column(title='学号', dataIndex='student.code', align='center')
      a-table-column(title='专业', dataIndex='student', align='center')
        template(slot-scope='scope')
          span {{ scope.major_name }}
      a-table-column(title='班级', align='center')
        template(slot-scope='scope')
          span {{ scope.student.adminclass_name }}
      a-table-column(title='提交时间', align='center', v-if='key !== "pending"')
        template(slot-scope='scope')
          span {{ scope.submited_at || scope.published_at || "-" }}
      a-table-column(title='打分', align='center', v-if='key !== "pending"')
        template(slot-scope='scope')
          span {{ (scope.score && `${parseInt(+scope.score)}分`) || "等待打分" }}
      a-table-column(title='状态', dataIndex='state', align='center', v-if='key === "pending" && title === "企业评价"')
        template(slot-scope='scope')
          span {{ scope === "pending" ? "待上传附件" : "" }}
      a-table-column(
        title='评论',
        dataIndex='comment_count',
        align='center',
        v-if='title === "实习报告" && key !== "pending"'
      )
</template>

<style lang="stylus" scoped>
.scored {
  color: #eb9e05 !important;
}

.pedding {
  color: #808080 !important;
}
</style>
