<template lang="pug">
a-menu.menu(:selectedKeys='selectedKeys', @selectChange='onMenuChange', mode='inline')
  //- home
  template(v-if='moduleType === "home"')
    a-menu-item(key='/home')
      a-icon(type='home')
      span 首页
    //- a-menu-item(key="/home/<USER>")
    //-   a-icon(type="appstore")
    //-   span 全部系统
  //- hr
  template(v-if='moduleType === "hr"')
    template(v-if='$utils.hasPermission("hr", "admin")')
      //- a-menu-item(key='/hr/contacts/user')
      //-   a-icon(type='team')
      //-   span 通讯录
      //- a-menu-item(key='/hr/teacher/star_teachers')
      //-   a-icon(type='team')
      //-   span 星标联系人
      a-menu-item(key='/hr/teachers/user')
        a-icon(type='team')
        span 我的信息
      a-menu-item(key='/hr/departments')
        a-icon(type='cluster')
        span 组织结构
      a-divider
      a-menu-item(key='/hr/teachers')
        a-icon(type='solution')
        span 花名册
      a-menu-item(key='/hr/admin/experts')
        a-icon(type='solution')
        span 专家
      a-menu-item(key='/hr/stats')
        a-icon(type='file-done')
        span 数据中心
      a-menu-item(key='/hr/modifications')
        a-icon(type='notification')
        span 审批动态
      //- a-menu-item(key="/hr/logs")
        a-icon(type="file-search")
        span 日志管理
      //- a-menu-item(key="/hr/recycles")
        a-icon(type="delete")
        span 回收站
      a-menu-item(key='/hr/duties')
        a-icon(type='audit')
        span 岗位管理
      a-menu-item(key='/hr/titles')
        a-icon(type='exception')
        span 职称管理
      a-menu-item(key='/hr/tags')
        a-icon(type='tags')
        span 标签管理
      a-menu-item(key='/hr/contract')
        a-icon(type='file')
        span 合同管理
      a-menu-item(key='/hr/log')
        a-icon(type='file')
        span 日志管理
      a-menu-item(key='/hr/recycles')
        a-icon(type='delete')
        span 回收站
    //- 普通用户显示花名册
    template(v-else)
      //- a-menu-item(key='/hr/contacts/user')
      //-   a-icon(type='team')
      //-   span 通讯录
      //- a-menu-item(key='/hr/teacher/star_teachers')
      //-   a-icon(type='team')
      //-   span 星标联系人
      a-menu-item(key='/hr/teachers/user')
        a-icon(type='team')
        span 我的信息
      a-menu-item(key='/hr/department/user')
        a-icon(type='cluster')
        span 组织结构
  //- studying
  template(v-if='moduleType === "studying"')
    template(v-if='authRole === "teacher"')
      a-menu-item(key='/studying/teacher/students')
        a-icon(type='team')
        span 我的学生
      a-menu-item(key='/studying/teacher/classes')
        a-icon(type='cluster')
        span 我的班级
      a-menu-item(key='/studying/teacher/instances')
        a-icon(type='cluster')
        span 学生审批
      a-menu-item(key='/studying/teacher/records')
        a-icon(type='solution')
        span 记录查询
      a-divider
    template(v-if='$utils.hasPermission("hr", "admin")')
      a-menu-item(key='/studying/admin/students')
        a-icon(type='solution')
        span 学生管理
      a-menu-item(key='/studying/admin/classes')
        a-icon(type='solution')
        span 班级管理
      a-menu-item(key='/studying/admin/records')
        a-icon(type='solution')
        span 记录管理
    template(v-if='authRole === "student"')
      a-menu-item(key='/studying/student/info')
        a-icon(type='team')
        span 我的信息
      a-menu-item(key='/studying/student/instances')
        a-icon(type='cluster')
        span 提交申请
  //- studying
  //- template(v-else-if='moduleType === "studying"')
  //-   a-menu-item(key='/studying/students')
  //-     a-icon(type='team')
  //-     span 学生列表
  //-   a-menu-item(key='/studying/students')
  //-     a-icon(type='team')
  //-     span 教育计划
  //- fund
  template(v-else-if='moduleType === "fund"')
    template(v-if='$utils.hasPermission("fund", "admin")')
      a-menu-item(key='/fund/routine_bases')
        a-icon(type='tags')
        span 资金卡统计
      a-sub-menu
        template(slot='title')
          a-icon(type='pay-circle')
          span 单据统计
        a-menu-item(key='/fund/payments')
          a-icon(type='file-protect')
          span 全部单据
        a-menu-item(key='/fund/review_payments')
          a-icon(type='exception')
          span 出纳初审单据
        a-menu-item(key='/fund/record_payments')
          a-icon(type='file-done')
          span 财务记账单据
    a-menu-item(key='/fund/teacher/routine_bases')
      a-icon(type='tag')
      span 我的资金卡
    a-menu-item(key='/fund/teacher/execute_routine_budgets')
      a-icon(type='crown')
      span 执行资金卡
    a-menu-item(key='/fund/teacher/inset_payments')
      a-icon(type='money-collect')
      span 发起的凭证
    a-menu-item(key='/fund/teacher/approval_payments')
      a-icon(type='property-safety')
      span 待审批的凭证
  //- meeting
  template(v-else-if='moduleType === "meeting"')
    template(v-if='$utils.hasPermission("meeting", "admin")')
      a-menu-item(key='/meeting/admin/activities')
        a-icon(type='flag')
        span 活动管理
    template(v-if='$utils.hasPermission("meeting", ["operate"])')
      a-menu-item(key='/meeting/user/activities')
        a-icon(type='flag')
        span 全部活动
      a-menu-item(key='/meeting/user/attendances')
        a-icon(type='profile')
        span 我报名的
  //- permit
  template(v-else-if='moduleType === "permit"')
    a-menu-item(key='/permit/teachers')
      a-icon(type='team')
      span 权限管理
  //- access
  template(v-else-if='moduleType === "access"')
    template(v-if='$utils.hasPermission("access", "admin")')
      a-menu-item(key='/access/activities')
        a-icon(type='team')
        span 考核管理
    a-menu-item(key='/access/teacher/activities')
      a-icon(type='cluster')
      span 人事考核
  //- pt
  template(v-else-if='moduleType === "pt"')
    template(v-if='authRole === "student"')
      a-menu-item(key='/pt/student/practices')
        a-icon(type='global')
        span 实习管理
    template(v-else)
      template(v-if='$utils.hasPermission("pt", "admin")')
        a-menu-item(key='/pt/activities')
          a-icon(type='flag')
          span 实习活动
        a-menu-item(key='/pt/templates')
          a-icon(type='layout')
          span 模板管理
        a-divider
      a-menu-item(key='/pt/teacher/activities')
        a-icon(type='global')
        span 实习管理
  //- ep
  template(v-else-if='moduleType === "ep"')
    template(v-if='$utils.hasPermission("ep", "admin")')
      a-menu-item(key='/ep/activities')
        a-icon(type='area-chart')
        span 疫情管理
      a-menu-item(key='/ep/admin/statistic')
        a-icon(type='pie-chart')
        span 疫情统计
      a-divider
    template(v-if='authRole === "student"')
      a-menu-item(key='/ep/student/attendances')
        a-icon(type='line-chart')
        span 每日健康
    template(v-else)
      a-menu-item(key='/ep/teacher/inspect_activities')
        a-icon(type='eye')
        span 巡查疫情
      a-menu-item(key='/ep/teacher/attendances')
        a-icon(type='line-chart')
        span 每日健康
  //- forms
  template(v-else-if='moduleType === "forms"')
    a-menu-item(key='/forms/admin/templates')
      a-icon(type='snippets')
      span 模板表单管理
  //- conference
  template(v-else-if='moduleType === "conference"')
    //- TODO:hasPermission meeting
      template
    a-menu-item(key='/conference/teacher/activities/weekly')
      a-icon(type='carry-out')
      span 一周会议安排
    template(v-if='$utils.hasPermission("meeting", "admin")')
      a-sub-menu
        template(slot='title')
          a-icon(type='schedule')
          span 会议审核
        a-menu-item(key='/conference/admin/activities/review/weekly')
          span 日历视图
        a-menu-item(key='/conference/admin/activities/review/list')
          span 列表视图
      a-menu-item(key='/conference/admin/rooms')
        a-icon(type='appstore')
        span 会议室管理
    template
      a-sub-menu
        template(slot='title')
          a-icon(type='schedule')
          span 我的会议
        a-menu-item(key='/conference/teacher/activities/mine/joined')
          span 我参与的
        a-menu-item(key='/conference/teacher/activities/mine/booked')
          span 我预约的
        a-menu-item(key='/conference/teacher/activities/mine/unreviewed')
          span 待我审核
      a-menu-item(key='/conference/teacher/activities/book')
        a-icon(type='appstore')
        span 预约会议
  template(v-else-if='moduleType === "teaching"')
    a-menu-item(key='/teaching/teacher/online_courses')
      span 课程列表
  template(v-else-if='moduleType === "assessment"')
    a-menu-item(key='/assessment/user/activities')
      a-icon(type='appstore')
      span 我的考核
    a-divider
    template(v-if='$utils.hasPermission("assessment", "admin")')
      a-menu-item(key='/assessment/admin/activities')
        a-icon(type='appstore')
        span 考核管理
      a-menu-item(key='/assessment/admin/score_templates')
        a-icon(type='appstore')
        span 评分表管理
  template(v-else-if='moduleType === "prevention"')
    a-menu-item(key='/prevention/admin/coming_and_goings')
      a-icon(type='appstore')
      span 出入记录
  template(v-if='moduleType === "homesite"')
    template(v-if='$utils.hasPermission("portal", "admin")')
      a-menu-item(key='/homesite/admin/banners')
        a-icon(type='home')
        span 轮播图
  template(v-if='moduleType === "homesite"')
    template(v-if='$utils.hasPermission("portal", "admin")')
      a-menu-item(key='/homesite/admin/titles')
        a-icon(type='home')
        span 主题图
  template(v-if='moduleType === "homesite"')
    template(v-if='$utils.hasPermission("portal", "admin")')
      a-menu-item(key='/homesite/admin/media')
        a-icon(type='home')
        span 媒体关注图
  template(v-if='moduleType === "homesite"')
    template(v-if='$utils.hasPermission("portal", "admin")')
      a-menu-item(key='/homesite/admin/newsImages')
        a-icon(type='home')
        span 图片新闻图
</template>

<script lang="ts">
import Vue from 'vue';
import { Component, Prop, Watch, Emit } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class SideMenu extends Vue {
  // data
  private selectedKeys: any = [];

  get isCollapse() {
    return this.$store.state.isCollapse;
  }
  get moduleType() {
    return (this.$route.path || '').split('/')[1] || '';
  }
  get currentRole() {
    return this.$store.getters.currentRole;
  }
  get authRole() {
    return this.$store.state.authRole;
  }
  get teacherId() {
    return this.$store.state.currentUser.id;
  }

  @Watch('$route')
  public name() {
    this.selectedKeys = [this.$route.path];
  }

  public mounted() {
    this.selectedKeys = [this.$route.path];
  }
  public onMenuChange(val: string[]) {
    this.$router.replace(val[0]);
  }
}
</script>

<style lang="stylus" scoped>
.menu {
  overflow: hidden;
  width: 200px;
  height: 100%;
  border-right: 0px;

  li {
    width: 100%;
  }
}

.divide-border {
  border-bottom: 1px solid #3333;
}

.box {
  border: 1px solid #333;
}
</style>
