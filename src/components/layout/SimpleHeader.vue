<template lang="pug">
header.navbar
  .brand
    img.logo(src="@/assets/images/school_logo.png" height="30")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';

@Component
export default class SimpleHeader extends Vue {}
</script>

<style lang="stylus" scoped>
.navbar
  display flex
  align-items center
  padding 0px 16px
  height 50px
  background #FFFFFF
  box-shadow 0 0 8px 0 rgba(0, 0, 0, 0.1)
  .brand
    flex-shrink 0
    width 300px
</style>
