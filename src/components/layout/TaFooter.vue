<template lang="pug">
footer
  a(href="http://www.beian.miit.gov.cn/")
    | 沪ICP备08109476号 | Copyright © 2020 Tallty Information Tecnolgy Co.,Ltd All Rights Reserved.
</template>

<script lang="ts">
import Vue from 'vue';
import { Component, Prop, Watch, Emit } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class TaFooter extends Vue {}
</script>

<style lang="stylus" scoped>
footer
  color #808080
  font-size 13px
  padding 0 16px
  height 36px
  line-height 36px
  text-align center
  a
    color #808080
</style>
