<template lang="pug">
header.navbar
  .brand
    component(:is='$t("globe.school.component")')
    ElderlyModeSwitch
    SpeechControl
  .modules
    .name
      | {{ $tools.formatModuleTitle(moduleType) || $t('globe.school.name') }}
    a-button(shape="circle" size="small" @click="backHome")
      img(src="@/assets/icons/tertiary.svg" height="20" width="20")
  .menus
    .menu-item.avatar-box
      a-popover(placement="bottomRight")
        template(slot="content")
          .templatex
            .line
              span.title 账号:
              span.text {{currentUser.name}}
            .but-set
              span.but
                router-link(to="/auth/info") 个人设置
              span.but(@click="logout") 退出登录
        a-avatar.avatar(:size="32")
          | {{ currentUser.name }}
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import sessionStore from '@/store/modules/session.store';
import store from '@/store';
import ComLogoStiei from '../logo/ComLogoStiei.vue';
import ComLogoSucvc from '../logo/ComLogoSucvc.vue';

@Component({
  components: {
    ComLogoStiei,
    ComLogoSucvc,
  },
})
export default class NavBar extends Vue {
  get currentUser() {
    return store.state.currentUser || {};
  }
  get moduleType() {
    return this.$route.path.split('/')[1] || '/';
  }
  get isStudent() {
    return store.getters.currentRole === 'student' || store.getters.currentRole === 'newcommer';
  }

  get isExpert() {
    return store.getters.currentRole === 'expert';
  }

  async logout() {
    await sessionStore.logout();
    if (this.isStudent) {
      this.$router.replace(`/auth/login?redirectUrl=${location.pathname}`);
    } else {
      this.$router.replace('/auth/login');
    }
  }

  backHome() {
    if (this.isStudent || this.isExpert) {
      // this.$router.replace('/portal').catch(() => {});
      window.location.href = '/education/home';
    } else {
      // this.$router.replace('/').catch(() => {});
      window.location.href = '/education/home';
    }
  }
}
</script>

<style lang="stylus" scoped>
.navbar
  display flex
  align-items center
  padding 0px 16px
  height 50px
  background #FFFFFF
  box-shadow 0 0 8px 0 rgba(0, 0, 0, 0.1)
  .brand
    flex-shrink 0
    width 300px
    display flex
    align-items center
    justify-content center
  .modules
    width 100%
    text-align center
    line-height 28px
    .name
      display inline-block
      margin-right 8px
      vertical-align middle
      font-weight 500
      font-size 16px
  .menus
    display flex
    flex-shrink 0
    justify-content flex-end
    width 300px
    height 100%
    .menu-item
      display flex
      align-items center
      padding 0 11px
      height 100%
      color #A6A6A6
      font-weight 500
      font-size 14px
      line-height 1
      cursor pointer
      &:hover
        color #3da8f5
        .action-btn
          background #ecf6fe
      .vertical-line
        width 1px
        height 14px
        background #ccc
      .action-btn
        display flex
        justify-content center
        align-items center
        padding 0
        width 24px
        height 24px
        border none
        border-radius 4px
        font-size 16px
        cursor pointer
    .qrcode
      width 140px
      height 140px
    .avatar-box
      padding-right 0
      .avatar
        border-radius 50%

.avatar
  background-color #58A8EF

.templatex
  padding 20px
  width 180px
  nav
    display flex
    align-items center
    .nav-user
      margin-left 16px
      .name
        color rgba(0, 0, 0, 0.85)
        font-size 16px
      .type
        color rgba(0, 0, 0, 0.45)
        font-size 14px
  .line
    margin 8px 0px
    color rgba(0, 0, 0, 0.45)
    font-size 14px
    .text
      margin-left 16px
      color rgba(0, 0, 0, 0.85)
  .but-set
    display flex
    justify-content space-between
    align-items center
    .but:hover
      cursor pointer

button
  border none

@media print
  .navbar
    display none
</style>
