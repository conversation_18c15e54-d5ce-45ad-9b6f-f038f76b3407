<script lang="ts">
import Vue from 'vue';
import { Component, Watch } from 'vue-property-decorator';
import AutoMenuTofuAppStore from '@/store/modules/tofu/auto_menu.store';
import { ITofu } from '@/models/tofu/app';

@Component({
  components: {},
})
export default class AutoSideMenu extends Vue {
  private selectedKeys: any = [];

  get store() {
    return AutoMenuTofuAppStore;
  }

  get moduleType() {
    return (this.$route.path || '').split('/')[1] || '';
  }

  @Watch('$route')
  public name() {
    this.selectedKeys = [this.$route.path];
    this.fetchMenus();
  }

  public mounted() {
    this.selectedKeys = [this.$route.path];
    this.fetchMenus();
  }

  public onMenuChange(val: string[]) {
    val[0].startsWith('http') ? window.open(val[0], '_blank') : this.$router.replace(val[0]);
  }

  public fetchMenus() {
    this.store.fetch({ count: true, nested: true, mod: this.moduleType });
  }

  private getCompoment(app: ITofu) {
    return app.name === '分割线' ? 'a-divider' : (app.subs || []).length === 0 ? 'a-menu-item' : 'a-sub-menu';
  }
}
</script>

<template lang="pug">
a-menu.menu(:selectedKeys='selectedKeys', @selectChange='onMenuChange', mode='inline')
  template
    component(v-for='app in (store.records[0] || { subs: [] }).subs', :is='getCompoment(app)', :key='app.url')
      .simple-menu(v-if='app.subs.length === 0 && getCompoment(app) !== "a-divider"')
        a-icon(:type='app.icon')
        span {{ app.name }}
        span(v-if='app.instance_count') {{ ` · ${app.instance_count}` }}
        .sub-item
      template(#title, v-if='app.subs.length !== 0')
        a-icon(:type='app.icon', v-if='app.icon')
        span {{ app.name }}
        span(v-if='app.instance_count') {{ ` · ${app.instance_count}` }}
      a-menu-item(v-if='app.subs.length !== 0', v-for='sub_app in app.subs', :key='sub_app.url')
        a-icon(:type='sub_app.icon', v-if='sub_app.icon')
        span {{ sub_app.name }}
        span(v-if='app.instance_count') {{ ` · ${app.instance_count}` }}
</template>

<style lang="stylus" scoped>
.menu
  overflow hidden
  width 200px
  height 100%
  border-right 0px
</style>
