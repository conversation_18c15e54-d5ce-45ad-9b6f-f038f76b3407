<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { IInstance, Instance } from '@/models/bpm/instance';

@Component({
  components: {},
})
export default class ComBpmInstanceDetailHeader extends Vue {
  @Prop({ type: Object }) readonly record!: IInstance;
  @Prop({ type: Boolean, default: false }) scroll!: boolean;

  qrCode = require('@/assets/images/bpm/qr_code.png');
  waiting = require('@/assets/images/bpm/waiting.png');

  get operatorDesc() {
    return new Instance().operatorDesc(this.record);
  }

  get isRunning() {
    return (
      this.record.current_token &&
      (this.record.current_token.state === 'processing' || this.record.current_token.state === 'preparing')
    );
  }
}
</script>

<template lang="pug">
.com-bpm-instance-detail-header
  img.img-qr-code(:src='qrCode')
  .number-box
    .number {{ record.seq }}
    .label 编号
  .scroll-box(:class='scroll ? "is-scroll" : "not-scroll"')
    .scroll-main
    .scroll-main
      .title {{ `${record.creator_name}发起的${record.workflow_name}` }}
      .wait(v-if='isRunning')
        img.wait-img(:src='waiting')
        span.text 等待{{ operatorDesc }}处理
</template>
<style lang="stylus" scoped>
.com-bpm-instance-detail-header
  position absolute
  top 0
  display flex
  height 55px
  width 100%
  align-items center
  padding 0 30px
  overflow hidden
  .img-qr-code
    width 16px
    height 16px
    margin-right 26px
  .number-box
    display flex
    border-radius 4px
    border: 1px solid #1890FF;
    overflow auto
    margin-right 24px
    font-size 12px
    .number
      padding 0 12px
      height: 24px;
      border-radius 4px 0 0 4px
      line-height 24px
      background: rgba(24, 144, 255, 0.2);
      color #1890FF
    .label
      width: 34px;
      height: 24px;
      background: #1890FF;
      line-height 24px
      text-align center
      color #FFFFFF
  .scroll-box
    flex 1
    height 55px
    width 100%
    margin-right 10px
    transition all 0.5s ease 0s
    position relative
    .scroll-main
      display flex
      height 55px
      align-items center
      .title
        font-size 18px
        color #262626
        font-weight bold
        margin-right 26px
      .wait
        .wait-img
          width 16px
          height 16px
          margin-right 10px
        .text
          color #FA8C15
          font-size 14px

  .is-scroll
    top -55px
  .not-scroll
    top 0px
</style>
