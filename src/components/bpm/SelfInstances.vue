<script lang="ts">
/**
 * 关联用户的审批动态
 */
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
// import instanceStore from '@/store/modules/bpm/instance.store'; 切换bpm-user-store
import instance, { InstanceType, IInstance } from '@/models/bpm/instance';
import ComBpmInstanceCard from './ComBpmInstanceCard.vue';
import { bpmUserInstanceStore } from '@/store/modules/bpm/user/instance.store';

@Component({
  components: {
    ComBpmInstanceCard,
  },
})
export default class BpmSelfInstances extends Vue {
  @Prop({ type: String, default: '动态' }) title!: string;
  @Prop({ type: Object, default: () => ({ workflow_modul_null: true }) }) modulQuery!: IObject;
  @Prop({ type: [Array, String], default: () => [InstanceType.Bpm] }) types!: InstanceType[];
  @Prop({ type: String, default: 'div' }) rootComponent!: 'div' | 'panel';

  activeQuery: IObject = {};
  loadingExport = false;
  activeState: string = 'approving';
  tabs: object[] = [
    { title: '待我提交', key: 'todo' },
    { title: '待我审批', key: 'approving' },
    { title: '我发起的', key: 'created' },
    { title: '我已审批', key: 'approved' },
    { title: '抄送我的', key: 'notified' },
  ];
  query: IObject = {};
  variables: string[] = [
    'seq',
    'creator_of_Student_type_code',
    'creator_of_Student_type_name',
    'creator_of_Teacher_type_code',
    'creator_of_Teacher_type_name',
    'summary',
  ];
  instance: IInstance | IObject = {};
  visible: boolean = false;

  get workflowId(): number {
    return +this.$route.params.workflowId;
  }

  // get instanceStore() {
  //   return instanceStore;
  // }

  get bpmUserInstanceStore() {
    return bpmUserInstanceStore;
  }

  get stateMap() {
    return instance.stateMap;
  }

  get isPanel() {
    return this.rootComponent === 'panel';
  }

  // created() {
  //   instance.setRole('user');
  // }

  mounted() {
    this.bpmUserInstanceStore.init();
    this.refresh();
  }

  async fetchRecords(page: number = 1, pageSize: number = 10) {
    const { id, type } = this.$store.state.currentUser;
    this.activeQuery = {
      [this.activeState]: [id, type],
      type_in: this.types,
      // ...this.modulQuery,
      ...this.query,
    };
    await this.bpmUserInstanceStore.index({
      page,
      per_page: pageSize,
      q: this.activeQuery,
    });
  }

  async fetchStatistic() {
    try {
      const { data } = await instance.statistic('', this.workflowId, this.types);
      this.tabs.forEach((o: any) => {
        this.$set(o, 'count', data.statistic[o.key]);
      });
    } catch (error) {
      this.$message.error('获取审批统计信息失败');
    }
  }

  refresh() {
    this.fetchRecords();
    this.fetchStatistic();
  }

  onDateChange(range: any) {
    const [start, end] = range;
    if (start && end) {
      this.query.created_at_gteq = start.toString();
      this.query.created_at_lteq = end.toString();
    } else {
      this.query.created_at_gteq = null;
      this.query.created_at_lteq = null;
    }
    this.fetchRecords(1);
  }

  onShow(record: IInstance) {
    this.instance = record;
    this.visible = true;
  }

  onFlowableEdit() {
    this.$message.warning('审核中，关联资源不可修改');
  }

  async onExport() {
    this.loadingExport = true;
    const { data } = await instance.export({ q: this.activeQuery });
    this.loadingExport = false;
    window.open(data.url);
  }
}
</script>

<template lang="pug">
component.container(:is='rootComponent', :class='{ "instance-panel": isPanel }')
  template(v-if='isPanel', slot='header')
    StepToolbar(v-model='activeState', mode='tabs', :steps='tabs', @change='fetchRecords(1)')
      Searcher.header-item(
        v-model='query',
        :variables='variables',
        placeholder='搜索编号、申请人、工号、概要',
        tips='检索动态',
        @change='fetchRecords(1)'
      )
      a-range-picker.date-item(@change='onDateChange', format='YYYY-MM-DD')
      TextButton(:icon='loadingExport ? "loading" : "download"', v-if='activeState !== "todo"', @click='onExport')
        | 导出
  template(v-else)
    TaTitleHeader.toolbar(:title='title')
      Searcher.header-item(
        v-model='query',
        :variables='variables',
        placeholder='搜索编号、申请人、工号、概要',
        tips='检索动态',
        @change='fetchRecords(1)'
      )
      a-range-picker.date-item(@change='onDateChange', format='YYYY-MM-DD')
      //- TextButton(:icon='loadingExport ? "loading" : "download"', v-if='activeState !== "todo"', @click='onExport')
      //-   | 导出
      TaExport(:store='bpmUserInstanceStore', :temporaryQuery='{ ...activeQuery }')
    StepToolbar(v-model='activeState', mode='tabs', :steps='tabs', @change='fetchRecords(1)')

  ListView(:store='bpmUserInstanceStore', @change='fetchRecords')
    template(slot-scope='{ record }')
      ComBpmInstanceCard(:instance='record', @refresh='refresh')
        template
          slot(name='dialog', :flowable='instance.flowable_info')
</template>

<style lang="stylus" scoped>
.instance-panel
  height 100%

.header-item
  margin-left 12px

.date-item
  margin-left 12px
  width 240px
</style>
