<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { TransitionTypes } from '@/models/bpm/workflow';
import { IApiKeyAttr, IWorkflow } from '@/models/bpm/workflow';
import { debounce } from 'lodash';
import FieldsSelector from './FieldsSelector.vue';
import { IFormTemplateItem } from '../../../interfaces/formTemplate.interface';

@Component({
  components: {
    FieldsSelector,
  },
})
export default class ApiCallbackNodeEditor extends Vue {
  @Prop({ type: Object, default: () => ({ options: {} }) }) readonly nodeCopy!: IObject;
  @Prop({
    type: Object,
    default: () => ({
      meta: {
        workflow_attributes: [],
      },
    }),
  })
  workflow!: IWorkflow;

  methods: string[] = ['post', 'get', 'delete', 'put', 'patch'];
  headers: any[] = [];
  keyAttrs: IApiKeyAttr[] = [];
  // select field
  visible: boolean = false;
  fields: IFormTemplateItem[] = [];
  debounceHeaderInput: () => void = () => {};
  debounceAttrInput: () => void = () => {};

  @Watch('nodeCopy', { deep: true, immediate: true })
  onNodeChange() {
    this.headers = Object.entries(this.nodeCopy.options.headers || {}).map((ary: any) => ({
      key: ary[0],
      value: ary[1],
    }));
    this.keyAttrs = this.nodeCopy.options.key_attrs || [];
  }
  @Watch('workflow', { deep: true, immediate: true })
  onWorkflowChange() {
    this.initFields();
  }

  get options() {
    return (
      this.nodeCopy.options || {
        headers: {},
        key_attrs: [],
      }
    );
  }

  beforeDestroy() {
    this.debounceAttrInput();
  }

  initFields() {
    const fields = this.workflow.form ? this.workflow.form.fields || [] : [];
    const extraAttrs = ((this.workflow.meta as any).workflow_attributes || []).map((o: any) => ({
      key: o.attr,
      name: o.name,
      layout: {},
      model: {
        attr_type: o.attr_type || 'number',
      },
    }));
    this.fields = fields.concat(extraAttrs);
  }

  created() {
    this.debounceHeaderInput = debounce(this.onHeaderInput, 1000);
    this.debounceAttrInput = debounce(this.onAttrChange, 1000);
  }

  addHeader() {
    this.headers.push({ key: '', value: '' });
  }
  onHeaderInput() {
    this.$set(
      this.nodeCopy.options,
      'headers',
      this.headers.reduce(
        (obj: object, h: any) => ({
          ...obj,
          [h.key]: h.value,
        }),
        {},
      ),
    );
  }
  onAttrChange() {
    this.$set(this.nodeCopy.options, 'key_attrs', this.keyAttrs);
  }
  onSelectFields(fields: IFormTemplateItem[]) {
    this.keyAttrs = fields.map((o: any) => ({
      key: o.key,
      key_name: o.name,
      name: o.name,
    }));
    this.onAttrChange();
  }
}
</script>

<template lang="pug">
.node-callback
  a-input(v-model="nodeCopy.options.url" placeholder="请输入接口地址")
    a-select(slot="addonBefore" defaultValue="post" style="width: 90px" v-model="nodeCopy.options.method")
      a-select-option(v-for="method in methods" :key="method" :value="method")
        | {{ method }}
  .partical
    h3.title Headers
    a-row.line(v-for="(header, index) in headers" :key="index" :gutter="16")
      a-col(:span="12")
        a-input.header-input(
          placeholder="请输入 Header 名"
          addonBefore="Key"
          v-model="header.key"
          auto-focus
          @input="debounceHeaderInput")
      a-col(:span="12")
        a-input.header-input(
          placeholder="请输入 Header 值"
          addonBefore="Value"
          v-model="header.value"
          @input="debounceHeaderInput")
    a-button(type="primary" icon="plus" size="small" @click="addHeader")
      | 增加 Header

  //- .partical
  //-   h3.title 字段映射
  //-   a-row.line(v-for="(keyAttr, index) in keyAttrs" :key="index" :gutter="16")
  //-     a-col(:span="8")
  //-       a-input(disabled :value="keyAttr.key_name")
  //-     a-col.text-center(:span="2")
  //-       a-icon.icon(type="arrow-right")
  //-     a-col(:span="8")
  //-       a-input.header-input(
  //-         placeholder="请输入映射字段名称"
  //-         v-model="keyAttr.name"
  //-         @input="debounceAttrInput")
  //-   a-button(type="primary" icon="setting" size="small" @click="visible = true")
  //-     | 配置字段

  FieldsSelector(
    v-model="visible"
    :fields="fields"
    :selectedFields="keyAttrs"
    @change="onSelectFields")
</template>

<style lang="stylus" scoped>
.node-callback
  padding 20px
  .partical
    padding 20px 0
    margin-top 20px
    border-top 1px solid #e8e8e8
    .line
      margin-bottom 10px
      .icon
        font-size 18px
        margin-top 5px
    .title
      margin-bottom 10px
</style>
