<script lang="ts">
/**
 * 工作流 - 审批节点编辑器
 */
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import department from '@/models/department';
import {
  IWorkflow,
  TransitionTypes,
  IWorkflowCorePlace,
  PlaceActionType,
  getDefaultPlaceActionConfig,
  PlaceActionConfig,
} from '@/models/bpm/workflow';
import FormDesignerDialog from '@/components/form/FormDesignerDialog.vue';
import { IFormTemplateItem } from '@/interfaces/formTemplate.interface';
import UserSelectorDialog from '@/components/hr/UserSelectorDialog.vue';
import FieldsSelector from './FieldsSelector.vue';
import { isEqual } from 'lodash';

@Component({
  components: {
    FormDesignerDialog,
    FieldsSelector,
    UserSelectorDialog,
  },
})
export default class ApprovalNodeEditor extends Vue {
  levels: number[] = [1, 2, 3, 4, 5];
  // 添加 department
  departments: any[] = [];
  // 添加 teacher
  memberSelectorVisible: boolean = false;
  selectedMembers: any[] = [];
  // 配置 place form
  formDesignerVisible: boolean = false;
  // 节点操作配置
  placeActionConfig: PlaceActionConfig = getDefaultPlaceActionConfig();

  @Prop({
    type: Object,
    default: () => ({
      options: {},
      fileds: { fields: [] },
      place_form: { fields: [] },
      callback_options: {},
    }),
  })
  readonly nodeCopy!: IWorkflowCorePlace;

  @Prop({
    type: Object,
    default: () => ({
      meta: {
        workflow_roles: [],
      },
    }),
  })
  workflow!: IWorkflow;

  get workflowRoles() {
    return (this.workflow.meta as any).workflow_roles || [];
  }
  get TransitionTypes() {
    return TransitionTypes;
  }
  get radioConfig() {
    const res: any[] = [
      {
        label: '学院主管',
        value: TransitionTypes.ApprovalLevelManager,
        option: { level: 1, manager: true },
      },
      {
        label: '部门主管',
        value: TransitionTypes.ApprovalSpecifyManager,
        option: { department_id: null, department_name: '', manager: true },
      },
      { label: '科研分管领导', value: TransitionTypes.ApprovalResearchManager, option: { manager: true } },
      { label: '直接主管', value: TransitionTypes.ApprovalDirectManager, option: { manager: true } },
      { label: '指派一人', value: TransitionTypes.ApprovalUser, option: { users: [] } },
      { label: '会签', value: TransitionTypes.ApprovalUserAll, option: { users: [] } },
      { label: '或签', value: TransitionTypes.ApprovalUserAny, option: { users: [] } },
      { label: '审批人自选', value: TransitionTypes.ApprovalSelect, option: { mode: 'and' } },
      { label: '发起人自选', value: TransitionTypes.ApprovalSponsorSelect, option: {} },
      { label: '发起人自己', value: TransitionTypes.ApprovalSponsorSelf, option: {} },
    ];
    if (this.workflowRoles.length) {
      res.push({ label: '分配角色', value: TransitionTypes.ApprovalFlowableRole, option: { role: '' } });
    }
    return res;
  }
  get selectedDepartment() {
    const { options } = this.nodeCopy;
    return options && options.department_id
      ? {
          label: options.department_name, // 对应于 depaartment 的 short_name
          value: options.department_id, // 对应于 depaartment 的 id
        }
      : null;
  }
  get selectedMemberIds() {
    return this.selectedMembers.map(o => o.id);
  }
  // 流程通用表单
  get formFields() {
    return this.nodeCopy.fields!.fields || [];
  }
  // 节点自身表单
  get placeFields() {
    return this.nodeCopy.place_form ? this.nodeCopy.place_form.fields || [] : [];
  }
  get allFields() {
    return this.formFields.concat(this.placeFields);
  }
  // 节点回调
  get tokenCallbacks() {
    const { meta } = this.workflow;
    return (meta && meta.token_callbacks) || [];
  }
  get callbackMethod() {
    return this.nodeCopy.callback_options ? this.nodeCopy.callback_options.callback_method || '' : '';
  }
  // 节点模型字段映射
  get workflowAttributes() {
    const { meta } = this.workflow;
    return (meta && meta.workflow_attributes) || [];
  }
  // 表单访问权限
  get headerAccessibility() {
    if (this.formFields instanceof Array && this.formFields.length > 0) {
      const firstAccessibility = this.formFields[0].accessibility;
      if (this.formFields.every((item: any) => item.accessibility === firstAccessibility)) {
        return firstAccessibility;
      }
      return null;
    }
    return null;
  }

  @Watch('nodeCopy', { deep: true, immediate: true })
  onNodeCopyChange() {
    this.onOptionsChange();
    if (this.nodeCopy.callback_options && this.nodeCopy.callback_options.action_alias) {
      this.placeActionConfig = this.nodeCopy.callback_options.action_alias;
    }
  }

  mounted() {
    this.fetchDepartments();
  }
  async fetchDepartments() {
    const { data } = await department.tree();
    const str = JSON.stringify(data.departments);
    const newStr = str.replace(/"id":/g, '"value":').replace(/"short_name":/g, '"label":');
    this.departments = JSON.parse(newStr);
  }
  onOptionsChange() {
    const { options } = this.nodeCopy;
    if (options) {
      if (options.users) {
        this.selectedMembers = options.users.map((o: any) => ({ ...o }));
      } else if (options.teachers) {
        // 兼容老版本 teachers
        this.selectedMembers = options.teachers.map((o: any) => ({
          id: o.teacher_id,
          name: o.teacher_name,
        }));
      } else {
        this.selectedMembers = [];
      }
    } else {
      this.selectedMembers = [];
    }
  }
  // =============== approval ===============
  onApprovalTypeChange(e: any) {
    const { option } = this.radioConfig.find(o => o.value === e.target.value);
    this.$set(this.nodeCopy, 'options', { ...option });
  }
  // 选择成员 users
  setSelectedUsers(members: any[]) {
    this.selectedMembers = members;
    this.$set(
      this.nodeCopy.options!,
      'users',
      members.map((o: any) => ({
        type: 'Teacher',
        ...this.$utils.only(o, ['id', 'name', 'type']),
      })),
    );
  }
  removeMember(index: number) {
    this.selectedMembers.splice(index, 1);
    this.setSelectedUsers(this.selectedMembers);
  }
  // 选择部门
  onSelectDepartment(obj: any) {
    this.$set(this.nodeCopy.options!, 'department_id', obj.value);
    this.$set(this.nodeCopy.options!, 'department_name', obj.label);
  }
  onModeChange(mode: string) {
    this.$set(this.nodeCopy.options!, 'mode', mode);
  }
  // =============== form ===============
  onTopRadioChanged(e: any) {
    const newFields = this.nodeCopy.fields!.fields.map((o: any) => ({
      ...o,
      accessibility: e.target.value,
    }));
    this.$set(this.nodeCopy.fields!, 'fields', newFields);
  }
  // =============== place form =============
  onPlaceFieldsChange(fields: any[]) {
    this.$set(this.nodeCopy, 'place_form', { fields });
  }
  // =============== token callback ===========
  tokenCallbackChange(cb: string) {
    this.$set(this.nodeCopy, 'callback_options', {
      callback_method: cb,
      callback_type: 'token_callback',
    });
  }
  // ============== token map key ============
  changeFiledMapKey(field: IFormTemplateItem, key: string) {
    this.$set(field, 'map_key', key || null);
  }
  // ============== action config ==============
  onActionFormChange() {
    const defaultConfig = getDefaultPlaceActionConfig();
    if (isEqual(defaultConfig, this.placeActionConfig)) {
      this.$set(this.nodeCopy, 'callback_options', {
        ...this.nodeCopy.callback_options,
        action_alias: null,
        action_permits: null,
      });
    } else {
      this.$set(this.nodeCopy, 'callback_options', {
        ...this.nodeCopy.callback_options,
        action_alias: this.placeActionConfig,
        action_permits: Object.entries(this.placeActionConfig).reduce(
          (obj, [key, conf]) => ({
            ...obj,
            [key]: conf.show,
          }),
          Object.create(null),
        ),
      });
    }
  }
  resetPlaceActionConfig() {
    this.$set(this.nodeCopy, 'callback_options', {
      ...this.nodeCopy.callback_options,
      action_alias: null,
      action_permits: null,
    });
    this.placeActionConfig = getDefaultPlaceActionConfig();
  }
}
</script>

<template lang="pug">
.approval-component
  a-tabs(defaultActiveKey="1")
    a-tab-pane(tab="设置审批人" key="1")
      .assign
        //- =============== 类型选择 ===============
        a-radio-group.radio-group(
          :options="radioConfig"
          v-model="nodeCopy.transition_type"
          @change="onApprovalTypeChange")
        //- =============== 不同类型的操作 ===============
        .operations
          //- 学院主管
          .operation(v-show="nodeCopy.transition_type === TransitionTypes.ApprovalLevelManager")
            label 发起人的&nbsp;&nbsp;&nbsp;&nbsp;
            a-select(
              style="width: 160px"
              v-model="nodeCopy.options.level")
              a-select-option(
                v-for="level in levels"
                :key="level"
                :value="level")
                | 第 {{ level }} 级学院
            span &nbsp;&nbsp;&nbsp;&nbsp;
            a-switch(
              checkedChildren="主管"
              unCheckedChildren="员工"
              v-model="nodeCopy.options.manager")
          //- 部门主管
          .operation(v-show="nodeCopy.transition_type === TransitionTypes.ApprovalSpecifyManager")
            a-tree-select(
              style="width: 300px"
              :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
              :treeData="departments"
              placeholder='请选择部门'
              :labelInValue="true"
              treeDefaultExpandAll
              allowClear
              showSearch
              treeNodeFilterProp="label"
              :value="selectedDepartment"
              @change="onSelectDepartment")
          //- 指定组织结构中的成员
          .operation(v-show="nodeCopy.transition_type.startsWith('Transitions::Approval::User')")
            a-button(icon="plus" type="primary" @click="memberSelectorVisible = true")
              | {{ selectedMembers.length > 0 ? '修改成员' : '添加成员' }}
            .tags(v-if="selectedMembers.length > 0")
              a-tag(
                color="blue"
                v-for="(member, index) in selectedMembers"
                :key="member.id"
                :closable="true"
                @close="removeMember(index)")
                | {{ member.name }}
              span.clear-members.text-primary(@click="setSelectedUsers([])")
                | 清空
          //- 审批人自选
          .operation(v-show="nodeCopy.transition_type === TransitionTypes.ApprovalSelect")
            a-select.select-mode(:value="nodeCopy.options.mode" @change="onModeChange")
              a-select-option(value="and") 会签
              a-select-option(value="or") 或签
            p.tips(v-if="nodeCopy.options.mode === 'and'")
              a-icon.text-primary(type="info-circle" theme="filled")
              span  会签（由本节点审批人设置后续审批人员，设置后，须所有审批人同意）
            p.tips(v-if="nodeCopy.options.mode === 'or'")
              a-icon.text-primary(type="info-circle" theme="filled")
              span  或签（由本节点审批人设置后续审批人员，设置后，任一名审批人同意或拒绝即可）
          //- 角色分配
          .operation(
            v-if="workflowRoles.length"
            v-show="nodeCopy.transition_type === TransitionTypes.ApprovalFlowableRole")
            a-radio-group.normal-radio-group(
              style="width: 160px"
              v-model="nodeCopy.options.role")
              a-radio.radio-item(
                v-for="role in workflowRoles"
                :key="role"
                :value="role")
                | {{ role }}

    a-tab-pane(tab="表单操作权限" key="2" v-if="formFields.length")
      .fields
        a-row.head
          a-radio-group.radio-group(
            defaultValue="readonly"
            :value="headerAccessibility"
            @change="onTopRadioChanged")
            a-col(:span="9")
              .cell 表单字段
            a-col(:span="5")
              .cell
                a-radio(value="read_and_write")
                  | 可编辑
            a-col(:span="5")
              .cell
                a-radio(value="readonly")
                  | 只读
            a-col(:span="5")
              .cell
                a-radio(value="hidden")
                  | 隐藏
        .body
          a-row(v-for="field in formFields" :key="field.key")
            a-radio-group.radio-group(
              defaultValue="readonly"
              v-model="field.accessibility")
              a-col(:span="9")
                .cell {{ field.name }}
              a-col(:span="5")
                .cell
                  a-radio(value="read_and_write")
              a-col(:span="5")
                .cell
                  a-radio(value="readonly")
              a-col(:span="5")
                .cell
                  a-radio(value="hidden")

    a-tab-pane(tab="高级设置" key="3")
      //- 节点表单
      .place-form
        h3.title 定义节点审批表单
        a-row(:gutter="20")
          a-col(:span="6" v-for="field in placeFields" :key="field.key")
            TaTag.field(type="primary")
              | {{ field.name }}
        a-button(type="primary" @click="formDesignerVisible = true" icon="edit")
          | 定义表单
      //- 功能回调
      .place-form(v-if="tokenCallbacks.length")
        h3.title 设置功能回调（非必填）
        a-select.form-item(:value="callbackMethod" placeholder="可配置节点回调" size="large" @change="tokenCallbackChange")
          a-select-option(value="")
            |  不设置回调
          a-select-option(v-for="cb in tokenCallbacks" :key="cb" :value="cb")
            | {{ cb }}
      //- 数据映射
      .place-form(v-if="workflowAttributes.length")
        h3.title 配置数据字段映射关系（非必填）
        Empty(desc="请选配置节点表单" v-if="allFields.length === 0")
        a-row.row(v-for="field in allFields" :key="field.key" :gutter="16")
          a-col(:span="8")
            a-input.form-item(disabled :value="field.name")
          a-col.text-center(:span="2")
            a-icon.icon(type="arrow-right")
          a-col(:span="8")
            a-select.form-item(
              placeholder="请选择映射字段"
              :value="field.map_key || undefined"
              @change="changeFiledMapKey(field, $event)")
              a-select-option(value="")
                | 无
              a-select-option(v-for="attr in workflowAttributes" :key="attr.attr" :value="attr.attr")
                | {{ attr.name }}
      //- 操作文案
      .place-form
        h3.title
          span 节点操作配置
          PopoverConfirm(
            title="重置"
            content="确定要重置此配置吗？"
            @confirm="resetPlaceActionConfig")
            TextButton(icon="redo") 重置
        a-row.row
          a-col(:span="3")
            strong 是否显示
          a-col(:span="5")
            strong 操作名称
          a-col(:span="16")
            strong 操作说明
        form(@change="onActionFormChange")
          a-row.row(v-for="(config, key) in placeActionConfig" :key="key" :gutter="16")
            a-col(:span="3")
              a-checkbox(v-model="config.show")
            a-col(:span="5")
              a-input(v-model.lazy="config.name" placeholder="自定义名称")
            a-col(:span="16")
              a-input(v-model="config.desc" placeholder="说明")

  UserSelectorDialog(
    title="人员选择器"
    v-model="memberSelectorVisible"
    :userIds="selectedMemberIds"
    :multiple="true"
    @selectUsers="setSelectedUsers")

  FormDesignerDialog(
    v-model="formDesignerVisible"
    :fields="placeFields"
    @change="onPlaceFieldsChange")
</template>

<style lang="stylus" scoped>
.assign
  padding 20px
  .radio-group
    line-height 32px
  .operations
    margin-top 16px
    padding 20px 0px 20px
    border-top 1px solid #e8e8e8
    .operation
      .normal-radio-group
        .radio-item
          display inline-block
          margin 6px
      .types
        margin-bottom 15px
      .tags
        margin-top 14px
        .clear-members
          cursor pointer
      .select-mode
        width 200px
        margin-bottom 10px

.fields
  width 100%
  .radio-group
    width 100%
  .body .cell
    padding 16px
    border-bottom 1px solid #e8e8e8
    word-break break-word
    transition all 0.3s
  .head .cell
    padding 16px
    border-bottom 1px solid #e8e8e8
    background #fafafa
    color rgba(0, 0, 0, 0.85)
    text-align left
    word-break break-word
    font-weight 500
    transition background 0.3s ease

.place-form
  padding 0 20px 20px
  border-bottom 1px solid #e8e8e8
  .title
    color #333
    margin-bottom 16px
    margin-top 20px
    display flex
    align-items center
  .field
    margin-bottom 20px
    width 100%
    text-align center
  .form-item
    min-width 200px
  .row
    margin-bottom 10px
    .icon
      font-size 18px
      margin-top 5px
</style>
