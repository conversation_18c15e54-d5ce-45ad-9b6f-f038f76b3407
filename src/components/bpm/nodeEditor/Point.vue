<script lang="ts">
/**
 * 工作流 - 指标节点编辑器
 * 计数指标：nodeCopy.options: { name: '', level: '' }
 * 属性指标：nodeCopy.options: { name: '', level: ''， key: '', key_name: '' }
 */
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';

@Component
export default class PointNodeEditor extends Vue {
  currentKey: string = '';
  currentTab: string = 'count';

  @Prop({
    type: Object,
    default: () => ({
      options: {
        key: '',
      },
    }),
  })
  readonly nodeCopy!: IObject;

  get formFields() {
    return this.nodeCopy.fields.fields;
  }
  get levelsMap() {
    return this.$store.state.levelsMap;
  }

  @Watch('nodeCopy', { deep: true, immediate: true })
  onNodeCopyChange() {
    this.initData();
  }

  initData() {
    if (this.nodeCopy.transition_type === 'Transitions::Point::Count') {
      this.currentTab = 'count';
    } else {
      this.currentTab = 'value';
    }
    this.onTabChange(this.currentTab);
  }
  onTabChange(tab: string) {
    if (tab === 'count') {
      this.nodeCopy.transition_type = 'Transitions::Point::Count';
      delete this.nodeCopy.options.key;
      delete this.nodeCopy.options.key_name;
    } else {
      this.nodeCopy.transition_type = 'Transitions::Point::Value';
      if (this.nodeCopy.options.key) {
        this.currentKey = this.nodeCopy.options.key;
      } else {
        const { key, name } = this.formFields[0];
        this.currentKey = key;
        this.nodeCopy.options.key = key;
        this.nodeCopy.options.key_name = name;
      }
    }
  }
  onFieldChange(e: any) {
    const field = this.formFields.find((o: any) => o.key === e.target.value);
    this.nodeCopy.options.key = field.key;
    this.nodeCopy.options.key_name = field.name;
  }
}
</script>

<template lang="pug">
.point-component
  a-tabs(defaultActiveKey="count" v-model="currentTab" @change="onTabChange")
    a-tab-pane(tab="按计数生成指标" key="count")
      .content
        a-row.row(:gutter="16")
          a-col(:span="2")
            .label 指标
          a-col(:span="22")
            a-input(v-model="nodeCopy.options.name" placeholder="请输入指标")

        a-row.row(:gutter="16")
          a-col(:span="2")
            .label 层级
          a-col(:span="22")
            a-select.select(
              v-model="nodeCopy.options.level"
              :options="Object.values(levelsMap)"
              placeholder="请选择层级")
    a-tab-pane(tab="按属性生成指标" key="value")
      .content
        a-row.row(:gutter="16")
          a-col(:span="2")
            .label 指标
          a-col(:span="22")
            a-input(v-model="nodeCopy.options.name" placeholder="请输入指标")

        a-row.row(:gutter="16")
          a-col(:span="2")
            .label 层级
          a-col(:span="22")
            a-select.select(
              v-model="nodeCopy.options.level"
              :options="Object.values(levelsMap)"
              placeholder="请选择层级")

        a-row.row(:gutter="16")
          a-col(:span="2")
            .label 属性
          a-col(:span="22")
            a-radio-group.radio-group(
              v-model="currentKey"
              @change="onFieldChange")
              a-radio(
                v-for="field in formFields"
                :value="field.key"
                :key="field.key")
                | {{ field.name }}
</template>

<style lang="stylus" scoped>
.content
  padding 20px
  .row
    margin-bottom 10px
  .label
    height 32px
    font-weight bold
    line-height 32px
  .select
    width 100%
  .radio-group
    height 32px
    line-height 32px
</style>
