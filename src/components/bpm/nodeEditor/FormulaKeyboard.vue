<script lang="ts">
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import { IWorkflow } from '@/models/bpm/workflow';
import { flatten } from 'lodash';
import { IFormTemplateItem } from '@/interfaces/formTemplate.interface';

@Component({
  components: {},
})
export default class FormulaKeyboard extends Vue {
  @Model('input', { type: Array, default: () => [] }) value!: string[];
  @Prop({ type: Object, default: () => ({}) }) workflow!: IWorkflow;

  formulaAry: any[] = [];
  symbols: string[] = ['+', '-', '*', '/', '(', ')'];
  numbers: string[][] = [
    ['1', '2', '3'],
    ['4', '5', '6'],
    ['7', '8', '9'],
    ['0', '.'],
  ];

  flattenNumbers = flatten(this.numbers);

  @Watch('value', { deep: true, immediate: true })
  onValueChange() {
    this.formulaAry = this.value;

    this.changeNodeOptions();
  }

  get variables() {
    const extraAttrs = ((this.workflow.meta as any).workflow_attributes || []).map((o: any) => ({
      key: o.attr,
      name: o.name,
    }));
    const storageAttrs = (this.workflow.storage as IObject).fields.map((field: IFormTemplateItem) => ({
      key: field.map_key,
      name: field.name,
    }));
    return extraAttrs.concat(storageAttrs);
  }

  get formulaView() {
    return this.formatFormula(this.formulaAry.map(item => (typeof item === 'string' ? item : item.name)));
  }

  get formulaValue() {
    return this.formatFormula(this.formulaAry.map(item => (typeof item === 'string' ? item : item.key)));
  }

  formatFormula(ary: any[]) {
    const result: any[] = [];
    let lastFlag: boolean = false;
    ary.forEach(i => {
      const flag = this.flattenNumbers.indexOf(i) >= 0;
      if (flag) {
        if (lastFlag !== flag) {
          result.push([]);
        }
        result[result.length - 1].push(i);
      } else {
        result.push([i]);
      }
      lastFlag = flag;
    });
    return result.map(itemAry => itemAry.join('')).join(' ');
  }

  changeNodeOptions() {
    this.$emit('change', this.formulaAry, this.formulaValue, this.formulaView);
  }

  clear() {
    this.formulaAry = [];
    this.changeNodeOptions();
  }

  add(val: IObject | string) {
    this.formulaAry.push(val);
    this.changeNodeOptions();
  }

  back() {
    this.formulaAry.pop();
    this.changeNodeOptions();
  }
}
</script>

<template lang="pug">
.formula-keybord
  .variables
    .title 计算对象：
    .buttom-group
      .buttom(v-for="item in variables" @click="add(item)")
        | {{ item.name }}
  .symbols
    .title 计算符号：
    .buttom-group
      .buttom(v-for="symbol in symbols" @click="add(symbol)")
        | {{ symbol }}
  .numbers
    .title 数字键盘：
    .content
      .buttom-group(v-for="numberGroup in numbers")
        .buttom(v-for="number in numberGroup" @click="add(number)")
          | {{ number }}
  .operations
    .title 功能键：
    .buttom-group
      .buttom(@click="back") 后退
      .buttom(@click="clear") 清空


</template>

<style lang="stylus" scoped>
.formula-keybord
  .variables, .symbols, .numbers, .operations
    display flex
    .title
      width 80px
    .buttom-group
      display flex
      .buttom
        display flex
        justify-content center
        align-items center
        margin 0 10px 10px 0
        padding 3px 10px
        min-width 30px
        border 1px solid #999
        border-radius 3px
        cursor pointer
</style>
