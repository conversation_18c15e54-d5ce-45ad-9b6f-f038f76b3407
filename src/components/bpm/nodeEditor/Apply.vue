<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import UserSelectorDialog from '@/components/hr/UserSelectorDialog.vue';

@Component({
  components: {
    UserSelectorDialog,
  },
})
export default class ApplyNodeEditor extends Vue {
  memberSelectorVisible: boolean = false;
  selectedMembers: any[] = [];
  resType: string = 'Teacher';
  radioConfig: any[] = [
    { label: '教师', value: 'Teacher', option: { res_type: 'Teacher' } },
    { label: '学生', value: 'Student', option: { res_type: 'Student' } },
  ];

  @Prop({
    type: Object,
    default: () => ({
      options: {},
      fileds: {
        fields: [],
      },
    }),
  })
  readonly nodeCopy!: IObject;

  get formFields() {
    return this.nodeCopy.fields.fields || [];
  }
  get headerAccessibility() {
    if (this.formFields instanceof Array && this.formFields.length > 0) {
      const firstAccessibility = this.formFields[0].accessibility;
      if (this.formFields.every((item: any) => item.accessibility === firstAccessibility)) {
        return firstAccessibility;
      }
      return null;
    }
    return null;
  }
  get selectedMemberIds() {
    return this.selectedMembers.map(o => o.id);
  }

  mounted() {
    this.onOptionsChange();
  }

  @Watch('nodeCopy', { deep: true, immediate: true })
  onNodeCopyChange() {
    this.onOptionsChange();
  }
  onApplyTypeChange(e: any) {
    const { option } = this.radioConfig.find(o => o.value === e.target.value);
    this.nodeCopy.options = { ...option };
  }
  onOptionsChange() {
    if (!this.nodeCopy.options) {
      return;
    }
    this.resType = this.nodeCopy.options.res_type || 'Teacher';
    this.selectedMembers = (this.nodeCopy.options.teachers || []).map((o: any) => ({
      id: o.teacher_id,
      name: o.teacher_name,
    }));
  }
  // 设置发起人 teachers
  setSelectedUsers(members: any[]) {
    this.selectedMembers = members;
    this.nodeCopy.options = this.nodeCopy.options || {};
    this.nodeCopy.options.teachers = members.map(o => ({
      teacher_id: o.id,
      teacher_name: o.name,
    }));
  }
  removeMember(index: number) {
    this.selectedMembers.splice(index, 1);
    this.setSelectedUsers(this.selectedMembers);
  }
  // =============== form ===============
  onTopRadioChanged(e: any) {
    this.nodeCopy.fields.fields = this.nodeCopy.fields.fields.map((o: any) => ({
      ...o,
      accessibility: e.target.value,
    }));
  }
}
</script>

<template lang="pug">
.apply-component
  a-tabs(defaultActiveKey="1")
    a-tab-pane(tab="表单操作权限" key="2" v-if="formFields.length")
      .fields
        a-row.head
          a-radio-group.radio-group(
            defaultValue="read_and_write"
            :value="headerAccessibility"
            @change="onTopRadioChanged")
            a-col(:span="9")
              .cell 表单字段
            a-col(:span="5")
              .cell
                a-radio(value="read_and_write")
                  | 可编辑
            a-col(:span="5")
              .cell
                a-radio(value="readonly")
                  | 只读
            a-col(:span="5")
              .cell
                a-radio(value="hidden")
                  | 隐藏
        .body
          a-row(v-for="field in formFields" :key="field.key")
            a-radio-group.radio-group(
              defaultValue="read_and_write"
              v-model="field.accessibility")
              a-col(:span="9")
                .cell {{ field.name }}
              a-col(:span="5")
                .cell
                  a-radio(value="read_and_write")
              a-col(:span="5")
                .cell
                  a-radio(value="readonly")
              a-col(:span="5")
                .cell
                  a-radio(value="hidden")
  UserSelectorDialog(
    title="选择人员"
    v-model="memberSelectorVisible"
    :userIds="selectedMemberIds"
    :multiple="true"
    @selectUsers="setSelectedUsers")
</template>

<style lang="stylus" scoped>
.assign
  padding 20px
  .operation
    .tags
      margin-top 14px
      .clear-members
        cursor pointer

.fields
  width 100%
  .radio-group
    width 100%
  .body .cell
    padding 16px
    border-bottom 1px solid #e8e8e8
    word-break break-word
    transition all 0.3s
  .head .cell
    padding 16px
    border-bottom 1px solid #e8e8e8
    background #fafafa
    color rgba(0, 0, 0, 0.85)
    text-align left
    word-break break-word
    font-weight 500
    transition background 0.3s ease
</style>
