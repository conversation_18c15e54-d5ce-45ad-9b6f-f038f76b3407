<script lang="ts">
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import { IWorkflow, TransitionTypes } from '@/models/bpm/workflow';
import Apply from './Apply.vue';
import Approval from './Approval.vue';
import Condition from './Condition.vue';
import Notify from './Notify.vue';
import Point from './Point.vue';
import Publish from './Publish.vue';
import FlowableCallback from './FlowableCallback.vue';
import ApiCallback from './ApiCallback.vue';
import FunctionCallback from './FunctionCallback.vue';
import FormulaDesigner from './FormulaDesigner.vue';

@Component({
  components: {
    Apply,
    Approval,
    Condition,
    Notify,
    Point,
    Publish,
    FlowableCallback,
    ApiCallback,
    FunctionCallback,
    FormulaDesigner,
  },
})
export default class NodeEditor extends Vue {
  subComponentSubmitCallback!: () => void;

  @Model('input', { type: Boolean, default: false }) visible!: boolean;
  @Prop({
    type: Object,
    default: () => ({
      options: {},
      fields: {},
      place_form: {
        fields: [],
      },
    }),
    required: true,
  })
  nodeCopy!: IObject;

  @Prop({
    type: Object,
    default: () => ({
      meta: {
        workflow_roles: [],
      },
    }),
  })
  workflow!: IWorkflow;

  get formatedWorkflow() {
    return {
      ...this.workflow,
      meta: this.workflow.meta || {},
    };
  }
  get transitionType() {
    return this.nodeCopy.transition_type || '';
  }
  get title() {
    if (this.nodeCopy.kind === 'condition') {
      return `条件 ${(this.nodeCopy.condition.index || 0) + 1}`;
    }
    if (this.nodeCopy.type === 'Places::StartPlace') {
      return '发起人';
    }
    if (this.nodeCopy.type === 'Places::EndPlace') {
      return '结束节点';
    }
    return this.nodeCopy.name;
  }
  get nodeDisabled() {
    if (this.transitionType.includes('Teacher')) {
      return !(this.nodeCopy.options && this.nodeCopy.options.teachers.length > 0);
    }
    return false;
  }
  get TransitionTypes() {
    return TransitionTypes;
  }

  onClose() {
    this.$emit('input', false);
    this.$emit('close');
  }
  onConfirmChange() {
    if (this.subComponentSubmitCallback) {
      this.subComponentSubmitCallback();
    }

    this.$emit('change', this.nodeCopy);

    this.onClose();
  }
  // 子组件提交事件，可以再点击外部确认按钮时，最子组件的逻辑做处理
  onBeforeSubmit(subComponentSubmitCallback: () => void) {
    this.subComponentSubmitCallback = subComponentSubmitCallback;
  }
}
</script>

<template lang="pug">
a-drawer.node-drawer(
  :visible="visible"
  placement="right"
  :closable="true"
  :width="800"
  :zIndex="10"
  :destroyOnClose="true"
  :mask="true"
  @close="onClose"
)
  .wrapper
    .header
      span {{ title }}
      .close-btn(@click="onClose")
        a-icon(type="close")
    .content
      Approval(
        v-if="transitionType.includes('Approval')"
        :nodeCopy.sync="nodeCopy"
        :workflow="formatedWorkflow"
        @beforeSubmit="onBeforeSubmit")
      Apply(
        v-if="(nodeCopy.type || '').includes('StartPlace')"
        :nodeCopy.sync="nodeCopy"
        @beforeSubmit="onBeforeSubmit")
      Point(
        v-else-if="transitionType.includes('Point')"
        :nodeCopy.sync="nodeCopy"
        @beforeSubmit="onBeforeSubmit")
      Notify(
        v-else-if="transitionType.includes('Notify')"
        :nodeCopy.sync="nodeCopy"
        :workflow="formatedWorkflow"
        @beforeSubmit="onBeforeSubmit")
      Condition(
        v-else-if="nodeCopy.kind === 'condition'"
        :nodeCopy.sync="nodeCopy"
        :workflow="formatedWorkflow"
        @beforeSubmit="onBeforeSubmit")
      Publish(
        v-else-if="transitionType.includes('Wechat')"
        :nodeCopy.sync="nodeCopy"
        @beforeSubmit="onBeforeSubmit")
      FlowableCallback(
        v-else-if="transitionType === TransitionTypes.FlowableCallback"
        :nodeCopy.sync="nodeCopy"
        :workflow="formatedWorkflow"
        @beforeSubmit="onBeforeSubmit")
      ApiCallback(
        v-else-if="transitionType === TransitionTypes.ApiCallback"
        :nodeCopy.sync="nodeCopy"
        :workflow="formatedWorkflow"
        @beforeSubmit="onBeforeSubmit")
      FunctionCallback(
        v-else-if="transitionType === TransitionTypes.FunctionCallback"
        :nodeCopy.sync="nodeCopy"
        :workflow="formatedWorkflow"
        @beforeSubmit="onBeforeSubmit")
      FormulaDesigner(
        v-else-if="transitionType === TransitionTypes.Formula"
        :nodeCopy.sync="nodeCopy"
        :workflow="formatedWorkflow"
        @beforeSubmit="onBeforeSubmit")
    .footer
      a-button(type="primary" @click="onConfirmChange" :disabled="nodeDisabled")
        | 确 认
</template>

<style lang="stylus" scoped>
.wrapper
  position relative
  z-index 999
  margin-right -20px
  margin-left -20px
  padding-top 128px
  padding-bottom 36px
  height 100%
  .header
    position absolute
    top 78px
    left 0
    width 100%
    height 49px
    border-bottom 1px solid #e5e5e5
    color #383838
    text-align center
    font-weight 500
    font-size 14px
    line-height 49px
    .close-btn
      position absolute
      top 0
      right 0
      display inline-block
      padding 17px
      font-size 16px
      line-height 1
      cursor pointer
      &:hover
        color #000
  .content
    position relative
    z-index 1
    overflow auto
    height 100%
  .footer
    position absolute
    bottom -20px
    left 0
    display flex
    justify-content flex-end
    align-items center
    padding 0 16px
    width 100%
    height 56px
    border-top 1px solid #e5e5e5
    button
      margin-left 12px
</style>
