<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { TransitionTypes } from '@/models/bpm/workflow';
import { IApiKeyAttr, IWorkflow } from '@/models/bpm/workflow';
import { debounce } from 'lodash';
import FieldsSelector from './FieldsSelector.vue';
import { IFormTemplateItem } from '../../../interfaces/formTemplate.interface';

@Component({
  components: {
    FieldsSelector,
  },
})
export default class FunctionCallbackNodeEditor extends Vue {
  @Prop({ type: Object, default: () => ({ options: {} }) }) readonly nodeCopy!: IObject;
  @Prop({
    type: Object,
    default: () => ({
      meta: {
        workflow_attributes: [],
      },
    }),
  })
  workflow!: IWorkflow;

  keyAttrs: IApiKeyAttr[] = [];
  visible: boolean = false;
  debounceAttrInput: () => void = () => {};

  @Watch('nodeCopy', { deep: true, immediate: true })
  onNodeChange() {
    this.keyAttrs = this.nodeCopy.options.key_attrs || [];
  }

  get options() {
    return (
      this.nodeCopy.options || {
        headers: {},
        key_attrs: [],
      }
    );
  }
  get fields() {
    const fields = this.workflow.form ? this.workflow.form.fields || [] : [];
    const extraAttrs = ((this.workflow.meta as any).workflow_attributes || []).map((o: any) => ({
      key: o.attr,
      name: o.name,
      layout: {},
      model: {
        attr_type: o.attr_type || 'number',
      },
    }));
    return fields.concat(extraAttrs);
  }

  beforeDestroy() {
    this.debounceAttrInput();
  }

  created() {
    this.debounceAttrInput = debounce(this.onAttrChange, 1000);
  }

  onAttrChange() {
    this.$set(this.nodeCopy.options, 'key_attrs', this.keyAttrs);
  }
  onSelectFields(fields: IFormTemplateItem[]) {
    this.keyAttrs = fields.map((o: any) => ({
      key: o.key,
      key_name: o.name,
      name: o.name,
    }));
    this.onAttrChange();
  }
}
</script>

<template lang="pug">
.node-callback
  .partical
    h3.title 配置服务方法
    a-input.input(v-model="nodeCopy.options.class_name" placeholder="请输入回调的类名" addonBefore="回调的类名")
    a-input.input(v-model="nodeCopy.options.function" placeholder="请输入回调的方法名" addonBefore="回调方法名")

  .partical
    h3.title 配置字段映射
    a-row.line(v-for="(keyAttr, index) in keyAttrs" :key="index" :gutter="16")
      a-col(:span="8")
        a-input(disabled :value="keyAttr.key_name")
      a-col.text-center(:span="2")
        a-icon.icon(type="arrow-right")
      a-col(:span="8")
        a-input.header-input(
          placeholder="请输入映射字段名称"
          v-model="keyAttr.name"
          @input="debounceAttrInput")
    a-button(type="primary" icon="setting" size="small" @click="visible = true")
      | 配置字段

  FieldsSelector(
    v-model="visible"
    :fields="fields"
    :selectedFields="options.key_attrs"
    @change="onSelectFields")
</template>

<style lang="stylus" scoped>
.node-callback
  padding 20px
  .input
    margin-bottom 10px
  .partical
    border-bottom 1px solid #e8e8e8
    padding-bottom 20px
    margin-bottom 20px
    &:last-child
      border-bottom none
    .line
      margin-bottom 10px
      .icon
        font-size 18px
        margin-top 5px
    .title
      margin-bottom 10px
</style>
