<script lang="ts">
/**
 * 工作流 - 条件节点编辑器
 */
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { IWorkflow } from '../../../models/bpm/workflow';

const BETWEEN = '~';

@Component
export default class ConditionNodeEditor extends Vue {
  // 发起人角色条件
  levels: number[] = [1, 2, 3, 4, 5];
  localFields: any[] = [
    {
      key: 'role',
      name: '发起人岗位',
      layout: {
        component: 'input',
      },
      model: {
        attr_type: 'number',
      },
    },
  ];
  // 模板
  visible: boolean = false;
  BETWEEN: '~' = BETWEEN;
  selectedRuleKeys: any[] = [];
  supportedComponents: string[] = ['input', 'textarea', 'checkbox', 'radio', 'select'];
  isFieldsChanged: boolean = false;
  defaultValue: IObject = {
    '<': 2,
    '<=': 1,
    '>': 1,
    '>=': 1,
    '==': 1,
  };
  // number 的规则
  operators: any[] = [
    { label: '小于', value: '<' },
    { label: '小于等于', value: '<=' },
    { label: '大于', value: '>' },
    { label: '大于等于', value: '>=' },
    { label: '等于', value: '==' },
    { label: '介于(两者之间)', value: BETWEEN },
  ];
  // string 的规则
  singleStringOperators: any[] = [
    { label: '等于', value: 'eql?' },
    { label: '包含', value: 'include?' },
  ];
  // Array<string> 的规则
  stringOperators: any[] = [
    { label: '等于', value: 'eql?' },
    { label: '包含', value: 'include?' },
    { label: '属于', value: 'in?' },
  ];
  fieldOptionsMap: IObject = {}; // 用来缓存多选条件的选项

  @Prop({
    type: Object,
    default: () => ({
      condition: {
        rules: [],
      },
    }),
  })
  readonly nodeCopy!: IObject;

  @Prop({
    type: Object,
    default: () => ({
      meta: {
        workflow_attributes: [],
      },
    }),
  })
  workflow!: IWorkflow;

  get fields() {
    const extraAttrs = ((this.workflow.meta as any).workflow_attributes || []).map((o: any) => ({
      key: o.attr,
      name: o.name,
      layout: {},
      model: {
        attr_type: o.attr_type || 'number',
      },
    }));
    const formFields = this.workflow.form ? this.workflow.form.fields || [] : [];
    const storageFields = this.workflow.storage ? this.workflow.storage.fields || [] : [];
    return this.localFields
      .concat(formFields)
      .concat(storageFields)
      .filter(o => this.supportedComponents.includes(o.layout.component))
      .concat(extraAttrs);
  }
  get fieldsMap() {
    return this.fields.reduce(
      (obj, item) => ({
        ...obj,
        [item.map_key || item.key]: item,
      }),
      {},
    );
  }
  get rulesRef() {
    // 为 nodeCopy.condition.rules 的引用，不可修改
    return this.nodeCopy.condition.rules;
  }

  @Watch('nodeCopy', { deep: true, immediate: true })
  onNodeCopyChange() {
    this.initData();
  }
  initData() {
    // 已选的字段
    this.selectedRuleKeys = this.rulesRef.map((o: any) => o.key);
    // 保存有可选选项的字段的 选项，便于显示
    this.fields.forEach(field => {
      if (field && field.layout.options) {
        this.fieldOptionsMap[field.map_key || field.key] = field.layout.options.map((o: any) => o.label);
      }
    });
    // 如果表单修改了，需要检查当前条件值是否存在，并更新
    this.nodeCopy.condition.rules.forEach((rule: any, index: number) => {
      if (!this.fieldsMap[rule.key]) {
        this.nodeCopy.condition.rules.splice(index, 1);
        this.isFieldsChanged = true;
      }
    });
  }
  // 改变操作符的时候，重置 rule 的默认值
  onChangeRuleOperator(operator: string, rule: any, index: number) {
    const currentRule = this.rulesRef[index];
    if (operator === BETWEEN) {
      // 数值型条件的默认值
      currentRule.opts = [
        { opt: '>=', val: 1 },
        { opt: '<', val: 2 },
      ];
    } else {
      currentRule.opts = [{ opt: operator, val: this.defaultValue[operator] }];
    }
  }
  onChangeStringRuleOperator(operator: string, rule: any, index: number) {
    const currentRule = this.rulesRef[index];
    if (operator === 'in?') {
      currentRule.exps = [{ opt: 'in?', val: JSON.stringify([]) }];
    } else {
      const field = this.fieldsMap[rule.key];
      const firstOption = field ? field.layout.options[0] || { label: '' } : { label: '' };
      currentRule.exps = [{ opt: 'eql?', val: JSON.stringify(firstOption.label) }];
    }
  }
  // 设置发起人角色的操作符
  onChangeRoleOperator(operator: string, rule: any, index: number) {
    const currentRule = this.rulesRef[index];
    if (operator === BETWEEN) {
      // 数值型条件的默认值
      currentRule.roles = [
        { opt: '>=', level: 1, manager: true },
        { opt: '<=', level: 2, manager: true },
      ];
    } else {
      currentRule.roles = [{ opt: operator, level: this.defaultValue[operator], manager: true }];
    }
  }
  // 确认选择作为条件的表单字段
  onConfirmRuleKeys() {
    const existRulesMap = this.$utils.objectify(this.rulesRef, 'key');
    const newRules = this.selectedRuleKeys.map(key => this.getRuleByField(existRulesMap, key));
    this.rulesRef.splice(0, this.rulesRef.length); // 清空 condition.rules
    this.rulesRef.push(...newRules); // 设置新的 rules
    this.visible = false;
    this.$nextTick(this.$forceUpdate);
  }
  /**
   * 此处规则，需要参考 ../form-designer/templates.json 配置项
   */
  getRuleByField(existRulesMap: any, key: string) {
    const field = this.fieldsMap[key];
    const rule: any = { key, key_name: field.name };
    if (existRulesMap[key]) {
      // 已存在的规则
      return existRulesMap[key];
    }
    if (field.key === 'role') {
      // 设置角色
      rule.roles = [
        { opt: '>=', level: 1, manager: true },
        { opt: '<=', level: 2, manager: true },
      ];
    } else if (field.model.attr_type === 'number') {
      // 数值类型规则
      rule.opts = [{ opt: '<', val: 2 }];
    } else if (['radio', 'select'].includes(field.layout.component)) {
      // 单选规则
      const firstOption = field.layout.options[0] || { label: '' };
      rule.exps = [{ opt: 'eql?', val: JSON.stringify(firstOption.label) }];
    } else {
      // 字符串规则
      rule.exps = [{ opt: 'include?', val: JSON.stringify('') }];
    }
    return rule;
  }
  // 由于多选条件，后端只接受序列号后的数组，所以要额外处理
  onCheckboxChange(rule: any, value: any) {
    this.$set(rule.exps[0], 'val', JSON.stringify(value));
    this.$nextTick(this.$forceUpdate);
  }
  onSelectChange(rule: any, value: any) {
    this.$set(rule.exps[0], 'val', JSON.stringify(value));
    this.$nextTick(this.$forceUpdate);
  }
  openWindow() {
    this.selectedRuleKeys = this.rulesRef.map((o: any) => o.key);
    this.visible = true;
  }
  removeRule(rule: any, index: number) {
    this.rulesRef.splice(index, 1);
    this.selectedRuleKeys = this.rulesRef.map((o: any) => o.key);
  }
}
</script>

<template lang="pug">
.condition-define
  a-alert(message="当审批同时满足以下条件时进入此流程" type="info" closeText="我知道了")
  a-alert(v-if="isFieldsChanged" message="表单已被修改，已自动更新当前条件" type="warning" closeText="我知道了")
  .rules
    strong 是否为默认条件：
    a-switch(v-model="nodeCopy.condition.is_default")

  .rules(v-if="!nodeCopy.condition.is_default")
    .rule(
      v-for="(rule, index) in rulesRef"
      :key="rule.key")
      //- =============== 字符型规则 ===============
      a-row(v-if="rule.exps && rule.exps.length > 0 && fieldsMap[rule.key].layout.options")
        a-col(:span="4")
          strong {{ rule.key_name }}
        a-col(:span="18")
          //- 1、多选的情况
          .range-rule-set(v-if="rule.exps[0].opt === 'in?'")
            .cell.flex-cell
              a-select.form-item(
                :options="stringOperators"
                :value="rule.exps[0].opt"
                @change="(operator) => { onChangeStringRuleOperator(operator, rule, index) }")
            .cell.flex-cell
              a-checkbox-group(
                :value="JSON.parse(rule.exps[0].val)"
                @change="(value) => { onCheckboxChange(rule, value); }")
                a-row.option-group
                  a-col(
                    v-for="(val, index) in fieldOptionsMap[rule.key]"
                    :key="index"
                    :span="24")
                    a-checkbox(:value="val")
                      | {{ val }}
          //- 2、单选的情况
          .range-rule-set(v-else)
            .cell.flex-cell
              a-select.form-item(
                :options="stringOperators"
                :value="rule.exps[0].opt"
                @change="(operator) => { onChangeStringRuleOperator(operator, rule, index) }")
            .cell.flex-cell
              a-select.form-item(
                :value="JSON.parse(rule.exps[0].val)"
                @change="(value) => { onSelectChange(rule, value); }")
                a-select-option(
                  v-for="(val, index) in fieldOptionsMap[rule.key]"
                  :key="index"
                  :value="val")
                  | {{ val }}
        a-col.text-center(:span="2")
          a-button(
            shape="circle"
            icon="delete"
            @click="removeRule(rule, index)")
      //- =============== 自定义字符规则 ===============
      a-row(v-if="rule.exps && rule.exps.length > 0 && !fieldsMap[rule.key].layout.options")
        a-col(:span="4")
          strong {{ rule.key_name }}
        a-col(:span="18")
          .rule-set
            .cell
              a-select.form-item(
                :options="singleStringOperators"
                v-model="rule.exps[0].opt")
            .cell
              a-input.form-item(
                v-model="rule.exps[0].val"
                placeholder="请输入匹配内容")
        a-col.text-center(:span="2")
          a-button(
            shape="circle"
            icon="delete"
            @click="removeRule(rule, index)")
      //- =============== 发起角色规则 ===============
      a-row(v-if="rule.key === 'role'")
        a-col(:span="4")
          strong {{ rule.key_name }}
        a-col(:span="18")
          //- 1、单个值
          .rule-set(v-if="rule.roles.length === 1")
            .cell
              a-select.form-item(
                :options="operators"
                :value="rule.roles[0].opt"
                @change="(operator) => { onChangeRoleOperator(operator, rule, index) }")
            .cell
              a-select.form-item(
                v-model="rule.roles[0].level")
                a-select-option(
                  v-for="level in levels"
                  :key="level"
                  :value="level")
                  | 第 {{ level }} 级学院
            .cell
              .center-box
                a-switch(
                  checkedChildren="主管"
                  unCheckedChildren="员工"
                  v-model="rule.roles[0].manager")
          //- 2、区间设置
          .range-rule-set(v-else)
            .cell.flex-cell
              a-select.form-item(
                :options="operators"
                :value="rule.roles.length > 1 ? BETWEEN : null"
                @change="(operator) => { onChangeRoleOperator(operator, rule, index) }")
            .cell.flex-cell
              a-select.form-item(
                v-model="rule.roles[0].level")
                a-select-option(
                  v-for="level in levels"
                  :key="level"
                  :value="level")
                  | 第 {{ level }} 级学院
              a-select.form-item(v-model="rule.roles[0].opt")
                a-select-option(value=">") <
                a-select-option(value=">=") ≤
              .center-box.form-item
                a-switch(
                  checkedChildren="主管"
                  unCheckedChildren="员工"
                  v-model="rule.roles[0].manager")
              a-tag.shrink-span(color="blue") {{ rule.key_name }}
              a-select.form-item(v-model="rule.roles[1].opt")
                a-select-option(value="<") <
                a-select-option(value="<=") ≤
              a-select.form-item(
                v-model="rule.roles[1].level")
                a-select-option(
                  v-for="level in levels"
                  :key="level"
                  :value="level")
                  | 第 {{ level }} 级学院
              .center-box.form-item
                a-switch(
                  checkedChildren="主管"
                  unCheckedChildren="员工"
                  v-model="rule.roles[1].manager")
        a-col.text-center(:span="2")
          a-button(
            shape="circle"
            icon="delete"
            @click="removeRule(rule, index)")
      //- =============== 数值型规则 ===============
      a-row(v-else-if="rule.opts && rule.opts.length > 0")
        a-col(:span="4")
          strong {{ rule.key_name }}
        a-col(:span="18")
          //- 1、单个值
          .rule-set(v-if="rule.opts.length === 1")
            .cell
              a-select.form-item(
                :options="operators"
                :value="rule.opts[0].opt"
                @change="(operator) => { onChangeRuleOperator(operator, rule, index) }")
            .cell
              a-input-number.form-item(v-model="rule.opts[0].val")
          //- 2、区间设置
          .range-rule-set(v-else)
            .cell.flex-cell
              a-select.form-item(
                :options="operators"
                :value="rule.opts.length > 1 ? BETWEEN : null"
                @change="(operator) => { onChangeRuleOperator(operator, rule, index) }")
            .cell.flex-cell
              a-input-number.form-item(v-model="rule.opts[0].val")
              a-select.form-item(v-model="rule.opts[0].opt")
                a-select-option(value=">") <
                a-select-option(value=">=") ≤
              a-tag.shrink-span(color="blue") {{ rule.key_name }}
              a-select.form-item(v-model="rule.opts[1].opt")
                a-select-option(value="<") <
                a-select-option(value="<=") ≤
              a-input-number.form-item(v-model.number="rule.opts[1].val")
        a-col.text-center(:span="2")
          a-button(
            shape="circle"
            icon="delete"
            @click="removeRule(rule, index)")
  .actions(v-if="!nodeCopy.condition.is_default")
    a-button(type="primary" icon="plus" @click="openWindow")
      | 添加条件
    span.tips 还有 {{ fields.length - selectedRuleKeys.length }} 个可用条件

  a-modal(
    title='选择条件'
    :visible='visible'
    :width="600"
    @ok='onConfirmRuleKeys'
    @cancel='visible = false')
    a-checkbox-group.rule-key-group(v-model="selectedRuleKeys")
      a-row(:gutter="16")
        a-col(:span="6" v-for="(value, key) in fieldsMap" :key="key")
          a-checkbox.field-checkbox-item(:value='key')
            | {{ value.name }}
</template>

<style lang="stylus" scoped>
.condition-define
  padding 20px
  .rules
    padding 20px 0
    border-bottom 1px solid #ddd
    .rule
      margin-bottom 20px
      .range-rule-set
        display flex
        flex-direction column
      .rule-set
        display flex
      .cell
        display flex
        width 100%
        .option-group
          padding 0 10px
      .flex-cell
        display flex
        align-items center
        margin-bottom 10px
      .form-item
        flex-grow 1
        margin 0 5px
      .shrink-span
        flex-shrink 0
        padding 0 5px
        font-size 12px
  .actions
    margin-top 20px
    .tips
      margin-left 10px
      color #aaa

.center-box
  display flex
  justify-content center
  align-items center
  width 100%

.rule-key-group
  width 100%
  .field-checkbox-item
    margin-bottom 10px
</style>
