<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { IWorkflow } from '@/models/bpm/workflow';
import { IFormTemplateItem } from '@/interfaces/formTemplate.interface';
import FormulaKeyboard from './FormulaKeyboard.vue';

interface IFormulaObject {
  name: string;
  key: string;
  formula: string;
  formulaAry: string[];
  formulaView: string;
}

@Component({
  components: {
    FormulaKeyboard,
  },
})
export default class FormulanDesigner extends Vue {
  @Prop({ type: Object, default: () => ({ options: {} }) }) readonly nodeCopy!: IObject;
  @Prop({ type: Object, default: () => ({}) }) workflow!: IWorkflow;

  formulaAry: any[] = [];
  // formulas 存引用
  formulas: Partial<IFormulaObject>[] = [];
  activeName: string | undefined = '';
  activeKey: string | undefined = '';
  activeIndex = 0;

  mounted() {
    this.nodeCopy.options.items = this.nodeCopy.options.items || [];
    this.formulas = this.nodeCopy.options.items;
  }

  keyboardChange(formulaAry: string[], formulaValue: string, formulaView: string) {
    this.formulas[this.activeIndex] = this.formulas[this.activeIndex] || [];
    Object.assign(this.formulas[this.activeIndex], {
      formulaAry: formulaAry,
      formulaView: formulaView,
      formula: formulaValue,
    });
    this.$forceUpdate();
  }

  onTabChange(index: number) {
    this.activeIndex = index;
  }

  addTab() {
    this.formulas.push({ formulaAry: [] });
  }

  deleteTab(index: number) {
    this.formulas.splice(index, 1);
  }
}
</script>

<template lang="pug">
.formula-designer
  .content
    .left
      .tab(
        v-for="(item, index) in formulas"
        @click="onTabChange(index)"
        :class="{ 'active-tab': (activeIndex === index) }"
      )
        .tag-content
          p {{ item.name }}
          p(v-if="item.key") {{ item.key }}
        .delete(@click.stop="deleteTab(index)") -
      .tab(@click="addTab") +
    .right(
      v-for="(item, index) in formulas"
      v-if="formulas[activeIndex] && activeIndex === index"
    )
      .screen
        .formula
          span {{ `${formulas[index].name || '计算公式'} `}}
          span(v-if="formulas[index].key") {{ `(${formulas[index].key})` }}
          span {{ `= ${formulas[index].formulaView || ''}` }}
      .input
        span 名称
        a-input(v-model="formulas[index].name")
      .input
        span KEY
        a-input(v-model="formulas[index].key")
      .keyboard(v-if="true")
        FormulaKeyboard(v-model="formulas[index].formulaAry" :workflow="workflow" @change="keyboardChange")
  .footer
    .formula(v-for="item in formulas")
      | {{ `${item.name || '计算公式'} = ${item.formulaView || ''}` }}

</template>

<style lang="stylus" scoped>
.formula-designer
  padding 10px 40px
  .content
    display flex
    .left
      margin-right 30px
      .active-tab
        background-color #888
      .tab
        position relative
        display flex
        justify-content center
        align-items center
        width 100px
        height 100px
        border 1px solid black
        cursor pointer
        .tag-content
          max-width 50px
          text-align center
        .delete
          position absolute
          top 0
          right 0
          width 20px
          height 20px
          border 1px solid black
          border-radius 50%
          text-align center
          font-size 20px
          line-height 15px
          // background-color #999
    .screen
      margin 10px 0
      padding 10px
      border 1px solid #999
      border-radius 3px
      .footer
        display flex
        justify-content flex-end
        .back
          margin-right 10px
          cursor pointer
        .clear
          cursor pointer
    .input
      margin-bottom 10px
    .keyboard
      .variables, .symbols, .numbers
        display flex
        .title
          width 80px
        .buttom-group
          display flex
          .buttom
            display flex
            justify-content center
            align-items center
            margin 0 10px 10px 0
            padding 3px 10px
            min-width 30px
            border 1px solid #999
            border-radius 3px
            cursor pointer
</style>
