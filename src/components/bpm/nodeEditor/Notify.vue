<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import department from '@/models/department';
import UserSelectorDialog from '@/components/hr/UserSelectorDialog.vue';
import { IWorkflow, TransitionTypes } from '@/models/bpm/workflow';

@Component({
  components: {
    UserSelectorDialog,
  },
})
export default class NotifyNodeEditor extends Vue {
  get radioConfig() {
    const res: any[] = [
      {
        label: '学院主管',
        value: TransitionTypes.NotifyLevelManager,
        option: { level: 1, manager: true },
      }, // 第 n 级学院主管
      {
        label: '部门主管',
        value: TransitionTypes.NotifySpecifyManager,
        option: { department_id: null, department_name: '', manager: true },
      }, // 部门主管
      // 指定成员 {teacher_id: null, teacher_name: ''}
      { label: '指定成员', value: TransitionTypes.NotifyTeacher, option: { teachers: [] } },
      { label: '直接主管', value: TransitionTypes.NotifyDirectManager, option: { manager: true } }, // 直接主管
      { label: '发起人自选', value: TransitionTypes.NotifySponsorSelect, option: { mode: 'teacher' } }, // 直接主管
    ];
    if (this.workflowRoles.length) {
      res.push({ label: '分配角色', value: TransitionTypes.NotifyFlowableRole, option: { role: '' } });
    }
    return res;
  }
  get workflowRoles() {
    return (this.workflow.meta as any).workflow_roles || [];
  }
  get TransitionTypes() {
    return TransitionTypes;
  }

  get notifyModes() {
    return [
      { label: '所有教师', value: 'teacher' },
      { label: '部门领导', value: 'department_manager' },
      { label: '校领导', value: 'school_manager' },
    ];
  }

  @Prop({
    type: Object,
    default: () => ({
      meta: {
        workflow_roles: [],
      },
    }),
  })
  workflow!: IWorkflow;
  // radioConfig: any[] = [
  //   {
  //     label: '学院主管',
  //     value: TransitionTypes.NotifyLevelManager,
  //     option: { level: 1, manager: true },
  //   }, // 第 n 级学院主管
  //   {
  //     label: '部门主管',
  //     value: TransitionTypes.NotifySpecifyManager,
  //     option: { department_id: null, department_name: '', manager: true },
  //   }, // 部门主管
  //   // 指定成员 {teacher_id: null, teacher_name: ''}
  //   { label: '指定成员', value: TransitionTypes.NotifyTeacher, option: { teachers: [] } },
  //   { label: '直接主管', value: TransitionTypes.NotifyDirectManager, option: { manager: true } }, // 直接主管
  // ];
  levels: number[] = [1, 2, 3, 4, 5];
  // 添加 department
  departments: any[] = [];
  // 添加 teacher
  memberSelectorVisible: boolean = false;
  selectedMembers: any[] = [];

  @Prop({
    type: Object,
    default: () => ({
      fileds: {
        fields: [],
      },
    }),
  })
  readonly nodeCopy!: IObject;

  get selectedDepartment() {
    const { options } = this.nodeCopy;
    return options.department_id
      ? {
          label: options.department_name, // 对应于 depaartment 的 short_name
          value: options.department_id, // 对应于 depaartment 的 id
        }
      : null;
  }
  get selectedMemberIds() {
    return this.selectedMembers.map(o => o.id);
  }

  @Watch('nodeCopy', { deep: true, immediate: true })
  onNodeCopyChange() {
    this.onOptionsChange();
  }

  mounted() {
    this.fetchDepartments();
  }
  async fetchDepartments() {
    const { data } = await department.tree();
    const str = JSON.stringify(data.departments);
    const newStr = str.replace(/"id":/g, '"value":').replace(/"short_name":/g, '"label":');
    this.departments = JSON.parse(newStr);
  }
  onOptionsChange() {
    if (this.nodeCopy.transition_type === 'Transitions::Notify::Teacher') {
      this.selectedMembers = this.nodeCopy.options.teachers.map((o: any) => ({
        id: o.teacher_id,
        name: o.teacher_name,
      }));
    }
  }
  // =============== notify ===============
  onNotifyTypeChange(e: any) {
    const { option } = this.radioConfig.find(o => o.value === e.target.value);
    this.nodeCopy.options = { ...option };
  }
  // 选择成员 teachers
  setSelectedUsers(members: any[]) {
    this.selectedMembers = members;
    this.nodeCopy.options.teachers = members.map(o => ({
      teacher_id: o.id,
      teacher_name: o.name,
    }));
  }
  removeMember(index: number) {
    this.selectedMembers.splice(index, 1);
    this.setSelectedUsers(this.selectedMembers);
  }
  // 选择部门
  onSelectDepartment({ label, value }: IObject) {
    this.nodeCopy.options.department_id = value;
    this.nodeCopy.options.department_name = label;
  }
}
</script>

<template lang="pug">
.notify-component
  a-tabs(defaultActiveKey="1")
    a-tab-pane(tab="设置抄送人" key="1")
      .assign
        a-radio-group(
          :options="radioConfig"
          v-model="nodeCopy.transition_type"
          @change="onNotifyTypeChange")
        .operations
          //- 发起人的主管
          .operation(v-show="nodeCopy.transition_type === 'Transitions::Notify::LevelManager'")
            label 发起人的&nbsp;&nbsp;
            a-select(
              style="width: 160px"
              v-model="nodeCopy.options.level")
              a-select-option(
                v-for="level in levels"
                :key="level"
                :value="level")
                | 第 {{ level }} 级学院主管
          //- 指定部门
          .operation(v-show="nodeCopy.transition_type === 'Transitions::Notify::SpecifyManager'")
            a-tree-select(
              style="width: 300px"
              :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
              :treeData="departments"
              placeholder='请选择部门'
              :labelInValue="true"
              treeDefaultExpandAll
              allowClear
              showSearch
              treeNodeFilterProp="label"
              :value="selectedDepartment"
              @change="onSelectDepartment")
          //- 指定组织结构中的成员
          .operation(v-show="nodeCopy.transition_type === 'Transitions::Notify::Teacher'")
            a-button(icon="plus" type="primary" @click="memberSelectorVisible = true")
              | {{ selectedMembers.length > 0 ? '修改成员' : '添加成员' }}
            .tags(v-if="selectedMembers.length > 0")
              a-tag(
                color="blue"
                v-for="(member, index) in selectedMembers"
                :key="index + 1"
                :closable="true"
                :onClose="() => removeMember(index)")
                | {{ member.name }}
              span.clear-members.text-primary(@click="setSelectedUsers([])")
                | 清空
          .operation(
            v-if="workflowRoles.length"
            v-show="nodeCopy.transition_type === TransitionTypes.NotifyFlowableRole")
            a-radio-group.normal-radio-group(
              style="width: 160px"
              v-model="nodeCopy.options.role")
              a-radio.radio-item(
                v-for="role in workflowRoles"
                :key="role"
                :value="role")
                | {{ role }}
          .operation(v-show="nodeCopy.transition_type === 'Transitions::Notify::SponsorSelect'")
            label 抄送&nbsp;&nbsp;
            a-select(
              style="width: 160px"
              v-model="nodeCopy.options.mode")
              a-select-option(
                v-for="mode in notifyModes"
                :key="mode.value"
                :value="mode.value")
                | {{ mode.label }}

  UserSelectorDialog(
    title="选择成员"
    v-model="memberSelectorVisible"
    :multiple="true"
    :userIds="selectedMemberIds"
    @selectUsers="setSelectedUsers")
</template>

<style lang="stylus" scoped>
.assign
  padding 20px
  .operations
    padding 30px 0px 20px
    .operation
      .tags
        margin-top 14px
        .clear-members
          cursor pointer

.fields
  margin-top -16px
  width 100%
  .radio-group
    width 100%
  .body .cell
    padding 16px
    border-bottom 1px solid #e8e8e8
    word-break break-word
    transition all 0.3s
  .head .cell
    padding 16px
    border-bottom 1px solid #e8e8e8
    background #fafafa
    color rgba(0, 0, 0, 0.85)
    text-align left
    word-break break-word
    font-weight 500
    transition background 0.3s ease
</style>
