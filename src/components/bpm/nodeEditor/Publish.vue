<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { TransitionTypes } from '../../../models/bpm/workflow';
import moment, { Moment } from 'moment';

@Component
export default class PublishNodeEditor extends Vue {
  @Prop({ type: Object, default: () => ({ options: {} }) }) readonly nodeCopy!: IObject;

  radioConfig: any[] = [
    {
      label: '直接发布',
      value: TransitionTypes.WechatDirectPublish,
      option: { enable_begin_at: 900, enable_end_at: 1800 },
    },
    { label: '定时发布', value: TransitionTypes.WechatTimedPublish, option: {} },
  ];
  openBegin: boolean = false;
  openEnd: boolean = false;

  get TransitionTypes() {
    return TransitionTypes;
  }
  get beginAt() {
    return this.timeNumberToMoment(this.nodeCopy.options.enable_begin_at || 900);
  }
  set beginAt(moment: Moment) {
    this.$set(this.nodeCopy.options, 'enable_begin_at', this.timeMomentToNumber(moment));
  }
  get endAt() {
    return this.timeNumberToMoment(this.nodeCopy.options.enable_end_at || 1800);
  }
  set endAt(moment: Moment) {
    this.$set(this.nodeCopy.options, 'enable_end_at', this.timeMomentToNumber(moment));
  }

  timeNumberToMoment(number: string) {
    return moment(String(number).padStart(4, '0'), 'HH:mm');
  }
  timeMomentToNumber(moment: Moment) {
    return Number(
      moment
        .format('HH:mm')
        .split(':')
        .join(''),
    );
  }
  onTypeChange(e: any) {
    const { option } = this.radioConfig.find(o => o.value === e.target.value);
    this.$set(this.nodeCopy, 'options', { ...option });
  }
  onBeginTimeChange(visible: boolean) {
    if (visible === false) {
      (this.$refs.endPicker as any).focus();
    }
  }
}
</script>

<template lang="pug">
.article-publish
  a-radio-group.radio-group(
    :options="radioConfig"
    v-model="nodeCopy.transition_type"
    @change="onTypeChange")
  .content(v-if="nodeCopy.transition_type === TransitionTypes.WechatDirectPublish")
    p.text-primary 在指定时间范围内，如果审批成功，将自动发布文章
    a-time-picker(
      v-model="beginAt"
      :open.sync="openBegin"
      size="large"
      placeholder="开始时间"
      format="HH:mm")
      a-button(slot="addon" size="small" type="primary" @click="openBegin = false")
        | 确认
    span  ~&nbsp;
    a-time-picker(
      :open.sync="openEnd"
      v-model="endAt"
      size="large"
      placeholder="结束时间"
      format="HH:mm")
      a-button(slot="addon" size="small" type="primary" @click="openEnd = false")
        | 确认
  .content(v-else)
    p.text-primary 由审批人员设置文章发布时间
</template>

<style lang="stylus" scoped>
.article-publish
  padding 20px
  .content
    padding 16px 0px
    margin-top 16px
    border-top 1px solid #e5e5e5
    p
      margin-bottom 15px
</style>
