<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { TransitionTypes } from '../../../models/bpm/workflow';
import { IWorkflow } from '../../../models/bpm/workflow';

@Component
export default class FlowableCallbackNodeEditor extends Vue {
  @Prop({ type: Object, default: () => ({ options: {} }) }) readonly nodeCopy!: IObject;
  @Prop({
    type: Object,
    default: () => ({
      meta: {
        workflow_callbacks: [],
      },
    }),
  })
  workflow!: IWorkflow;

  get callbacks() {
    return (this.workflow.meta as any).workflow_callbacks || [];
  }

  setCallback(e: any) {
    const callback = this.callbacks.find((o: any) => o.callback_method === e.target.value);
    this.$set(this.nodeCopy, 'options', callback);
  }
}
</script>

<template lang="pug">
.node-callback
  a-radio-group.radio-group(@change="setCallback" :value="nodeCopy.options.callback_method")
    a-radio.radio-item(
      v-for="callback in callbacks"
      :key="callback.callback_method"
      :value="callback.callback_method")
      | {{ callback.name }}
</template>

<style lang="stylus" scoped>
.node-callback
  padding 20px
</style>
