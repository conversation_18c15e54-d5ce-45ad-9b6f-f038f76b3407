<script lang="ts">
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import { IFormTemplateItem } from '@/interfaces/formTemplate.interface';

@Component({
  components: {},
})
export default class BpmFieldsSellector extends Vue {
  keys: string[] = [];

  @Model('input', { type: Boolean, default: false }) private value!: boolean;
  @Prop({ type: Array, default: () => [] }) private fields!: IFormTemplateItem[];
  @Prop({ type: Array, default: () => [] }) private selectedFields!: string[];

  @Watch('selectedFields', { deep: true, immediate: true })
  onFieldsChange() {
    this.keys = this.selectedFields.map((o: any) => o.key);
  }

  get fieldsMap() {
    return this.$utils.objectify(this.fields, 'key');
  }
  get selectedFieldsMap() {
    return this.$utils.objectify(this.selectedFields, 'key');
  }

  confirm() {
    const selectedFields = this.keys.map((o: string) => this.selectedFieldsMap[o] || this.fieldsMap[o]);
    this.$emit('change', selectedFields);
    this.close();
  }

  close() {
    this.$emit('input', false);
  }
}
</script>

<template lang="pug">
a-modal(
  title="选择字段"
  :visible="value"
  :width="600"
  @ok="confirm"
  @cancel="close")
  a-checkbox-group.rule-key-group(v-model="keys")
    a-row(:gutter="16")
      a-col(:span="6" v-for="(field, index) in fields" :key="field.key")
        a-checkbox.field-checkbox-item(:value='field.key')
          | {{ field.name }}
</template>

<style lang="stylus" scoped>
.rule-key-group
  width 100%
  .field-checkbox-item
    margin-bottom 10px
</style>
