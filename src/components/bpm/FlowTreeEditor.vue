<script lang="ts">
import { Component, Vue, Prop, Model } from 'vue-property-decorator';
import FlowTree from '../flowTree';
import { defaultCore, defaultMenuPlaces, defaultWorkflow } from '@/models/bpm/defaultValues';
import { IWorkflow, IWorkflowCorePlace, WorkflowTypes, WorkflowState, IWorkflowCore } from '@/models/bpm/workflow';
import { IPlaceMenuTemplate } from '../flowTree/types';
import NodeEditor from './nodeEditor/NodeEditor.vue';

@Component({
  components: {
    FlowTree,
    NodeEditor,
  },
})
export default class BpmFlowTreeEditor extends Vue {
  @Model('change', { type: Object, default: () => defaultWorkflow }) workflow!: IWorkflow;
  @Prop({ type: Array, default: () => defaultMenuPlaces }) menuPlaces!: IPlaceMenuTemplate[];
  @Prop({ type: Boolean, default: false }) readonly disabled!: boolean; // 是否可编辑
  @Prop({ type: Boolean, default: false }) readonly showStartPoint!: boolean;
  @Prop({ type: Boolean, default: true }) readonly showEndPoint!: boolean;
  @Prop({ type: Boolean, default: true }) readonly showStartNode!: boolean;
  @Prop({ type: Boolean, default: false }) readonly showEndNode!: boolean;

  flowTreeRef: IObject = {};
  activeNodeRef: IWorkflowCorePlace | IObject = {}; // 真实操作节点的引用, 修改将直接反应到节点树上
  activeNodeCopy: IWorkflowCorePlace | IObject = {
    fields: [],
    options: [],
    place_form: {
      fields: [],
    },
  }; // 用于节点编辑的副本
  nodeEditorVisible: boolean = false;

  get formFields() {
    return this.workflow.form ? this.workflow.form.fields : [];
  }

  mounted() {
    this.$nextTick(() => {
      this.flowTreeRef = this.$refs.flowTree;
    });
  }

  selectNode(node: IWorkflowCorePlace, nodeCopy: IWorkflowCorePlace) {
    this.activeNodeRef = node;
    this.activeNodeCopy = nodeCopy;
    if (node.kind !== 'condition') {
      this.updatePlaceAccessibilityFields(this.activeNodeCopy, this.formFields);
    }
    this.nodeEditorVisible = true;
  }

  // 因为 place 存储的 fields 是管理员设置的表单权限，
  // 结构为：[{ key: 'form_item_key', accessibility: 'readonly | hidden | read_and_write' }]
  // 如果后面 form 的模板发生了更新，place.fields.fields 的内容很可能会过时，出现脏数据，同时也无法响应最新的表单结构
  // 所以当点击节点时，检查 place 的 fields，使用最新的 form fields 更新 place 的 fields，同时权限使用 place 自身
  // 权限或者默认权限。
  updatePlaceAccessibilityFields(activeNodeCopy: IWorkflowCorePlace | IObject, formFields: any[]) {
    const placeFieldsMap = (activeNodeCopy.fields.fields || []).reduce(
      (obj: any, f: any) => ({
        ...obj,
        [f.key]: f.accessibility,
      }),
      {},
    );
    const defaultAccessibility = activeNodeCopy.type === 'Places::StartPlace' ? 'read_and_write' : 'readonly';
    this.activeNodeCopy.fields.fields = formFields.map((item: any) => ({
      ...item,
      accessibility: placeFieldsMap[item.key] || defaultAccessibility,
    }));
  }

  onFlowTreeChanged(core: IWorkflowCore) {
    this.$set(this.workflow, 'core', core);
  }

  nodeChanged(node: IObject) {
    // 先更新节点原始引用对象
    Object.assign(this.activeNodeRef, { ...node });
    // 更新流程 core
    const core = this.flowTreeRef.getCore();
    this.onFlowTreeChanged(core);
  }
}
</script>

<template lang="pug">
.flow-tree-editor
  FlowTree(
    ref="flowTree"
    :core="workflow.core"
    :activeNode="activeNodeRef"
    :places="menuPlaces"
    :disabled="disabled"
    :showStartPoint="showStartPoint"
    :showStartNode="showStartNode"
    :showEndPoint="showEndPoint"
    :showEndNode="showEndNode"
    @change="onFlowTreeChanged"
    @select="selectNode")
    span(slot="start" v-if="!showStartPoint")

  NodeEditor(
    ref="nodeEditor"
    v-model="nodeEditorVisible"
    :nodeCopy="activeNodeCopy"
    :workflow="workflow"
    @change="nodeChanged"
    @close="activeNodeRef = {}")
</template>

<style lang="stylus" scoped>
.flow-tree-editor
  position relative
  width 100%
  height 100%
</style>
