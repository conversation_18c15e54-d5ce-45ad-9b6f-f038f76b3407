<script lang="ts">
import { IWorkflow, WorkflowTypes } from '@/models/bpm/workflow';
import { Component, Vue, Prop } from 'vue-property-decorator';
import { bpmUserWorkflowStore } from '../../store/modules/bpm/user/workflow.store';
import { TaIndexViewConfigInterface } from '../global/TaIndex';
import ComWorkflowCell from './ComWorkflowCell.vue';

@Component({
  components: {
    ComWorkflowCell,
  },
})
export default class ComWorkflowList extends Vue {
  // 不带分组的 workflow list
  @Prop({ type: Object, default: () => ({}) }) private defaultQuery!: IObject;

  get store() {
    return bpmUserWorkflowStore;
  }

  get config(): TaIndexViewConfigInterface {
    return {
      recordName: '业务',
      store: this.store,
      splitCount: 2,
    };
  }

  mounted() {
    // const roleFilter = this.$store.state.currentUser.type === 'Teacher' ? 'for_teacher' : 'for_student';

    this.store.init({
      params: {
        q: {
          type_eq: WorkflowTypes.Bpm,
          state_eq: 'done',
          // [roleFilter]: true,
          ...this.defaultQuery,
        },
      },
    });
  }

  onShow(record: IWorkflow) {
    this.$emit('onShow', record);
  }
}
</script>

<template lang="pug">
.com-workflow-list
  TaTitleHeader(title='流程申请')
  TaIndexView(:config='config', @onShow='onShow')
    template(#card='{ record }')
      ComWorkflowCell.cell(:workflow='record')
</template>

<style lang="stylus" scoped>
.cell
  margin 0 10px
</style>
