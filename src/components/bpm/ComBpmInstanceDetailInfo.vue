<script lang="ts">
import { Prop, Vue, Component } from 'vue-property-decorator';
import { Instance } from '@/models/bpm/instance';

@Component({})
export default class ComBpmInstanceDetailInfo extends Vue {
  @Prop({ type: Object }) record!: IObject;

  waiting = require('@/assets/images/bpm/waiting.png');

  get operatorDesc() {
    return new Instance().operatorDesc(this.record);
  }

  get isRunning() {
    return (
      this.record.current_token &&
      (this.record.current_token.state === 'processing' || this.record.current_token.state === 'preparing')
    );
  }
}
</script>

<template lang="pug">
.com-bpm-instance-detail-info.flex
  .container-left
    .header.flex
      .title
        | {{ `${record.creator_name}发起的${record.workflow_name}` }}
      .time
        | ·{{ record.created_at | format }}

  .container-right(v-if='isRunning')
    img.wait-img(:src='waiting')
    .status 等待{{ operatorDesc }}处理
</template>

<style lang="stylus" scoped>
.flex
  display flex
  justify-content flex-start
.com-bpm-instance-detail-info
  align-items center
  font-family PingFangSC-Regular, PingFang SC
  width 100%
  height 100%
  // min-width 670px
  // padding 0 36px 0 24px
  .container-left
    width 100%
    flex 1
    margin-right 20px
    .header
      margin-bottom 12px
      .title
        font-size 18px
        font-weight 600
        color #262626
        line-height 25px
        margin-right 36px
        min-width 260px
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      .time
        font-size 14px
        color #8c8c8c
        line-height 14px
    .details
      .work-number
        font-size 14px
        color #8c8c8c
        line-height 14px
        margin-right 16px
      .specialty
        font-size 14px
        color #8c8c8c
        line-height 14px
  .container-right
    display flex
    justify-content flex-end
    .wait-img
      width 16px
      height 16px
      margin-right 10px
    .status
      font-size 14px
      color #FA8C15
      line-height 20px
</style>
