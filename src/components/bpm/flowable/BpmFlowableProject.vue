<template lang="pug">
.flowable-finance-project
  .name 关联资金卡信息
  a-row(:gutter="20")
    a-col(:span="12")
      Cell(label="资金卡号" :value="project.project_uid")
    a-col(:span="12")
      Cell(label="资金卡名称" :value="project.project_name")
    a-col(:span="12")
      Cell(label="总金额" :value="project.amount | toCurrency")
    a-col(:span="12")
      Cell(label="学校" :value="project.school_name")
    a-col(:span="12")
      Cell(label="发起人" :value="instance.creator_name")
    a-col(:span="12")
      Cell(label="一级项目" :value="project.project_category_name")
    a-col(:span="12")
      Cell(label="项目负责人" :value="project.owner_name")
    a-col(:span="12")
      Cell(label="开始日期" :value="project.start_at | format('YYYY-MM-DD')")
    a-col(:span="12")
      Cell(label="结束日期" :value="project.end_at | format('YYYY-MM-DD')")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import Cell from '@/components/global/Cell.vue';
import { IInstance } from '@/models/bpm/instance';

@Component({
  components: {
    Cell,
  },
})
export default class BpmFlowableProject extends Vue {
  @Prop() private instance!: IInstance;

  get project() {
    return {
      id: this.instance.flowable_id,
      ...this.instance.flowable_info,
    };
  }
}
</script>

<style lang="stylus" scoped>
.flowable-finance-project
  position relative
  padding 14px 0
  border-bottom 1px solid #e8e8e8
  .title
    margin 10px 0
    color #383838
    font-weight 500
    font-size 16px
</style>
