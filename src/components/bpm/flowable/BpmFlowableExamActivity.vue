<template lang="pug">
.flowable-finance-project
  .name.flex-between
    span 关联考试信息
    TextButton(icon="eye" @click="onShow")
      | 查看考试详情
  .module
    a-row(:gutter="20")
      a-col(:span="12")
        Cell(label="考试名称" :value="activity.title")
      a-col(:span="12")
        Cell(label="所属部门" :value="activity.department_name")
      a-col(:span="12")
        Cell(label="考生人数" :value="activity.student_count")
      a-col(:span="12")
        Cell(label="考试时长" :value="`${activity.duration_in_min} 分钟`")
      a-col(:span="12")
        Cell(label="开始时间" :value="activity.start_at | format('YYYY-MM-DD HH:mm')")
      a-col(:span="12")
        Cell(label="发起人" :value="instance.creator_name")
</template>

<script lang="ts">
/**
 * 考试信息
 */
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import Cell from '@/components/global/Cell.vue';
import { IInstance } from '@/models/bpm/instance';

@Component({
  components: {
    Cell,
  },
})
export default class BpmFlowableExamActivity extends Vue {
  @Prop() private instance!: IInstance;

  get activity() {
    return {
      id: this.instance.flowable_id,
      ...this.instance.flowable_info,
    };
  }

  onShow() {
    this.$utils.open(`/exam/teacher/exam_activities/${this.activity.id}`, '_blank');
  }
}
</script>

<style lang="stylus" scoped>
.flowable-finance-project
  position relative
  .name
    margin 10px 0
    color #383838
    font-weight 500
    font-size 16px
  .module
    padding 14px 0px
    border-bottom 1px #e6e6e6 solid
</style>
