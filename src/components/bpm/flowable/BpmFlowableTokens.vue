<template lang="pug">
.instance-tokens
  .token(v-for="token in tokens" :key="token.id")
    .name
      span {{ token.name }} -
      span  {{ token.operator_name }} -
      span.text-gray  {{ token.updated_at | format }}
    TemplateFormViewer(
      :formData="token.token_payload"
      :template="token.place_form.fields"
      :border="false")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { IInstance } from '@/models/bpm/instance';
import { TransitionTypes } from '@/models/bpm/workflow';
import TemplateFormViewer from '../../form/TemplateFormViewer.vue';

@Component({
  components: {
    TemplateFormViewer,
  },
})
export default class BpmFlowableTokens extends Vue {
  @Prop() private instance!: IInstance;

  get tokens() {
    return (this.instance.tokens || []).filter(t => t.place_form && t.place_form.fields && t.place_form.fields.length);
  }
}
</script>

<style lang="stylus" scoped>
.instance-tokens
  position relative
  margin 20px 0
  .token
    border-top 1px #e6e6e6 solid
    &:last-child
      border-bottom 1px #e6e6e6 solid
    .name
      margin 20px 0px 0px
      color #383838
      font-weight 500
      font-size 16px
</style>
