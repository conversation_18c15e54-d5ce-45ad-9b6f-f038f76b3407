<template lang="pug">
.flowable-finance-meeting-activity
  .name 关联会议信息
  ActivityMeetingCard(:record='meeting_activity')
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { IInstance } from '@/models/bpm/instance';
import ActivityMeetingCard from '../../conference/ActivityMeetingCard.vue';

@Component({
  components: {
    ActivityMeetingCard,
  },
})
export default class BpmFlowableMeetingActivity extends Vue {
  @Prop() private instance!: IInstance;

  get meeting_activity() {
    return {
      id: this.instance.flowable_id,
      ...this.instance.flowable_info,
    };
  }
}
</script>

<style lang="stylus" scoped>
.flowable-finance-meeting-activity
  position relative
  padding 14px 0
  border-bottom 1px solid #e8e8e8
  .name
    margin 10px 0
    color #383838
    font-weight 500
    font-size 16px
</style>
