<template lang="pug">
.flowable-finance-voucher(v-loading="voucherStore.loading")
  .name 单据信息
  .module
    a-row(:gutter="20")
      a-col(:span="12")
        Cell(label="资金卡号" :value="project.uid")
      a-col(:span="12")
        Cell(label="资金卡名称" :value="project.name")
      a-col(:span="12")
        Cell(label="发起人" :value="instance.creator_name")
      a-col(:span="12")
        Cell(label="一级项目" :value="project.project_category_name")
      a-col(:span="12")
        Cell(label="项目负责人" :value="project.owner_name")
      a-col(:span="12" v-for="role in project.project_roles" :key="role.id")
        Cell(:label="role.approval_role_name" :value="role.teacher_name")
      a-col(:span="12")
        Cell(label="单据类型" :value="voucherTypeText[voucher.type]")
  .module.no-border
    .module-title 单据内容
    a-table(:dataSource="voucher.payments || []" :pagination="false" rowKey="id")
      a-table-column(title="结算编号" dataIndex="finance_uid")
      a-table-column(title="二级" dataIndex="catalog_name")
      a-table-column(title="三级" dataIndex="budget_name")
      a-table-column(title="资金来源" dataIndex="origin_name")
      a-table-column(title="科目" dataIndex="subject_name")
      a-table-column(title="金额" dataIndex="amount")
        template(slot-scope="amount")
          | {{ amount | toCurrency }}
      a-table-column(title="资产采购方式" dataIndex="budget_purchase")
  .module.no-border(v-if='voucher.type === VoucherType.Asset')
    .module-title 资产请购信息
      a-table(:dataSource="voucher.budget_lock_grps || []" :pagination="false" rowKey="id")
        a-table-column(title='名称', dataIndex='name')
          template(slot-scope='scope, record')
            .info {{ `【${record.name}】` }}
        a-table-column(title='基本信息')
          template(slot-scope='record')
            .info 资金卡名：{{ record.name }}
            .info 资金卡号：{{ record.seq }}
            .info 创建时间：{{ record.created_at | format }}
        a-table-column(title='金额详情',  width='160px')
          template(slot-scope='record')
            .info 总金额：{{ record.amount }}
            .info 已使用：{{ record.completed_payment_amount }}
            .info 已锁定：{{ record.locking_amount }}
            .info 请购中：{{ record.processing_payment_amount }}
        a-table-column(title='状态', width='100px', align='center')
          template(slot-scope='record')
            TaTag(:type='lockStateMap[record.state].type', size='small')
              | {{ lockStateMap[record.state].label }}
  .module
    template(v-if="voucher.type === VoucherType.Routine || voucher.type === VoucherType.Asset")
      .module-title
        | 付款信息
        span.notice 注意事项：报销材料中请务必附上打款银行账户信息
      a-row(:gutter="20")
        a-col(:span="12")
          Cell(label="收款人（单位）" :value="payeeMeta.name")
        a-col(:span="12")
          Cell(label="部门" :value="payeeMeta.department")
        a-col(:span="12")
          Cell(label="付款方式" :value="payeeMeta.payment_way")
        a-col(:span="12")
          Cell(label="经办人联系方式" :value="payeeMeta.phone")
        a-col(:span="24")
          Cell(label="事由备注" :value="voucher.remark")
    template(v-else)
      .module-title(v-if="voucher.type === VoucherType.Outside") 出差人信息
      .module-title(v-if="voucher.type === VoucherType.Loan") 借款人信息
      a-row(:gutter="20")
        a-col(:span="12")
          Cell(label="姓名" :value="payeeMeta.name")
        a-col(:span="12")
          Cell(label="部门" :value="payeeMeta.department")
        a-col(:span="12")
          Cell(label="付款方式" :value="payeeMeta.payment_way")
        a-col(:span="12")
          Cell(label="经办人联系方式" :value="payeeMeta.phone")
        a-col(:span="24")
          Cell(label="事由备注" :value="voucher.remark")
  .module
    .flex-between.total-fee
      .module-title 报销费用合计
      .price
        div ￥{{ voucher.final_amount | toCurrency }}
        div.text-gray {{ voucher.capital_amount }}
        div.text-gray 劳务费个税：{{ (voucher.tax || 0) | toCurrency }}
    template(v-if="voucher.type === 'Finance::OutsideVoucher'")
      OutsideVoucherInfo(:voucherMeta="meta")
  .module
    template(v-if="voucher.type === VoucherType.Asset")
      .module-title 资产单据附件({{ assetAttachments.length || 0 }})
      div.assetAttachments(v-for="(item, index) in assetAttachments" :key="index")
        .collaspe
          AssetCollaspeTable(:item="item")
    .module-title 单据附件({{ attachments.length || 0 }})
    Attachments(:attachments="attachments")
  .module(style="border: none" v-if="templateForm.template.length")
    .module-title {{ templateForm.name }}
    TemplateFormViewer(
      :instance="instance"
      :template="templateForm.template"
      :formData="templateForm.formData")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { VoucherStore } from '@/store/modules/finance/voucher.store';
import instanceModel, { IInstance } from '@/models/bpm/instance';
import Attachments from '@/components/global/Attachments.vue';
import OutsideVoucherInfo from '@/components/finance/OutsideVoucherInfo.vue';
import Cell from '@/components/global/Cell.vue';
import TemplateFormViewer from '@/components/form/TemplateFormViewer.vue';
import { VoucherType } from '@/models/finance/voucher';
import FileCell from '@/components/fund/FileCell.vue';
import AssetFileCell from '@/components/fund/AssetFileCell.vue';
import AssetCollaspeTable from '@/components/fund/AssetCollaspeTable.vue';

@Component({
  components: {
    Attachments,
    OutsideVoucherInfo,
    Cell,
    TemplateFormViewer,
    FileCell,
    AssetFileCell,
    AssetCollaspeTable,
  },
})
export default class BpmFlowableVoucher extends Vue {
  @Prop({ type: Object, default: () => ({}) }) private instance!: IInstance;

  activePanel: string[] = [];

  get voucherStore() {
    return VoucherStore.withActivityTeacher;
  }
  get voucher() {
    return this.voucherStore.record;
  }
  get project() {
    return this.voucher.project || { project_roles: [] };
  }
  get payeeMeta() {
    return this.voucher.payee_meta || {};
  }
  get VoucherType() {
    return VoucherType;
  }
  get voucherTypeText() {
    return this.voucherStore.voucherTypeText;
  }
  // 动态表单
  get templateForm() {
    const form =
      this.voucher.payee_meta && this.voucher.payee_meta.form
        ? this.voucher.payee_meta.form
        : { name: '', templateId: null, template: [], formData: {} };
    return {
      name: form.name,
      templateId: form.templateId,
      template: form.template || [],
      formData: form.formData || {},
    };
  }
  get meta() {
    return this.voucher.meta || { route: {} };
  }
  get attachments() {
    return (this.voucher.receipts || []).map((o: any) => o.attachment);
  }
  get assetAttachments() {
    return this.instance?.flowable_info?.asset_infos || [];
  }

  getPreviewPath(item: any) {
    const { url, name } = item;
    return `/fund/file_preview?file_url=${url}&file_type=pdf&file_name=${encodeURIComponent(name)}`;
  }

  get stateMap() {
    return instanceModel.stateMap;
  }

  get lockStateMap(): IObject {
    return {
      doing: { label: '进行中', type: 'warning', value: 'doing' },
      done: { label: '已完成', type: 'success', value: 'done' },
      cancelled: { label: '已取消', type: 'danger', value: 'cancelled' },
    };
  }

  @Watch('instance', { immediate: true })
  onInstanceChange() {
    if (this.$route.path.includes('/admin/')) {
      this.voucherStore.setRole('admin');
    } else {
      this.voucherStore.setRole('teacher');
    }
    this.voucherStore.find(this.instance.flowable_id!);
  }
}
</script>

<style lang="stylus" scoped>
.flowable-finance-voucher
  position relative
  .name
    margin 22px 0px 4px
    font-weight 500
    font-size 16px
    line-height 28px
  .module-title
    font-weight 500
    font-size 16px
    line-height 40px
  .no-border
    border none !important
  .module
    padding 14px 0px
    border-bottom 1px #e6e6e6 solid
    .collaspe
      margin-bottom 10px
    .price
      text-align right
      font-weight 500
      font-size 16px
    .file
      width 100%
      cursor pointer
      &:hover
        opacity 0.8
    .total-fee
      margin-bottom 10px
    .assetAttachments
      border-bottom 1px solid #E8E8E8
      .row
        line-height 20px
        display flex
        align-items center
        .item
          margin 10px 40px 10px 0
          line-height 20px
          font-size 14px
  .qrcode-box
    position absolute
    top 60px
    right 20px
    .qrcode
      width 80px
      height 80px
    .qrcode-title
      margin-top 12px
      text-align center
      letter-spacing 1px
      font-size 14px
      line-height 20px
  .notice
    margin-left 12px
    color red
    font-weight 600
    font-size 18px
</style>
