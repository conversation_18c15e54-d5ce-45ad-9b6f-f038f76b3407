<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import InstanceDetail from './InstanceDetail.vue';
import { IInstance, IFlowableType } from '../../models/bpm/instance';
// flowable model
import { Project } from '../../models/finance/project';
// flowable components
import BpmFlowableProject from './flowable/BpmFlowableProject.vue';
import BpmFlowableVoucher from './flowable/BpmFlowableVoucher.vue';
import BpmFlowableExamActivity from './flowable/BpmFlowableExamActivity.vue';
import BpmFlowableTokens from './flowable/BpmFlowableTokens.vue';
import ComCorseInfo from '@/components/teaching/teacher/ComCorseInfo.vue';
import { Voucher } from '@/models/finance/voucher';
import ComBpmInstanceDetailHeader from './ComBpmInstanceDetailHeader.vue';
import BpmFlowableMeetingActivity from './flowable/BpmFlowableMeetingActivity.vue';

@Component({
  components: {
    InstanceDetail,
    BpmFlowableProject,
    BpmFlowableVoucher,
    BpmFlowableExamActivity,
    BpmFlowableTokens,
    ComBpmInstanceDetailHeader,
    BpmFlowableMeetingActivity,
  },
})
export default class InstanceDetailDialog extends Vue {
  @Prop({ type: String }) readonly title!: string;
  @Prop({ type: Boolean, default: false }) readonly value!: boolean;
  @Prop({ required: true }) readonly instanceId!: number;

  get visible() {
    return this.value;
  }
  set visible(val) {
    this.$emit('input', val);
  }
  get IFlowableType() {
    return IFlowableType;
  }

  emitChange(...args: any) {
    this.$emit('change', ...args);
  }
  print(instance: IInstance) {
    this.$utils.open(`/bpm/user/instances/${instance.id}/print`);
  }
  async flowableEdit(instance: IInstance) {
    if (instance.flowable_type === IFlowableType.FinanceProject) {
      const hasPower = this.$utils.hasPermission('finance', 'admin');
      if (hasPower) {
        const { data } = await new Project().find(instance.flowable_id!);
        this.$utils.open(`/finance/admin/activities/${data.activity_id}/projects/${data.id}/edit`);
      } else {
        this.$message.warning('管理员权限才可修改关联的资金卡');
      }
    } else if (instance.flowable_type === IFlowableType.Voucher) {
      const { data } = await new Voucher().find(instance.flowable_id!);
      this.$utils.open(`/finance/teacher/activities/${data.activity_id}/vouchers/${data.id}/edit`);
    } else if (instance.flowable_type === IFlowableType.ExamActivity) {
      this.$router.push(`/exam/teacher/exam_activities/${instance.flowable_id}/edit`);
    } else if (instance.flowable_type === IFlowableType.MeetingActivity) {
      this.$utils.open(`/conference/teacher/activities/mine/${instance.flowable_id}`);
    } else {
      this.$emit('flowableEdit', instance);
    }
  }
  onClose() {
    this.$emit('input', false);
  }
  afterFetch() {
    this.$emit('afterFetch', false);
  }
}
</script>

<template lang="pug">
MainModal.modal(v-model='visible', width='85%')
  template(#title)
    .empty empty
  InstanceDetail(
    ref='detailContent'
    :instanceId='instanceId',
    @afterFetch='afterFetch',
    @change='emitChange',
    @flowableEdit='flowableEdit',
    @print='print'
  )
    //- flowable header
    template(#flowableOnTop='{ instance }')
      BpmFlowableProject(:instance='instance', v-if='instance.flowable_type === IFlowableType.FinanceProject')

    //- flowable info
    template(#flowable='{ instance }')
      BpmFlowableVoucher(:instance='instance', v-if='instance.flowable_type === IFlowableType.Voucher')
      BpmFlowableExamActivity(:instance='instance', v-else-if='instance.flowable_type === IFlowableType.ExamActivity')
      BpmFlowableMeetingActivity(
        :instance='instance',
        v-else-if='instance.flowable_type === IFlowableType.MeetingActivity'
      )
      slot(name='context')
      slot(:instance='instance')

    //- default slot
    template(#default='{ instance }')
      BpmFlowableTokens(:instance='instance')
</template>

<style lang="stylus" scoped>
.empty
  opacity 0
</style>
