<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import instanceModel, { IInstance } from '@/models/bpm/instance';

@Component({
  components: {},
})
export default class BpmInstanceCell extends Vue {
  @Prop({ type: Object, default: () => ({}) }) private instance!: IInstance;
  @Prop({ type: Boolean, default: true }) private showActions!: boolean;

  visible: boolean = true;

  get stateMap() {
    return instanceModel.stateMap;
  }

  onClick(...args: any) {
    this.$emit('click', ...args);
  }
  async onDelete() {
    await instanceModel.delete(this.instance.id!);
    this.visible = false;
  }

  get summary() {
    const summaryObj = this.instance.summary || {};
    return Object.keys(summaryObj)
      .map(key => [key, summaryObj[key]].join('：'))
      .join('，');
  }
}
</script>

<template lang="pug">
.instance-cell(@click.stop="onClick" v-if="visible")
  .header
    .workflow-name
      img.icon(src="@/assets/images/bpm/app.png" height="20" width="20")
      span(v-if="instance.flowable_type === 'Modification'") {{ instance.workflow_name || '人事信息修改审批流程' }}
      span(v-if="instance.flowable_type === 'Finance::Voucher'") {{ instance.workflow_name || '财务报销系统流程' }}
      span(v-if="instance.flowable_type === 'Finance::LoanVoucher'") {{ instance.workflow_name || '财务报销系统流程' }}
      span(v-else) {{ instance.workflow_name }}
    .right
      //- PopoverConfirm.actions(
      //-   v-if='showActions'
      //-   title="删除"
      //-   content="您确认要删除此申请吗？"
      //-   placement="bottomRight"
      //-   @confirm="onDelete")
      //-   IconTooltip(icon="delete" tips="删除")
      a-tag.state(:color="stateMap[instance.state].color")
        | {{ stateMap[instance.state].label }}
  .user-name {{ instance.creator_name }}
  .summary.infos(v-if="summary")
    span {{ summary }}
  .infos
    template(v-if="instance.creator_type === 'Student'")
      span
        | 学号：{{ instance.creator_code }}
    template(v-else)
      span
        | 工号：{{ instance.creator_code }}
    span
      | 院部：{{ (instance.creator_department_path || []).join(' / ') }} / {{ instance.creator_department_name }}
    span
      | 编号：{{ instance.seq }}
    span
      | 申请时间：{{ instance.created_at | format }}
  .infos(v-if="instance.last_token")
    span 当前阶段：{{ instance.last_token.name }}
    span 操作者：{{ instance.last_token.operator_name }}
    span 状态：{{ stateMap[instance.last_token.state].label }}
</template>

<style lang="stylus" scoped>
.instance-cell
  margin-bottom 12px
  padding 14px 12px
  border 1px solid rgba(0, 0, 0, 0.08)
  border-radius 4px
  background rgba(255, 255, 255, 1)
  &:hover
    border-color #3DA8F5
    cursor pointer
    .header .right .actions
      display block
  .header
    display flex
    justify-content space-between
    align-items center
    margin-bottom 12px
    .workflow-name
      display flex
      align-items center
      height 20px
      color rgba(56, 56, 56, 1)
      font-size 14px
      line-height 20px
      .icon
        display inline-block
        margin-right 8px
    .right
      display inline-flex
      align-items center
      .actions
        display none
        margin-right 14px
  .user-name
    margin-bottom 8px
    height 20px
    color rgba(56, 56, 56, 1)
    font-weight 500
    font-size 14px
    line-height 20px
  .infos
    color rgba(128, 128, 128, 1)
    font-weight 400
    font-size 14px
    line-height 20px
    span
      margin-right 30px
</style>
