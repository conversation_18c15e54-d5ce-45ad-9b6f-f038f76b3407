<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { bpmUserInstanceEditStore, bpmUserInstanceStore } from '@/store/modules/bpm/user/instance.store';
import {
  TaIndexSearcherOptionInterface,
  TaIndexViewConfigInterface,
  TaIndexViewTabInterface,
} from '@/components/global/TaIndex';
import { IInstance } from '@/models/bpm/instance';
import BpmInstanceCell from '@/components/bpm/InstanceCell.vue';
import InstanceDetailDialog from '@/components/bpm/InstanceDetailDialog.vue';
import { bpmUserWorkflowStore } from '@/store/modules/bpm/user/workflow.store';
import { IWorkflow } from '@/models/bpm/workflow';
import { IFormTemplateItem } from '@/interfaces/formTemplate.interface';
import TaIndexView from '@/components/global/TaIndexView.vue';
import { AxiosResponse } from 'axios';
import { ActiveStore } from '@/lib/ActiveStore';

@Component({
  components: {
    BpmInstanceCell,
    InstanceDetailDialog,
  },
})
export default class ComInstanceList extends Vue {
  @Prop({ type: String, default: '申请列表' }) title!: string;
  @Prop({ type: Object, required: true }) store!: ActiveStore;

  activeRecord: Partial<IInstance> = {};
  visibleDetail = false;

  searcherSimpleOptions: TaIndexSearcherOptionInterface[] = [
    { label: '单号', key: 'seq', type: 'string' },
    { label: '名称', key: 'workflow_name', type: 'string' },
  ];

  get config(): TaIndexViewConfigInterface {
    return {
      recordName: '申请',
      store: this.store,
      searcherSimpleOptions: this.searcherSimpleOptions,
    };
  }

  get tabs(): TaIndexViewTabInterface[] {
    const { id, type } = this.$store.state.currentUser;
    return [
      { label: '待我提交', key: 'todo', query: { todo: [id, type] } },
      { label: '待我审批', key: 'approving', query: { approving: [id, type] } },
      { label: '我发起的', key: 'created', query: { created: [id, type] } },
      { label: '我已审批', key: 'approved', query: { approved: [id, type] } },
      { label: '抄送我的', key: 'notified', query: { notified: [id, type] } },
    ];
  }

  onShow(record: IInstance) {
    this.activeRecord = record;
    this.visibleDetail = true;
  }
}
</script>

<template lang="pug">
.com-instance-list
  TaTitleHeader(:title='title')
  TaIndexView(ref='taIndexView', :config='config', :tabs='tabs', :tabsLeftMargin='0')
    template(#card='{ record }')
      BpmInstanceCell.cell(:instance='record', :showActions='false', @click='onShow(record)')
    template(#right-actions)
      slot(name='right-actions')
  InstanceDetailDialog(v-model='visibleDetail', :instanceId='activeRecord.id')
</template>

<style lang="stylus" scoped>
.cell
  margin 10px 0 0 0
</style>
