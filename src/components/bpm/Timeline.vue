<script lang="ts">
/**
 * 流程审批操作及审批记录
 * Events:
 *  change 审批发生变更
 *  print 打印
 *  flowableEdit 编辑管理的 flowable 对象
 */
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import FlowTreeViewer from '../flowTree/FlowTreeViewer.vue';
import instanceModel, { IInstance, InstanceType } from '@/models/bpm/instance';
import workflowModel, { userWorkflow, IWorkflow, ITokenFormResponse } from '@/models/bpm/workflow';
import tokenModel, { IToken, TokenTypes } from '@/models/bpm/token';
import UserField from '@/components/form/UserField.vue';
import UserManyField from '@/components/form/UserManyField.vue';
import { IFormTemplateItem } from '@/interfaces/formTemplate.interface';
import BpmFormEditor from '@/components/bpm/BpmFormEditor.vue';
import InstanceActions from './InstanceActions.vue';
import TemplateFormatter from '../form/TemplateFormatter.vue';
import { log } from 'util';

@Component({
  components: {
    UserField,
    UserManyField,
    FlowTreeViewer,
    BpmFormEditor,
    InstanceActions,
    TemplateFormatter,
  },
})
export default class InstanceTimeline extends Vue {
  @Prop({ type: Object, default: () => ({ tokens: [], current_token: {} }), required: true }) instance!: IInstance;
  @Prop({
    type: Object,
    default: () => ({
      workflow: { id: undefined },
      formTemplate: [],
      formEditable: false,
    }),
    required: true,
  })
  tokenFormResponse!: ITokenFormResponse;

  workflow: IWorkflow = { id: undefined };
  // form
  formEditable: boolean = false;
  formTemplate: IFormTemplateItem[] = [];
  formVisible: boolean = false;
  errors: IObject[] = [];
  isValidate: boolean = false;
  // 选择人员
  assignUsers: any[] = [];
  assignVisible: boolean = false;
  loading: boolean = false;
  // other
  flowTreeVisible: boolean = false;
  formattedTemplate: IFormTemplateItem[] = [];

  get stateMap() {
    return instanceModel.stateMap;
  }
  get formatedTokens() {
    return (this.instance.tokens || [])
      .filter(
        t => (t.type === TokenTypes.ApprovalSelect && t.state !== 'completed') || t.type !== TokenTypes.ApprovalSelect,
      )
      .map(t => ({
        ...t,
        extra: this.getTokenIdentify(t),
      }));
  }
  get currentToken(): IToken {
    return this.instance.current_token || {};
  }
  get enableActionsMap() {
    return this.instance._enableActionsMap || { transition: {} };
  }
  get tokenUsers() {
    return (this.currentToken.options || { users: [] }).users;
  }
  // template form
  get formData() {
    return this.instance.payload || {};
  }
  get TokenTypes() {
    return TokenTypes;
  }

  @Watch('tokenFormResponse')
  onTokenResponseChange() {
    tokenModel.setRole('user');
    workflowModel.setRole('user');
    this.initData();
  }

  @Watch('formattedTemplate')
  onFormatedTemplateChange() {
    this.validateForm();
  }

  @Watch('formData')
  onFormDataChange() {
    this.validateForm();
  }

  mounted() {
    this.initData();
  }

  initData() {
    if (!this.instance.id) return;

    try {
      const { workflow, formTemplate, formEditable } = this.tokenFormResponse;
      this.workflow = workflow;
      this.formTemplate = formTemplate;
      this.formEditable = (this.enableActionsMap.submit || this.enableActionsMap.accept) && formEditable;
      this.validateForm();
    } catch (error) {
      this.$message.error('表单验证出现异常');
    }
  }

  validateForm() {
    this.isValidate = false;

    workflowModel.validateForm(this.formData, this.formattedTemplate, {
      success: () => {
        this.errors = [];
        this.isValidate = true;
      },
      fail: errors => {
        console.log(errors, 'errors');
        this.errors = errors;
        this.isValidate = false;
      },
    });
  }
  getTokenIdentify(token: IToken) {
    const { type, state, previous_id: previousId } = token;
    let extra: IObject = { kind: 'approval', title: '审批', icon: 'check-circle' };

    if (type === TokenTypes.Token && previousId) {
      extra = { kind: 'end', title: '结束', icon: 'check-circle' };
    } else if (type === TokenTypes.Submit) {
      extra = { kind: 'start', title: '提交', icon: 'play-circle' };
    } else if (type === TokenTypes.Notify) {
      extra = { kind: 'notify', title: '抄送', icon: 'notification' };
    } else if (type === TokenTypes.ApprovalNotify) {
      extra = { kind: 'notify', title: '抄送', icon: 'notification' };
    } else if (type === TokenTypes.Approval && state === 'preparing') {
      extra = { kind: 'assign', title: '指派', icon: 'team' };
    } else if (type === TokenTypes.Approval && state === 'failed') {
      extra = { kind: 'approval', title: '审批', icon: 'close-circle' };
    } else if (type === TokenTypes.Approval && state === 'rejected') {
      extra = { kind: 'approval', title: '审批', icon: 'close-circle' };
    } else if (type === TokenTypes.Approval && state === 'canceld') {
      extra = { kind: 'approval', title: '审批', icon: 'close-circle' };
    }

    extra.color = this.stateMap[token.state!].color;
    return extra;
  }

  // 选择单个审批人
  async assignSingleUser() {
    try {
      if (this.loading) return;
      this.loading = true;
      await tokenModel.assign(this.currentToken.id, {
        operator_id: this.assignUsers[0].id,
        operator_type: this.assignUsers[0].type || 'Teacher',
      });
      this.loading = false;
      this.assignVisible = false;
      this.$message.success('指派成功');
      this.onChange();
    } catch (error) {
      this.loading = false;
    }
  }

  async assignManyUsers() {
    try {
      if (this.loading) return;
      this.loading = true;
      const currentUser = this.$store.state.currentUser;
      await tokenModel.assign(this.currentToken.id, {
        operator_id: currentUser.id,
        operator_type: currentUser.type || 'Teacher',
        options: {
          users: this.assignUsers.map(user => ({
            ...this.$utils.only(user, ['id', 'code', 'name', 'type']),
          })),
        },
      });
      this.loading = false;
      this.assignVisible = false;
      this.$message.success('指派成功');
      this.onChange();
    } catch (error) {
      this.loading = false;
    }
  }

  onChange() {
    this.$emit('change');
  }
  print() {
    this.$emit('print', this.instance);
  }
  flowableEdit() {
    this.$emit('flowableEdit', this.instance);
  }
}
</script>

<template lang="pug">
.tokens-container
  TemplateFormatter(
    ref='TemplateFormatter',
    v-model='formattedTemplate'
    :payload='formData'
    :template='formTemplate'
  )
  .tokens-content
    TextButton.workflow-button(icon='eye', @click='flowTreeVisible = true')
      | 查看流程图
    a-timeline.tokens-timeline
      a-timeline-item(v-if='token.type !== "Tokens::Condition"', v-for='token in formatedTokens', :key='token.id')
        //- Icon
        a-icon.status-icon(slot='dot', :type='token.extra.icon', :style='{ color: token.extra.color }')
        //- Content
        .timeline-content
          .title {{ token.name || token.extra.title }}
          .time
            span {{ token.updated_at | format }}
            a-tag.tag(:color='stateMap[token.state].color')
              | {{ stateMap[token.state].label }}
          .token(:class='token.state')
            template(
              v-if='token.type === TokenTypes.ApprovalNotify && token.state !== "completed"'
            )
              a-avatar.photo.avatar(:src='token.operator_avatar', :size='24')
                | {{ token.operator_name }}
              .action-info
                .operator {{ token.operator_name || "系统" }}
                .actions
                  .action-desc
                    span 请选择之后的抄送人，点击
                    span.action(@click='assignVisible = true')
                      | 选择抄送人
            //- 抄送
            template(v-else-if='token.extra.kind === "notify"')
              .notify
                a-tag.notify-user(color='blue', v-for='user in (token.options.users || [])', :key='user.id')
                  | {{ user.name }}
            //- 审批
            template(v-else)
              a-avatar.photo.avatar(:src='token.operator_avatar', :size='24')
                | {{ token.operator_name }}
              .action-info
                .operator {{ token.operator_name || "系统" }}
                .comment(v-if='token.comment') {{ token.comment }}
                .comment(v-if='token.options && token.options.publish_at')
                  | 发布时间：{{ token.options.publish_at | format }}
                .actions(v-if='token.id === currentToken.id')
                  template(v-if='enableActionsMap.edit')
                    template(v-if='formEditable')
                      //- 表单可编辑，验证通过
                      .action-desc(v-if='isValidate')
                        span 修改提交资料，可点击
                        span.action(@click='formVisible = true') 编辑
                      //- 表单可编辑，验证失败
                      .action-desc(v-else)
                        span 还有 {{ errors.length }} 个内容待完善，点击
                        span.action(@click='formVisible = true') 编辑
                    //- flowable 对象编辑
                    template(v-else)
                      .action-desc(v-if='instance.flowable_id')
                        span 修改关联内容，可点击
                        span.action(@click='flowableEdit') 编辑
                  //- 指派审批人
                  template(v-if='enableActionsMap.assign')
                    .action-desc
                      span 请选择之后的审批人，点击
                      span.action(@click='assignVisible = true')
                        | 选择审批人

  .footer
    //- 当表单可编辑的时候，不显示 flowable edit, 造成疑惑
    InstanceActions(:formValid='isValidate', :instance='instance', @change='onChange', @print='print')

  a-modal(
    v-if='currentToken.type === TokenTypes.Approval',
    title='选择审批人',
    v-model='assignVisible',
    :confirmLoading='loading',
    @ok='assignSingleUser',
    :width='360'
  )
    UserField(v-model='assignUsers', :multiple='false', :defaultOperators='tokenUsers')

  a-modal(
    v-if='currentToken.type === TokenTypes.ApprovalSelect',
    title='选择审批人',
    v-model='assignVisible',
    :confirmLoading='loading',
    @ok='assignManyUsers',
    :width='360'
  )
    UserField(v-model='assignUsers', :multiple='true', :defaultOperators='tokenUsers')

  a-modal(
    v-if='currentToken.type === TokenTypes.ApprovalNotify',
    title='选择抄送人',
    v-model='assignVisible',
    :confirmLoading='loading',
    @ok='assignManyUsers',
    :width='360'
  )
    UserManyField(v-model='assignUsers', :multiple='true', :defaultOperators='tokenUsers')

  FlowTreeViewer(v-model='flowTreeVisible', :workflow='workflow')

  BpmFormEditor(
    v-model='formVisible',
    :title='workflow.name',
    :workflow='workflow',
    :instance='instance',
    @success='onChange'
  )
</template>

<style lang="stylus" scoped>
.status-icon
  font-size 24px

.tokens-container
  display flex
  flex-direction column
  height 100%
  .footer
    display flex
    flex-shrink 0
    justify-content flex-end
    align-items center
    border-top 1px solid #E6E7EB
    &:empty
      display none
  .tokens-content
    overflow auto
    padding 16px
    height 100%
    .workflow-button
      margin-bottom 16px
    .tokens-timeline
      padding 5px 0 0 5px
      .timeline-content
        padding-left 8px
        .title
          color #000
          font-size 14px
          line-height 22px
        .time
          display flex
          justify-content space-between
          align-items center
          margin-bottom 6px
          color #000
          line-height 20px
          span
            font-size 14px
          .tag
            padding 0 6px
            height 20px
            font-size 12px
            line-height 18px
        .token
          display flex
          padding 8px
          background #F5F5F5
          .notify-user
            margin-top 3px
          .photo
            margin-right 8px
          .action-info
            .operator
              height 24px
              font-weight bold
              line-height 24px
            .comment
              margin 6px 0
              color #A6A6A6
              white-space pre-wrap
              font-size 14px
              line-height 20px
            .actions
              text-align left
              .action-desc
                margin-bottom 6px
                color #808080
                font-size 14px
                line-height 20px
                &:last-child
                  margin-bottom 0
                .action
                  margin 0 4px
                  color #3DA8F5
                  word-break keep-all
                  cursor pointer
                  &:hover
                    color darken(#3DA8F5, 30%)
        .accepted
          background rgb(228, 253, 218)
        .rejected
          background #FEF7E8
        .terminated
          background rgb(255, 236, 228)
</style>
