<script lang="ts">
/**
 * 流程的审批动态
 */
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import instanceStore from '@/store/modules/bpm/instance.store';
import instance, { InstanceType, IInstance } from '@/models/bpm/instance';
import InstanceDetailDialog from '@/components/bpm/InstanceDetailDialog.vue';
import BpmInstanceCell from '@/components/bpm/InstanceCell.vue';
import { bpmAdminInstanceStore } from '@/store/modules/bpm/admin/instance.store';

@Component({
  components: {
    InstanceDetailDialog,
    BpmInstanceCell,
  },
})
export default class BpmAdminInstances extends Vue {
  @Prop({ type: String, default: '动态' }) title!: string;
  @Prop({ type: [Array, String], default: () => [InstanceType.Bpm] }) types!: InstanceType[];
  @Prop({ type: String, default: 'div' }) rootComponent!: 'div' | 'panel';

  activeState: string = 'processing';
  tabs: object[] = [
    { title: '全部', key: 'total' },
    { title: '待提交', key: 'created' },
    { title: '进行中', key: 'processing' },
    { title: '已完成', key: 'completed' },
    { title: '已终止', key: 'terminated' },
  ];
  query: IObject = {};
  variables: string[] = [
    'seq',
    'workflow_name',
    'creator_of_Teacher_type_code',
    'creator_of_Teacher_type_name',
    'creator_of_Student_type_code',
    'creator_of_Student_type_name',
  ];
  instance: IInstance | IObject = {};
  visible: boolean = false;

  get temporaryQuery(): IObject {
    return {
      ...this.query,
      state_eq: this.activeState === 'total' ? '' : this.activeState,
      workflow_id_eq: this.workflowId,
    };
  }

  get workflowId(): number {
    return +this.$route.params.workflowId;
  }
  get instanceStore() {
    return instanceStore;
  }
  get stateMap() {
    return instance.stateMap;
  }
  get isPanel() {
    return this.rootComponent === 'panel';
  }

  created() {
    instance.setRole('user');
  }

  instanceEditStore = bpmAdminInstanceStore;

  mounted() {
    this.fetchRecords();
    this.instanceEditStore.init({
      parents: [{ type: 'workflows', id: this.workflowId }],
    });
  }
  async fetchRecords(page: number = 1, pageSize: number = 10) {
    await instanceStore.fetch({
      page,
      per_page: pageSize,
      q: {
        type_in: this.types,
        state_eq: this.activeState === 'total' ? '' : this.activeState,
        workflow_id_eq: this.workflowId,
        ...this.query,
      },
    });
    const tab = this.tabs.find((t: any) => t.key === this.activeState);
    this.$set(tab!, 'count', instanceStore.totalCount);
  }
  onDateChange(range: any) {
    const [start, end] = range;
    if (start && end) {
      this.query.created_at_gteq = start.toString();
      this.query.created_at_lteq = end.toString();
    } else {
      this.query.created_at_gteq = null;
      this.query.created_at_lteq = null;
    }
    this.fetchRecords(1);
  }
  onShow(record: IInstance) {
    this.instance = record;
    this.visible = true;
  }
  onFlowableEdit() {
    this.$message.warning('');
  }
}
</script>

<template lang="pug">
component.container(:is="rootComponent" :class="{ 'instance-panel': isPanel }")
  template(v-if="isPanel" slot="header")
    StepToolbar(
      v-model="activeState"
      mode="tabs"
      :steps="tabs"
      @change="fetchRecords(1)")
      Searcher.header-item(
        v-model="query"
        :variables="variables"
        placeholder="搜索编号、申请人、工号、流程"
        tips="检索动态"
        @change="fetchRecords(1)")
      a-range-picker.date-item(
        @change="onDateChange"
        format="YYYY-MM-DD")
      TaExport(:store='instanceEditStore', :temporaryQuery='temporaryQuery')

  template(v-else)
    TaTitleHeader.toolbar(:title="title")
      Searcher.header-item(
        v-model="query"
        :variables="variables"
        placeholder="搜索编号、申请人、工号、流程"
        tips="检索动态"
        @change="fetchRecords(1)")
      a-range-picker.date-item(
        @change="onDateChange"
        format="YYYY-MM-DD")
      TaExport(:store='instanceEditStore', :temporaryQuery='{ ...query, workflow_id_eq: workflowId }')
    StepToolbar(
      v-model="activeState"
      mode="tabs"
      :steps="tabs"
      @change="fetchRecords(1)")

  ListView(:store="instanceStore" @change="fetchRecords")
    template(slot-scope="{ record }")
      BpmInstanceCell(:instance="record" @click="onShow(record)")

  InstanceDetailDialog(
    title="审批详情"
    v-model="visible"
    :instanceId="instance.id"
    @flowableEdit="onFlowableEdit")
</template>

<style lang="stylus" scoped>
.instance-panel
  height 100%

.header-item
  margin-left 12px

.date-item
  margin-left 12px
  width 240px
</style>
