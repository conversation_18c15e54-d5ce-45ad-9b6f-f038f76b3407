<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import store from '@/store';
import infoStore from '@/store/modules/info.store';
@Component({
  components: {},
})
export default class componentName extends Vue {
  @Prop({ type: Object, default: () => ({}) }) instance: any;
  @Prop({ type: Object, default: () => ({}) }) workflow: any;

  state: string = '';

  private info: any = {};
  get authRole() {
    return store.state.authRole || 'teacher';
  }
  public mounted() {
    infoStore.setRole(this.authRole);
    this.fetchData();
  }

  public fetchData() {
    infoStore.findAndSetRolePermits();
    this.info = store.state.currentUser || {};
    console.log(this.info);
  }
}
</script>

<template lang="pug">
.com-word-print.flex
  .title.no-2 上海市事业单位工作人员年度考核登记表
  .subtitle.no-4 ( {{$moment().format("YYYY")}} 年度 )
  .table-main.no-4
    .row.flex
      .plaid-1.plaid 姓  名
      .plaid-1.plaid {{ info.name }}
      .plaid-1.plaid 性  别
      .plaid-1.plaid {{info.sex}}
      .plaid-1.plaid 出生日期
      .plaid-1.plaid {{info.birthday}}
    .row.flex
      .plaid-1.plaid 民  族
      .plaid-1.plaid
      .plaid-1.plaid 政治面貌
      .plaid-1.plaid {{info.politics}}
      .plaid-1.plaid 文化程度
      .plaid-1.plaid {{info.degree}}
    .row.flex
      .plaid-1.plaid 单位、部门及职务
      .plaid-2.plaid {{instance.creator_department_path[0]}} {{instance.creator_department_name}}
      .plaid-1.plaid 岗位类别及等级
      .plaid-2.plaid
    .row.flex
      .plaid-1.plaid 任现职时间
      .plaid-1.plaid
      .plaid-1.plaid 从事或分管工作
      .plaid-3.plaid
    .row-2.flex
      .plaid-1.plaid 个<br>人<br>总<br>结
      .plaid-4.plaid.no-6 {{instance.payload.textarea_1608081266667}}
  <br>
  <br>
  <br>
  <br>
  .table-main.no-4
    .row-1.flex
      .plaid-1.plaid 主管领导<br>评语和<br>考核档次<br>建 议
      .plaid-4.plaid.no-6
        .signature
          span.signature-unit 签名：
          span.signature-unit 年
          span.signature-unit 月
          span.signature-unit 日
    .row-1.flex
      .plaid-1.plaid 党政领导<br>班子集体<br>考核档次<br>意 见
      .plaid-4.plaid.no-6
        .signature
          span.signature-unit 盖章：
          span.signature-unit 年
          span.signature-unit 月
          span.signature-unit 日
    .row-1.flex
      .plaid-1.plaid 被考核人<br>意 见
      .plaid-4.plaid.no-6
        .signature
          span.signature-unit 签名：
          span.signature-unit 年
          span.signature-unit 月
          span.signature-unit 日
    .row-1.flex
      .plaid-1.plaid 未确定档<br>次或不参<br>加考核情<br>况 说 明
      .plaid-4.plaid.no-6
        .signature
          span.signature-unit 盖章或签名：
          span.signature-unit 年
          span.signature-unit 月
          span.signature-unit 日
  .ending.no-4 中共上海市委组织部、上海市人力资源和社会保障局印制

</template>

<style lang="stylus" scoped>
.flex
  display flex
  align-items center
.plaid-1
  height 100%
  width 150px
  min-width 150px
  min-height 50px
  border-right 1px solid #262626
  text-align center
  word-wrap:break-word
  display flex
  align-items center
  justify-content center
.plaid-2
  height 100%
  width 300px
  min-height 50px
  border-right 1px solid #262626
  text-align center
  word-wrap:break-word
  display flex
  align-items center
  justify-content center
.plaid-3
  height 100%
  width 450px
  min-height 50px
  border-right 1px solid #262626
  text-align left
  word-wrap:break-word
  display flex
  align-items center
  justify-content center
.plaid-4
  height 100%
  width 100%
  width 750px
  min-height 50px
  border-right 1px solid #262626
  text-align left
  word-wrap:break-word
  display flex
  // align-items center
  justify-content center
  padding 40px 40px !important
  letter-spacing 2px
.no-4
  font-size 24px
  line-height 30px
.no-2
  font-size 32px
  line-height 40px
.no-6
  font-size 18px
  line-height 26px
.row
  height 100px
  border-bottom 1px solid #262626
.row-1
  height 250px
  border-bottom 1px solid #262626
.row-2
  height 1000px
  border-bottom 1px solid #262626
.com-word-print
  flex-direction column
  .title
    margin-bottom 12px
  .subtitle
    margin-bottom 12px
  .table-main
    border 1px solid #262626
    border-bottom 0px
    .signature
      position absolute
      bottom 10px
      right 20px
      .signature-unit
        margin-right 30px
    .row
      .plaid
        position relative
        padding 0 4px
        &:last-of-type
          border-right 0px
    .row-1
      .plaid
        position relative
        padding 0 4px
        &:last-of-type
          border-right 0px
    .row-2
      .plaid
        position relative
        padding 0 4px
        &:last-of-type
          border-right 0px
  .ending
    width 600px
    text-align right
    line-height 80px
</style>
