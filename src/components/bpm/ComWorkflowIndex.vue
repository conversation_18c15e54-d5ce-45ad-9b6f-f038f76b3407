<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import workflowStore from '@/store/modules/bpm/workflow.store';
import { IWorkflow, WorkflowTypes } from '@/models/bpm/workflow';
import ComWorkflowCell from './ComWorkflowCell.vue';

interface IGroup {
  key: string;
  apps: IWorkflow[];
}

@Component({
  components: {
    ComWorkflowCell,
  },
})
export default class ComWorkflowsIndex extends Vue {
  // 带分组的 workflow index
  @Prop({ type: Object, default: () => ({ modul_eq: '业务流程' }) }) private defaultQuery!: IObject;
  queryObject: IObject = {};
  groups: IGroup[] = [];

  get workflowStore() {
    return workflowStore;
  }

  created() {
    workflowStore.setRole('user');
  }

  mounted() {
    this.fetch();
  }

  async fetch(page: number = 1) {
    const { data } = await workflowStore.fetch({
      page,
      per_page: 999999,
      q: {
        type_eq: WorkflowTypes.Bpm,
        state_eq: 'done',
        ...this.queryObject,
        ...this.defaultQuery,
      },
    });
    if (workflowStore.records.length) {
      const cache = this.$utils.groupBy(workflowStore.records, (record: IWorkflow) => record.catalog || '其他');
      const otherGroups: IGroup[] = [{ key: '其他', apps: cache['其他'] }];
      delete cache['其他'];
      const mainGroups: IGroup[] = Object.entries(cache).map((ary: any) => ({ key: ary[0], apps: ary[1] }));
      this.groups = mainGroups.concat(otherGroups);
    } else {
      this.groups = [];
    }
  }
  show(workflow: IWorkflow) {
    this.$router.push(`/bpm/user/workflows/${workflow.id}`);
  }
}
</script>

<template lang="pug">
.container
  TaTitleHeader.toolbar(title="业务申请")
    Searcher(
      v-model="queryObject"
      :variables="['name']"
      tips="检索业务"
      @change="fetch(1)")
  .content(v-loading="workflowStore.loading")
    Empty(type="school" desc="暂无业务申请" v-if="workflowStore.records.length === 0 && !workflowStore.loading")
    .group(v-for="group in groups" :key="group.key")
      .group-name {{ group.key }}
      a-row(:gutter="16")
        a-col(:span="12" v-for="workflow in group.apps" :key="workflow.id")
          .workflow(@click="show(workflow)")
            ComWorkflowCell(:workflow='workflow')
</template>

<style lang="stylus" scoped>
.container
  .content
    display block
    .group
      padding 20px 0 12px
      border-bottom 1px solid #E8E8E8
      &:last-child
        border-bottom none
      .group-name
        margin-bottom 16px
        height 20px
        color rgba(56, 56, 56, 1)
        font-weight 500
        font-size 14px
        line-height 20px
</style>
