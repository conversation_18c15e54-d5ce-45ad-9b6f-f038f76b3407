<script lang="ts">
import { IWorkflow } from '@/models/bpm/workflow';
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class ComWorkflowCell extends Vue {
  @Prop({ type: Object, required: true }) workflow!: IWorkflow;
}
</script>

<template lang="pug">
.com-workflow-cell
  img.icon(src="@/assets/images/bpm/app.png")
  .title.text-ellipsis {{ workflow.name }}
  .date.text-ellipsis 最新更新 {{ workflow.updated_at | format }}
</template>

<style lang="stylus" scoped>
.com-workflow-cell
  position relative
  margin-bottom 12px
  padding 15px 16px
  padding-left 68px
  height 70px
  border-radius 3px
  background rgba(250, 250, 250, 1)
  &:hover
    background rgba(255, 255, 255, 1)
    box-shadow 0px 0px 4px 0px rgba(0, 0, 0, 0.1)
    cursor pointer
    .title
      color #3DA8F5
  .icon
    position absolute
    top 15px
    left 16px
    width 40px
    height 40px
  .title
    height 20px
    color rgba(56, 56, 56, 1)
    font-weight 500
    line-height 20px
  .date
    height 20px
    color rgba(166, 166, 166, 1)
    font-weight 400
    line-height 20px
</style>
