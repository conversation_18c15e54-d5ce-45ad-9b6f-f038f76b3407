<script lang="ts">
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import { Student, IStudent } from '@/models/res/student';

@Component
export default class StudentSelector extends Vue {
  confirmLoading: boolean = false;
  currentPage: number = 1;
  totalCount: number = 1;
  perPage: number = 10;
  members: IStudent[] = [];
  query: IObject = {
    //name_or_code_or_department_name_cont_any: '',
    s: [],
  };
  sorter: IObject = {};
  loading: boolean = false;
  // filter
  timer: any = null;
  // 选择
  selectedMembers: IStudent[] = [];
  selectedMemberIds: number[] = [];

  @Model('input', { type: Boolean }) readonly visible!: boolean;
  @Prop({ type: Array, default: () => [] }) readonly defaultStudents!: IStudent[];
  @Prop({ type: Boolean, default: true }) readonly multiple!: boolean;

  get rowSelection() {
    return this.multiple
      ? {
          selectedRowKeys: this.selectedMemberIds,
          onChange: this.selectionChange,
        }
      : null;
  }
  get pagination() {
    return {
      current: this.currentPage,
      pageSize: this.perPage,
      total: this.totalCount,
      showQuickJumper: true,
      hideOnSinglePage: false,
      showSizeChanger: true,
      pageSizeOptions: ['10', '20', '30', '40', '50', '100'],
    };
  }
  get dialogWidth() {
    return this.multiple ? '1100px' : '850px';
  }

  @Watch('visible')
  onVisibleChange(val: boolean) {
    if (val) {
      this.init();
    }
  }

  async init() {
    await this.fetchMembers();
    this.setCheckedRows();
  }
  async fetchMembers(page = this.currentPage, pageSize: number = this.perPage) {
    try {
      this.loading = true;
      this.perPage = pageSize;
      const studentModel = new Student();
      const { data } = await studentModel.index({
        page,
        per_page: pageSize,
        q: {
          ...this.query,
        },
      });
      this.currentPage = data.current_page;
      this.totalCount = data.total_count;
      this.members = data.students;
      this.loading = false;
    } catch (error) {
      this.loading = false;
    }
  }
  setCheckedRows() {
    this.selectedMemberIds = this.defaultStudents.map(o => o.id!);
    this.selectedMembers = this.defaultStudents.concat();
  }
  // tree
  onSearch(e: any) {
    window.clearTimeout(this.timer);
    this.timer = window.setTimeout(() => {
      const key = ['name', 'code', 'major_name', 'department_name', 'adminclass_name'].join('_or_') + '_cont_any';
      if (e.target.value) {
        const keywords = this.$utils.parseStringToArray(e.target.value);
        this.query[key] = keywords;
      } else {
        this.query[key] = '';
      }
      this.fetchMembers(1);
    }, 800);
  }
  // table
  tableChange(pagination: any, filters: any, sorter: any) {
    if (this.perPage !== pagination.pageSize) {
      this.fetchMembers(pagination.current, pagination.pageSize);
    } else if (this.currentPage && pagination.current !== this.currentPage) {
      this.fetchMembers(pagination.current);
    } else if (this.sorter.order !== sorter.order || this.sorter.field !== sorter.field) {
      this.sorterChange(sorter);
      this.fetchMembers(1);
    } else {
      this.filterChange(filters);
      this.fetchMembers(1);
    }
  }
  // 表格列排序，更新 query 对象
  sorterChange(sorter: any) {
    this.sorter = { ...sorter };
    let ransackQuerySort = ['id desc'];
    if (sorter.order) {
      ransackQuerySort = [`${sorter.field} ${sorter.order === 'descend' ? 'desc' : 'asc'}`];
      this.query.s = ransackQuerySort;
    } else {
      const index = (this.query.s || []).find((o: any) => o.includes(sorter.field));
      this.query.s.splice(index, 1);
    }
  }
  // 表格列过滤，更新 query 对象
  filterChange(filters: any) {
    this.query = {
      ...this.query,
      ...Object.keys(filters || {}).reduce(
        (obj: any, key) => ({
          ...obj,
          [`${key}_in`]: filters[key],
        }),
        {},
      ),
    };
  }
  selectionChange(ids: number[], members: any[]) {
    this.selectedMemberIds = ids;
    const allMembersMap = this.selectedMembers.concat(members).reduce(
      (obj, item) => ({
        ...obj,
        [`${item.id}`]: item,
      }),
      {},
    );
    this.selectedMembers = Object.values(allMembersMap).filter((o: any) => ids.includes(o.id)) as IStudent[];
  }
  removeTeacher(index: number) {
    this.selectedMembers.splice(index, 1);
    this.selectedMemberIds.splice(index, 1);
  }
  // modal
  chooseMember(member: IObject) {
    this.$emit('change', [member], [member.id]);
    this.close();
  }
  handleOk() {
    this.$emit('change', this.selectedMembers, this.selectedMemberIds);
    this.close();
  }
  close() {
    this.$emit('input', false);
  }
  clearSelectedMembers() {
    this.selectedMembers = [];
    this.selectedMemberIds = [];
  }
}
</script>

<template lang="pug">
a-modal(
  title="选择学生"
  :visible="visible"
  :width="dialogWidth"
  okText="确认"
  cancelText="取消"
  @ok="handleOk"
  @cancel="close"
  :centered="true"
  :footer="multiple ? undefined : null"
  :zIndex="1001"
  :comfirmLoading="confirmLoading")
  .member-selector
    //- =============== 人员列表 ===============
    .member-box.data-box
      .title(v-if="multiple")
        | 学生列表：
      .content
        a-input-search(
          placeholder="搜索 学号、姓名、班级"
          @change="onSearch")
        .members
          a-table(
            :dataSource="members"
            :rowSelection="rowSelection"
            :pagination="pagination"
            :loading="loading"
            @change="tableChange"
            :scroll="{ y: 346, x: 680 }"
            rowKey="id")
            a-table-column(title="姓名" dataIndex="name" key="name" :width="80")
            a-table-column(title="学号" dataIndex="code" key="code" :sorter="true" :width="160")
            a-table-column(title="班级" key="adminclass_name" dataIndex="adminclass_name" :width="120")
            a-table-column(title="专业" key="major_name" dataIndex="major_name" :width="120")
            a-table-column(title="学院" key="college_name" dataIndex="college_name" :width="120")
            a-table-column(title="选择" :width="80" v-if="!multiple")
              template(slot-scope="text, record, index")
                a-button(type="primary" size="small" @click.stop="chooseMember(record, index)")
                  | 选择
    //- =============== 已选 ===============
    .member-box(v-if="multiple")
      .title.flex-between
        span 已经选择（{{ selectedMembers.length }}）：
        PopoverConfirm(
          v-if="selectedMembers.length"
          title="提醒"
          content="确认要情况已选学生列表吗？"
          @confirm="clearSelectedMembers")
          TextButton.text-warning
            | 清空
      .content
        .members
          .member(v-for="(student, index) in selectedMembers" :key="student.id")
            .info
              | {{ student.name }}
            a-icon.close(type="close-circle" @click="removeTeacher(index)")
</template>

<style lang="stylus" scoped>
.member-selector
  display flex
  margin -16px -10px
  .member-box
    padding 0 5px
    width 100%
    .title
      height 32px
      font-weight bold
      font-size 16px
      line-height 32px
    .content
      display flex
      flex-direction column
      padding 10px
      height 500px
      border 1px solid #dedede
      background #f4f6f8
      .members
        overflow auto
        margin-bottom -10px
        height 100%
        .checkbox-group
          width 100%
        .student
          margin-left 0
          padding 8px
          width 100%
          cursor pointer
          &:hover
            background #eee
      .member
        display flex
        justify-content space-between
        align-items center
        margin-bottom 4px
        padding 6px
        border-radius 4px
        background #fff
        color #888
        line-height 24px
        cursor pointer
        &:hover
          background #ECEFF3
        .close
          cursor pointer
          &:hover
            color red
  .data-box
    flex-shrink 0
    width 800px
    .columns
      display inline-flex
      align-items baseline
      width 500px
      .col
        width 100%
</style>
