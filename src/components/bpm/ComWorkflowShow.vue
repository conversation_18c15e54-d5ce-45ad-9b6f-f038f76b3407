<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import workflowStore from '@/store/modules/bpm/workflow.store';
import instanceModel, { InstanceType, IInstance } from '@/models/bpm/instance';
import instanceStore from '@/store/modules/bpm/instance.store';
import BpmFormEditor from '@/components/bpm/BpmFormEditor.vue';
import instance from '@/models/bpm/instance';
import ComBpmInstanceCard from '@/components/bpm/ComBpmInstanceCard.vue';
import { bpmUserInstanceEditStore } from '@/store/modules/bpm/user/instance.store';

@Component({
  components: {
    ComBpmInstanceCard,
    BpmFormEditor,
  },
})
export default class ComWorkflowShow extends Vue {
  @Prop({ type: String, default: '/bpm/user/workflows' }) backUrl!: string;

  breadcrumbs: object[] = [];
  currentMenus: string[] = ['todo'];
  query: IObject = {};
  variables: string[] = ['seq', 'creator_of_Teacher_type_code', 'creator_of_Teacher_type_name', 'summary'];
  menuObject: IObject = {
    todo: { icon: 'clock-circle', text: '待我提交', key: 'todo', count: 0 },
    approving: { icon: 'issues-close', text: '待我审批', key: 'approving', count: 0 },
    created: { icon: 'file-add', text: '我发起的', key: 'created', count: 0 },
    approved: { icon: 'check-circle', text: '我已审批', key: 'approved', count: 0 },
    notified: { icon: 'notification', text: '抄送我的', key: 'notified', count: 0 },
  };
  activeQuery: IObject = {};
  loadingExport = false;
  // detail
  instance: IInstance = {
    current_token: {},
  };
  visible: boolean = false;
  // form
  formVisible: Boolean = false;

  get workflowId(): number {
    return +this.$route.params.id;
  }
  get workflow() {
    return workflowStore.record;
  }
  get formTemplate() {
    return this.workflow.form ? this.workflow.form.fields : [];
  }
  get instanceStore() {
    return instanceStore;
  }
  get activeState() {
    return this.currentMenus[0] || 'todo';
  }
  get title() {
    return this.menuObject[this.activeState].text;
  }

  @Watch('activeState')
  onStateChange() {
    this.fetchInstances(1);
  }

  created() {
    workflowStore.setRole('user');
    instanceStore.setRole('user');
  }

  instanceNewStore = bpmUserInstanceEditStore;

  mounted() {
    this.fetchWorkflow();
    this.fetchInstances();
    this.instanceNewStore.init({
      parents: [{ type: 'workflows', id: this.workflowId }],
    });
  }
  async fetchWorkflow() {
    await workflowStore.find(this.workflowId);
    this.breadcrumbs = [{ title: '流程列表', url: this.backUrl }, { title: this.workflow.name }];
  }
  async fetchInstances(page: number = 1, pageSize: number = 10) {
    const { id, type } = this.$store.state.currentUser;
    this.activeQuery = {
      type: InstanceType.Bpm,
      [this.activeState]: [id, type],
      ...this.query,
    };
    await instanceStore.fetchByParent({
      parentId: this.workflowId,
      page,
      per_page: pageSize,
      q: this.activeQuery,
    });
    this.fetchStatistic();
  }
  async fetchStatistic() {
    if (!this.workflow.id) return;
    const { data } = await instanceModel.statistic(InstanceType.Bpm, +this.workflow.id);
    Object.values(this.menuObject).forEach((menu: any) => {
      menu.count = data.statistic[menu.key];
    });
  }
  onDateChange(range: any) {
    const [start, end] = range;
    if (start && end) {
      this.query.created_at_gteq = start.toString();
      this.query.created_at_lteq = end.toString();
    } else {
      this.query.created_at_gteq = null;
      this.query.created_at_lteq = null;
    }
    this.fetchInstances(1);
  }
  onShow(record: IInstance) {
    this.instance = record;
    this.visible = true;
  }
  newInstance() {
    this.instance = {};
    this.formVisible = true;
  }
  onSave(instance: IInstance) {
    this.formVisible = false;
    this.currentMenus = ['created'];
    this.fetchInstances(1);
    this.onShow(instance);
  }
  async onExport() {
    this.loadingExport = true;
    const { data } = await instance.export({ q: { ...this.activeQuery, workflow_id_eq: this.workflowId } });
    this.loadingExport = false;
    window.open(data.url);
  }
}
</script>

<template lang="pug">
.workflows-container
  StepToolbar.toolbar(mode='tabs', :breadcrumbs='breadcrumbs')
  .wrapper
    Panel.sidebar
      .title 控制台
      a-menu.menu(mode='inline', v-model='currentMenus')
        a-menu-item(:key='menu.key', v-for='menu in Object.values(menuObject)')
          a-icon(:type='menu.icon')
          span {{ menu.text }}
          span(v-if='menu.count') · {{ menu.count }}
      .trigger
        TextButton(icon='plus-circle', theme='filled', @click='newInstance')
          | 发起申请
    Panel.content(:title='title', :bordered='false')
      template(slot='actions')
        Searcher(
          v-model='query',
          :variables='variables',
          placeholder='搜索编号、申请人、工号、概要',
          tips='检索记录',
          @change='fetchInstances(1)'
        )
        a-range-picker.date-item(@change='onDateChange', format='YYYY-MM-DD')
        TaExport(:store='instanceNewStore', :temporaryQuery='{ ...activeQuery, workflow_id_eq: workflowId }')
        //- TextButton(:icon='loadingExport ? "loading" : "download"', v-if='activeState !== "todo"', @click='onExport')
        //-   | 导出
      ListView(:store='instanceStore', @change='fetchInstances')
        template(slot-scope='{ record }')
          ComBpmInstanceCard(:instance='record', :key='record.id')
            template
              slot(name='dialog', :flowable='instance.flowable_info')

  BpmFormEditor(
    v-model='formVisible',
    :title='workflow.name',
    :workflow='workflow',
    :instance='instance',
    @success='onSave'
  )
</template>

<style lang="stylus" scoped>
.workflows-container
  position relative
  overflow auto
  padding-top 48px
  width 100%
  height 100%
  background #f5f5f5
  .toolbar
    position absolute
    top 0
    left 0
    z-index 100
    margin-bottom 0
    width 100%
    background-color #f5f5f5
  .wrapper
    display flex
    padding 16px 60px 0
    width 100%
    height 100%
    .sidebar
      flex-shrink 0
      overflow auto
      margin-right 16px
      padding 10px 0
      width 280px
      height 100%
      .title
        padding 10px 20px
        color rgba(166, 166, 166, 1)
        text-shadow 0px 1px 2px rgba(0, 0, 0, 0.1)
        font-size 14px
        line-height 20px
      .trigger
        margin 10px 20px 10px
        padding 18px 0
        border-top 1px solid rgba(232, 232, 232, 1)
    .content
      flex-grow 1
      height 100%
      .date-item
        width 240px
</style>
