<template lang="pug">
.container
  TaTitleHeader.toolbar(:title="title")
    Searcher(
      v-model="query"
      :variables="['name']"
      tips="检索流程"
      @change="fetchRecords(1)")
    TextButton(icon="plus-circle" theme="filled" @click="onNew" v-can:bpm="'admin'")
      | 新建流程
  StepToolbar(
    mode="tabs"
    :steps="steps"
    v-model="activeKey"
    @change="fetchRecords(1)")
  .main
    AdminTable(
      :store="store"
      :data="store.formatedRecords"
      rowClassName="click-row"
      @rowClick="onShow"
      @change="fetchRecords"
      :hideOnSinglePage="true")
      a-table-column(title="名称" dataIndex="name")
      a-table-column(title="状态" dataIndex="stateText")
      a-table-column(title="类型" dataIndex="type")
        template(slot-scope="type")
          | {{ WorkflowTypeName[type] }}
      a-table-column(title="模块" dataIndex="catalog")
      a-table-column(title="创建时间" dataIndex="created_at")
        template(slot-scope="created_at")
          | {{ created_at | format('YYYY-MM-DD HH:mm') }}
      a-table-column(title="更新时间" dataIndex="updated_at")
        template(slot-scope="updated_at")
          | {{ updated_at | format('YYYY-MM-DD HH:mm') }}
      a-table-column(:width="120")
        template(slot-scope="record")
          .table-hover-col
            PopoverConfirm(
              title="提示"
              type="primary"
              content="您确认要复制该流程吗？"
              @confirm="onCopy(record)")
              IconTooltip(icon="copy" tips="复制")
            IconTooltip(icon="edit" tips="编辑" @click="onEdit(record)")
            PopoverConfirm(
              title="删除"
              content="您确认删除该流程吗？"
              @confirm="destroy(record.id)")
              IconTooltip(icon="delete" tips="删除")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import workflowStore from '@/store/modules/bpm/workflow.store';
import { IWorkflow, WorkflowTypes, WorkflowTypeName } from '@/models/bpm/workflow';
import qs from 'qs';

@Component
export default class AdminWorkflows extends Vue {
  @Prop({ type: Array, default: () => [WorkflowTypes.Bpm] }) types!: WorkflowTypes[];

  query: object = {};
  activeKey: string = '';
  steps: object[] = [
    { title: '全部流程', key: '' },
    { title: '已发布', key: 'done' },
    { title: '草稿箱', key: 'todo' },
  ];

  get title() {
    return typeof this.types && this.types.length === 1 ? WorkflowTypeName[this.types[0]] : '流程配置';
  }
  get setupString() {
    return qs.stringify({ types: this.types, title: this.title });
  }
  get WorkflowTypeName() {
    return WorkflowTypeName;
  }
  get store() {
    return workflowStore || {};
  }

  @Watch('setupString')
  onTypeChange() {
    this.fetchRecords(1);
  }

  mounted() {
    workflowStore.setRole('admin');
    this.fetchRecords();
  }
  fetchRecords(page: number = 1, query?: any) {
    workflowStore.fetch({
      page,
      per_page: 10,
      q: {
        state_eq: this.activeKey,
        type_in: this.types,
        ...query,
        ...this.query,
      },
    });
  }
  onNew() {
    this.$emit('new', this.setupString);
  }
  onShow(w: IWorkflow) {
    this.$emit('show', w.id, this.setupString);
  }
  onEdit(w: IWorkflow) {
    this.$emit('edit', w.id, this.setupString);
  }
  async destroy(id: number) {
    await this.store.delete(id);
    this.$message.success('删除成功');
  }
  async onCopy(w: IWorkflow) {
    await this.store.clone(w.id!);
    this.$message.success('复制成功');
  }
}
</script>

<style lang="stylus" scoped></style>
