<script lang="ts">
/**
 * instance 流程的操作
 * Events:
 *  print
 *  change
 */
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { IInstance, InstanceType } from '@/models/bpm/instance';
import tokenModel, { IToken, TokenTypes } from '@/models/bpm/token';
import { PlaceActionType, getDefaultPlaceActionConfig } from '@/models/bpm/workflow';
import TemplateForm from '@/components/form/TemplateForm.vue';
import UserField from '../form/UserField.vue';

@Component({
  components: {
    TemplateForm,
    UserField,
  },
})
export default class InstanceActions extends Vue {
  @Prop({ type: Object, default: () => ({}) }) instance!: IInstance;
  @Prop({ type: Boolean, default: false }) disabled!: boolean;
  @Prop({ type: Boolean, default: true }) formValid!: boolean;

  // 审批备注
  tokenComment: string = '';
  tokenFormVisible: boolean = false;
  tokenLoading: boolean = false;
  // 打回，退回
  nextPlaceId: number | null = 0;
  loading: boolean = false;
  // 微信审核
  publishAt: any = null;
  mode: string = 'time';
  publishVisible: boolean = false;
  assignUsers: any[] = [];

  @Watch('instance', { immediate: true })
  onInstanceChange() {
    if (this.instance && this.instance.id) {
      this.nextPlaceId = this.instance._defaultNextPlaceId!;
    }
  }

  get currentToken() {
    return (
      this.instance.current_token || {
        options: {
          users: [],
        },
        place_form: {
          fields: [],
        },
      }
    );
  }
  get tokenFormTemplate() {
    const { place_form } = this.currentToken;
    return place_form && place_form.fields ? place_form.fields || [] : [];
  }
  get tokenPayload() {
    const { token_payload } = this.currentToken;
    return token_payload ? token_payload : {};
  }
  get placeActionConfig() {
    return {
      ...getDefaultPlaceActionConfig(),
      ...this.currentToken.action_alias,
    };
  }
  get tokenUsers() {
    return (this.currentToken.options || { users: [] }).users;
  }
  get enableActionsMap() {
    return this.disabled ? {} : this.instance._enableActionsMap || {};
  }
  get historyPlaceOptions() {
    return this.instance._historyPlaceOptions || [];
  }
  get InstanceType() {
    return InstanceType;
  }
  // 微信审核发布
  get shouldSetPublishTime() {
    const { currentToken } = this;
    const { currentUser } = this.$store.state;
    return (
      currentToken.operator_id === currentUser.id &&
      currentToken.state === 'preparing' &&
      currentToken.type === TokenTypes.WechatTimedPublish
    );
  }

  async fire(action: PlaceActionType) {
    try {
      this.loading = true;
      this.nextPlaceId = this.nextPlaceId ? this.nextPlaceId : null;
      if (action === 'forward' && this.assignUsers.length < 1) {
        this.$message.error('请选择转办人');
        this.loading = false;
        return false;
      }
      await tokenModel.fire(action, this.currentToken.id, this.tokenComment, this.nextPlaceId, this.assignUsers[0]);
      this.loading = false;
      this.$message.success('操作成功');
      this.$emit('change');
    } catch (error) {
      this.loading = false;
    }
  }
  onInputReason(reasonKey: string) {
    this.tokenComment = this.tokenComment.slice(0, 200);
  }
  print() {
    this.$emit('print');
  }
  open() {
    this.$utils.open(`this.currentToken.options.url`, '_blank');
  }
  // 提交 token 表单
  openTokenForm() {
    this.tokenFormVisible = true;
  }
  async submitTokenForm() {
    (this.$refs.tokenForm as any).submit({
      success: async (payload: IObject) => {
        this.tokenLoading = true;
        await tokenModel
          .update({
            id: this.currentToken.id,
            token_payload: payload,
          })
          .finally(() => {
            this.tokenLoading = false;
          });
        this.tokenFormVisible = false;
        this.fire('accept');
      },
    });
  }
  // 微信审核
  handleOpenPanel(open: boolean) {
    if (open) {
      this.mode = 'time';
    }
  }
  async updatePublishTime() {
    try {
      this.loading = true;
      await tokenModel.update({
        id: this.currentToken.id,
        options: {
          publish_at: this.publishAt.toISOString(),
        },
      });
      this.publishVisible = false;
      this.loading = false;
      this.$message.success('设置成功');
      this.$emit('change');
    } catch (error) {
      this.loading = false;
    }
  }
}
</script>

<template lang="pug">
.instance-actions(v-loading="loading")
  slot(name="left")
  //- ============= other actions ============
  template(v-if="shouldSetPublishTime")
    Popover(v-model="publishVisible" title="设置定时发送")
      template(#main)
        a-date-picker.date-picker(
          v-model="publishAt"
          :mode="mode"
          showTime
          format="YYYY-MM-DD HH:mm:ss"
          :disabledDate="(current) => current && current < $moment()"
          @openChange="handleOpenPanel"
          @panelChange="(value, mod) => mode = mod")
      a-button(
        block
        type="primary"
        size="default"
        @click="updatePublishTime"
        :loading="loading"
        :disabled="!publishAt"
        slot="footer")
        | 确认
      a-button(size="default" type="primary")
        | 设置发布时间
  //- ============== enable actions ============
  a-button(
    v-if="enableActionsMap.print"
    @click="print"
    size="default"
    type="primary")
    | {{ placeActionConfig.print.name }}
  a-button(
    v-if="enableActionsMap.open"
    @click="open"
    size="default"
    type="primary")
    | {{ placeActionConfig.open.name }}
  //- 发起人首次提交
  PopoverConfirm(
    v-if="enableActionsMap.submit && formValid"
    title="提交流程"
    :content="placeActionConfig.submit.desc"
    placement="top"
    type="primary"
    @confirm="fire('submit')")
    a-button(type="primary" size="default")
      | {{ placeActionConfig.submit.name }}
  //- ===================== 他人审批 ===================
  template(v-if="enableActionsMap.accept && formValid")
    //- 1. 含有 token 模板
    a-button.btn-success(size="default" v-if="tokenFormTemplate.length > 0" @click="openTokenForm")
      | {{ placeActionConfig.accept.name }}
    //- 2. 不含模板
    PopoverConfirm(
      v-else
      title="提示"
      placement="top"
      type="primary"
      @confirm="fire('accept')"
      @close="tokenComment = ''")
      template(slot="content")
        .reason-form
          h4.place-select {{ placeActionConfig.accept.desc }}
          a-textarea(v-model="tokenComment" placeholder="备注，非必填" :rows="3" @input="onInputReason")
          span.count {{ tokenComment.length }}/200
      a-button.btn-success(size="default")
        | {{ placeActionConfig.accept.name }}
  //- forward
  PopoverConfirm(
    v-if="enableActionsMap.forward"
    title="提示"
    placement="top"
    :width="300"
    :autoClose='false'
    @confirm="fire('forward')"
    @close="tokenComment = ''")
    template(slot="content")
      .reason-form
        h4.place-select {{ placeActionConfig.forward.desc }}
        UserField(v-model='assignUsers', :multiple='false')
        a-textarea(v-model="tokenComment" placeholder="非必填" :rows="3" @input="onInputReason")
        span.count {{ tokenComment.length }}/200
    a-button(size="default" type="info")
      | {{ placeActionConfig.forward.name }}
  //- reject
  PopoverConfirm(
    v-if="enableActionsMap.reject"
    title="提示"
    placement="top"
    :width="300"
    @confirm="fire('reject')"
    @close="tokenComment = ''")
    template(slot="content")
      .reason-form
        h4.place-select {{ placeActionConfig.reject.desc }}
        a-select.place-select(
          v-if="historyPlaceOptions.length"
          placeholder="打回节点 (可选)"
          :options="historyPlaceOptions"
          v-model="nextPlaceId")
        a-textarea(v-model="tokenComment" placeholder="非必填" :rows="3" @input="onInputReason")
        span.count {{ tokenComment.length }}/200
    a-button(size="default" type="danger")
      | {{ placeActionConfig.reject.name }}
  //- fail
  PopoverConfirm(
    v-if="enableActionsMap.fail"
    title="提示"
    placement="top"
    :width="300"
    @confirm="fire('fail')"
    @close="tokenComment = ''")
    template(slot="content")
      .reason-form
        h4.place-select {{ placeActionConfig.fail.desc }}
        a-select.place-select(
          v-if="historyPlaceOptions.length"
          placeholder="退回节点 (可选)"
          :options="historyPlaceOptions"
          v-model="nextPlaceId")
        a-textarea(v-model="tokenComment" placeholder="非必填" :rows="3" @input="onInputReason")
        span.count {{ tokenComment.length }}/200
    a-button.btn-warning(size="default")
      | {{ placeActionConfig.fail.name }}
  //- recall
  PopoverConfirm(
    v-if="enableActionsMap.recall"
    title="提示"
    placement="top"
    :width="300"
    @confirm="fire('recall')"
    @close="tokenComment = ''")
    template(slot="content")
      .reason-form
        h4.place-select {{ placeActionConfig.recall.desc }}
        a-textarea(v-model="tokenComment" placeholder="非必填" :rows="3" @input="onInputReason")
        span.count {{ tokenComment.length }}/200
    a-button.btn-warning(size="default")
      | {{ placeActionConfig.recall.name }}
  //- terminate
  PopoverConfirm(
    v-if="enableActionsMap.terminate"
    title="提示"
    placement="top"
    :width="300"
    @confirm="fire('terminate')"
    @close="tokenComment = ''")
    template(slot="content")
      .reason-form
        h4.place-select {{ placeActionConfig.terminate.desc }}
        a-textarea(v-model="tokenComment" placeholder="非必填" :rows="3" @input="onInputReason")
        span.count {{ tokenComment.length }}/200
    a-button.btn-warning(size="default")
      | {{ placeActionConfig.terminate.name }}
  slot

  MainModal(
    :title="currentToken.name"
    v-model="tokenFormVisible"
    :width="900")
    .token-form
      TemplateForm(
        ref="tokenForm"
        :formData="tokenPayload"
        :template="tokenFormTemplate")
    .footer-actions(slot="footer")
      a-button(size="large" @click="tokenFormVisible = false")
        | 取消
      a-button(
        type="primary"
        :loading="tokenLoading"
        @click="submitTokenForm"
        size="large")
        | 确认提交
</template>

<style lang="stylus" scoped>
.instance-actions
  padding 10px 12px 4px
  text-align right
  &:empty
    padding 0
  button
    margin-bottom 6px
    margin-left 8px
    &:first-child
      margin-left 0
  .date-picker
    width 100% !important

.reason-form
  position relative
  .title
    margin-bottom 8px
    color #333333
  .form
    position relative
    margin-bottom 10px
    .tips
      margin-bottom 10px
    .count
      position absolute
      right 10px
      bottom 14px
      color #999999
      font-size 12px
      user-select none
  .place-select
    margin-bottom 12px
    width 100%

.token-form
  padding 20px
.footer-actions
  text-align right
</style>
