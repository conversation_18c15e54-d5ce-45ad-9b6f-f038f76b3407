<script lang="ts">
import { IInstance, InstanceType } from '@/models/bpm/instance';
import { bpmUserInstanceStore } from '@/store/modules/bpm/user/instance.store';
import { Component, Vue, Prop } from 'vue-property-decorator';
import { TaIndexSearcherOptionInterface, TaIndexViewConfigInterface, TaIndexViewTabInterface } from '../global/TaIndex';
import TaIndexView from '../global/TaIndexView.vue';
import ComApprivalHeaderCard from './ComApprivalHeaderCard.vue';
import ComBpmInstanceCard from './ComBpmInstanceCard.vue';

interface CardInterface {
  label: string;
  number: number;
  key: string;
  queryKey: string;
  color: string;
  radiusColor: string[];
}

interface MenuInterface {
  label: string;
  number: number;
  color: string;
  query: IObject;
}

interface ThreeLevelStatInterface {
  instance_stat_count: { [key: string]: number };
  workflow_id_count: { [key: string]: number };
  workflow_id_name: { [key: string]: string };
  workflow_modul_count: { [key: string]: number };
}

@Component({
  components: {
    ComApprivalHeaderCard,
    ComBpmInstanceCard,
  },
})
export default class ComInstanceIndex extends Vue {
  @Prop({ type: String, default: '动态' }) title!: string;
  @Prop({ type: Array }) moduls!: string[];

  timeQuery: IObject = {};

  cards: CardInterface[] = [
    { label: '待我提交', number: 0, key: 'waitSubmit', queryKey: 'todo', color: '#333', radiusColor: ['#eee', '#999'] },
    {
      label: '待我审批',
      number: 0,
      key: 'waitApprival',
      queryKey: 'approving',
      color: '#FA8B16',
      radiusColor: ['#FEECCD', '#FF9F01'],
    },
    {
      label: '我发起的',
      number: 0,
      key: 'ownInitiate',
      queryKey: 'created',
      color: '#333',
      radiusColor: ['#DBEBF7', '#3CA8F5'],
    },
    {
      label: '我已审批',
      number: 0,
      key: 'ownApprival',
      queryKey: 'approved',
      color: '#333',
      radiusColor: ['#F1F9EB', '#9ED979'],
    },
    {
      label: '抄送我的',
      number: 0,
      key: 'copyOwn',
      queryKey: 'notified',
      color: '#333',
      radiusColor: ['#FFEDEC', '#FF8478'],
    },
  ];

  activeCard: CardInterface = this.cards[1];

  tabs: TaIndexViewTabInterface[] = [];

  menus: MenuInterface[] = [];
  activeMenu: Partial<MenuInterface> = {};

  searcherOptions: TaIndexSearcherOptionInterface[] = [
    { label: '编号', key: 'seq', type: 'string' },
    { label: '流程名称', key: 'workflow_name', type: 'string' },
    { label: '教师姓名', key: 'creator_of_Teacher_type_name', type: 'string' },
    { label: '教师工号', key: 'creator_of_Teacher_type_code', type: 'string' },
    { label: '学生姓名', key: 'creator_of_Student_type_name', type: 'string' },
    { label: '学生工号', key: 'creator_of_Student_type_code', type: 'string' },
    // { label: '概要', key: 'summary', type: 'string' },
  ];

  get store() {
    return bpmUserInstanceStore;
  }

  get config(): TaIndexViewConfigInterface {
    return {
      recordName: '审批',
      store: this.store,
      showExport: true,
      searcherSimpleOptions: this.searcherOptions,
      showCount: true,
    };
  }

  mounted() {
    this.initStore();
  }

  initStore() {
    const { id, type } = this.$store.state.currentUser;

    this.store.init({
      params: {
        q: {
          workflow_modul_in: this.moduls,
          ...this.timeQuery,
          ...this.activeMenu.query,
          [this.activeCard.queryKey]: [id, type],
        },
      },
    });
  }

  onIndex(_: IObject, params: IObject) {
    this.store
      .sendCollectionAction({
        action: 'three_level_stat',
        silence: true,
        config: { params },
      })
      .then(res => {
        const { data } = res;
        this.cardAssignment(data);
        this.tabAssignment(data);
        this.menuAssignment(data);
      });
  }

  cardAssignment(data: ThreeLevelStatInterface) {
    this.cards[0].number = data.instance_stat_count.todo;
    this.cards[1].number = data.instance_stat_count.approving;
    this.cards[2].number = data.instance_stat_count.created;
    this.cards[3].number = data.instance_stat_count.approved;
    this.cards[4].number = data.instance_stat_count.notified;
  }

  tabAssignment(data: ThreeLevelStatInterface) {
    const group = data.workflow_modul_count;
    this.tabs = [];
    if (!(this.moduls?.length === 1)) {
      this.tabs.push({ label: '全部流程', key: 'count', num: group.count, query: {} });
    }

    Object.keys(group).forEach(key => {
      if (!(key === 'count') && (!this.moduls || this.moduls.includes(key))) {
        if (key) {
          this.tabs.push({ label: key, key: key, num: group[key], query: { workflow_modul_eq: key } });
        } else {
          this.tabs.push({ label: key, key: key, num: group[key], query: { workflow_modul_null: true } });
        }
      }
    });
  }

  menuAssignment(data: ThreeLevelStatInterface) {
    this.menus = [];
    Object.keys(data.workflow_id_count).forEach(id => {
      this.menus.push({
        label: data.workflow_id_name[id],
        number: data.workflow_id_count[id],
        color: '#333',
        query: { workflow_id_eq: +id },
      });
    });
  }

  onDateChange(range: any) {
    const [start, end] = range;
    if (start && end) {
      this.timeQuery.created_at_gteq = start.toString();
      this.timeQuery.created_at_lteq = end.toString();
    } else {
      this.timeQuery.created_at_gteq = null;
      this.timeQuery.created_at_lteq = null;
    }
    this.initStore();
  }

  onCardClick(item: CardInterface) {
    this.activeCard = item;
    if (this.tabs[0] && this.tabs[0].key) {
      (this.$refs.taIndexView as TaIndexView<IInstance>).activeTabKey = this.tabs[0].key;
    }
    this.activeMenu = {};
    this.initStore();
  }

  onMenuClick(item: MenuInterface) {
    this.activeMenu = item;
    this.initStore();
  }

  beforeTabChange(key: string) {
    this.activeCard = this.cards[1];
    this.activeMenu = {};
    if ((this.$refs.taIndexView as TaIndexView<IInstance>).activeTabKey === key) {
      this.initStore();
    }
  }

  onCardFresh() {
    this.slienceRefresh();
  }

  slienceRefresh() {
    (this.$refs.taIndexView as TaIndexView<IInstance>).slienceRefresh();
  }
}
</script>

<template lang="pug">
.com-instance-index
  TaTitleHeader(:title='title')
  .card-box
    .card-item(v-for='item in cards')
      ComApprivalHeaderCard(
        @click.native='onCardClick(item)',
        :active='item.label === activeCard.label',
        :record='item'
      )
  TaIndexView.background(
    ref='taIndexView',
    :config='config',
    :tabs='tabs',
    :sidebarWidth='menus.length > 0 ? 170 : 0',
    :tabsLeftMargin='0'
    @beforeTabChange='beforeTabChange',
    @onIndex='onIndex'
  )
    template(#card='{ record }')
      ComBpmInstanceCard.card(
        :instance='record',
        @refresh='onCardFresh',
      )

    template(#right-actions)
      a-range-picker.date-item(@change='onDateChange', format='YYYY-MM-DD')

    template(#sidebar)
      .menu
        .menu-item(
          v-for='item in menus',
          :key='item.label',
          :class='{ active: item.label === activeMenu.label }',
          @click='onMenuClick(item)'
        )
          a-tooltip(placement='topLeft')
            template(#title)
              span {{ item.label }}
            .menu-item-title.text-ellipsis {{ item.label }}
          .menu-item-num(:style='{ color: item.color }') {{ item.number }}
</template>

<style lang="stylus" scoped>
.com-instance-index
  >>> .group
    max-height 60vh
    overflow-y scroll
  padding-bottom 20px
.card-box
  display grid
  margin-bottom 18px
  grid-template-columns 1fr 1fr 1fr 1fr 1fr
  grid-column-gap 40px
.background
  padding 5px 20px 5px 0
  background white
.icon-add
  position fixed
  right 60px
  bottom 60px
  width 50px
  height 50px
  border-radius 50%
  background #0080FF
  color #FFFFFF
  text-align center
  font-size 30px
  cursor pointer
.show-task-button
  margin 10px 0
.card
  margin-left 10px
.menu
  padding-top 10px
  padding-right 10px
  height 60vh
  overflow-y scroll
  .menu-item
    position relative
    display flex
    justify-content space-between
    align-items center
    height 50px
    color #484848
    cursor pointer
    user-select none
    .menu-item-title
      margin-left 20px
      width 100px
      font-size 12px
    .menu-item-num
      margin-right 10px
      font-size 14px
      font-family 'DINCond-Medium'
    &.active
      background #F1FCFE
    &.active:before
      position absolute
      top 0
      left 0
      width 5px
      height 100%
      background #3DA8F5
      content ''
</style>
