<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import instanceModel, { IInstance, InstanceType } from '@/models/bpm/instance';
import InstanceDetailDialog from './InstanceDetailDialog.vue';
import tokenModel from '@/models/bpm/token';

@Component({
  components: {
    InstanceDetailDialog,
  },
})
export default class ComBpmInstanceCard extends Vue {
  @Prop({ type: Object, default: () => ({}) }) private instance!: IInstance;
  @Prop({ type: Boolean, default: true }) private showActions!: boolean;
  visibleDialog = false;
  visibleTaskDrawer = false;

  get stateMap() {
    return instanceModel.stateMap;
  }

  get summary() {
    const summaryObj = this.instance.summary || {};
    return Object.keys(summaryObj).map(key => [key, summaryObj[key]].join('：'));
  }

  async onClick() {
    if (this.instance.type && this.instance.type === InstanceType.Notice) {
      if (this.instance?.last_token?.state === 'processing' && this.instance.state === 'processing') {
        await tokenModel.fire('accept', this.instance.last_token.id, '');
        this.$emit('refresh');
      }
      if (this.instance?.payload?.url) {
        window.open(this.instance?.payload?.url);
      }
      // await tokenModel.fire(action, this.currentToken.id, this.tokenComment, this.nextPlaceId, this.assignUsers[0]);
    } else {
      this.visibleDialog = true;
    }
  }

  onFlowableEdit() {
    this.$message.warning('审核中，关联资源不可修改');
  }

  handleInstanceDetailDialogChange() {
    if (!this.visibleDialog) {
      this.$emit('refresh');
    }
  }
}
</script>

<template lang="pug">
.instance-cell(@click.stop='onClick')
  .header
    .workflow-name
      img.icon(src='@/assets/images/bpm/app.png', height='20', width='20')
      span {{ instance.workflow_name }}
      .user-name(v-if='instance.creator_name') {{ `· ${instance.creator_name}` }}
    .right
      .seq.gray-text
        | 编号：{{ instance.seq }}
      a-tag.state(:color='stateMap[instance.state].color')
        | {{ stateMap[instance.state].label }}
  .summary.gray-text(v-if='summary.length !== 0')
    span.line(v-for='item in summary') {{ item }}
  .infos.gray-text
    .line
      span 申请时间：{{ $moment(instance.created_at).format('YYYY-MM-DD HH:mm') }}
      template(v-if='instance.last_token')
        span 当前阶段：{{ instance.last_token.name }}
        span 操作者：{{ instance.last_token.operator_name }}
        span 状态：{{ stateMap[instance.last_token.state].label }}
  InstanceDetailDialog(
    v-if='visibleDialog',
    title='审批详情',
    v-model='visibleDialog',
    :instanceId='instance.id',
    @flowableEdit='onFlowableEdit',
    @input='handleInstanceDetailDialogChange'
  )
    template(#default='{ instance: dialogInstance }')
      slot(name='dialog', :instance='dialogInstance')
</template>

<style lang="stylus" scoped>
.instance-cell {
  // margin-bottom 12px
  margin-top: 10px;
  padding: 14px 12px 7px 12px;
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 4px;
  background: rgba(255, 255, 255, 1);

  &:hover {
    border-color: #3DA8F5;
    cursor: pointer;

    .header .right .actions {
      display: block;
    }
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 3px;

    .workflow-name {
      display: flex;
      align-items: center;
      height: 20px;
      color: rgba(56, 56, 56, 1);
      font-size: 14px;
      line-height: 20px;

      .user-name {
        margin-left: 16px;
        height: 20px;
        color: rgba(56, 56, 56, 1);
        font-weight: 500;
        font-size: 14px;
        line-height: 20px;
      }

      .icon {
        display: inline-block;
        margin-right: 8px;
      }
    }

    .right {
      display: inline-flex;
      align-items: center;

      .seq {
        padding-right: 20px;
      }
    }
  }

  .summary {
    overflow: hidden;
    padding: 7px 6px 0 6px;
    border-bottom: 1px solid #E8E8E8;

    .line {
      margin-bottom: 8px;
    }

    span {
      float: left;
      margin-right: 30px;
    }
  }

  .infos {
    padding: 7px 6px;

    .line {
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0px;
      }
    }

    span {
      margin-right: 30px;
    }
  }
}

.gray-text {
  color: rgba(128, 128, 128, 1);
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
}
</style>
