<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import instance from '@/models/bpm/instance';
import { ITeacher } from '@/models/teacher';

import { scheduleLessonsStore } from '@/store/modules/teaching/schedule_lessons';
import { scheduleCoursesStore, shareScheduleCoursesStore } from '@/store/modules/teaching/schedule_courses';
import ComTeachingPlan from '@/components/teaching/teacher/ComTeachingPlan.vue';
import ComBpmInstanceDetailInfo from './ComBpmInstanceDetailInfo.vue';

@Component({
  components: {
    ComTeachingPlan,
    ComBpmInstanceDetailInfo,
  },
})
export default class InstanceBaseInfo extends Vue {
  @Prop({ type: Object, default: () => ({}) }) readonly instance!: any;
  @Prop({ type: Object, default: () => {} }) courses!: {};
  @Prop({ type: Object, default: () => {} }) readonly teachers!: IObject;
  @Prop({ type: Object, default: () => null }) readonly schedule_lessons!: IObject;

  // private teachers: ITeacher[] = [];
  // private schedule_lessons: any = [];
  private course_id = 0;

  get stateMap() {
    return instance.stateMap;
  }

  get shareScheduleCoursesStore() {
    return shareScheduleCoursesStore;
  }

  get teacherCode() {
    return this.$store.state.currentUser.code;
  }

  get lessonsStore() {
    return scheduleLessonsStore;
  }

  get title() {
    return `${this.instance.creator_name}提交的${this.instance.workflow_name}`;
  }

  get teacherDepartment() {
    const { creator_department_path: path, creator_department_name: name } = this.instance;
    return path ? `${path.join(' / ')} / ${name}` : name;
  }

  public onShow() {
    this.$utils.open(`/teaching/teacher/courses/${this.instance.flowable_info.course_id}/educations`, '_blank');
  }
}
</script>

<template lang="pug">
.instance-base-info-card
  ComBpmInstanceDetailInfo(:record='instance')
  .base-info-content(v-if='instance')
    .seq {{ instance.seq }}
    .sub-title 申请人信息
    a-row(:gutter='16')
      a-col.item(:span='12')
        label 申请人
        span {{ instance.creator_name }}
      template(v-if='instance.creator_type === "Student"')
        a-col.item(:span='12')
          label 学号
          span {{ instance.creator_code }}
        a-col.item(:span='12')
          label 申请时间
          span {{ instance.created_at | format }}
      template(v-else)
        a-col.item(:span='12')
          label 工号
          span {{ instance.creator_code }}
        a-col.item(:span='12')
          label 申请时间
          span {{ instance.created_at | format }}
        a-col.item(:span='24')
          label 部门
          span {{ teacherDepartment }}
  .base-info-content(v-if="instance.flowable_type === 'Teaching::ScheduleCourse'")
    .sub-title
      span 课程信息
      //- TextButton(icon='eye', @click='onShow')
        //- | 查看考试详情
    a-row(:gutter='16')
      a-col.item(:span='12')
        label 课程名称
        span {{ instance.flowable_info.course_set_name }}
      a-col.item(:span='12')
        label 任课老师
        span {{ instance.flowable_info.teacher_name }}
      a-col.item(:span='12')
        label 课程代码
        span {{ instance.flowable_info.course_set_code }}
      a-col.item(:span='12')
        label 总课时
        span {{ instance.flowable_info.course_set_period }}
      //- a-col.item(:span='12')
      //-   label 学年
      //-   span {{ instance.flowable_info.semester_school_year }}
      //- a-col.item(:span='12')
      //-   label 学期
      //-   span {{ instance.flowable_info.semester_name }}
      //- a-col.item(:span='12')
      //-   label 专业
      //-   span {{ instance.flowable_info.major_name }}
      //- a-col.item(:span='12')c.course_set.course_dir.department_major
      //-   label 班级
      //-   span {{ instance.flowable_info.course_name }}
      //- a-col.item(:span='12')
      //-   label 人数
      //-   span {{ instance.flowable_info.course_std_count }}
    ComTeachingPlan(
      :tableData='instance.flowable_info.schedule_lessons',
      :scheduleCourse='instance.flowable_info || {}'
      :print='true'
    )
  .base-info-content(v-if="instance.flowable_type === 'Teaching::ScheduleCourse' ")
    slot(name="table")
  .base-info-content(v-if='instance.type === "Meeting::ApplicationFormInstance"')
    a-row(:gutter='16')
      a-col.item(:span='12')
        label 备注
        span {{ instance.flowable_info.comment }}
</template>

<style lang="stylus" scoped>
.instance-base-info-card
  position relative
  .base-info-title
    height 28px
    color rgba(0, 0, 0, 1)
    font-weight 500
    font-size 20px
    line-height 28px
  .base-info-content
    padding 14px 0
    border-bottom 1px solid #E8E8E8
    .seq
      font-size 14px
      line-height 20px
      color rgba(56, 56, 56, 1)
    .sub-title
      display flex
      flex-direction row
      justify-content space-between
      margin 10px 0
      color rgba(56, 56, 56, 1)
      font-weight 500
      font-size 16px
    .item
      margin 10px 0
      font-size 14px
      line-height 20px
      width fit-content
      max-width 100%
      overflow: hidden;
      text-overflow:ellipsis;
      white-space: nowrap;
      label
        display inline-block
        width 100px
        color rgba(128, 128, 128, 1)
      span
        color rgba(56, 56, 56, 1)
    .qrcode
      width 80px
      height 80px
</style>
