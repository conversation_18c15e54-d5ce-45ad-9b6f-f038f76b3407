<script lang="ts">
/**
 * 流程审批详情
 * Events:
 *  change 审批发生变更
 *  print 打印
 *  flowableEdit 编辑管理的 flowable 对象
 *
 * Slots:
 *  flowable 插入关联的 flowable 对象详情
 */
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { Workflow, ITokenFormResponse } from '@/models/bpm/workflow';
import instanceModel, { IInstance } from '@/models/bpm/instance';
import { IToken } from '@/models/bpm/token';
// components
import InstanceBaseInfo from './InstanceBaseInfo.vue';
import Timeline from './Timeline.vue';
import TemplateFormViewer from '../form/TemplateFormViewer.vue';
import { CommentPanel } from '@/components/comment';
import commentStore from '@/store/modules/comment.store';

import { scheduleLessonsStore } from '@/store/modules/teaching/schedule_lessons';
import { scheduleCoursesStore, shareScheduleCoursesStore } from '@/store/modules/teaching/schedule_courses';
import ComTeachingPlan from '@/components/teaching/teacher/ComTeachingPlan.vue';
import ComBpmInstanceDetailHeader from './ComBpmInstanceDetailHeader.vue';

@Component({
  components: {
    TemplateFormViewer,
    Timeline,
    InstanceBaseInfo,
    CommentPanel,
    ComTeachingPlan,
    ComBpmInstanceDetailHeader,
  },
})
export default class InstanceDetail extends Vue {
  @Prop({ type: Number }) readonly instanceId!: number;
  @Prop({ type: Boolean, default: true }) readonly showTokens!: boolean;

  schedule_lessons: any = [];
  teachers: any = [];

  course_id: number = 0;
  instanceLoading: boolean = false;
  instance: any = {
    payload: {},
  };
  // 表单相关
  tokenFormResponse: ITokenFormResponse = {
    workflow: { id: undefined },
    token: {},
    formTemplate: [],
    formEditable: false,
  };
  formData: IObject = {};
  formTemplate: object[] = [];
  visibleForm: boolean = true;

  get commentsCount() {
    return commentStore.totalCount ? `在线讨论 (${commentStore.totalCount})` : '在线讨论';
  }
  get formViewerEditable() {
    const { edit, accept, submit } = this.instance._enableActionsMap || { edit: false, accept: false, submit: false };
    return edit || accept || submit;
  }

  @Watch('instanceId', { immediate: true })
  async onInstanceChange() {
    this.resetData();
    if (this.instanceId) {
      await this.fetchInstance();
    }

    this.$emit('afterFetch', this.instance);
  }

  created() {
    window.addEventListener('scroll', this.handleScroll, true);
    instanceModel.setRole('user');
  }
  resetData() {
    this.instance = { payload: {} };
    this.tokenFormResponse = {
      workflow: { id: undefined },
      token: {},
      formTemplate: [],
      formEditable: false,
    };
    this.formData = {};
    this.formTemplate = [];
  }
  async fetchInstance() {
    try {
      this.instanceLoading = true;
      const { data } = await instanceModel.find(this.instanceId);
      this.instance = data;
      if (this.instance.flowable_info) {
        this.teachers.push({ name: this.instance.flowable_info.teacher_name });
        this.course_id = this.instance.flowable_info.course_id;
        // this.fetchData();
        this.schedule_lessons = this.instance.flowable_info.schedule_lessons;
      }
      this.formData = { ...this.instance.payload };
      await this.findTokenFormAndWorkflow(data.workflow_id!, data.current_token!.id as number);
      this.instanceLoading = false;
    } catch (error) {
      this.instanceLoading = false;
      this.$message.error('获取审批详情异常');
      throw error;
    }
  }
  // 获取【当前流程节点】的 workflow 和 加权表单模板
  async findTokenFormAndWorkflow(workflowId: number, tokenId: number) {
    try {
      if (!workflowId) return;
      const tokenFormResponse = await new Workflow({ role: 'user' }).findTokenForm(workflowId, tokenId);
      this.tokenFormResponse = tokenFormResponse;
      this.formTemplate = tokenFormResponse.formTemplate;
    } catch (error) {
      this.$message.error('获取动态表单模板异常');
    }
  }
  async emitChange() {
    this.$emit('change', this.instance);
    this.visibleForm = false;
    await this.fetchInstance();
    this.$nextTick(() => {
      this.visibleForm = true;
    });
  }
  print() {
    this.$emit('print', this.instance);
  }
  flowableEdit() {
    this.$emit('flowableEdit', this.instance);
  }

  get shareScheduleCoursesStore() {
    return shareScheduleCoursesStore;
  }

  get teacherCode() {
    return this.$store.state.currentUser.code;
  }

  get lessonsStore() {
    return scheduleLessonsStore;
  }

  async fetchData() {
    this.shareScheduleCoursesStore.init({
      parents: [{ id: this.course_id, type: 'courses' }],
    });
    await this.shareScheduleCoursesStore.find();
    this.schedule_lessons = this.shareScheduleCoursesStore.record.schedule_lessons;
  }

  scroll = false;

  handleScroll() {
    const scrollTop = (this.$refs.instanceContainer as any)?.scrollTop;
    if (scrollTop && scrollTop > 84) {
      this.scroll = true;
    } else {
      this.scroll = false;
    }
  }
}
</script>

<template lang="pug">
.workflow-instance-detail(v-loading="instanceLoading")
  ComBpmInstanceDetailHeader(v-if='instance.seq', :scroll='scroll', :record='instance')
  .instance-container
    .instance
      .instance-content(id="instanceContainer", ref='instanceContainer')
        InstanceBaseInfo(:instance="instance")
          template(#table)
            //- span 000
            //- ComTeachingPlan(
            //-   :tableData='schedule_lessons',
            //-   :teachers='teachers',
            //-   :Loading='false',
            //-   :show="true"
            //-   :showFile="false"
            //-   :updateDateState="false"
            //- )
        .module
          //- flowable 关联对象
          template(v-if="instance.flowable_id")
            slot(name="flowableOnTop" :instance="instance")
          //- 动态表单模块
          template(v-if="formTemplate.length && visibleForm")
            .sub-title 提交资料
            TemplateFormViewer(
              :formData="formData"
              :template="formTemplate"
              :instance="instance"
              :editable="formViewerEditable")
          //- flowable 关联对象
          template(v-if="instance.flowable_id")
            slot(name="flowable" :instance="instance")
          //- 默认
          slot(:instance="instance")
    .tokens-comments(v-if="showTokens")
      a-tabs(defaultActiveKey="1")
        a-tab-pane.panel(tab="审批流程" key="1")
          Timeline(
            :instance="instance"
            :tokenFormResponse="tokenFormResponse"
            @change="emitChange"
            @flowableEdit="flowableEdit"
            @print="print")
        a-tab-pane.panel(:tab="commentsCount" key="2" forceRender)
          CommentPanel(
            :commentableId="instance.id"
            commentableType="Instance"
            :showHeader="false")
</template>

<style lang="stylus" scoped>
.workflow-instance-detail
  height 100%
  width 100%
  background #fff
  .instance-container
    display flex
    height 100%
    width 100%
    .instance
      position relative
      flex 0 0 66.66%
      height 100%
      overflow hidden
      .instance-content
        overflow auto
        padding 16px 24px
        height 100%
        .module
          padding 14px 0
          .sub-title
            margin 10px 0
            color rgba(56, 56, 56, 1)
            font-weight 500
            font-size 16px
    .tokens-comments
      position relative
      flex-shrink 0
      flex 0 0 33.33%
      height 100%
      border-left 1px solid #EFF0F2
</style>
