<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class ComApprivalHeaderCard extends Vue {
  @Prop({ type: Object }) record!: IObject;
  @Prop({ type: <PERSON><PERSON><PERSON> }) active!: boolean;
}
</script>

<template lang="pug">
.apprival-card-box(:style='active ? "border: 1px solid white" : ""')
  .label {{ record.label }}
  .number(:style="{ color: record.color }") {{ record.number }}
  .radius-box
    .radius.radius_0(:style="{ background:record.radiusColor[0] }")
    .radius.radius_1(:style="{ background:record.radiusColor[1] }")
  .active-icon-box(v-show="active")
    .line
    a-icon.down-icon(type="caret-down")
</template>

<style lang="stylus" scoped>
.apprival-card-box
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.08);
.apprival-card-box:hover
  border 1px solid #3CA8F5
  border-radius 2px
  // overflow hidden
.apprival-card-box
  background #fff
  height 48px
  cursor: pointer;
  position relative
  border-radius 4px
  display flex
  align-items center
  .label
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    margin 0 20px
    font-weight: 400;
    z-index 2
  .number
    font-size: 22px;
    font-family: DINCondensed-Bold, DINCondensed;
    font-weight: bold;
    z-index 2
  .radius-box
    position: absolute
    width 100%
    height 100%
    z-index 1
    border-radius 4px
    top 0
    overflow: hidden;
    .radius
      width 60px
      height 60px
      position absolute
      right 0
      top 0
      border-radius 50%
    .radius_0
      top 5px
      right -28px
      opacity: 0.3;
    .radius_1
      top 27px
      right 15px
      opacity: 0.15;
  .active-icon-box
    display flex
    width calc(100% - 8px)
    left 4px
    z-index 0
    bottom -11px
    position absolute
    flex-direction column
    .line
      height 1px
      width calc(100% + 2px)
      background #3CA8F5
    .down-icon
      color #3CA8F5
      position: relative;
      top -4px
      font-size 10px
</style>
