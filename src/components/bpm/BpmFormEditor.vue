<script lang="ts">
/**
 * BPM instance payload 表单编辑
 * 暂存：只是创建 instance, 保存 payload
 * 提交：创建 instance 保存 payload, 且 accept current_token
 * 更新：更新 instance.payload
 *
 * Events:
 *  success
 *  fail
 */
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import { IFormTemplateItem } from '@/interfaces/formTemplate.interface';
import TemplateForm from '@/components/form/TemplateForm.vue';
import instanceModel, { IInstance, IFlowableType } from '@/models/bpm/instance';
import workflowModel, { IWorkflow } from '@/models/bpm/workflow';
import tokenModel, { IToken } from '@/models/bpm/token';
import { ActiveStore } from '@/lib/ActiveStore';

type submitType = 'save' | 'accept'; // 保存 | 提交 token

@Component({
  components: {
    TemplateForm,
  },
})
export default class BpmFormEditor extends Vue {
  @Model('input', { type: Boolean, default: false }) value!: boolean;
  @Prop({ type: Object, default: () => ({}), required: true }) workflow!: IWorkflow;
  @Prop({ type: Object, default: () => ({ current_token: {}, payload: {} }) }) instance!: IInstance;
  @Prop({ type: Object, default: () => ({}) }) flowable!: IObject;
  // 新逻辑需要用 bpm/flowable/finance_project/1/instances create
  @Prop({ type: Object }) store!: ActiveStore<IInstance>;
  @Prop({ type: String }) flag!: string;
  @Prop({ type: String, default: '' }) title!: string;

  currentToken: IToken = {};
  formData: IObject = {};
  template: IFormTemplateItem[] = [];
  loading: boolean = false;
  throttleDisabled: boolean = false;

  get visible() {
    return this.value;
  }
  set visible(value: boolean) {
    this.$emit('input', value);
  }

  @Watch('value')
  async onVallueChange() {
    if (this.value) {
      this.registerModels();
      await this.fetchData();
    }
  }

  registerModels() {
    const role = 'user';
    workflowModel.setRole(role);
    instanceModel.setRole(role);
    tokenModel.setRole(role);
  }

  async fetchData() {
    try {
      this.loading = true;
      const currentToken: any = this.instance ? this.instance.current_token || {} : {};
      const { workflow, token, formTemplate } = await workflowModel.findTokenForm(this.workflow.id, currentToken.id);
      this.formData = this.instance.payload || {};
      this.template = formTemplate;
      this.currentToken = token;
      this.loading = false;
    } catch (error) {
      this.loading = false;
    }
  }

  submit(type: submitType = 'save') {
    if (this.throttleDisabled) return;
    this.throttleDisabled = true;
    const formRef = this.$refs.form as any;
    // 暂存不校验表单
    if (type === 'save') {
      this.createOrUpdatePayload(formRef.getFieldsValue(), type);
      return;
    }
    formRef.submit({
      success: (payload: IObject) => {
        this.createOrUpdatePayload(payload, type);
        setTimeout(() => {
          this.throttleDisabled = false;
        }, 1000);
      },
      fail: (error: any) => {
        this.$emit('fail', error);
        this.throttleDisabled = false;
      },
    });
  }

  async createOrUpdatePayload(formData: IObject, type: submitType) {
    try {
      this.loading = true;
      const instance = { ...this.instance };
      const payload = { ...instance.payload, ...formData }; // 合并新旧数据
      if (this.instance.id) {
        // 更新
        await instanceModel.update({ id: this.instance.id, payload });
        Object.assign(instance, { payload });
        this.$emit('update', instance);
      } else {
        // 提交 或 暂存
        let res;
        if (this.store) {
          res = await this.store.create({ payload, flag: this.flag, ...this.flowable });
        } else {
          res = await instanceModel.createByParent(this.workflow.id!, { payload, ...this.flowable });
        }
        const { data } = res;
        Object.assign(instance, data);
        // 如果是创建并提交，执行 token accept
        if (type === 'accept') {
          await this.accept(data.current_token!);
        }
        this.$emit('create', instance);
      }
      this.$emit('success', instance);

      this.formData = this.instance.payload || {};

      this.loading = false;
      this.visible = false;
      this.throttleDisabled = false;
    } catch (error) {
      this.$emit('fail', error);
      this.loading = false;
      this.throttleDisabled = false;
    }
  }

  async accept(token: IToken) {
    if (token.id) {
      await tokenModel.accept(token.id, '发起审批');
    }
  }
}
</script>

<template lang="pug">
MainModal(v-model='visible', :title='title', :destroyOnClose='true', width='90%')
  .payload-editor__content(v-loading='loading')
    TemplateForm(ref='form', :formData='formData', :template='template', :showLabel='true' :instance='instance')
  .payload-editor__actions(slot='footer')
    template(v-if='instance.id')
      //- 更新 instance.payload
      a-button(type='primary', size='large', @click='submit("save")', :disabled='throttleDisabled', :loading='loading')
        | 更新
    template(v-else)
      //- 创建 instance
      a-button(size='large', @click='submit("save")', :disabled='throttleDisabled', :loading='loading')
        | 暂存
      //- 创建 instance 且 accept instance.current_token
      a-button(
        type='primary',
        size='large',
        @click='submit("accept")',
        :disabled='throttleDisabled',
        :loading='loading'
      )
        | 提交
</template>

<style lang="stylus" scoped>
.payload-editor__content
  overflow auto
  padding 16px 20px
  height 100%

.payload-editor__actions
  text-align right
</style>
