<script lang="ts">
import { TaIndexViewTabInterface } from '@/components/global/TaIndex';
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class ComActivitiesPersonnelExpertTable extends Vue {
  @Prop({ type: Object, default: () => {} }) private store!: IObject;

  state: string = '';

  mounted() {
    this.fetchData();
  }

  fetchData() {}

  private tabs: TaIndexViewTabInterface[] = [{ label: '', key: '', num: 0, background: '', color: '', mode: 'table' }];

  get config() {
    return {
      recordName: '专家',
      store: this.store,
      searcherSimpleOptions: [],
    };
  }
}
</script>

<template lang="pug">
.com-activities-personnel-expert-table
  TaIndexView(:tabs="tabs" :config='config')
    template(#header)
      span.title 专家列表
    template(#right-actions) 
      span.active
        a-icon.icon(type="plus-circle")
        | 添加专家
    template(#table)
      a-table-column(title='姓名',ellipsis="true" :width='170')
      a-table-column(title='联系方式',ellipsis="true" :width='170')
      a-table-column(title='',ellipsis="true" :width='200')
</template>

<style lang="stylus" scoped>
.com-activities-personnel-expert-table
  .title
    color #383838
    font-size 16px
    font-weight 600
    font-family PingFangSC-Semibold, PingFang SC
  .active
    color #3DA8F5
    cursor pointer
    .icon
      margin-right 5px
</style>
