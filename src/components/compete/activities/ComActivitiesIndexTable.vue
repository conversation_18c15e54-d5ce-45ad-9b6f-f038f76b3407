<script lang="ts">
import { TaIndexViewTabInterface } from '@/components/global/TaIndex';
import { IActivity } from '@/models/access/activity';
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class ComActivitiesIndexTable extends Vue {
  @Prop({ type: Object, default: () => {} }) private store!: IObject;

  private key: string = 'published';

  private menus: any = [
    {
      key: 'pending',
      title: '开始比赛',
    },
    {
      key: 'finished',
      title: '结束比赛',
    },
  ];

  private tabs: TaIndexViewTabInterface[] = [
    { label: '已发布', key: 'published', num: 0, background: '', color: '', mode: 'table' },
    { label: '未开始', key: 'notstarted', num: 0, background: '', color: '', mode: 'table' },
    { label: '进行中', key: 'pending', num: 0, background: '', color: '', mode: 'table' },
    { label: '已结束', key: 'finished', num: 0, background: '', color: '', mode: 'table' },
    { label: '草稿箱', key: 'draft', num: 0, background: '', color: '', mode: 'table' },
  ];

  // draft notstarted

  get config() {
    return {
      showCount: true,
      showActions: true,
      recordName: '比赛',
      store: this.store,
      searcherSimpleOptions: [{ label: '比赛名次', type: 'string', key: 'name' }],
    };
  }

  mounted() {
    this.fetchData();
    this.store.init({ q: { state: this.key } });
  }

  fetchData() {}

  private onCreate(): void {
    this.$router.push({ path: '/compete/admin/activities/form' });
  }

  private onShow({ id }: IActivity): void {
    this.$router.push({ path: `/compete/admin/activities/${id}` });
  }

  private tabChange(val: any): void {
    this.key = val.key;
    this.store.init({ params: { q: { state_eq: val.key } } });
  }

  private async onHandleMenus(item: any, val: any): Promise<void> {
    await this.store.update({
      id: val.id,
      state: item.key,
    });
    this.$message.success('状态修改成功');
  }
}
</script>

<template lang="pug">
.com-activities-index-table
  TaIndexView(
    :tabs="tabs",
    :config="config"
     @onShow="onShow"
     :tabsLeftMargin="0"
     @tabChange="tabChange"
     @onCreate="onCreate"
  )
    template(#table)
      a-table-column(title='名称', dataIndex='name', ,ellipsis="true" :width='170')
      a-table-column(title='子比赛',  ellipsis="true" :width='170')
      a-table-column(title='比赛人数', ellipsis="true" :width='170')
      a-table-column(title='比赛时间', dataIndex="start_at" ellipsis="true" :width='170')
        template(slot-scope="scope")
          span {{ $moment(scope).format("YYYY-MM-DD HH:mm") }}
      a-table-column(title='状态', ellipsis="true" :width='170')
        template(slot-scope="scope")
          a-dropdown
            span.state
              span(:class="{activities:  scope.state === 'pending' }")
                | {{ scope.state === "pending" ? "比赛中" : scope.state ==='finished' ? "已结束" : "未开始" }}
              a-icon.icon(type="down")
            a-menu(slot="overlay")
              a-menu-item(v-for="item in menus", :key='item.key', @click="onHandleMenus(item,scope)")
                span {{ item.title }}
</template>

<style lang="stylus" scoped>
.com-activities-index-table
  .icon
    font-size 12  px
    margin-left 5px
    color #8591A2
.state
  cursor pointer
.activities
  color #6DC37D
</style>
