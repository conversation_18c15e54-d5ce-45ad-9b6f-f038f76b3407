<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class ComActivitiesInfoPanel extends Vue {
  @Prop({ type: Object, default: () => {} }) private store!: IObject;
  @Prop({ type: Number, default: () => {} }) private activitiesId!: number;
  private cells: any = [
    {
      label: '比赛名称',
      value: '',
      key: 'name',
    },
    {
      label: '总裁判',
      value: '俞佳莺',
      key: 'teacher',
    },
    {
      label: '总人数',
      value: '',
      key: 'count',
    },
    {
      label: '开始时间',
      value: '',
      key: 'start_at',
    },
    {
      label: '结束时间',
      value: '',
      key: 'end_at',
    },
    {
      label: '',
      value: '',
      key: '',
    },
    {
      label: '内容介绍',
      value:
        '2020-2021学年第一学期，护理学院医护比赛。2020-2021学年第一学期，护理学院医护比赛。2020-2021学年第一学期，护理学院医护比赛。',
      key: 'content',
    },
  ];

  mounted() {
    this.store.init();
    this.fetchData();
  }

  private async fetchData(): Promise<void> {
    let { data } = await this.store.find(this.activitiesId);
    this.cells.forEach((item: any) => {
      if (item.key in data) {
        item.value =
          item.key === 'start_at' || item.key === 'end_at'
            ? this.$moment(data[item.key]).format('YYYY年MM月DD日')
            : data[item.key];
      }
    });
  }
}
</script>

<template lang="pug">
.com-activities-info-panel
  .title 比赛信息
  .content
    .cell(v-for="(item,index) in cells" :key="index")
      span.label {{ item.label }}
      a-tooltip.value(placement="topLeft")
        template(#title)
          span {{ item.value  }}
        span.value {{ item.value }}
    
</template>

<style lang="stylus" scoped>
.com-activities-info-panel
  width 100%
  height 100%
  border-radius 2px
  padding 5px 20px 0 20px
  background #ffffff
  margin-bottom 16px
  min-height 220px
  .title
    height 48px
    color #383838
    font-size 16px
    font-weight 600
    line-height 48px
    border-bottom 1px solid #E5E5E5
    font-family PingFangSC-Semibold, PingFang SC
  .content
    width 100%
    display flex
    flex-wrap wrap
    flex-direction row
    padding 21px 24px
    .cell
      display flex
      flex-direction row
      width 30%
      cursor pointer
      flex-wrap wrap
      margin-right 20px
      margin-bottom 20px
      .label
        width 110px
        color #383838
        font-size 14px
        font-weight 500
        font-family PingFangSC-Medium, PingFang SC
      .value
        flex 1
        color #808080
        font-size 14px
        overflow hidden
        white-space nowrap
        display inline-block
        text-overflow ellipsis
        font-family PingFangSC-Regular, PingFang SC
</style>
