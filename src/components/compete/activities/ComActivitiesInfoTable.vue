<script lang="ts">
import { TaIndexViewTabInterface } from '@/components/global/TaIndex';
import { Component, Vue, Prop } from 'vue-property-decorator';
import { Template } from '@/views/compete/activities/template';

@Component({
  components: {},
})
export default class ComActivitiesIndexTable extends Vue {
  @Prop({ type: Object, default: () => {} }) private store!: IObject;
  @Prop({ type: Array, default: () => [] }) private readonly parents!: [];

  private tabs: TaIndexViewTabInterface[] = [
    { label: '全部', key: 'release', num: 9, background: '', color: '', mode: 'table' },
    { label: '未开始', key: 'notstarted', num: 2, background: '', color: '', mode: 'table' },
    { label: '进行中', key: 'pedding', num: 4, background: '', color: '', mode: 'table' },
    { label: '已结束', key: 'end', num: 5, background: '', color: '', mode: 'table' },
  ];

  get config() {
    return {
      showCount: true,
      showActions: true,
      recordName: '比赛',
      store: this.store,
      template: this.template,
      searcherSimpleOptions: [{ label: '比赛名称', key: 'name', type: 'string' }],
    };
  }

  get template(): IObject {
    return Template;
  }

  mounted() {
    this.fetchData();
    this.store.init({ parents: this.parents });
  }

  fetchData() {}

  private onCreate(formDate: any) {}

  onShow() {}
}
</script>

<template lang="pug">
.com-activities-index-table
  TaIndexView(:tabs="tabs", :config="config" @onShow="onShow" :tabsLeftMargin="0" @onCreate="onCreate")
    template(#table)
      a-table-column(title='名称', dataIndex="name" ,ellipsis="true" :width='170')
      a-table-column(title='比赛人数', ellipsis="true" :width='170')
      a-table-column(title='比赛时间', dataIndex="start_at" ellipsis="true" :width='170')
        template(slot-scope="scope")
          span {{ $moment(scope).format("YYYY-MM-DD HH:mm") }}
</template>

<style lang="stylus" scoped>
.com-activities-index-table
  height 100%
  background #ffffff
  padding 20px
  min-height  500px
</style>
