<script lang="ts">
import { TaIndexViewTabInterface } from '@/components/global/TaIndex';
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class ComActivitiesPersonnelPlayerTable extends Vue {
  @Prop({ type: Object, default: () => {} }) private store!: IObject;

  private tabs: TaIndexViewTabInterface[] = [{ label: '', key: '', num: 0, background: '', color: '', mode: 'table' }];

  get config() {
    return {
      recordName: '选手',
      store: this.store,
      searcherSimpleOptions: [],
    };
  }
}
</script>

<template lang="pug">
.com-activities-personnel-player-table
  TaIndexView(:tabs="tabs" :config='config')
    template(#header)
      span.title 选手列表
    template(#right-actions)
      span.active
        a-icon.icon(type="plus-circle")
        | 导入学生
    template(#table)
      a-table-column(title='姓名',ellipsis="true" :width='170')
      a-table-column(title='联系方式',ellipsis="true" :width='170')
      a-table-column(title='班级',ellipsis="true" :width='170')
      a-table-column(title='',ellipsis="true" :width='170')
</template>

<style lang="stylus" scoped>
.com-activities-personnel-player-table
  .title
    color #383838
    font-size 16px
    font-weight 600
    font-family PingFangSC-Semibold, PingFang SC
  .active
    color #3DA8F5
    cursor pointer
    .icon
      margin-right 5px
</style>
