<script lang="ts">
import { TaIndexViewTabInterface } from '@/components/global/TaIndex';
import { Component, Vue, Prop } from 'vue-property-decorator';
import ComActivitiesPersonnelList from './ComActivitiesPersonnelList.vue';

@Component({
  components: { ComActivitiesPersonnelList },
})
export default class ComActivitiesPersonnelPlayerTable extends Vue {
  @Prop({ type: String, default: '' }) prop!: string;
  @Prop({ type: Object, default: () => {} }) private store!: IObject;

  state: string = '';

  mounted() {
    this.fetchData();
  }

  private Item: any = {
    ScoringItem: '评分项',
    category: '评分类目',
    score: '得',
  };

  private Items: any = {
    ScoringItem: '服装、鞋帽整洁 ',
    category: '职业形象',
    score: '1',
  };
  private listData: any = [
    {
      id: 1,
      ScoringItem: '服装、鞋帽整洁 ',
      category: '职业形象',
      score: 23,
    },
    {
      id: 2,
      ScoringItem: '服装、鞋帽整洁 ',
      category: '职业形象',
      score: 23,
    },
    {
      id: 3,
      ScoringItem: '服装、鞋帽整洁 ',
      category: '职业形象',
      score: 23,
    },
  ];

  private tabs: TaIndexViewTabInterface[] = [{ label: '', key: '', num: 0, background: '', color: '', mode: 'table' }];

  get config() {
    return {
      recordName: '选手',
      store: this.store,
      searcherSimpleOptions: [{ type: 'string', label: '姓名', key: 'name' }],
      detailConfig: {
        detailMode: 'drawer',
        detailWidth: '60%',
      },
    };
  }

  fetchData() {}
}
</script>

<template lang="pug">
.com-activities-personnel-player-table
  TaIndexView(:tabs="tabs" :config="config")
    template(#header)
      span.title 选手列表
    template(#table)
      a-table-column(title='姓名',ellipsis="true" :width='200')
      a-table-column(title='专业',ellipsis="true" :width='170')
      a-table-column(title='班级',ellipsis="true" :width='300')
      a-table-column(title='得分',ellipsis="true" :width='150')
    template(#detail)
      ComActivitiesPersonnelList(:record="Item" :class="{listHeader: true}")
      TaIndexList(:data="listData")
        template(#default="{ record }")
          ComActivitiesPersonnelList(:record='record')
</template>

<style lang="stylus" scoped>
.com-activities-personnel-player-table
  width 100%
  height 100%
  padding 20px
  min-height 500px
  border-radius 2px
  background #ffffff
  .title
    color #383838
    font-size 16px
    font-weight 600
    font-family PingFangSC-Semibold, PingFang SC
.listHeader
  background #F6F6F6
  color #383838
</style>
