<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class ComActivitiesPersonnelDrawerList extends Vue {
  @Prop({ type: Object, default: () => {} }) private readonly record!: any;
  mounted() {}
}
</script>

<template lang="pug">
.com-activities-personnel-drawer-list
  .left
    .category {{ record.category }}
    .score {{ record.ScoringItem }}
  .rigth {{ `${record.score}分` }}
</template>

<style lang="stylus" scoped>
.com-activities-personnel-drawer-list
  width 100%
  height 42px
  display flex
  color #808080
  font-size 14px
  padding 0 10px
  background #ffffff
  flex-direction row
  align-items center
  justify-content space-between
  border-bottom 1px solid #E5E5E5
  font-weight 600
  .rigth
    line-height 42px
    height 100%
  .left
    flex 1
    display flex
    flex-direction row
    margin-right 15px
    .category
      margin-right 64px
    .score
      width 500px
      flex 1
      overflow hidden
      white-space nowrap
      display inline-block
      text-overflow ellipsis
</style>
