<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

import { intlUserReportStore } from '@/store/modules/intl/user/report.store';
import { TaIndexViewTabInterface } from '@/components/global/TaIndex';
import { IReport } from '@/models/pt/report';

@Component({
  components: {},
})
export default class ComInltReports extends Vue {
  @Prop({ type: Number }) private readonly sourceId!: number;
  @Prop({ type: String }) private readonly sourceName!: string;
  @Prop({ type: Object, default: () => {} }) private store!: IObject;

  private type: any = 0;
  private activeName: string = '周报';
  private tabs: Array<TaIndexViewTabInterface> = [
    { label: ' ', key: 'reports', num: 0, background: '', color: '', mode: 'table' },
  ];
  private muen: Array<{ name: string; type: number }> = [
    { name: '周报', type: 0 },
    { name: '总结报告', type: 1 },
  ];

  get config(): Object {
    return {
      store: this.store,
      recordName: '报告管理',
      searcherSimpleOptions: [{ key: 'title', label: '标题', type: 'string' }],
    };
  }

  mounted() {
    this.store.init({
      parents: [{ type: this.sourceName, id: this.sourceId }],
      params: { q: { flag_eq: 0 } },
    });
  }

  private onCreate(): void {
    this.$router.push({
      path: `/intl/user/activities/${this.sourceId}/reports/form`,
      query: { parents: this.type },
    });
  }

  private onShow(val: IReport): void {
    this.$router.push({
      path: `/intl/user/activities/${this.sourceId}/reports/${val.id}/edit`,
      query: { parents: this.type },
    });
  }

  private onSelect(name: string, type: string): void {
    this.type = +type;
    this.activeName = name;
    this.store.init({ parents: [{ type: this.sourceName, id: this.sourceId }], params: { q: { flag_eq: this.type } } });
  }
}
</script>

<template lang="pug">
.ComInltReports
  .muen 
    .title 分类
    .muen-item(v-for="item in muen" :key="item.name" 
              @click="onSelect(item.name, item.type)" 
              :class="{active: activeName === item.name}")
      .name {{ item.name }}
  .table
    TaIndexView(:tabs="tabs" :config="config" @onShow="onShow")
      template(#header)
        .table-title {{ activeName }}
      template(#table)
        a-table-column(title='#' :width="10")
          template(slot-scope='scope')
            span {{scope._index}}
        a-table-column(title='标题' dataIndex="title" :width="20")
        a-table-column(title='姓名' :width="20")
          template(slot-scope="scope")
            span {{ scope.user.name }}
        a-table-column(title='工号' :width="20")
          template(slot-scope="scope")
            span {{ scope.user.code }}
        a-table-column(title='年周' :width="20")
        a-table-column(title='提交时间' :width="20")
          template(slot-scope="scope")
            span {{  $moment(scope.created_at).format('YYYY/MM/DD HH:MM:SS') }}
      template(#right-actions)
        TextButton.btn(icon="plus-circle" @click="onCreate")  添加{{ activeName }}

</template>

<style lang="stylus" scoped>
.ComInltReports
  display flex
  flex-direction row
  padding-top 20px
  .muen
    width 300px
    background  white
    height 260px
    .title
      margin 20px
      font-size 16px
      font-family PingFangSC-Medium, PingFang SC
      font-weight 500
      color #383838
    .muen-item
      height 70px
      line-height 70px
      .name
        padding 0 20px
        font-size 14px
        font-family PingFangSC-Medium, PingFang SC
        font-weight 500
        color #383838
      .bottom
        margin 0 20px
        font-size 14px
        font-family PingFangSC-Regular, PingFang SC
        font-weight 400
        color #A6A6A6
        margin-top 10px
        .label
          margin-right 10px
        .right
          margin-left 20px
    .muen-item:hover
      background #fafafa
  .table
    padding 10px 20px
    margin-left 20px
    background white
    flex 1
    .table-title
      font-weight 600
      font-size 16px
      font-family PingFangSC-Semibold, PingFang SC
.active
  box-sizing content-box
  border-left 3px solid #3DA8F5
  background #fafafa
</style>
