<script lang="ts">
import UserSelector from '@/components/hr/UserSelector.vue';

import { Component, Vue, Prop } from 'vue-property-decorator';
import { TaIndexViewTabInterface } from '@/components/global/TaIndex';

@Component({
  components: { UserSelector },
})
export default class ComIntlDetails extends Vue {
  @Prop({ type: Number }) private readonly sourceId!: number;
  @Prop({ type: String }) private readonly sourceName!: string;
  @Prop({ type: Object, default: () => {} }) private store!: IObject;
  @Prop({ type: Object, default: () => {} }) private userStore!: IObject;

  private tab: any = {};
  private plan: any = {};
  private amount: number = 0;
  private title: string = '添加人员';
  private visibleInfo: boolean = false;
  private completedPaymentAmount: number = 0;
  private processingPaymentAmoun: number = 0;
  private selectedTeacherIds: Array<number> = [];
  private searcherSimpleOptions: Array<any> = [
    { key: 'name', label: '名字', type: 'string' },
    { key: 'code', label: '工号', type: 'string' },
  ];
  private tabs: Array<TaIndexViewTabInterface> = [
    { label: '全部人员', key: 'all', num: 0, background: '', color: '', mode: 'table' },
    { label: '管理员', key: 'owned_', num: 0, background: '', color: '', mode: 'table' },
    { label: '组长', key: 'manage_', num: 0, background: '', color: '', mode: 'table' },
    { label: '组员', key: 'following_', num: 0, background: '', color: '', mode: 'table' },
  ];

  get config(): IObject {
    return {
      recordName: '成员',
      store: this.userStore,
      searcherSimpleOptions: this.searcherSimpleOptions,
    };
  }

  get activityId(): number | undefined {
    return +this.$route.params.activityId || undefined;
  }

  mounted() {
    this.fetchData();
  }

  private selectPeople(): void {
    this.visibleInfo = true;
  }

  private setTitle(val: string): void {
    switch (val) {
      case 'all':
        this.title = '添加人员';
        break;
      case 'owned_':
        this.title = '添加管理员';
        break;
      case 'manage_':
        this.title = '添加组长';
        break;
      case 'following_':
        this.title = '添加组员';
        break;
    }
  }

  private changeSelect(ids: Array<number>): void {
    this.selectedTeacherIds = ids;
  }

  private tabChange(val: TaIndexViewTabInterface): void {
    this.tab = val;
    this.setTitle(val.key);
    this.setStoreInit();
  }

  private async onConfirm(): Promise<void> {
    let params = {};
    switch (this.tab.key) {
      case 'owned_':
        params = { id: this.sourceId, owned_teacher_ids: this.selectedTeacherIds };
        break;
      case 'manage_':
        params = { id: this.sourceId, manage_teacher_ids: this.selectedTeacherIds };
        break;
      case 'following_':
        params = { id: this.sourceId, following_teacher_ids: this.selectedTeacherIds };
        break;
    }
    this.visibleInfo = false;
    await this.store.update(params);
    this.setStoreInit();
    this.$message.success('添加成功');
  }

  private async fetchData(): Promise<void> {
    let { data } = await this.store.find(this.activityId);
    this.plan = data;
    this.amount = this.plan.finance_budget_info.amount;
    this.completedPaymentAmount = this.plan.finance_budget_info.completed_payment_amount;
    this.processingPaymentAmoun = this.plan.finance_budget_info.processing_payment_amount;
  }

  private setStoreInit(): void {
    const params = { q: { pastable_pastings_type_eq: this.tab.key } };
    const parents = [{ type: this.sourceName, id: this.sourceId }];
    this.tab.key !== 'all' ? this.userStore.init({ parents, params }) : this.userStore.init({ parents });
  }
}
</script>

<template lang="pug">
.com-intl-details
  Panel(title="计划信息")
    .Panel
      .Panel-left
        a-row.row(:span="21")
          a-col.col(:span="7")
            span.label 计划名称
            span {{ plan.name }}
          a-col.col(:span="7")
            span.label 管理员
            a-popover(placement="topLeft" trigger="hover")
              template(slot="content")
                span(v-for="item in plan.owned_teachers" :key="item.id")
                  span  {{ item.name }}
              span(v-for="item in plan.owned_teachers" :key="item.id")
                span  {{ item.name }}
          a-col.col(:span="7")
            span.label 可报销
            span {{ completedPaymentAmount | toCurrency}}
        a-row.row(:span="21")
          a-col.col(:span="7")
            span.label 开始时间
            span {{ plan.start_at }}
          a-col.col(:span="7")
            span.label 组长
            a-popover(placement="topLeft" trigger="hover")
              template(slot="content")
                span(v-for="item in plan.manage_teachers" :key="item.id")
                  span  {{ item.name }}
              span(v-for="item in plan.manage_teachers" :key="item.id")
                span  {{ item.name }}
          a-col.col(:span="7")
            span.label 报销中
            span {{ Number(completedPaymentAmount) - Number(processingPaymentAmoun) | toCurrency }}
        a-row.row(:span="21")
          a-col.col(:span="7")
             span.label 结束时间
             span {{plan.end_at}}
          a-col.col(:span="7")
            span.label 总人数
            span {{ plan.member_count }}
          a-col.col(:span="7")
            span.label 已报销
            span  {{ processingPaymentAmoun | toCurrency}}
      .Panel-right
        .box
          .title 实际使用率
          a-progress( type="dashboard" :percent='$tools.getRate(completedPaymentAmount,amount)', strokeColor="#75C940")
        .box
          .title 预期使用率
          a-progress( type="dashboard" 
            :percent='$tools.getRate((+processingPaymentAmoun)+(+completedPaymentAmount),amount)', 
            strokeColor="#75C940")
  .table
    TaIndexView(:tabs="tabs" :config="config" @tabChange="tabChange")
      template(#actions)
        TextButton.btn(icon="plus-circle" @click="selectPeople") 添加人员
      template(#table)
        a-table-column(title='序号' :width="10")
          template(slot-scope='scope')
            span {{scope._index}}
        a-table-column(title='姓名' dataIndex="name" :width="20")
        a-table-column(title='工号' dataIndex="code" :width="20")
        a-table-column(title='部门' dataIndex="department_name" :width="20")
        a-table-column(title='岗位'   :width="20")
  MainModal(
    v-model="visibleInfo"
    :title="title"
    :width="1200")
    .modal-row(slot="footer")
      .count
        span 已选择
        span.text-primary(style="margin: 0px 4px") {{ selectedTeacherIds.length || 0 }}
        span 人
      a-button(type="primary" size="large" style="width: 100px" @click="onConfirm") 确定
    UserSelector(
      ref="memberSelector"
      :userIds="selectedTeacherIds"
      style="padding: 0px 20px"
      @change="changeSelect")

</template>

<style lang="stylus" scoped>
.com-intl-details
  height 100%
  .Panel
    display flex
    flex-direction  row
    padding 20px 0px
    .Panel-left
      width 70%
      .row
        margin-bottom 20px
        .label
          display inline-block
          width 120px
          font-size 14px
          font-family PingFangSC-Medium, PingFang SC
          font-weight 500
          color #383838
        .col
          margin-right 20px
          overflow hidden
          text-overflow ellipsis
          white-space nowrap
    .Panel-right
      flex 1
      display flex
      justify-content space-around
      .box
        text-align center
        .title
          margin-bottom 5px
          font-size 14px
          font-family PingFangSC-Medium, PingFang SC
          font-weight 500
          color #383838
  .table
    margin-top 16px
    background white
.btn
  margin-right 20px
</style>
