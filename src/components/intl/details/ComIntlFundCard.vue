<script lang="ts">
import toCurrency from '@/helpers/filters/toCurrency';
import FinanceProjectTable from '@/components/finance/ProjectTable.vue';

import { IBudget } from '@/models/finance/budget';
import { IProject } from '@/models/finance/project';
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { TaIndexViewTabInterface } from '@/components/global/TaIndex';
import { intlUserActivityStore } from '@/store/modules/intl/user/activity.store';
import { financeTeacherOwnActivitiesProjectStore } from '@/store/modules/finance/teacher/own/project.store';
import { financeTeacherOwnProjectBudgetsStore } from '@/store/modules/finance/teacher/own/budgets.store';

export interface ISteps {
  title: string;
  key: string;
  icon: string;
}

@Component({
  components: { FinanceProjectTable },
})
export default class ComIntlFundCard extends Vue {
  @Prop({ type: Object, default: () => {} }) store!: IObject;
  @Prop({ type: Number }) private readonly sourceId!: string;
  @Prop({ type: String }) private readonly sourceName!: string;
  @Prop({ type: Object, default: () => {} }) activeStore!: IObject;

  private visible: boolean = false;
  private activeStep: string = 'school';
  private selectedRowKeys: Array<number> = [];
  private steps: Array<ISteps> = [
    { title: '关联学校', key: 'school', icon: 'link' },
    { title: '关联资金卡', key: 'finance', icon: 'link' },
    { title: '关联三级内容', key: 'context', icon: 'link' },
  ];
  private tabs: Array<TaIndexViewTabInterface> = [
    { label: ' ', key: 'all', num: 0, background: '', color: '', mode: 'table' },
  ];

  get quantity(): number {
    return this.store.records.length;
  }

  get userActivityStore(): IObject {
    return intlUserActivityStore;
  }

  get budgetsStore(): IObject {
    return financeTeacherOwnProjectBudgetsStore;
  }

  get projectsStore(): IObject {
    return financeTeacherOwnActivitiesProjectStore;
  }

  // 总金额
  get amount(): number {
    let amount: number = 0;
    this.store.records.map((item: IBudget) => {
      amount = amount + Number(item.amount);
    });
    return amount;
  }

  //
  get config(): IObject {
    return {
      store: this.store,
      recordName: '资金卡',
      searcherSimpleOptions: [],
      tableConfig: { bordered: true, showHeader: true },
    };
  }

  get projectsConfig(): IObject {
    return {
      store: this.projectsStore,
      recordName: '',
      searcherSimpleOptions: [
        { key: 'name', label: '资金卡', type: 'string' },
        { key: 'school_name', label: '学校', type: 'string' },
        { key: 'uid', label: '资金卡号', type: 'string' },
      ],
    };
  }

  // 已收件
  get checkedPaymentAmount(): number {
    let amount: number = 0;
    this.store.records.map((item: IBudget) => {
      amount = amount + Number(item.checked_payment_amount);
    });
    return amount;
  }

  // 报销中
  get processingPaymentAmount(): number {
    let amount: number = 0;
    this.store.records.map((item: IBudget) => {
      amount = amount + Number(item.processing_payment_amount);
    });
    return amount;
  }

  // 已报销
  get completedPaymentAmount(): number {
    let amount: number = 0;
    this.store.records.map((item: IBudget) => {
      amount = amount + Number(item.completed_payment_amount);
    });
    return amount;
  }

  get activeConfig(): IObject {
    return {
      store: this.activeStore,
      recordName: '',
      searcherSimpleOptions: [{ key: 'school_name', label: '学校', type: 'string' }],
      tableConfig: {},
    };
  }

  get budgetsConfig(): IObject {
    return {
      store: this.budgetsStore,
      recordName: '',
      searcherSimpleOptions: [{ key: 'name', label: '资金卡 ', type: 'string' }],
      tableConfig: {
        rowSelection: { selectedRowKeys: this.selectedRowKeys, onChange: this.onSelectChange },
      },
    };
  }

  // 可报销
  get reimbursement(): number {
    let amount: number = 0;
    this.store.records.map((item: IBudget) => {
      amount =
        amount + Number(item.amount) - Number(item.processing_payment_amount) - Number(item.completed_payment_amount);
    });
    return amount;
  }

  mounted() {
    this.userActivityStore.init();
  }

  private onShow(): void {
    this.visible = true;
  }

  private handleOk(): void {
    this.visible = false;
  }

  private handleCancel(): void {
    this.visible = false;
    this.activeStep = 'school';
  }

  private onSelectChange(selectedRowKeys: any): void {
    this.selectedRowKeys = selectedRowKeys;
  }

  private activeShow(val: IProject): void {
    this.activeStep = 'finance';
    this.projectsStore.init({ parents: [{ type: 'activities', id: val.id }] });
  }

  private getStatus(val: string): string {
    let state = val === 'doing' ? '进行中' : val === 'todo' ? '未完成' : '已完成';
    return state;
  }

  private budgetsShow(val: IBudget): void {
    this.activeStep = 'context';
    this.budgetsStore.init({ parents: [{ type: 'projects', id: val.id }] });
  }

  private async onSumbit(): Promise<void> {
    const params = { id: +this.sourceId, finance_budget_ids: this.selectedRowKeys };
    await (this.selectedRowKeys.length > 0 ? this.userActivityStore.update(params) : this.$message.warning('关联失败'));
    this.store.init({ parents: [{ type: this.sourceName, id: this.sourceId }] });
    this.visible = false;
  }

  private getBalanceAmount(record: IBudget) {
    return this.$utils.toCurrency(
      Number(record.amount) - Number(record.processing_payment_amount) - Number(record.completed_payment_amount),
    );
  }

  private getProjectAmount(record: IProject) {
    return this.$utils.toCurrency(
      Number(record.amount) - Number(record.processing_voucher_amount) - Number(record.completed_voucher_amount),
    );
  }

  private previous(): void {
    this.activeStep = this.activeStep === 'context' ? 'finance' : this.activeStep === 'finance' ? 'school' : 'context';
  }
}
</script>

<template lang="pug">
.ComIntlFundCard
  //-  资金使用情况统计
  Panel(title="资金使用情况统计")
    template(#actions)
      .actions(@click="onShow")
        a-icon(type="link")
        span 关联资金卡
    .Panel
      .Panel-left
        .data-edition
          .title  总金额
          .number
            | {{ amount | toCurrency }}
            span 元
        .data-edition
          .title  可报销
          .number
            | {{ reimbursement | toCurrency}}
            span 元
        .data-edition
          .title  报销中
          .number
            | {{ processingPaymentAmount | toCurrency}}
            span 元
        .data-edition
          .title  已报销
          .number
            | {{ completedPaymentAmount | toCurrency}}
            span 元
      .Panel-right
        .box
          .title 实际使用率
          a-progress(
            type="dashboard"
            :percent="$tools.getRate(completedPaymentAmount, amount)"
            strokeColor='#9EE995'
          )
        .box
          .title 预期使用率
          a-progress(
            type="dashboard"
            :percent='$tools.getRate((+completedPaymentAmount) + (+checkedPaymentAmount), amount)', 
            strokeColor='#9EE995'
          )
  //-  关联资金卡表格
  .table
    TaIndexView(:tabs="tabs" :config="config")
      template(#header)
        .title 已关联资金卡· {{ quantity }}
      template(#table)
        a-table-column(title='二级内容' dataIndex="catalog_name" :width="20")
        a-table-column(title='三级内容' dataIndex="name" :width="20")
        a-table-column(title='科目' dataIndex="subject_name" :width="20")
        a-table-column(title='来源' dataIndex="origin_name"  :width="20")
          template(slot-scope="origin_name")
            | {{ origin_name || '资金池统筹' }}
        a-table-column(title='金额' dataIndex="amount" :width="20")
          template(slot-scope="amount")
            span {{ amount | toCurrency }}
        a-table-column(title='金额详情' :width="30")
          template(slot-scope="record")
            .info 可报销：{{ getBalanceAmount(record) }}
            .info 报销中：{{ record.processing_payment_amount | toCurrency }}
            .info 已报销：{{ record.completed_payment_amount | toCurrency }}
            .info(v-if='record.sub_project_amount > 0') 已分配：{{ record.sub_project_amount | toCurrency }}
        a-table-column(title='实际使用率' :width="20" align="center")
          template(slot-scope="record")
            a-progress( 
              type="dashboard"
              :width="64"
              :percent="$tools.getRate(record.completed_payment_amount, record.amount)"
              strokeColor="#75C940")
        a-table-column(title='预期使用率' :width="20" align="center")
         template(slot-scope="{ completed_payment_amount, checked_payment_amount, amount }")
          a-progress(
            type="dashboard"
            :width="64"
            :percent="$tools.getRate((+completed_payment_amount) + (+checked_payment_amount), amount)"
            strokeColor="#75C940")
  //-  关联资金卡弹窗
  a-modal.modal(
      :visible="visible"
      :closable="false"
      @ok="handleOk"
      @cancel="handleCancel"
      :width="1200"
      :footer="false"
  )
    .header
      .title 添加资金卡
      StepToolbar.toolbar(
      v-model="activeStep"
      :steps="steps"
      :border="true"
      mode="steps")   
    //-  1 关联学校
    .context(v-if="activeStep === 'school'")
      TaIndexView(:tabs="tabs" :config="activeConfig" @onShow="activeShow") 
        template(#header)
          span.title 项目列表
        template(#table)  
          a-table-column(title='学校' dataIndex="school_name" :width="500")
          a-table-column(title='年份' dataIndex="year" :width="300")
          a-table-column(title='状态' dataIndex="state" :width="300")
            template(slot-scope="scope")
              span {{ getStatus(scope) }}
    //- 2 关联资金卡
    .context(v-if="activeStep === 'finance'") 
      TaIndexView(:tabs="tabs" :config="projectsConfig" @onShow="budgetsShow") 
        template(#header)
          span.title 执行资金卡
        template(#table)  
          a-table-column(title='资金卡' dataIndex="name" :width="270")
          a-table-column(title='资金卡号' dataIndex="uid" :width="270")
          a-table-column(title='总金额'  dataIndex="amount" :width="270")
          a-table-column(title='可报销金额' :width="270")
            template(slot-scope="record")
              span {{ getProjectAmount(record) }}
    //-  3 关联三级内容
    .context(v-if="activeStep === 'context'")
      TaIndexView(:tabs="tabs" :config="budgetsConfig") 
        template(#header)
          span.title 选择三级内容
        template(#table)  
          a-table-column(title='二级内容' dataIndex="catalog_name" :width="150")
          a-table-column(title='三级内容' dataIndex="name" :width="150")
          a-table-column(title='科目' dataIndex="subject_name" :width="140")
          a-table-column(title='来源' dataIndex="origin_name"  :width="140")
            template(slot-scope="origin_name")
              | {{ origin_name || '资金池统筹' }}
          a-table-column(title='金额' dataIndex="amount" :width="150")
            template(slot-scope="amount")
              span {{ amount | toCurrency }}
          a-table-column(title='金额详情' :width="150")
            template(slot-scope="record")
              .info 可报销：{{ getBalanceAmount(record) }}
              .info 报销中：{{ record.processing_payment_amount | toCurrency }}
              .info 已报销：{{ record.completed_payment_amount | toCurrency }}
              .info(v-if='record.sub_project_amount > 0') 已分配：{{ record.sub_project_amount | toCurrency }}
          a-table-column(title='实际使用率' :width="170")
            template(slot-scope="record")
              a-progress(
                type="dashboard"
                :width="64"
                :percent="$tools.getRate(record.completed_payment_amount, record.amount)"
                strokeColor="#75C940")
    //-  关联资金卡弹窗 footer button
    .footer
      a-button(@click="handleCancel") 取消
      a-button(type="primary" @click="previous" v-if="activeStep !== 'school'") 上一步
      a-button(type="primary" @click='onSumbit' v-if="activeStep === 'context'") 确认
    
</template>

<style lang="stylus" scoped>
.ComIntlFundCard
  .Panel
    padding 20px 50px
    display flex
    flex-direction row
    .Panel-left
      width 70%
      display flex
      flex-direction row
      justify-content center
      .data-edition
        height 100%
        width 25%
        .title
          font-size 12px
          font-family PingFangSC-Medium, PingFang SC
          font-weight 500
          color #A6A6A6
        .number
          margin-top 21px
          font-size 28px
          font-family DINCondensed-Bold, DINCondensed
          font-weight bold
          color #383838
          span
            font-size 12px
            font-family PingFangSC-Regular, PingFang SC
            font-weight 400
            color #808080
            margin-left 5px
    .Panel-right
      flex 1
      display flex
      flex-direction row
      justify-content center
      .box
        text-align center
        margin 0 40px
        .title
          margin-bottom 10px
  .table
    padding 17px 20px 77px 20px
    margin-top 16px
    background #ffffff
    .financeProjectTable
      margin-top 13px
.modal
  text-align center
  .header
    display flex
    flex-direction row
    justify-content center
    align-items center
    .toolbar
      position relative
      left 25%
      margin 0px auto
      display flex
      flex-direction row
      justify-content center
      align-items center
    .title
      width 100px
      font-size 18px
      font-family PingFangSC-Medium, PingFang SC
      font-weight 500
      color #383838
  .context
    min-height 400px
    .title
      font-size 14px
      font-family PingFangSC-Medium, PingFang SC
      font-weight 500
      color #383838
  .footer
    display flex
    flex-direction row
    justify-content flex-end
.actions
  font-size 14px
  font-family PingFangSC-Medium, PingFang SC
  font-weight 500
  color #3DA8F5
  cursor pointer
</style>
