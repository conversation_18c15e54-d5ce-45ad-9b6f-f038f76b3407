<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

interface ComAssessmentUserInfoLabelDataInterface {
  name: string;
  lines: { label: string; value: string }[][];
}

@Component({
  components: {},
})
export default class ComAssessmentUserInfoLabel extends Vue {
  @Prop({ type: Object, required: true }) data!: IObject;
}
</script>

<template lang="pug">
.com-assessment-user-info-label
  .info
    .name {{ data.name }}
    .line(v-for='(line_info, index) in data.lines', :key='index'  )
      p(v-for='item in line_info', :key='item.label') {{ `${item.label}：${item.value}`}}
</template>

<style lang="stylus" scoped>
.com-assessment-user-info-label
  .info
    background #3CA9F4
    padding 16px 18px
    border-radius 4px
    .name
      font-size 16px
      font-weight 500
      color #FFFFFF
      line-height 20px
      margin-bottom 2px
    .line
      display flex
      margin 8px 0
      p
        margin-right 40px
        font-size 14px
        font-weight 400
        color #FFFFFF
        line-height 20px
</style>
