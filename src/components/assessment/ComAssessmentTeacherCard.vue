<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import activityStore from '@/store/modules/access/activity.store';
import { IAssessmentActivity } from '@/types/model';
import ComAssessmentCard from './ComAssessmentCard.vue';

@Component({
  components: {
    ComAssessmentCard,
  },
})
export default class ComAssessmentTeacherCard extends Vue {
  @Prop({ type: Object, required: true }) record!: IAssessmentActivity;
}
</script>

<template lang="pug">
.com-assessment-teacher-card
  ComAssessmentCard(:record='record')
    template(#bottom)
      .bottom
        .ta-tag(v-if='record.scored')
          a-icon(type="team")
          span 考核他人
        .ta-tag(v-if='record.entried')
          a-icon(type="user")
          span 被考核人
</template>

<style lang="stylus" scoped>
.bottom
  padding 10px 0
  display flex
  justify-content flex-end
  .ta-tag
    span
      margin-left 4px
</style>
