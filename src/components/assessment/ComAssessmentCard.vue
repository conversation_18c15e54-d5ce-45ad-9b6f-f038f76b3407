<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import activityStore from '@/store/modules/access/activity.store';
import { IAssessmentActivity } from '@/types/model';
import { ActiveStore } from '@/lib/ActiveStore';

@Component({
  components: {},
})
export default class ComAssessmentCard extends Vue {
  @Prop({ type: Object, required: true }) record!: IAssessmentActivity;

  get currentIndex() {
    const existsIndex = this.record.stages.stages.map(item => item.key).indexOf(this.record.stage?.key);
    if (existsIndex === -1) {
      const start_at = this.record.stages.stages[0]?.start_at;
      if (start_at && this.$moment(this.record.stages.stages[0].start_at).unix() >= this.$moment().unix()) {
        return -1;
      } else {
        return this.record.stages.stages.findIndex(stage => ['doing', 'done'].includes(stage.state as string));
      }
    }
    return existsIndex;
  }
}
</script>

<template lang="pug">
.com-assessment-card
  .card
    .card-top
      .name {{ record.name }}
      slot(name='right-top', :record='record')
    .card-middle
      .cell
        .key 被考核
        .value {{ record.entry_count }} 人
      .cell
        .key 考核内容
        .value {{ record.content }}
      .cell
        .key 考核分类
        .value(v-if='typeof record.catalogs === "object"') {{ record.catalogs.map(i => i.name).join('、') }}
      .cell
        .key 考核分组
        .value {{ record.group_count }}

      .steps
        a-steps(:current="currentIndex" size="small", )
          a-step(
            v-for="(step, key) in record.stages.stages"
            :key="key"
            :title="step.name"
            style="width: 240px"
          )
            p(slot="description" style="width: 240px")
              span(v-if="step.start_at && step.end_at") {{ step.start_at }} 至 {{ step.end_at }}
              span(v-else) 无
    .card-bottom(v-if='$slots.bottom')
      slot(name='bottom', :record='record')
</template>

<style lang="stylus" scoped>
.card
  margin-bottom 12px
  padding 16px 16px 0px
  width 100%
  border 1px solid rgba(0, 0, 0, 0.08)
  border-radius 2px
  .card-top
    display flex
    justify-content space-between
    align-records center
    width 100%
    .name
      color #383838
      font-weight 500
      font-size 14px
      line-height 20px
  .card-middle
    padding 6px 0px 16px
    width 100%
    .cell
      display flex
      padding 2px 0px
      width 100%
      font-size 14px
      line-height 22px
      .key
        min-width 90px
        color #a6a6a6
      .value
        color #808080
    .steps
      margin-top 10px
      width 100%
  .card-bottom
    padding 8px 0px
    border-top 1px #E8E8E8 solid
</style>
