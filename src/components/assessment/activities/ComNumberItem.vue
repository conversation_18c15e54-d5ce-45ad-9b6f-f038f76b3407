<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class ComNumberItem extends Vue {
  @Prop({ type: String, default: '' }) prop!: string;
  state: string = '';
  mounted() {
    this.fetchData();
  }
  fetchData() {}
}
</script>

<template lang="pug">
.com-number-item
  .num F99999
  a-icon.icon(type="close-circle")
</template>

<style lang="stylus" scoped>
.com-number-item
  height 40px
  width 176px
  display flex
  border-radius 3px
  background #FFF7E6
  flex-direction row
  align-items center
  justify-content center
  position relative
  .icon
    position absolute
    top -2px
    right 0px
</style>
