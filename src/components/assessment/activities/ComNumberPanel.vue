<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class ComNumberPanel extends Vue {
  @Prop({ type: String, default: '' }) prop!: string;

  private visible: boolean = false;
  private value: number | string = '';
  private activeNum: string = '';

  private NumberArray: Array<any> = [
    {
      num: 'F00001',
    },
    {
      num: 'F00002',
    },
    {
      num: 'F00003',
    },
    {
      num: 'F00004',
    },
    {
      num: 'F00005',
    },
    {
      num: 'F00006',
    },
  ];

  mounted() {
    this.fetchData();
  }

  fetchData() {}

  private addNumber(): void {
    this.visible = true;
  }

  // 失去焦点
  private blur(): void {
    this.visible = false;
    this.value ? this.addNum() : '';
    this.value = '';
  }

  private addNum(): void {
    this.NumberArray.push({ num: this.value });
    this.$message.success('编号添加成功');
  }

  private pressEnter(): void {
    this.visible = false;
  }

  private onHandleActionNum(item: any): void {
    this.activeNum = item.num;
  }
}
</script>

<template lang="pug">
.com-number-panel
  .header
    .title 编号管理
    .active 导入编号
  .content
    .items(v-for="item in NumberArray" @click="onHandleActionNum(item)" :class="{action: item.num === activeNum}")
      .num {{ item.num }}
      a-icon.icon(type="close-circle" :class="{actionicon: item.num === activeNum}")
    .additems(@click="addNumber")
      a-icon.plus_icon(type="plus")
      span 添加编号
    .additems_input(v-if="visible")
      a-input.input(
        type="text"
        @blur="blur"
        maxLength="6"
        v-model="value"
        placeholder="请输入编号" 
        @pressEnter="pressEnter"
      )
</template>

<style lang="stylus" scoped>
.com-number-panel
  width 100%
  height 100%
  display flex
  flex-direction column
  .header
    height 40px
    display flex
    flex-direction row
    justify-content space-between
    align-items center
    border-bottom 1px solid #E5E5E5
    .title
      font-size 16px
      font-family PingFangSC-Semibold, PingFang SC
      font-weight 600
      color #383838
    .active
      color #3DA8F5
      cursor pointer
      font-weight 500
  .content
    margin-top 18px
    display flex
    flex-direction row
    flex-wrap wrap
    justify-content flex-start
    align-content flex-start
    flex-shrink 1
    .items,.additems
      cursor pointer
      height 40px
      width 176px
      display flex
      border-radius 3px
      background #FFF7E6
      flex-direction row
      align-items center
      justify-content center
      position relative
      // margin 0 10px
      margin-right 20px
      margin-bottom 12px
      .num
        color #FA8C15
        font-size 16px
        font-weight 500
      .icon
        display none
        position absolute
        top -8px
        right -8px
        color #FA8C15
    .additems
      color #808080
      background #F5F5F5
      .plus_icon
        margin-right 5px
    .additems_input
      height 40px
      width 176px
      .input
        width 100%
        height 100%
.action
  border 1px solid #FA8C15
.actionicon
  display block !important
  z-index 9
</style>
