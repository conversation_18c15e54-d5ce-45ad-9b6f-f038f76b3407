<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import ComNumberCard from './ComNumberCard.vue';
import ComNumberGreenCard from './ComNumberGreenCard.vue';

@Component({
  components: { ComNumberCard: ComNumberCard, ComNumberGreenCard: ComNumberGreenCard },
})
export default class ComNumberSortPanle extends Vue {
  @Prop({ type: String, default: '' }) prop!: string;

  state: string = '';

  private CardArray: any = [
    {
      name: '张三',
      code: '2019141110',
      num: '',
    },
    {
      name: '张三',
      code: '2019141110',
      num: '',
    },
    {
      name: '张三',
      code: '2019141110',
      num: '',
    },
    {
      name: '张三',
      code: '2019141110',
      num: '99999',
    },
    {
      name: '张三',
      code: '2019141110',
      num: '99999',
    },
    {
      name: '张三',
      code: '2019141110',
      num: '99999',
    },
  ];

  mounted() {
    this.fetchData();
  }

  fetchData() {}
  private onHandlerlottery(): void {
    this.$emit('lottery');
  }
}
</script>

<template lang="pug">
.com-number-sort-panle
  .header 选手编号排序
  .content
    template(v-for="item in CardArray")
      component(:is="item.num ? 'ComNumberCard': 'ComNumberGreenCard'" :item="item" @lottery="onHandlerlottery")
</template>

<style lang="stylus" scoped>
.com-number-sort-panle
  position relative
  width 100%
  height 100%
  display flex
  flex-direction column
  .header
    height 40px
    display flex
    color #383838
    font-size 16px
    font-weight 600
    align-items center
    flex-direction row
    justify-content space-between
    border-bottom 1px solid #E5E5E5
    font-family PingFangSC-Semibold, PingFang SC
  .content
    margin-top 18px
    display flex
    flex-direction row
    flex-wrap wrap
    justify-content flex-start
    align-content flex-start
    flex-shrink 1
    .cards
      display flex
      flex-direction row
      width 225px
      height 90px
      background #F5F5F5
      border-radius 3px
      justify-content center
      align-items center
      padding 8px 20px
      .right
        display flex
        flex-direction column
        justify-content center
        width 70px
        height 100%
        font-family PingFangSC-Semibold, PingFang SC
        .btn
          cursor pointer
          text-align center
          height 30px
          line-height 30px
          background #FFF7E6
          color #FA8C15
      .left
        flex 1
        color #A6A6A6
        font-weight 500
</style>
