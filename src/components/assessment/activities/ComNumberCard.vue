<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class Card extends Vue {
  @Prop({ type: String, default: '' }) prop!: string;
  @Prop({ type: Object, default: () => [] }) private readonly item!: IObject;

  mounted() {
    this.fetchData();
  }

  fetchData() {}

  private onHandleLottery(): void {
    this.$emit('lottery');
  }
}
</script>

<template lang="pug">
.card
  .left
    .name 张三
    .code 2019141110
    .num 编号： 暂无
  .right
    .btn(@click="onHandleLottery") 点击抽奖
</template>

<style lang="stylus" scoped>
.card
  display flex
  flex-direction row
  width 225px
  height 90px
  background #F5F5F5
  border-radius 3px
  justify-content center
  align-items center
  padding 8px 20px
  margin-right 10px
  margin-bottom 10px
  .right
    display flex
    flex-direction column
    justify-content center
    width 70px
    height 100%
    font-family PingFangSC-Semibold, PingFang SC
    .btn
      cursor pointer
      text-align center
      height 30px
      line-height 30px
      background #FFF7E6
      color #FA8C15
  .left
    flex 1
    color #A6A6A6
    font-weight 500
</style>
