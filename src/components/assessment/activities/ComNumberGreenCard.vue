<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class ComNumberGreenCard extends Vue {
  @Prop({ type: String, default: '' }) prop!: string;
  @Prop({ type: Object, default: () => [] }) private readonly item!: IObject;

  mounted() {
    this.fetchData();
  }

  fetchData() {}
}
</script>

<template lang="pug">
.card
  .top
    .name 吴亦凡
    .code 2019141110
  .bottom   
    .num 编号：2020001

</template>

<style lang="stylus" scoped>
.card
  display flex
  flex-direction column
  width 225px
  height 90px
  background #F0F9F2
  border-radius 3px
  padding 18px 14px 8px 27px
  margin 0 10px 10px 0px
  color #6DC37D
  font-family PingFangSC-Medium, PingFang SC
  .top
    height 27px
    display flex
    flex-direction row
    align-items center
    .name
      font-size 16px
      font-weight 500
    .code
      margin-left 8px
      font-size 14px
  .bottom
    flex 1
    display flex
    flex-direction column
    justify-content flex-end
    align-items flex-end
</style>
