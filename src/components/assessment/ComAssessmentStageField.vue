<script lang="ts">
import { IAssessmentActivityStagesItem } from '@/types/model';
import { Moment } from 'moment';
import { log } from 'util';
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';

let uid = 0;

@Component({
  components: {},
})
export default class ComAssessmentStageField extends Vue {
  @Model('change', { type: Object, default: () => ({}) }) value?: { stages: IAssessmentActivityStagesItem[] };
  @Prop({ type: Boolean, default: false }) disabled?: boolean;

  items: IAssessmentActivityStagesItem[] = [];

  keyGenerator() {
    return `key_${Date.now()}_${++uid}`;
  }

  @Watch('items', { deep: true })
  handleitemsChange() {
    this.$emit('change', { stages: this.items });
  }

  @Watch('value', { deep: true, immediate: true })
  handleValueChange() {
    if (JSON.stringify(this.value?.stages) !== JSON.stringify(this.items)) {
      this.items = this.value?.stages || [];
    }
  }

  getTimeRangeByIndex(index: number) {
    return this.items[index].start_at && this.items[index].end_at
      ? [
          this.$moment(this.$moment(this.items[index].start_at), 'YYYY-MM-DD'),
          this.$moment(this.$moment(this.items[index].end_at), 'YYYY-MM-DD'),
        ]
      : null;
  }

  onChange(index: number, dates: Moment[]) {
    this.items[index].start_at = dates[0].format('YYYY-MM-DD');
    this.items[index].end_at = dates[1].format('YYYY-MM-DD');
  }

  addItem() {
    const item = {
      name: '',
      key: this.keyGenerator(),
      start_at: '',
      end_at: '',
    };

    this.items.push(item);
  }
}
</script>

<template lang="pug">
.com-assessment-stage-field
  .item(v-for='(item, index) in items')
    a-form-item(:label='`考核阶段${index + 1}`')
      a-input(
        v-model='item.name',
        :disabled='disabled',
        placeholder='请输入阶段名称',
      )
      a-range-picker(
        size='large',
        style='width:440px',
        :disabled='disabled',
        :value='getTimeRangeByIndex(index)',
        @change='onChange(index, $event)',
      )

  TextButton.button(v-if='!disabled', icon='plus-circle', theme='filled', @click='addItem')
    | 添加阶段
</template>

<style lang="stylus" scoped>
.button
  margin 0 0 20px -8px !important
</style>
