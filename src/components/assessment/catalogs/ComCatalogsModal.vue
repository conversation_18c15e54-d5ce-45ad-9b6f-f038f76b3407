<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class ComCatalogsModal extends Vue {
  @Prop({ type: String, default: '' }) prop!: string;
  @Prop({ type: Number, default: () => 600 }) private readonly width!: number;
  @Prop({ type: String, default: '关联评分表' }) private readonly title!: string;
  @Prop({ type: Boolean, default: () => false }) private readonly visible!: boolean;
  state: string = '';

  private items: any = [
    {
      name: '评分表评分表评分表名称1',
    },
    {
      name: '评分表评分表评分表名称2',
    },
    {
      name: '评分表评分表评分表名称3',
    },
  ];

  mounted() {
    this.fetchData();
  }

  handleOk() {
    this.$emit('handleOk');
  }

  handleCancel() {
    this.$emit('cancel');
  }

  private onHandle(item: any): void {}

  fetchData() {}
}
</script>

<template lang="pug">
a-modal.com-catalogs-modal(
  :title="title"
  :visible="visible"
  @ok="handleOk"
  @cancel="handleCancel"
  :width="width"
)
  .items(v-for="item in items")
    .name {{ item.name }}
    a-button(type="primary" @click="onHandle") 选择
    //- link
</template>

<style lang="stylus" scoped>
.items
  width 90%
  height 58px
  display flex
  margin 0 auto
  padding-left 46px
  align-items center
  flex-direction row
  justify-content space-between
  border-bottom 1px solid  #E5E5E5
  .name
    color #383838
    font-size 14px
    font-weight 500
    font-family PingFangSC-Medium, PingFang SC
</style>
