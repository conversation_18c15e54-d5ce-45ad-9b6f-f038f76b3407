<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import ComCatalogsStepToolbar from './ComCatalogsStepToolbar.vue';
import ComCatalogsMenu from './ComCatalogsMenu.vue';
@Component({
  components: { ComCatalogsStepToolbar, ComCatalogsMenu },
})
export default class ComCatalogsDimensionDetails extends Vue {
  @Prop({ type: String, default: '' }) prop!: string;
  @Prop({ type: Object, default: () => {} }) private store!: IObject;
  @Prop({ type: Array, default: () => [] }) private readonly parents!: [];
  @Prop({ type: Object, default: () => {} }) private readonly dimensions!: IObject;
  @Prop({ type: String, default: () => '' }) private readonly catalogName!: string;

  state: string = '';

  get id(): number {
    return +this.$route.params.id;
  }

  get breadcrumb(): Array<{ name: string; url?: string }> {
    return [
      { name: '考核分类', url: `/assessment/admin/activities/${this.id}/catalogs` },
      { name: this.catalogName },
      { name: this.dimensions.name },
    ];
  }

  mounted() {
    this.fetchData();
    this.store.init({ parnts: this.parents });
    this.store.find(this.dimensions.id);
    // console.log(this.dimensions);
  }

  fetchData() {}
}
</script>

<template lang="pug">
.ComCatalogsDimensionDetails
  .header 
    ComCatalogsStepToolbar(:breadcrumb="breadcrumb")
  .content
    .left 
      //- ComCatalogsMenu
    .right 

</template>

<style lang="stylus" scoped>
.ComCatalogsDimensionDetails
  height 100%
  width 100%
  .header
    height 54px
    display flex
    flex-direction row
    align-items center
    padding 0 20px
    border-bottom  1px solid #DDDDDD
  .content
    width 100%
    height 100%
    display flex
    flex-direction row
    .left
      height 100%
      width 280px
      border 1px solid #E5E5E5
      border-top 0px
</style>
