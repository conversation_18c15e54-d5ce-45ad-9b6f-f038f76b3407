<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class ComCatalogsEvaluationItem extends Vue {
  @Prop({ type: String, default: '' }) prop!: string;
  @Prop({ type: Object, default: () => {} }) private readonly record!: IObject;

  state: string = '';

  private items: any = [
    {
      label: '总分',
      content: '100分',
      key: '',
    },
    {
      label: '考核项目',
      content: '10人',
      key: '',
    },
    {
      label: '考核人',
      content: '10人',
      key: '',
    },

    {
      label: '被考核人',
      content: '10人',
      key: '',
    },
  ];

  mounted() {
    this.fetchData();
  }

  fetchData() {}
}
</script>

<template lang="pug">
.com-catalogs-evaluation-item
  .top  辅导员工作认同度测评表
  .icon
    a-icon(type="delete")
  .bottom
    .item(v-for="item in items")
      span.label {{ item.label }}
      span.content {{ item.content }}
</template>

<style lang="stylus" scoped>
.com-catalogs-evaluation-item
  display flex
  flex-direction column
  width 100%
  height 120px
  margin-bottom 10px
  border-radius 2px
  padding 20px 30px
  background-image linear-gradient(to right, #3DA8F5 0%,  #70D3FB 100%)
  .top
    width 100%
    height 25px
    color #FFFFFF
    font-size 18px
    font-weight 500
    font-family PingFangSC-Medium, PingFang SC
  .bottom
    width 100%
    display flex
    font-size 14px
    font-weight 500
    flex-direction row
    font-family PingFangSC-Medium, PingFang SC
    .item
      color #ffffff
      margin-right 30px
      .label
        margin-right 15px

  .icon
    flex 1
    display flex
    color #FFFFFF
    align-items center
    flex-direction  row
    justify-content flex-end
</style>
