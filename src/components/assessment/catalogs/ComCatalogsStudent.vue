<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

import ComCatalogsEvaluationItem from '@/components/assessment/catalogs/ComCatalogsEvaluationItem.vue';

import ComCatalogsCommitItem from '@/components/assessment/catalogs/ComCatalogsCommitItem.vue';
import { TaIndexViewTabInterface } from '@/components/global/TaIndex';

@Component({
  components: { ComCatalogsEvaluationItem, ComCatalogsCommitItem },
})
export default class ComCatalogsStudent extends Vue {
  @Prop({ type: Object, default: () => {} }) private store!: IObject;

  state: string = '';

  mounted() {
    this.fetchData();
  }

  private tabs: TaIndexViewTabInterface[] = [
    { label: '', key: 'execute', num: 0, background: '', color: '', mode: 'list' },
  ];

  get config() {
    return {
      store: this.store,
      recordName: ' ',
      searcherSimpleOptions: [{ type: 'string', label: '名称', key: 'name' }],
    };
  }

  fetchData() {}
}
</script>

<template lang="pug">
.com-catalogs-student
  TaIndexView( :config="config", :tabs="tabs")
    template(#card='{ record }')
      ComCatalogsEvaluationItem(:record="record")
</template>

<style lang="stylus" scoped>
.com-catalogs-student
  width  100%
  height 100%
  padding 0 20px
</style>
