<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class ComCatalogsCommitItem extends Vue {
  mounted() {
    this.fetchData();
  }

  fetchData() {}
}
</script>

<template lang="pug">
.com-catalogs-commit-item
  .top
  .bottom
  
</template>

<style lang="stylus" scoped>
.com-catalogs-commit-item
  width 100%
  height 80px
  border-radius 5px
  padding 14px 20px
  display flex
  background #E8E8E8
  margin-top 12px
  .top
    width 100%
    height 20px
    display flex
    flex-direction row
</style>
