<script lang="ts">
import { IDimensions } from '@/models/assessment/admin/activity/dimensions';
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class ComCatalogsMenu extends Vue {
  @Prop({ type: Object, default: () => {} }) private store!: IObject;
  @Prop({ type: Array, default: () => [] }) private readonly parents!: [];
  @Prop({ type: Object, default: () => {} }) private readonly activityStore!: IObject;

  private form: any = {};
  private visible: boolean = false;
  private activeName: string = '部门领导';

  mounted() {
    this.store.init({ parents: this.parents });
    // this.store.index();
  }

  get records(): Array<any> {
    return this.store.records || [];
  }

  get activityRecord(): IObject {
    return this.activityStore.record || {};
  }

  get stages(): IObject {
    return this.activityRecord.stages || {};
  }

  get stageOptions(): IObject {
    return (this.stages.stages || []).map((item: any) => {
      return { label: item.name, value: item.key };
    });
  }

  get template() {
    return [
      {
        key: 'name',
        label: '考核维度',
        widgetType: 'text',
        widget: 'input',
        placeholder: '请输入考核维度',
      },
      {
        key: 'weight',
        label: '评分占权重比',
        widgetType: 'number',
        widget: 'input',
        placeholder: '请输入权重',
        suffix: '%',
      },
      {
        key: 'stage',
        label: '考核阶段',
        widgetType: 'text',
        widget: 'select',
        placeholder: '请选择',
        options: this.stageOptions,
      },
      {
        key: 'type',
        label: '考核类型',
        widgetType: 'text',
        widget: 'select',
        placeholder: '请选择考核类型',
        options: [
          {
            label: '部门领导',
            value: 'Assessment::Dimensions::Department',
          },
          {
            label: '班级学生',
            value: 'Assessment::Dimensions::Adminclass',
          },
          {
            label: '互评',
            value: 'Assessment::Dimensions::Mutual',
          },
        ],
      },
    ];
  }

  private onHandleActive(item: IDimensions): void {
    this.activeName = item.name;
    this.$emit('active', item);
  }

  private onHandleAdd(): void {
    this.form = {};
    this.visible = !this.visible;
  }

  private async submit(val: any): Promise<void> {
    this.visible = false;
    await this.store.create(val);
    this.$message.success('维度创建成功');
  }

  private getType(val: string): string | undefined {
    switch (val) {
      case 'Assessment::Dimensions::Department':
        return '部门领导';
      case 'Assessment::Dimensions::Adminclass':
        return '班级学生';
      case 'Assessment::Dimensions::Mutual':
        return '互评';
    }
  }

  private getStageLabel(val: string) {
    let values: Array<string> = this.stageOptions.map((item: { label: string; value: string }) => {
      return item.value;
    });
    return values.indexOf(val) > -1 ? this.stageOptions[values.indexOf(val)].label : '';
  }
}
</script>

<template lang="pug">
.com-catalogs-menu
  .menu-add(@click="onHandleAdd")
    a-icon.icon(type="plus-circle")
    span 创建考核维度
    PopoverForm(
      :value="visible"
      placement="bottomRight"
      :form="form"
      title="创建考核维度"
      :template="template"
      @submit="submit"
    )
  .menu-item(
    v-for="item in records"
     @click="onHandleActive(item)"
     :class="{activemenu: activeName === item.name}"
  )
    .top(:class="{activemenuTop: activeName === item.name}")
      span {{ item.name }}
      span  | {{ getType(item.type) }}
    .bottom
      span  {{ `评分占比${item.weight}%`  }}
      span  | {{ getStageLabel(item.stage) }}
</template>

<style lang="stylus" scoped>
.com-catalogs-menu
  width 100%
  height 100%
  .menu-add
    height 56px
    width 100%
    display flex
    color #808080
    cursor pointer
    padding-left 20px
    align-items center
    flex-direction row
    border-bottom  1px solid #E5E5E5
    font-family PingFangSC-Regular, PingFang SC
    .icon
      margin-right 5px
  .menu-item
    height 76px
    width 100%
    display flex
    cursor pointer
    padding-left 20px
    flex-direction column
    justify-content center
    border-bottom  1px solid #E5E5E5
    font-family PingFangSC-Regular, PingFang SC
    .top
      font-size 14px
      color #383838
    .bottom
      color #A6A6A6
      font-size 12px
  .menu-item:hover
    .top
      color #3DA8F5 !important

.activemenu
  border-left 4px solid #3DA8F5 !important
  box-sizing border-box
.activemenuTop
  color #3DA8F5 !important
</style>
