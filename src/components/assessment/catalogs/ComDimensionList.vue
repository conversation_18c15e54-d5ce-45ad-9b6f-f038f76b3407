<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class ComDimensionList extends Vue {
  @Prop({ type: Array, default: () => [] }) private readonly list!: [];
  mounted() {
    this.fetchData();
  }

  fetchData() {}

  private getDimensionState() {
    this.list.forEach((item: any) => {
      if (item.score) {
        return true;
      }
    });
  }
}
</script>

<template lang="pug">
.ComDimensionList
  template
    .items(v-for="item in list")
      .label
        | {{ `${item.name}:    ` }}
        | {{ `${Math.round(item.score * 100) / 100 || 0} 分` }}
        | {{ `已评分: ${item.score_stat.done || 0}人` }}
        | {{ `待评分: ${item.score_stat.todo || 0}人` }}
</template>

<style lang="stylus" scoped>
.ComDimensionList
  height 100%
  width 100%
  .items
    display flex
    flex-direction row
    .label
      width 100%
      font-size 14px
      line-height 25px
      color #808080
</style>
