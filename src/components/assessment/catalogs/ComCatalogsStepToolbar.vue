<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class ComCatalogsStepToolbar extends Vue {
  @Prop({ type: String, default: '' }) prop!: string;
  @Prop({ type: String, default: () => '>' }) private separator!: string;
  @Prop({ type: Array, default: () => [] }) private readonly breadcrumb!: [];
  @Prop({ type: Boolean, default: () => false }) private displayHideAndShow!: boolean;

  mounted() {
    this.fetchData();
  }

  fetchData() {}

  private breadcrumbPage(item: { name: string; url?: string }): void {
    if (item.url) {
      this.$router.push({ path: item.url });
      this.$emit('breadcrumbPage');
    }
  }
}
</script>

<template lang="pug">
.ComCatalogsStepToolbar
  slot(name="right")
  .breadcrumb
    a-breadcrumb(:separator="separator")
      a-breadcrumb-item.breadcrumb_item(v-for="item in breadcrumb", :key='item.name')
        span(@click="breadcrumbPage(item)") {{ item.name }}
  slot(name="right")
  .prompt(v-if="displayHideAndShow")
    a-icon.icon(type="exclamation-circle")
    span 还缺评分占比 10%
</template>

<style lang="stylus" scoped>
.ComCatalogsStepToolbar
  display flex
  flex-direction row
.breadcrumb
  cursor pointer
.breadcrumb_item
  font-size 16px
  font-family PingFangSC-Medium, PingFang SC
  font-weight 500
  color #808080
.breadcrumb_item:last-child
  color #383838
.prompt
  padding 0 5px
  background #F2FBFF
  min-width 200px
  margin-left 10px
  display flex
  flex-direction row
  align-items center
  border-radius 2px
  .icon
    color #3DA8F5
    margin-right 15px
  span
    color #3DA8F5
    font-size  12px
    font-family PingFangSC-Regular, PingFang SC
</style>
