<script lang="ts">
import { TaIndexViewTabInterface } from '@/components/global/TaIndex';
import { IFormTemplateItem } from '@/interfaces/formTemplate.interface';
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { commAdminScoreTemplateStore } from '@/store/modules/comm/admin/score_template.store';
import {
  IAssessmentActivityStagesItem,
  IAssessmentCatalog,
  IAssessmentDimension,
  ICommScoreTemplate,
} from '@/types/model';
import { IDimensions } from '@/models/assessment/admin/activity/dimensions';
import TaIndexView from '@/components/global/TaIndexView.vue';

import ComCatalogsStepToolbar from './ComCatalogsStepToolbar.vue';
import { ActiveStore } from '@/lib/ActiveStore';
import { assessmentAdminDimensionFormStore } from '@/store/modules/assessment/admin/dimension.store';
import TaImport from '@/components/global/TaImport.vue';

@Component({
  components: { ComCatalogsStepToolbar },
})
export default class ComCatalogsDimensions extends Vue {
  @Prop({ type: Object, default: () => {} }) private store!: ActiveStore;
  @Prop({ type: Array, default: () => [] }) private readonly parents!: [];
  @Prop({ type: Object, default: () => {} }) private readonly activityStore!: IObject;
  @Prop({ type: String, default: () => '' }) private readonly catalogName!: string;

  private options: Array<{ label: string; value: number }> = [];
  private typeOptions: IObject = [
    {
      label: '部门领导',
      value: 'Assessment::Dimensions::Department',
    },
    {
      label: '班级学生',
      value: 'Assessment::Dimensions::Adminclass',
    },
    {
      label: '互评',
      value: 'Assessment::Dimensions::Mutual',
    },
  ];

  stageOptions: Array<{ label: string; value: number }> = [];

  get activityRecord(): IObject {
    return this.activityStore.record || {};
  }

  get scoreTemplateStore() {
    return commAdminScoreTemplateStore;
  }

  get id(): number {
    return +this.$route.params.id;
  }

  get breadcrumb(): Array<{ name: string; url?: string }> {
    return [{ name: '考核分类', url: `/assessment/admin/activities/${this.id}/catalogs` }, { name: this.catalogName }];
  }

  @Watch('activityRecord.id', { immediate: true })
  handleActivityChange() {
    this.stageOptions = (this.activityRecord.stages || { stages: [] }).stages.map(
      (stage: IAssessmentActivityStagesItem) => ({
        label: stage.name,
        value: stage.key,
      }),
    );
  }

  created() {
    this.scoreTemplateStore.init();
    this.scoreTemplateStore.index({ per_page: 999999 }).then(res => {
      const options = res.data.records.map((record: ICommScoreTemplate) => ({
        label: record.name,
        value: record.id,
      }));
      this.options = options;
    });
  }

  get scoreTemplateIdOptions(): Array<{ label: string; value: number }> {
    return this.scoreTemplateStore.records.map((item: ICommScoreTemplate) => {
      return { label: item.name, value: item.id };
    });
  }

  get formDimensionStore() {
    return assessmentAdminDimensionFormStore;
  }

  private getscoreTemplateName(id: string | number): any {
    return this.scoreTemplateIdOptions.forEach((item: { label: string; value: number }) => {
      if (id === item.value) {
        return item.label;
      }
    });
  }

  private getStageOptionsName(val: number): string | undefined {
    return this.stageOptions.find(item => val === item.value)?.label;
  }

  private getType(val: string): string | undefined {
    switch (val) {
      case 'Assessment::Dimensions::Department':
        return '部门领导';
      case 'Assessment::Dimensions::Adminclass':
        return '班级学生';
      case 'Assessment::Dimensions::Mutual':
        return '互评';
    }
  }

  get template(): IFormTemplateItem[] {
    return [
      {
        key: 'name',
        name: '考核维度',
        layout: {
          component: 'input',
          placeholder: '请输入考核维度名称',
          type: 'string',
          required: true,
        },
        model: {
          attr_type: 'string',
        },
      },
      {
        key: 'weight',
        name: '评分占权重比',
        layout: {
          component: 'input',
          placeholder: '请输入评分占权重比',
          type: 'number',
          required: true,
          max: 100,
          min: 0,
          unit: '%',
        },
        model: {
          attr_type: 'number',
        },
      },
      {
        key: 'stage',
        name: '考核阶段',
        layout: {
          component: 'select',
          placeholder: '请选择考核阶段',
          type: 'string',
          required: true,
          options: this.stageOptions,
        },
        model: {
          attr_type: 'string',
        },
      },
      {
        key: 'type',
        name: '考核类型',
        layout: {
          component: 'select',
          placeholder: '请选择考核类型',
          type: 'string',
          required: true,
          options: [
            {
              label: '部门领导',
              value: 'Assessment::Dimensions::Department',
            },
            {
              label: '班级学生',
              value: 'Assessment::Dimensions::Adminclass',
            },
            {
              label: '互评',
              value: 'Assessment::Dimensions::Mutual',
            },
            {
              label: '其他',
              value: 'Assessment::Dimensions::SpecifyUser',
            },
          ],
        },
        model: {
          attr_type: 'string',
        },
      },
      {
        key: 'if_container_1612319432919',
        name: '条件块',
        layout: {
          component: 'if_container',
          conditionKey: 'type',
          conditionValue: 'Assessment::Dimensions::SpecifyUser',
          templateIndexAry: [5, 6],
        },
        accessibility: 'hidden',
        model: {
          attr_type: 'string',
        },
      },
      {
        key: 'teacher_ids',
        name: '考核人-教师',
        layout: {
          span: 24,
          required: false,
          component: 'teacher',
          placeholder: '请输入',
        },
        model: {
          attr_type: 'array',
        },
      },
      {
        key: 'student_ids',
        name: '考核人-学生',
        layout: {
          span: 24,
          required: false,
          component: 'student',
          placeholder: '请输入',
        },
        model: {
          attr_type: 'array',
        },
      },
      {
        key: 'if_container_1612319432919',
        name: '块结束',
        model: {
          attr_type: 'string',
        },
        layout: {
          component: 'container_end',
        },
        accessibility: 'hidden',
      },
      {
        key: 'score_template_id',
        name: '关联评分表',
        layout: {
          component: 'select',
          placeholder: '请选择关联评分表',
          type: 'string',
          options: this.scoreTemplateIdOptions,
        },
        model: {
          attr_type: 'number',
        },
      },
      {
        key: 'paste_assessment_dimension_ids',
        name: '统计纬度',
        layout: {
          component: 'store_field',
          placeholder: '请选择统计纬度',
          type: 'array',
          multiple: true,
        },
        model: {
          attr_type: 'array',
          store: this.formDimensionStore,
          storeConfig: {
            initParams: {
              parents: [{ type: 'catalogs', id: +this.$route.params.catalogId }],
            },
            tableColumns: [
              { title: '名称', dataIndex: 'name', type: 'string' },
              { title: '阶段', dataIndex: 'stage', type: 'string' },
            ],
          },
        },
      },
    ];
  }

  get config() {
    return {
      store: this.store,
      recordName: '考核维度',
      showActions: true,
      template: this.template,
      mode: 'table',
      tableConfig: {
        bordered: true,
      },
      searcherSimpleOptions: [{ label: '名称', key: 'name', type: 'string' }],
      formDataDecode: this.formDataDecode,
      formDataEncode: this.formDataEncode,
    };
  }

  private onEdit(scope: any): void {
    (this.$refs.TaIndexView as TaIndexView<IDimensions>).onEdit(scope);
  }

  private deleteCatalogs(id: number): void {
    this.$confirm({
      title: '确定要继续执行此操作吗?',
      content: '该操作是不可逆的',
      okText: '确定',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        await this.store.delete(id);
        this.$message.success('成功删除分组');
      },
      onCancel: () => {},
    });
  }

  private onShow(item: IDimensions): void {
    this.$emit('onShow', item);
  }

  formDataDecode(record: IAssessmentDimension) {
    return {
      ...record,
      weight: record.weight / 100,
      options: {
        users: record.teacher_ids
          ?.map(id => ({ id, type: 'Teacher' } || []))
          .concat(record.student_ids?.map(id => ({ id, type: 'Student' })) || []),
      },
    };
  }

  formDataEncode(record: IAssessmentDimension) {
    return {
      ...record,
      weight: Math.round(record.weight * 100),
      teacher_ids: (record.options?.users?.filter(record => record.type === 'Teacher') || []).map(record => record.id),
      student_ids: (record.options?.users?.filter(record => record.type === 'Student') || []).map(record => record.id),
    };
  }

  importOptions: IObject = {};

  onImport(record: IDimensions) {
    this.importOptions = { dimension_id: record.id };
    (this.$refs.importComponent as TaImport).onClickFileInput();
  }

  refresh() {
    (this.$refs.TaIndexView as TaIndexView<IDimensions>).slienceRefresh();
  }
}
</script>

<template lang="pug">
.ComCatalogsDimensions
  TaIndexView(ref='TaIndexView', :config="config" @onShow="onShow")
    template(#header)
      ComCatalogsStepToolbar(:breadcrumb="breadcrumb")
    template(#table)
      a-table-column(title='名称', dataIndex="name", :width='170')
      a-table-column(title='评分占权重比', dataIndex="weight", :width='130')
        template(slot-scope="scope")
          span {{ Math.round(scope * 100) }}%
      a-table-column(title='考核阶段', dataIndex="stage", :width='170')
        template(slot-scope="scope")
          span {{ getStageOptionsName(scope) || "-" }}
      a-table-column(title='考核类型', dataIndex="type", :width='170')
        template(slot-scope="scope")
          span {{ getType(scope) || "-" }}
      a-table-column(title='人数', dataIndex="entry_count" :width='170')
        template(slot-scope="scope")
          span {{`被考核人: ${scope || 0}` }}
      a-table-column(title='评分情况', dataIndex="dimension_stat" :width='200')
        template(slot-scope="scope")
          span {{ `已评 ${scope && scope.score_stat.done || 0} 人` }}，
          span {{ `待评 ${scope && scope.score_stat.todo || 0} 人` }}；
      a-table-column(title='关联互评评分表', dataIndex="score_template_name", :width='200')
        template(slot-scope="scope")
          span {{ scope ||  "-" }}
      a-table-column(title='操作', :width='170')
        template(slot-scope="scope")
          .active
            IconTooltip(icon='import', tips='导入分数', @click='onImport(scope)')
            IconTooltip(icon="edit", tips="编辑", @click='onEdit(scope)')
            IconTooltip(icon="delete", tips="删除" @click="deleteCatalogs(scope.id)")

  TaImport.hidden(
    ref='importComponent',
    :store='store',
    :confirmOptions='importOptions'
    @success='refresh'
  )

</template>

<style lang="stylus" scoped>
.ComCatalogsDimensions
  padding 20px
.active
  display none
tr:hover
  .active
    display block
.hidden
  display none
</style>
