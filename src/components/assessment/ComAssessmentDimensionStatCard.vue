<script lang="ts">
import { IAssessmentDimensionStat } from '@/types/model';
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { assessmentUserScoredEntryScoreStore } from '../../store/modules/assessment/user/scored/entry_score.store';

@Component({
  components: {},
})
export default class ComAssessmentDimensionStatCard extends Vue {
  @Prop({ type: Object, required: true }) dimensionStat!: IAssessmentDimensionStat;
  @Prop({ type: Number, required: true }) entryId!: number;

  visibleDetail = false;

  get entryScoreStore() {
    return assessmentUserScoredEntryScoreStore;
  }

  get config() {
    return {
      store: this.entryScoreStore,
      mode: 'table',
    };
  }

  onShowDetail() {
    this.visibleDetail = true;
  }

  onCloseDetail() {
    this.visibleDetail = false;
  }

  onTaIndexViewMounted() {
    this.entryScoreStore.init({
      parents: [{ type: 'entries', id: this.entryId }],
      params: { q: { dimension_id_eq: this.dimensionStat.id } },
    });
  }
}
</script>

<template lang="pug">
.com-dimension-stat-card
  .dimension(@click='onShowDetail')
    .top
      .name {{ dimensionStat.name }}
      .score {{ dimensionStat.score }}分
    .bottom
      .stat 已评：{{ dimensionStat.score_stat.done || 0 }}人 未评：{{ dimensionStat.score_stat.todo || 0 }}人
    .info-icon
      a-icon(type='info-circle', theme='filled')
  a-drawer(
    :visible='visibleDetail',
    :width='800',
    :closable='false',
    @close='onCloseDetail'
  )
    TaIndexView(:config='config', @mounted='onTaIndexViewMounted', v-if='visibleDetail')
      template(#header)
        TaTitleHeader(title='打分情况')
      template(#table)
        a-table-column(title='考核人',  dataIndex="user_name" :width='120')
        a-table-column(title='考核维度',  dataIndex="dimension_name" :width='120')
        a-table-column(title='工号', dataIndex="user_code"  :width='120')
        a-table-column(title='部门', dataIndex="user_department_name" :width='120')
        a-table-column(title='总分', dataIndex="score"  :width='120')
</template>

<style lang="stylus" scoped>
.dimension
  cursor pointer
  height 84px
  background #FFFFFF
  border-radius 4px
  border 3px solid #EDF7FF
  padding 15px 17px
  display flex
  flex-direction column
  justify-content space-between
  position relative
  .top
    display flex
    justify-content space-between
    font-size 14px
    font-weight 600
    line-height 20px
    .name
      color #383838
    .score
      color #808080
  .bottom
    font-size 12px
    font-weight 400
    color #808080
    line-height 17px
  .info-icon
    position absolute
    bottom 9px
    right 9px
    color #EDF7FF
</style>
