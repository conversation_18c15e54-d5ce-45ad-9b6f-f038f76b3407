<script lang="ts">
import { IAssessmentScore, ICommScoreTemplateForm, ICommScoreTemplateFormCatalog } from '@/types/model';
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import InstanceDetailDialog from '../../bpm/InstanceDetailDialog.vue';

@Component({
  components: {
    InstanceDetailDialog,
  },
})
export default class ComAssessmentScoreForm extends Vue {
  @Model('change', { type: Boolean, required: true }) visibleEdit!: boolean;
  @Prop({ type: Boolean, required: true }) editable!: boolean;
  @Prop({ type: Object, required: true }) template!: ICommScoreTemplateForm;
  @Prop({ type: Object, required: true }) score!: IAssessmentScore;

  formData: IObject = {};
  visibleInstance = false;

  get alreadyEdited() {
    return !!this.score.catalog_payload && JSON.stringify(this.score.catalog_payload) !== '{}';
  }

  get catalogPayload() {
    return this.template.catalogs.reduce((out: IObject, catalog: ICommScoreTemplateFormCatalog) => {
      out[catalog.id] = catalog.items.map(item => this.formData[item.id] || 0).reduce((o, n) => o + n, 0);
      return out;
    }, {});
  }

  get allFisish() {
    return (
      Object.keys(this.formData).length ===
        this.template.catalogs.reduce((o, catalog) => o + catalog.items.length, 0) &&
      Object.values(this.formData).filter(i => typeof i !== 'number').length === 0
    );
  }

  @Watch('catalogPayload', { immediate: true })
  handleCatalogPayloadChange() {
    const sumScore = this.template.catalogs.reduce((sum: number, catalog: ICommScoreTemplateFormCatalog) => {
      return catalog.weight * catalog.items.map(item => this.formData[item.id] || 0).reduce((o, n) => o + n, 0) + sum;
    }, 0);
    this.$emit('sumScoreChange', sumScore.toFixed(2));
  }

  @Watch('score.id', { immediate: true })
  handleScoreIdChange() {
    this.formData = { ...this.score.item_payload } || {};
  }

  onCancel() {
    this.$emit('change', false);
    this.formData = {};
  }

  onStart() {
    this.$emit('change', true);
    this.formData = this.score.item_payload || {};
  }

  onSubmit() {
    const state = this.allFisish ? 'done' : 'todo';
    this.$emit('submit', {
      id: this.score.id,
      item_payload: { ...this.formData },
      catalog_payload: this.catalogPayload,
      state: state,
    });
  }

  onInstanceShow() {
    this.visibleInstance = true;
  }
}
</script>

<template lang="pug">
.com-assessment-score-form
  .collapse
    .collapse-panel(v-for="(catalog, index) in template.catalogs")
      .panel-header.flex-between
        span {{ catalog.name }}
        span {{ catalog.items.length }}
      .panel-middle
        .question-item(v-for="(item, index) in catalog.items" :key="item.id")
          .question-header
            span(style="color: #FF4F3E") *
            span {{ item.name }}（最高分：{{ item.max_score }}）
          .question-middle
            template(v-if="!(editable && visibleEdit)")
              strong.text-primary 分值：{{ formData[item.id] || '-' }} 分
            template(v-else)
              a-input-number.number-input(
                v-model="formData[item.id]"
                size="large"
                :min="0"
                :max="item.max_score"
                :placeholder="`分值， 满分${item.max_score}`"
              )
    .submit(v-if='true')
      .submit-instance(v-if='score.submit_instance_id && score.user_type !== "Student"')
        a-button.button(size="large" @click="onInstanceShow") 查看自评
      template(v-if="alreadyEdited")
        template(v-if="visibleEdit")
          a-button(size="large" @click="onCancel") 取消修改
          a-button(type="primary" size="large" @click="onSubmit")
            | {{  allFisish ? '提交修改' : '暂存' }}
        template(v-else)
          a-button(type="primary" size="large" @click="onStart") 修改考核
      template(v-else)
        template(v-if="visibleEdit")
          a-button(type="primary" size="large" @click="onSubmit") {{  allFisish ? '提交' : '暂存' }}
        template(v-else)
          a-button(type="primary" size="large" @click="onStart") 修改考核


  InstanceDetailDialog(
    v-model='visibleInstance',
    title='自评详情',
    :instanceId='score.submit_instance_id'
  )
</template>

<style lang="stylus" scoped>
.com-assessment-score-form
  width 100%
  position relative
  // height 100%
  // overflow-y scroll
  .collapse
    width 100%
    .collapse-panel
      margin-top 20px
      width 100%
      .panel-header
        padding 12px 16px
        width 100%
        background #EDF7FF
        color #3DA8F5
        font-size 14px
        line-height 20px
      .panel-middle
        background #fff
        padding-bottom 30px
        .question-item
          width 100%
          .question-header
            padding 20px 0px 10px
            color rgba(38, 38, 38, 0.85)
            font-weight 500
            font-size 14px
            line-height 20px
          .question-middle
            margin-bottom 20px
            color #A6A6A6
            font-weight 500
            font-size 14px
            line-height 20px
    .submit
      position sticky
      bottom 0px
      // left 200px
      z-index 1
      display flex
      justify-content flex-end
      box-sizing border-box
      padding 10px 16px
      width 100%
      border-top 1px #E5E5E5 solid
      background #fff
      .submit-instance
        margin-right 12px
        .button
          color #3DA8F5
          border-color #3DA8F5
      button
        width 100px
  .number-input
    width 100%
</style>
