<script lang="ts">
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import ComAssessmentStageField from '@/components/assessment/ComAssessmentStageField.vue';
import { Moment } from 'moment';
import { IAssessmentActivity } from '@/types/model';
import { assessmentAdminActivityStore } from '../../store/modules/assessment/admin/activity.store';
import { ActiveStore } from '@/lib/ActiveStore';
import UserIdsField from '../form/UserIdsField.vue';
import TimeRangeSwitchInput from '../form/TimeRangeSwitchInput.vue';

@Component({
  components: {
    TimeRangeSwitchInput,
    ComAssessmentStageField,
    UserIdsField,
  },
})
export default class ComAssessmentActivityForm extends Vue {
  @Prop({ type: Boolean, default: false }) disabled?: boolean;
  @Prop({ type: Boolean, default: false }) showSensitiveInfo?: boolean;
  @Prop({ type: Object, default: () => assessmentAdminActivityStore }) store?: ActiveStore<IAssessmentActivity>;

  attachments: object[] = [];
  dateRange: (string | null)[] = [null, null];
  formData: Partial<IAssessmentActivity> = {};
  stateOptions = [
    { label: '正在进行', value: 'published' },
    { label: '已结束', value: 'rejected' },
    { label: '草稿箱', value: 'pending' },
  ];

  get keyGenerator() {
    return `key_${Date.now()}`;
  }

  @Watch('store.record', { deep: true, immediate: true })
  handleRecordChange() {
    if (this.$route.params.id) {
      this.formData = JSON.parse(JSON.stringify(this.store?.record));
      if (this.formData && !(this.formData.stages && this.formData.stages.stages)) {
        this.formData.stages = { stages: [] };
      }
      if (this.formData && !this.formData.attachments) {
        this.formData.attachments = { documents: [] };
      }
      if (this.formData) {
        this.dateRange = [this.formData.start_at || null, this.formData.end_at || null];
      }
    } else {
      this.formData.attachments = { documents: [] };
    }
  }

  mounted() {
    this.store?.init();
    this.handleRecordChange();
  }

  get documents() {
    return this.formData.attachments && this.formData.attachments.documents ? this.formData.attachments.documents : [];
  }

  get payload() {
    return {
      ...this.formData,
      start_at: this.dateRange[0] || undefined,
      end_at: this.dateRange[1] || undefined,
    };
  }

  onSuccess(fileItems: any[]) {
    if (!this.formData.attachments) {
      this.formData.attachments = { documents: [] };
    }
    this.formData.attachments.documents = fileItems;
  }

  onCreate() {
    this.store
      ?.create(this.payload)
      .then(res => {
        this.$message.success('创建成功');
        this.$emit('success', res);
      })
      .catch(error => {
        this.$message.error('创建失败');
        throw error;
      });
  }

  onUpdate() {
    this.store
      ?.update(this.payload)
      .then(res => {
        this.$message.success('更新成功');
        this.$emit('success', res);
      })
      .catch(error => {
        this.$message.error('更新失败');
        throw error;
      });
  }
}
</script>

<template lang="pug">
.assessment-activity-form
  .form
    a-form(layout="vertical")
      a-form-item(label="考核标题（必填）")
        a-input(
          v-model="formData.name"
          size="large"
          :disabled="disabled"
          placeholder="请输入考核标题")
      a-form-item(label="考核时间（必填）")
        TimeRangeSwitchInput(
          :showTime='false'
          :disabled="disabled"
          v-model='dateRange'
        )
      ComAssessmentStageField(
        v-model='formData.stages'
        :disabled='disabled'
      )
      a-form-item(label="考核内容（必填）")
        a-textarea(
          v-model="formData.content"
          size="large"
          :disabled="disabled"
          placeholder="请输入考核内容")
      a-form-item(label="考核状态")
        a-select(
          v-model='formData.state'
          :options='stateOptions'
        )
      a-form-item(label="考核附件")
        template(v-if="disabled")
          Attachments(
            :attachments="documents"
          )
        template(v-else)
          FileUploader(
            ref="uploader"
            :value="formData.attachments.documents"
            @change="onSuccess"
          )
            a-button(type="primary" size="large") 上传附件
      a-form-item(v-if='showSensitiveInfo', label="管理老师")
        UserIdsField(v-model='formData.paste_manage_teacher_ids', :multiple='true', :disabled='disabled')
  .bottom(v-if='!disabled')
    .actions
      a-button(v-if='formData.id', type='primary', @click='onUpdate')
        | 保存
      a-button(v-else, type='primary', @click='onCreate')
        | 创建
</template>

<style lang="stylus" scoped>
.assessment-activity-form
  width 100%
  overflow-x hidden
  overflow-y scroll
  .form
    width 480px
    margin 0 auto
    padding 20px 0 50px 0
    position relative
    background white
  .bottom
    height 50px
    width 100%
    border-top 1px solid #E5E5E5
    position relative
    bottom 0
    background white
    .actions
      margin 0 auto
      padding 9px 0
      width 480px
      display flex
      justify-content flex-end
</style>
