<script lang="ts">
import { Component, Vue, Prop, Model } from 'vue-property-decorator';

import TemplateForm from '@/components/form/TemplateForm.vue';
import { IFormTemplateItem } from '@/interfaces/formTemplate.interface';
import { any } from 'lodash/fp';

@Component({
  components: { TemplateForm },
})
export default class TaFormModal extends Vue {
  @Model('change', { type: Boolean, required: true }) visible!: boolean;
  @Prop({ type: Boolean, default: () => false }) readonly loading!: boolean;
  @Prop({ type: Object, default: () => ({}), required: true }) private readonly formData!: IObject;
  @Prop({ type: Array, default: () => [], required: true }) private readonly template!: IFormTemplateItem[];

  // modal config
  @Prop({ type: String, default: () => '' }) private readonly title!: string;
  @Prop({ type: String, default: () => '确认' }) private readonly okText!: string;
  @Prop({ type: Object, default: () => {} }) private readonly bodyStyle!: IObject;
  @Prop({ type: String, default: () => '取消' }) private readonly cancelText!: string;
  @Prop({ type: Object, default: () => {} }) private readonly maskStyle!: IObject; // 遮罩样式
  @Prop({ type: Boolean, default: () => false }) private readonly closable!: boolean; // 是否显示右上角的关闭按钮
  @Prop({ type: String, default: () => '600px' }) private readonly width!: string | number; // modal width

  private onHandlerOk(): void {
    (this.$refs.form as any).submit({
      success: (formData: any) => {
        this.$emit('submit', formData);
        if (this.formData.id) {
          this.$emit('update', { id: this.formData.id, ...formData });
        } else {
          this.$emit('create', formData);
        }
      },
    });
  }

  private onHandlerCancel(): void {
    this.$emit('cancel');
  }

  private onFieldsChange(props: any, fields: any) {
    this.$emit('fieldsChange', props, { ...fields });
  }
}
</script>

<template lang="pug">
a-modal(
  :title="title"
  :visible="visible"
  :bodyStyle="bodyStyle"
  :width='width'
  @cancel="onHandlerCancel"
)
  .dialog-form
    slot(name="body")
    TemplateForm(
      ref="form"
      :formData="formData"
      :template="template"
      :showActions="false"
      @fieldsChange="onFieldsChange")
  template(slot="footer")
    .actions
      slot(name="actions_top")
      a-button(key="back" @click="onHandlerCancel" size="large")
        | {{ cancelText }}
      slot(name="actions_center")
      a-button(key="submit" type="primary" :loading="loading" @click="onHandlerOk" size="large" :disabled="loading")
        | 确认
      slot(name="actions_bottom")
</template>

<style lang="stylus" scoped></style>
