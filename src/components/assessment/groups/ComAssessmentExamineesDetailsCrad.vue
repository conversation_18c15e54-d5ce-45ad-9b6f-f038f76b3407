<script lang="ts">
import { values } from 'lodash';
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class ComAssessmentExamineesDetailsCrad extends Vue {
  @Prop({ type: String, default: '' }) prop!: string;
  @Prop({ type: Object, default: () => {} }) private user!: IObject;

  mounted() {
    this.fetchData();
  }

  fetchData() {}
}
</script>

<template lang="pug">
.ComAssessmentExamineesDetailsCrad
  .top
    .name {{ user.user_name || "" }}
    .fraction 
      slot(name="grade")
      span.score {{ user.score || 0}} 分
  .content
    .lines
      .item(v-for="item in user.userInfo")
        .label {{ item.label }}
        .value {{ item.value }}
    .btn  
      slot(name="btn")
  .bottom
    slot(name="bottom")
</template>

<style lang="stylus" scoped>
.ComAssessmentExamineesDetailsCrad
  min-height 106px
  padding 10px 16px
  background linear-gradient(180deg, #38A7F4 0%, #45AEF4 100%)
  border-radius 5px
  margin-bottom 16px
  display flex
  flex-direction column
  .top
    display flex
    flex-direction row
    justify-content space-between
    height 20px
    font-size 16px
    font-family PingFangSC-Medium, PingFang SC
    font-weight 500
    color #FFFFFF
    .score
      margin-left 5px
  .content
    margin-top 15px
    font-size 14px
    font-family PingFangSC-Regular, PingFang SC
    color #FFFFFF
    height 20px
    display flex
    flex-direction row
    justify-content space-between
    .lines
      display flex
      flex-direction row
      .item
        min-width 150px
        display flex
        flex-direction row
        .label
          margin-right 10px
  .bottom
    flex 1
    margin-top 10px
    font-size 14px
    font-family PingFangSC-Regular, PingFang SC
    color #FFFFFF
</style>
