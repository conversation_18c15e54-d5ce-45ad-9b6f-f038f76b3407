<script lang="ts">
import { TaIndexViewTabInterface } from '@/components/global/TaIndex';
import TaIndexView from '@/components/global/TaIndexView.vue';
import { IGroup } from '@/models/assessment/admin/activity/groups';
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import ComDimensionList from '@/components/assessment/catalogs/ComDimensionList.vue';

import UserSelectorDialog from '@/components/hr/UserSelectorDialog.vue';
import { IAssessmentActivity, IAssessmentCatalog, IAssessmentSubGroup } from '@/types/model';
import { IUser } from '@/models/intl/user/activities/students';
import { AxiosResponse } from 'axios';
import { assessmentAdminActivityStore } from '@/store/modules/assessment/admin/activity.store';
import TeacherField from '../../form/TeacherField.vue';
import { AssessmentAdminSyncEntryLevelApi } from '@/apis/assessment/admin/sync_entry_level.api';

@Component({
  components: { TeacherField, ComDimensionList },
})
export default class ComAssessmentGroupsExaminees extends Vue {
  @Prop({ type: Array, default: () => [] }) private readonly parents!: [];
  @Prop({ type: Object, default: () => {} }) private entryStore!: IObject;
  @Prop({ type: Object, default: () => undefined }) private subGroupsStore!: IObject;
  @Prop({ type: Object, default: () => {} }) private catalogStore!: IObject;
  @Prop({ type: Boolean, default: () => true }) private showActive!: boolean;
  @Prop({ type: Boolean, default: () => false }) private showExport!: boolean;
  @Prop({ type: Object, default: () => {} }) private activity!: IAssessmentActivity;

  @Watch('total_count')
  onchange() {
    this.tabsConcat();
  }

  private visible: boolean = false;
  private key: string = '';
  private count: number = 0;
  private catalog: Partial<IAssessmentCatalog> = {};
  private activeSubGroup: Partial<IAssessmentSubGroup> = {};
  private label: string = '全部';
  private detailMode: string = '';
  private total_count: number = 0;
  private calalogName: string = '';
  private userIds: Array<number> = [];
  private catalogRecords: Array<any> = [];
  private userVisible: boolean = false;
  private tabs: Array<TaIndexViewTabInterface> = [
    { label: '全部', key: '', num: -1, background: '', color: '', mode: 'table' },
  ];

  get config() {
    return {
      showCount: true,
      recordName: '被考核人',
      store: this.entryStore,
      tableConfig: { bordered: true, rowKey: 'id' },
      showExport: this.showExport,
      searcherSimpleOptions: [
        { type: 'string', label: '名字', key: 'user_of_Teacher_type_name' },
        { type: 'string', label: '工号', key: 'user_of_Teacher_type_code' },
      ],
    };
  }

  get records(): any {
    return (this.subGroupsStore && this.subGroupsStore.records) || {};
  }

  get groupId(): number | undefined {
    return +this.$route.params.groupId || undefined;
  }

  get activityId(): number {
    return +this.$route.params.id;
  }

  // get catalogRecords(): any {
  //   return this.catalogStore.records || [];
  // }

  mounted() {
    // this.groupId ? this.fetchData() : '';
    this.fetchData();
    this.catalogStore ? this.catalogStore.init({ parents: this.parents }) : '';
    this.getCatalogs();
    if (this.activity) {
      for (const catalog of this.activity?.catalogs || []) {
        this.tabs.push({
          label: catalog.name,
          key: `${catalog.id}`,
          mode: 'table',
        });
      }
    }
  }

  private async getCatalogs(): Promise<void> {
    if (this.showActive) {
      let { data } = await this.catalogStore.index();
      this.catalogRecords = data.records;
    }
  }

  private tabsConcat(): void {
    this.tabs[0].num = this.total_count;
  }

  private async fetchData(): Promise<void> {
    if (this.subGroupsStore) {
      this.subGroupsStore.init({
        parents: this.parents,
        params: { q: { group_id_eq: this.groupId } },
      });
      let { data } = await this.subGroupsStore.index();

      let tab = data.records.map((item: any) => {
        this.count = this.count + item.entry_count || 0;
        return {
          label: item.catalog_name || item.name,
          key: item.id + '',
          num: item.entry_count || 0,
          background: '',
          color: '',
          mode: 'table',
        };
      });

      this.tabs.push(...tab);
      this.tabs[0].num = this.count;
    }

    this.initEntryStore({ sub_group_group_id_eq: this.groupId });
  }

  private initEntryStore(query: IObject, val: any = null) {
    this.entryStore.init({ parents: this.parents, params: { group_key: val, q: { ...query, s: ['score desc'] } } });
  }

  private tabChange(val: any): void {
    this.key = val;
    this.label = val.label;
    val.label === '全部'
      ? this.initEntryStore({ sub_group_group_id_eq: this.groupId })
      : this.groupId
      ? this.initEntryStore({ sub_group_id_eq: val.key })
      : this.initEntryStore({ catalog_id_eq: val.key }, val.key);
  }

  private onShow(val: any): void {
    this.$emit('onShow', val);
  }

  private onIndex(data: any) {
    if (!this.subGroupsStore) {
      this.tabs[0].num = data.total_count;
    }
  }

  private addEntry(): void {
    if (this.label === '全部') {
      this.visible = true;
    } else {
      (this.$refs.teacherField as any).open();
    }
  }
  private handleOk(): void {
    if (this.activeSubGroup.catalog_name) {
      this.visible = false;
      (this.$refs.teacherField as any).open();
    } else {
      this.$message.warning('请选择考核类别');
    }
  }

  private selectCatalogs(item: any): void {
    this.calalogName = item.name;
    this.catalog = item;
  }

  selectSubGroup(item: IAssessmentSubGroup) {
    this.activeSubGroup = item;
  }

  private shutDownUser(): void {
    // this.userVisible = false;
    this.calalogName = '';
  }
  private selectUsers() {
    this.userIds.forEach(userId => {
      this.entryStore.create({
        config: { params: { sequence: userId } },
        formData: { sub_group_id: this.activeSubGroup?.id, user_type: 'Teacher', user_id: userId },
      });
    });
  }

  private getDimensionState(val: any) {
    val.forEach((item: any) => {
      if (item.score) {
        return true;
      }
    });
  }

  onExport1() {
    assessmentAdminActivityStore.init();
    assessmentAdminActivityStore
      .sendMemberAction({
        id: this.$route.params.id,
        action: 'export_entries',
        config: {
          responseType: 'arraybuffer',
        },
      })
      .then(res => {
        const url = window.URL.createObjectURL(
          new Blob([res.data as string], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          }),
        );
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
      });
  }

  onExport2() {
    assessmentAdminActivityStore.init();
    assessmentAdminActivityStore
      .sendMemberAction({
        id: this.$route.params.id,
        action: 'export_compete_entries',
        config: {
          responseType: 'arraybuffer',
        },
      })
      .then(res => {
        const url = window.URL.createObjectURL(
          new Blob([res.data as string], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          }),
        );
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
      });
  }
  async onEvaluation() {
    await new AssessmentAdminSyncEntryLevelApi({
      parents: [{ type: 'activities', id: this.activityId }],
    })
      .create({ id: this.activityId })
      .then(res => {
        if (res.status == 201) {
          this.entryStore.index();
          this.$message.success('成功');
        }
      });
  }
  onInitScore() {
    assessmentAdminActivityStore.init();
    assessmentAdminActivityStore
      .sendMemberAction({
        id: this.$route.params.id,
        action: 'init_scores',
      })
      .then(() => this.$message.success('激活成功'))
      .catch(() => this.$message.error('激活失败'));
  }
}
</script>

<template lang="pug">
.com-assessment-groups-examinees
  TaIndexView(
    ref='TaIndexView',
    :tabs='tabs',
    :config='config',
    @tabChange='tabChange',
    @onShow='onShow',
    :tabsLeftMargin='0',
    @onIndex='onIndex'
  )
    template(#right-actions)
      TextButton(icon='setting', @click='onInitScore') 激活
      TextButton(icon='setting', @click='onEvaluation') 一键评定
      .actions(@click='addEntry', v-if='showActive')
        a-icon(type='plus-circle')
        span 添加被考核人

    template(#table)
      a-table-column(title='被考核人信息', :width='170')
        template(slot-scope='scope')
          .assessment
            p
              span.label 名字:
              span {{ scope.user_name }}
            p
              span.label 工号:
              span {{ scope.user_code }}
            p
              span.label 部门:
              span {{ scope.user_department_name }}
      a-table-column(title='自评状态', :width='100')
        template(slot-scope='scope')
          .assessment.SelfEvaluation(:class='{ prepare: scope.is_prepare }') {{ scope.is_prepare ? "已自评" : "待自评" }}
      a-table-column(title='各项维度分数', dataIndex='dimension_stat', :width='270')
        template(slot-scope='scope')
          .assessment
            p(v-for='item in scope')
              span.dimension_label {{ item.name }}:
              span.dimension
                span {{ `${item.score || 0}分` }},
                span {{ `已评分: ${item.score_stat.done || 0}人` }}，
                span {{ `待评分: ${item.score_stat.todo || 0}人` }}，
      a-table-column(title='分类', :width='100')
        template(slot-scope='scope')
          .assessment {{ scope.catalog_name }}
      a-table-column(title='总分', :width='170')
        template(slot-scope='scope')
          .assessment
            .content
              p {{ scope.score || 0 }}分，
              p
                span {{ `已评分: ${scope.score_stat.done || 0}人` }}，
                span {{ `待评分: ${scope.score_stat.todo || 0}人` }}；
      a-table-column(v-if='$scopedSlots.level', title='考核等第', :width='120')
        template(slot-scope='scope, record')
          .assessment_department
            slot(name='level', :record='record')

          //- .assessment_department {{ "_" }}
  a-modal(title='添加被考核人', :visible='visible', @ok='handleOk', @cancel='visible = false')
    .content
      .item(
        v-for='item in records',
        :key='item.id',
        @click='selectSubGroup(item)',
        :class='{ active: item.id === activeSubGroup.id }'
      )
        span {{ item.catalog_name }}
  //- UserSelectorDialog(
  //-   title="添加被考核人"
  //-   :visible="userVisible"
  //-   :userIds="userIds"
  //-   :multiple="true"
  //-   @input="shutDownUser"
  //-   @selectUsers="selectUsers"
  //- )
  TeacherField.hidden(
    ref='teacherField',
    v-model='userIds',
    :multiple='true',
    @cancel='shutDownUser',
    @ok='selectUsers'
  )
</template>

<style lang="stylus" scoped>
.com-assessment-groups-examinees
  .hidden
    display none
  height 100%
  width 100%
  overflow scroll
.assessment
  display flex
  flex-direction column
  padding-left 12px
  overflow hidden
  text-overflow ellipsis
  white-space nowrap
  .label
    margin-right 5px
.assessment_department
  padding 0 12px
.custom-actions
  display flex
  align-items center
.dimension
  span
    margin 0px 5px
    color #808080
    font-size 14px
.dimension_label
  display inline-block
  width 85px
  // border 1px solid #3333
  // margin-right 10px
  font-size 14px
  color #808080
.icon
  color #3DA8F5
.SelfEvaluation
  z-index 99
  color #3DA8F5
.actions
  margin-left 20px
  color #3DA8F5
  cursor pointer
  span
    margin-left 5px
.prepare
  color #6DC37D
.content
  display flex
  flex-direction row
  flex-wrap wrap
  // justify-content space-between
  .item
    width 130px
    height 60px
    background #F5F5F5
    border-radius 3px
    margin-top 10px
    display flex
    flex-direction row
    justify-content center
    align-items center
    color #A6A6A6
    cursor pointer
    margin 0 10px
  .item:hover
    color #6DC37D
    background #F0F9F2
  .active
    color #6DC37D
    background #F0F9F2
</style>
