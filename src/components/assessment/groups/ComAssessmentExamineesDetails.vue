<script lang="ts">
import { TaIndexViewTabInterface } from '@/components/global/TaIndex';
import { IDimensions } from '@/models/assessment/admin/activity/dimensions';
import { Component, Vue, Prop } from 'vue-property-decorator';
import ComAssessmentExamineesDetailsCrad from './ComAssessmentExamineesDetailsCrad.vue';
import ComAssessmentExamineesDetailsItem from './ComAssessmentExamineesDetailsItem.vue';
import ComAssessmentScoreForm from '../scores/ComAssessmentScoreForm.vue';

@Component({
  components: {
    ComAssessmentExamineesDetailsCrad,
    ComAssessmentExamineesDetailsItem,
    ComAssessmentScoreForm,
  },
})
export default class ComAssessmentExamineesDetails extends Vue {
  @Prop({ type: Object, default: () => {} }) private readonly store!: IObject;
  @Prop({ type: Array, default: () => [] }) private readonly parents!: [];
  @Prop({ type: String, default: () => '' }) private readonly userName!: string;
  @Prop({ type: Object, default: () => {} }) private readonly entryStore!: IObject;
  @Prop({ type: Number, default: () => 0 }) private readonly entryId!: number;
  @Prop({ type: Array, default: () => [] }) private readonly breadcrumbs!: IObject;

  private user: IObject = {};
  private activeNaem: string = 'scoreDetails';
  private score_stat: IObject = {};
  private visibleEdit = false;

  private userInfo: any = [
    {
      label: '自评打分:',
      value: '20分',
      key: '',
    },
    {
      label: '工号:',
      value: '',
      key: 'user_code',
    },
    {
      label: '部门:',
      value: '党委校部',
      key: 'user_department_name',
    },
    {
      label: '职务:',
      value: '副主任',
      key: '',
    },
  ];

  private data: any = {
    user_name: '',
    score: 90,
    userInfo: [
      {
        label: '总体自评:',
        value: '20分',
        key: 'is_prepare',
      },
      {
        label: '工号:',
        value: '',
        key: 'user_code',
      },
      {
        label: '部门:',
        value: '党委校部',
        key: 'user_department_name',
      },
      {
        label: '分类:',
        value: '',
        key: 'catalog_name',
      },

      {
        label: '分组:',
        value: '',
        key: 'group_name',
      },
    ],
  };

  get id(): number {
    return +this.$route.params.id;
  }

  async created() {
    // this.entryStore.init({ parents: [{ type: 'activities', id: this.id }] });

    let { data } = await this.entryStore.find(this.entryId);
    this.data.user_name = data.user_name;
    this.data.score = data.score;
    this.data.userInfo.forEach((item: any) => {
      if (item.key in data) {
        item.value = data[item.key];
        if (item.key === 'is_prepare') {
          item.value = data[item.key] ? '已自评' : '待自评';
        }
      }
    });
    this.score_stat = data.score_stat;
    let tab = data.dimensions.map((item: IDimensions) => {
      return { label: item.name, key: item.id + '', num: 0, background: '', color: '', mode: 'table' };
    });
    this.tabs.push(...tab);
  }

  mounted() {
    this.store.init({ parents: this.parents });
  }

  private tabs: TaIndexViewTabInterface[] = [
    { label: '全部', key: '', num: 0, background: '', color: '', mode: 'table' },
  ];

  private steps: IObject[] = [{ title: '评分详情', key: 'scoreDetails', type: 'route' }];

  get config() {
    return {
      store: this.store,
      recordName: '评分表',
      detailConfig: {
        detailMode: 'drawer',
        detailWidth: '60%',
      },
      searcherSimpleOptions: [
        { type: 'string', label: '考核人', key: 'user_of_Teacher_type_name' },
        { type: 'string', label: '工号', key: 'user_of_Teacher_type_code' },
      ],
    };
  }

  private onHandler(item: any): void {
    item.title !== this.breadcrumbs[this.breadcrumbs.length - 1].title ? this.$emit('breadcrumb') : '';
  }
  private onShow(record: any): void {
    this.store.SET_RECORD({});
    this.user.user_name = record.user_name;
    this.user.score = record.score;
    this.userInfo.forEach((item: any) => {
      if (item.key in record) {
        item.value = record[item.key];
      }
    });
    this.user.userInfo = this.userInfo;
    this.store.find(record.id);
    this.$nextTick(() => {
      const parent = (this.$refs.detailContent as HTMLElement).parentElement;
      if (parent) {
        parent.style.padding = '0';
        parent.style.padding = '20px 10px 0 10px';
      }
    });
  }

  private deleteGroups(id: number): void {
    this.$confirm({
      title: '确定要继续执行此操作吗?',
      content: '该操作是不可逆的',
      okText: '确定',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        await this.store.delete(id);
        this.$message.success('成功删除分组');
      },
      onCancel: () => {},
    });
  }

  private tabChange(val: TaIndexViewTabInterface) {
    this.store.init({ parents: this.parents, params: { q: { dimension_id_eq: val.key } } });
  }

  private deleteEntry(): void {
    this.$confirm({
      title: '确定要继续执行此操作吗?',
      content: '该操作是不可逆的',
      okText: '确定',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        await this.entryStore.delete(this.entryId);
        this.$message.success('成功删除被考核人');
      },
      onCancel: () => {},
    });
  }

  onCloseDetail() {
    this.visibleEdit = false;
  }
}
</script>

<template lang="pug">
.com-assessment-groups-examinees-details
  .examinees-details-header
    StepToolbar.toolbar(
      :value="activeNaem"
      :breadcrumbs.sync="breadcrumbs"
      :steps="steps"
      :border="true"
      :stepStyle="{padding: 0}"
      @breadcrumbBackPage="onHandler"
      mode="tabs")
      template(#right)
        IconTooltip(icon="delete", tips="删除被考核人" @click="deleteEntry")

  .table_top
    ComAssessmentExamineesDetailsCrad(:user="data")
      template(#bottom)
        span {{ `已评分：${score_stat.done || 0}人`}}
  .content
    TaIndexView(
      :config="config"
      :tabs="tabs"
      @onShow="onShow"
      :tabsLeftMargin="0"
      @tabChange="tabChange"
      @onCloseDetail="onCloseDetail"
    )
      template(#header)
      template(#table_top)
        //- .table_top
          ComAssessmentExamineesDetailsCrad(:user="data")
            template(#bottom)
              span {{ `已评分：${score_stat.done || 0}人`}}
      template(#table)
        a-table-column(title='评分表名称', dataIndex="score_template_name"  :width='270')
        a-table-column(title='考核人',  dataIndex="user_name" :width='120')
        a-table-column(title='考核维度',  dataIndex="dimension_name" :width='120')
        a-table-column(title='工号', dataIndex="user_code"  :width='120')
        a-table-column(title='部门', dataIndex="user_department_name" :width='120')
        a-table-column(title='总分', dataIndex="score"  :width='120')
        a-table-column(title='', align="right"  :width='50')
          template(slot-scope="scope")
            .active
              IconTooltip(icon="delete", tips="删除" @click="delete(scope.id)")
      template(#detail)
        .detail_content(ref='detailContent')
          ComAssessmentExamineesDetailsCrad(:user="user")
          ComAssessmentScoreForm(
            v-model='visibleEdit',
            v-if='store.record.id',
            :score='store.record',
            :template='store.record.score_template.form',
            :editable='true'
          )


</template>

<style lang="stylus" scoped>
.com-assessment-groups-examinees-details
  height 100%
  width 100%
  overflow scroll
  .titlex
    height 100%
    width 100%
  .breadcrumb
    cursor pointer
    color #383838
    font-size 16px
    font-family PingFangSC-Medium, PingFang SC
  .breadcrumb:first-child
    color #808080
.detail_content
  padding 0 20px
.active
  display none
tr:hover
  .active
    display block
.table_top
  margin-top 10px
</style>
