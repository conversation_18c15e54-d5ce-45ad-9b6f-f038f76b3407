import { IFormTemplateItem } from '@/interfaces/formTemplate.interface';

export const Template: IFormTemplateItem[] = [
  {
    key: 'name',
    name: '分组名称',
    layout: {
      component: 'input',
      placeholder: '请输入分组名称',
      type: 'string',
      required: true,
    },
    model: {
      attr_type: 'string',
    },
  },
  {
    key: 'catalogs',
    name: '关联分类',
    layout: {
      component: 'checkbox',
      placeholder: '请选择',
      required: true,
      span: 10,
      options: [{ label: '辅导员' }, { label: '任课教师' }, { label: '教导, 管理, 工勤' }],
    },
    model: {
      attr_type: 'array',
    },
  },
];
