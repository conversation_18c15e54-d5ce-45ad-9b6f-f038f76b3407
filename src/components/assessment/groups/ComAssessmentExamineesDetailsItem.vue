<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class ComAssessmentExamineesDetailsItem extends Vue {
  @Prop({ type: String, default: '' }) prop!: string;

  state: string = '';

  mounted() {
    this.fetchData();
  }

  fetchData() {}
}
</script>

<template lang="pug">
.com-assessment-examinees-details-item
  .header 
    .title 思想道德情况
    .count 3/3
  .content
    .item
      .title
        span.star *
        span 1. 学习创新，自觉学习政治理论，专业知识和先进的办学理念。
      .fraction 分值：92分
    .item
      .title
        span.star *
        span 2. 学习创新，自觉学习政治理论，专业知识和先进的办学理念。
      .fraction 分值：92分
    .item
      .title
        span.star *
        span 3. 学习创新，自觉学习政治理论，专业知识和先进的办学理念。
      .fraction 分值：92分
</template>

<style lang="stylus" scoped>
.com-assessment-examinees-details-item
  height 100%
  widows 100%
  .header
    display flex
    flex-direction row
    justify-content space-between
    align-items center
    height 48px
    line-height  48px
    background  #EDF7FF
    padding 0 16px
    color #3DA8F5
  .content
    .item
      padding 20px 0
      display flex
      flex-direction column
      border-bottom 1px solid #E8E8E8
      .title
        position relative
        left -10px
        height 30px
        .star
          position relative
          // left -10px
          color #FF4F3E
          font-size 16px
      .fraction
        color #3DA8F5
        // padding-left 6px
</style>
