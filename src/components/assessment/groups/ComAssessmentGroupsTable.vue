<script lang="ts">
import { TaIndexViewTabInterface } from '@/components/global/TaIndex';
import InfoCard from '@/components/hr/InfoCard.vue';
import { IFormTemplateItem } from '@/interfaces/formTemplate.interface';
import { ICatalog } from '@/models/finance/catalog';
import { Component, Vue, Prop } from 'vue-property-decorator';

import TaFormModal from '@/components/assessment/TaFormModal.vue';
import TaIndexView from '@/components/global/TaIndexView.vue';
import { IGroup } from '@/models/assessment/admin/activity/groups';

@Component({
  components: { TaFormModal },
})
export default class ComAssessmentGroupsTable extends Vue {
  @Prop({ type: Object, default: () => {} }) private store!: IObject;
  @Prop({ type: Object, default: () => {} }) private catalogStore!: IObject;
  @Prop({ type: Array, default: () => [] }) private readonly parents!: [];

  private visible: boolean = false;
  private tabs: TaIndexViewTabInterface[] = [
    { label: '', key: 'execute', num: 0, background: '', color: '', mode: 'table' },
  ];

  get template(): IFormTemplateItem[] {
    return [
      {
        key: 'name',
        name: '分组名称',
        layout: {
          component: 'input',
          placeholder: '请输入分组名称',
          type: 'string',
          required: true,
        },
        model: {
          attr_type: 'string',
        },
      },
      {
        key: 'catalog_ids',
        name: '关联分类',
        layout: {
          component: 'checkbox',
          placeholder: '请选择',
          required: true,
          span: 21,
          options: this.options,
        },
        model: {
          attr_type: 'array',
        },
      },
      {
        key: 'paste_department_ids',
        name: '所属部门',
        layout: {
          component: 'department',
          placeholder: '请选择',
          required: true,
          multiple: true,
        },
        model: {
          attr_type: 'array',
        },
      },
    ];
  }

  get records(): Array<ICatalog> {
    return this.catalogStore.records;
  }

  get options(): Array<any> {
    return this.records.map((item: ICatalog) => {
      return { label: item.name, value: item.id };
    });
  }

  get id(): number {
    return +this.$route.params.id;
  }

  get config() {
    return {
      store: this.store,
      recordName: '分组',
      showActions: true,
      template: this.template,
      searcherSimpleOptions: [{ type: 'string', label: '名称', key: 'name' }],
      formDataDecode: this.formDataDecode,
      formDataEncode: this.formDataEncode,
    };
  }

  mounted() {
    this.store.init({ parents: this.parents });
    this.catalogStore.init({ parents: this.parents });
    this.catalogStore.index();
  }

  formDataDecode(record: IGroup) {
    return {
      ...record,
      paste_department_ids: record.paste_department_ids,
    };
  }

  formDataEncode(record: IGroup) {
    return {
      ...record,
      paste_department_ids: (record.departments || []).map(i => i.id),
    };
  }

  private deleteGroups(id: number): void {
    this.$confirm({
      title: '确定要继续执行此操作吗?',
      content: '该操作是不可逆的',
      okText: '确定',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        await this.store.delete(id);
        this.$message.success('成功删除分组');
      },
      onCancel: () => {},
    });
  }

  private submit(val: any): void {
    this.visible = false;
  }

  private editGroupd(item: any) {
    const formData: any = {
      ...item,
      catalog_ids: item.catalogs.map((item: any) => {
        return item.id;
      }),
    };
    (this.$refs.TaIndexView as TaIndexView<IGroup>).onEdit(formData);
  }

  private afterUpdate(): void {
    this.store.init({ parents: this.parents });
  }

  private onShow(val: any): void {
    this.$router.push({ path: `/assessment/admin/activities/${this.id}/groups/${val.id}/examinees` });
  }
}
</script>

<template lang="pug">
.com-assessment-catalogs-table
  TaIndexView(ref="TaIndexView" :config="config" :tabs="tabs" @onShow="onShow" @afterUpdate="afterUpdate")
    template(#header)
      span.title 考核分组
    template(#table)
      a-table-column(title='名称', dataIndex="name", :width='170')
      a-table-column(title='分类', dataIndex="catalogs" :width='270')
        template(slot-scope="scope")
          span(v-for="item in scope" :key="item.id") {{ `${item.name}    ` }}
      a-table-column(title='被考核人数', dataIndex="entry_count" :width='100')
      a-table-column(title='打分完成率', dataIndex="done_score_ratio" :width='100')
        template(slot-scope="scope")
          span {{ Number.parseFloat(scope*100).toFixed(1) }} %
      a-table-column(title='', align="right" :width='200')
        template(slot-scope="scope")
          .active
            IconTooltip(icon="edit", tips="编辑" @click="editGroupd(scope)")
            IconTooltip(icon="delete", tips="删除" @click="deleteGroups(scope.id)")
</template>

<style lang="stylus" scoped>

.title
  font-size 16px
  color  #383838
  font-weight 500
  font-family PingFangSC-Medium, PingFang SC
.active
  display none
tr:hover
  .active
    display block
</style>
