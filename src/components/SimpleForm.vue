<template lang="pug">
.form
  .form-content
    .form-item(v-for="(form, index) in templates" :key="index")
      .form-item-label {{form.label}}
      template(v-if="form.widget === 'input'")
        a-input(
          v-model="object[form.key]"
          type="text"
          @focus="this.type = 'password'"
          :placeholder="form.placeholder"
          size="large"
          v-if="form.widgetType === 'password'")
        a-input-number(
          v-model="object[form.key]"
          :min="0"
          :placeholder="form.placeholder"
          :formatter="value => `${value || 0}${form.suffix || ''}`"
          :max="500"
          size="large"
          v-else-if="form.widgetType === 'number'")
        a-input(v-model="object[form.key]" :type="form.widgetType" :placeholder="form.placeholder" size="large" v-else)
      template(v-else-if="form.widget === 'select'")
        a-select(v-model="object[form.key]" :placeholder="form.placeholder" size="large" allowClear style="width: 100%")
          a-select-option(v-for="(option, index) in form.options" :key="index" :value="option.value") {{option.label}}
      template(v-else-if="form.widget === 'department'")
        a-tree-select.form-item(
          style="width: 100%"
          :dropdownStyle="{ maxHeight: '360px', overflow: 'auto' }"
          :treeData="departmentTreeData"
          showSearch
          allowClear
          size="large"
          placeholder="请选择部门"
          :treeDefaultExpandAll="true"
          :defaultValue="object[form.key]"
          @change="(val) => { object[form.key] = val.toString() }")

  .form-footer(v-if="showFooter")
    a-button(type="primary" size="large" @click="onSubmit") {{ submitTitle }}
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import { Department } from '@/models/department';

@Component({
  components: {},
})
export default class SimpleForm extends Vue {
  @Prop({ type: Object, default: () => ({}) }) value: any;
  @Prop({ type: Array, default: () => [] }) templates: any;
  @Prop({ type: String, default: '确定' }) submitTitle!: string;
  @Prop({ type: Boolean, default: true }) showFooter?: boolean;
  object: any = {};
  departmentTreeData: any[] = [];

  @Watch('value', { deep: true, immediate: true })
  watchChange() {
    this.object = this.mergeObject(this.templates, this.value);
    this.fetchDepartments();
  }

  @Emit('submit')
  onSubmit() {
    this.$emit('input', this.object);
    return this.object;
  }

  mergeObject(templateObject: any, source: any): any {
    return (templateObject || []).reduce((obj: any, item: any) => {
      obj[item.key] = (source[item.key] || '').toString() || templateObject[item.key];
      return {
        ...source,
        ...obj,
      };
    }, {});
  }

  async fetchDepartments() {
    const department = new Department();
    const { data } = await department.tree();
    this.departmentTreeData = Department.convertTreeData(data.departments);
  }
}
</script>

<style lang="stylus" scoped>
.form
  width 100%
  .form-content
    padding 16px 16px 0px
    width 100%
    .form-item
      padding-bottom 16px
      width 100%
      .form-item-label
        padding-bottom 8px
        color #808080
        font-size 14px
        line-height 20px
    .ant-input, .ant-select-lg, .ant-calendar-picker
      width 100%
      height 40px
      line-height 40px
  .form-footer
    padding 12px 16px
    width 100%
    border-top 1px #E5E5E5 solid
    button
      width 100%
      color #fff

.ant-radio-wrapper
  line-height 26px
.ant-input-number-lg
  width 100%
  height 40px
  line-height 40px
</style>
