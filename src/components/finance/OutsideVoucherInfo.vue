<template lang="pug">
.outside-page
  template(v-if="mode !== 'print'")
    .table-module
      .flex-bettween 行程
      table
        tr
          td(
            v-for="(item, index) in voucherMeta.route.attrkey"
            :key="index"
            :class="{'text-right': item.key === 'amount'}")
            | {{ item.label }}
        tr(v-for="(item, index) in voucherMeta.route.value" :key="index" v-if="item.start_city || item.end_city")
          td {{ item.start_city }}
          td {{ item.end_city }}
          td {{ item.start_at }}
          td {{ item.end_at }}
          td.text-right ￥{{ item.amount | toCurrency }}
        tr
          td 合计
          td.text-right(colspan="4")
            | ￥{{ getTotalAmount(voucherMeta.route.value) }}

    .table-module(v-for="(key, keyIndex) in dataKeys" :key="keyIndex")
      .table-title.flex-between
        strong {{ voucherMeta[key].label }}
        span(v-if="key === 'city_traffic'") ￥{{ voucherMeta[key].value | toCurrency }}
      table(v-if="key !== 'city_traffic'")
        tr
          td(
            v-for="(item, index) in voucherMeta[key].attrkey"
            :key="index"
            :colspan="item.key === 'amount' ? 2 : 1"
            :class="{'text-right': item.key === 'amount'}")
            | {{ item.label }}
        template(v-if="key !== 'summary'")
          tr(v-for="(item, index) in voucherMeta[key].value" :key="index" v-if="item.amount")
            td {{ item.number }}
            td {{ item.days }}
            td.text-right ￥{{ item.amount | toCurrency }}
        template(v-else)
          tr(v-for="(item, index) in voucherMeta[key].value" :key="index" v-if="item.amount")
            td(colspan="2")
              .black.text-ellipsis {{ item.content }}
            td.gray.text-right ￥{{ item.amount | toCurrency }}
        tr
          td(colspan="2") 合计
          td.text-right ￥{{ getTotalAmount(voucherMeta[key].value) }}
  template(v-else)
    table
      tr
        td 报销项
        td(colspan="5") 报销细则
        td 报销金额合计
      //- 行程
      template(v-if="routes.length")
        tr
          td.title(:rowspan="routes.length + 1  - (accommodationItems.length || 0)")
            | 行程
          td(v-for="(label, index) in ['出发地', '出发时间', '到达地', '到达时间', '票价']" :key="index")
            | {{ label }}
          td(:rowspan="routes.length + 1- (accommodationItems.length || 0)")
            | ￥{{ getTotalAmount(routes) }}
        tr(v-for="(item, index) in routes" :key="index" v-if="item.start_city || item.end_city")
          td.black {{ item.start_city }}
          td {{ item.start_at }}
          td.black {{ item.end_city }}
          td {{ item.end_at }}
          td ￥{{ item.amount | toCurrency }}
      //- 未乘卧铺补贴
      template(v-if="sleeperSubsidyItems.length")
        tr
          td(:rowspan="sleeperSubsidyItems.length + 1").title 未乘卧铺补贴
          td(v-for="(label, index) in ['人', '天', '金额']" :key="index" :colspan="index === 2 ? 1 : 2") {{ label }}
          td(:rowspan="sleeperSubsidyItems.length + 1")
            span ￥{{ getTotalAmount(sleeperSubsidyItems) }}
        tr(v-for="(item, index) in sleeperSubsidyItems" :key="index + 100")
          td(colspan="2") {{ item.number }}
          td(colspan="2") {{ item.days }}
          td ￥{{ item.amount | toCurrency }}
      //- 住宿费
      template(v-if="accommodationItems.length")
        tr
          td(:rowspan="accommodationItems.length + 1").title 住宿费
          td(v-for="(label, index) in ['人', '天', '金额']" :key="index" :colspan="index === 2 ? 1 : 2")
            | {{ label }}
          td(:rowspan="accommodationItems.length + 1")
            | ￥{{ getTotalAmount(accommodationItems) }}
        tr(v-for="(item, index) in accommodationItems" :key="index + 200")
          td(colspan="2") {{ item.number }}
          td(colspan="2") {{ item.days }}
          td ￥{{ item.amount | toCurrency }}
      //- 住勤补贴
      template(v-if="serviceSubsidyItems.length")
        tr
          td.title(:rowspan="serviceSubsidyItems.length + 1")
            | 助勤补贴
          td(v-for="(label, index) in ['人', '天', '金额']" :key="index" :colspan="index === 2 ? 1 : 2")
            | {{ label }}
          td(:rowspan="serviceSubsidyItems.length + 1")
            | ￥{{ getTotalAmount(serviceSubsidyItems) }}
        tr(v-for="(item, index) in serviceSubsidyItems" :key="index + 400")
          td(colspan="2") {{ item.number }}
          td(colspan="2") {{ item.days }}
          td ￥{{ item.amount | toCurrency }}
      //- 市内交通费
      template(v-if="trafficValue")
        tr
          td.title 市内交通费
          td(colspan="5")
          td ￥{{ trafficValue | toCurrency }}
      //- 其他费用
      template(v-if="summaryItems.length")
        tr
          td(:rowspan="summaryItems.length + 1").title 其他费用
          td(
            v-for="(label, index) in ['摘要', '金额']"
            :key="index"
            :colspan="index === 0 ? 4 : 1"
            :class="{'text-right': index === 3}")
            | {{ label }}
          td(:rowspan="summaryItems.length + 1")
            | ￥{{ getTotalAmount(summaryItems) }}
        tr(v-for="(item, index) in summaryItems" :key="index + 600")
          td(colspan="4") {{ item.content }}
          td ￥{{ item.amount | toCurrency }}
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class OutsideVoucherInfo extends Vue {
  @Prop({ type: Object, default: () => ({}) }) private voucherMeta?: any;
  @Prop({ type: String, default: 'detail' }) private mode?: string;

  getTotalAmount(val: any[]): string | number {
    if (!val || val.length === 0) return 0;
    const sum = (val || []).reduce(
      (sum: number, item: any) => sum + (typeof item.amount === 'number' ? Number(item.amount) : 0),
      0,
    );
    return this.$utils.toCurrency(sum);
  }
  get routes() {
    return this.voucherMeta.route ? this.voucherMeta.route.value || [] : [];
  }
  get accommodationItems() {
    return this.voucherMeta.accommodation ? this.voucherMeta.accommodation.value || [] : [];
  }
  get sleeperSubsidyItems() {
    return this.voucherMeta.sleeper_subsidy ? this.voucherMeta.sleeper_subsidy.value || [] : [];
  }
  get serviceSubsidyItems() {
    return this.voucherMeta.service_subsidy ? this.voucherMeta.service_subsidy.value || [] : [];
  }
  get summaryItems() {
    return this.voucherMeta.summary ? this.voucherMeta.summary.value || [] : [];
  }
  get trafficValue() {
    return this.voucherMeta.city_traffic && this.voucherMeta.city_traffic.value;
  }

  get dataKeys() {
    return ['sleeper_subsidy', 'accommodation', 'service_subsidy', 'city_traffic', 'summary'];
  }
}
</script>

<style lang="stylus" scoped>
fontSize = 16px
lineHeight = 20px

.outside-page
  color #383838
  font-weight 500
  font-size fontSize
  line-height lineHeight
  .table-module
    padding 14px 0px
    width 100%
    border-top 1px #e8e8e8 solid
    font-weight 400
    font-size 14px
    line-height 40px
    table, td
      padding 0px
      border none
    .flex-bettween
      font-weight 500
      font-size 16px
    .black
      color #383838

table
  width 100%
  border 1px #808080 solid
  td
    padding 8px
    height 36px
    border 1px #808080 solid

.title
  width 100px
</style>
