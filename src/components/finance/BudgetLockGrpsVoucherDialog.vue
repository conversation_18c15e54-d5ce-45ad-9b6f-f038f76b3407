<script lang="ts">
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import { IVoucher } from '@/models/finance/voucher';
import { VoucherStore } from '@/store/modules/finance/voucher.store';
import activityStore from '@/store/modules/finance/activity.store';
import VoucherInstanceDialog from '@/components/finance/VoucherInstanceDialog.vue';
import { financeTeacherVouchersStore } from '@/store/modules/finance/teacher/vouchers.store';
import { financeAdminVouchersStore } from '@/store/modules/finance/admin/voucher.store';

@Component({
  components: {
    VoucherInstanceDialog,
  },
})
export default class BudgetLockGrpsVoucherDialog extends Vue {
  @Model('change', { type: Boolean, default: false }) value!: boolean;
  @Prop({ type: Object, required: true, default: () => ({}) }) budget!: IObject;
  @Prop({ type: String, required: true, validator: val => ['admin', 'teacher/own', 'teacher/execute'].includes(val) })
  private role!: 'admin' | 'teacher/own' | 'teacher/execute'; // 确定 voucher 接口

  vouchers: IVoucher[] = [];
  totalCount: number = 0;
  currentPage: number = 1;
  loading: boolean = false;

  voucher: IVoucher = {};
  instanceVisible: boolean = false;

  get activityStore() {
    return activityStore;
  }

  get vouchersStore() {
    return this.role === 'admin' ? financeAdminVouchersStore : financeTeacherVouchersStore;
  }
  get stateMap() {
    return VoucherStore.withActivityTeacher.stateMap;
  }
  get voucherTypeText() {
    return VoucherStore.withActivityTeacher.voucherTypeText;
  }
  get visible() {
    return this.value;
  }
  set visible(val: boolean) {
    this.$emit('change', val);
  }

  @Watch('visible')
  onOpen() {
    if (this.value) {
      if (this.$route.path.includes('teacher/own')) {
        this.vouchersStore.init({
          parents: [
            { type: 'own', id: null },
            { type: 'budget_lock_grps', id: this.budget.id },
          ],
        });
      } else {
        this.vouchersStore.init({ parents: [{ type: 'budget_lock_grps', id: this.budget.id }] });
      }
      this.fetchVouchers(1);
    }
  }

  async fetchVouchers(page: number = 1) {
    if (this.budget.id) {
      this.loading = true;
      this.vouchersStore
        .index()
        .then((res: IObject) => {
          this.vouchers = res.data.vouchers;
          this.currentPage = res.data.current_page;
          this.totalCount = res.data.total_count;
        })
        .finally(() => {
          this.loading = false;
        });
    }
  }

  async onShow(record: any) {
    this.voucher = { ...record };
    this.instanceVisible = true;
  }
}
</script>

<template lang="pug">
MainModal(v-model='visible', width='1200px')
  template(slot='title')
    .flex-between(style='padding-right: 34px')
      strong {{ budget.name + " —— 单据列表" }}
  AdminTable(
    :data='vouchers',
    :currentPage='currentPage',
    :totalCount='totalCount',
    :loading='loading',
    :bordered='true',
    :hideOnSinglePage='true',
    rowClassName='click-row',
    @rowClick='onShow',
    @change='fetchVouchers'
  )
    a-table-column(title='#', dataIndex='_index')
    a-table-column(title='流程单号', dataIndex='seq', :width='160')
      template(slot-scope='seq')
        .text-wrap {{ seq }}
    a-table-column(title='资金卡号', dataIndex='project.uid')
    a-table-column(title='资金卡名', dataIndex='project.name')
    a-table-column(title='金额', :width='200', align='right')
      template(slot-scope='record')
        .text-right 总金额：{{ record.amount | toCurrency }}
        .text-right 报销金额：{{ record.final_amount | toCurrency }}
        .text-right 劳务费个税：{{ (record.tax || 0) | toCurrency }}
    a-table-column(title='收款人信息', :width='140')
      template(slot-scope='scope')
        .text 名称：{{ scope.payee_meta && scope.payee_meta.name }}
        .text 部门：{{ scope.payee_meta && scope.payee_meta.department }}
        .text 支付方式：{{ scope.payee_meta && scope.payee_meta.payment_way }}
    a-table-column(title='事由备注', dataIndex='remark')
      template(slot-scope='remark')
        a-tooltip(:title='remark')
          .text-ellipsis(style='max-width: 150px')
            | {{ remark }}
    a-table-column(title='创建时间', dataIndex='created_at')
      template(slot-scope='created_at')
        | {{ created_at | format }}
    a-table-column(title='类型', dataIndex='type')
      template(slot-scope='type')
        | {{ voucherTypeText[type] }}
    a-table-column(title='状态', dataIndex='state', :width='100')
      template(slot-scope='state')
        span(:class='stateMap[state].class')
          | {{ stateMap[state].text }}

  VoucherInstanceDialog(v-model='instanceVisible', :voucher='voucher', @instanceChange='fetchVouchers(currentPage)')
</template>

<style lang="stylus" scoped></style>
