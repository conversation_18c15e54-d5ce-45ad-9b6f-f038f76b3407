<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { IVoucher, VoucherType } from '@/models/finance/voucher';

@Component({
  components: {},
})
export default class FinanceVoucherTable extends Vue {
  @Prop({ type: Object, default: () => ({}) }) private store!: IObject;
  @Prop({ type: String, default: '单据列表' }) private title!: string;
  @Prop({ type: Function }) private exportFunction!: () => void;
  @Prop({ type: Boolean, default: true }) private operable!: boolean;
  @Prop({ type: Boolean, default: true }) private isPanelMode!: boolean;

  query: IObject = {};
  timeQuery: IObject = {};
  exportLoading: Boolean = false;

  get currentUser() {
    return this.$store.state.currentUser || {};
  }
  get stateOptions() {
    return Object.values(this.store.loanStateMap || {});
  }
  get component() {
    return this.isPanelMode ? 'Panel' : 'TitleContainer';
  }
  get VoucherType() {
    return VoucherType;
  }
  get searcherOptions() {
    return [
      { label: '金额', value: 'amount', type: 'number' },
      { label: '支付方式', value: 'payment_way', type: 'string' },
    ];
  }

  fetchRecords(page: number, tableQuery?: IObject, pageSize?: number) {
    this.$emit('change', page, { ...tableQuery, ...this.timeQuery, ...this.query }, pageSize);
  }
  onShow(...args: any) {
    this.$emit('show', ...args);
  }
  onEdit(...args: any) {
    this.$emit('edit', ...args);
  }
  onDelete(...args: any) {
    this.$emit('delete', ...args);
  }
  async onExport() {
    try {
      this.exportLoading = true;
      if (this.exportFunction) {
        await this.exportFunction();
      }
      this.exportLoading = false;
    } catch (error) {
      this.exportLoading = false;
    }
  }
  onDateChange(range: any) {
    const [start, end] = range;
    if (start && end) {
      this.timeQuery.created_at_gteq = start.toString();
      this.timeQuery.created_at_lteq = end.toString();
    } else {
      this.timeQuery.created_at_gteq = null;
      this.timeQuery.created_at_lteq = null;
    }
    this.fetchRecords(1);
  }
}
</script>

<template lang="pug">
component.voucher-table(:title="`${title} · ${store.totalCount}`" :is="component")
  template(slot="actions")
    RansackSearcher.header-item(
      v-model="query"
      :options="searcherOptions"
      :variables="['seq', 'projects_name', 'projects_uid', 'remark']"
      tips="检索单据"
      placeholder="流水单号、资金卡号、资金卡名"
      @change="fetchRecords(1)")
    a-range-picker.header-item(
      @change="onDateChange"
      format="YYYY-MM-DD HH:mm"
      :showTime="{ defaultValue: $moment('00:00:00', 'HH:mm:ss') }")
    TextButton(
      icon="download"
      @click="onExport"
      :loading="exportLoading"
      :disabled="exportLoading"
      v-if="exportFunction")
      | 导出

  AdminTable(
    :store="store"
    :hideOnSinglePage="false"
    rowClassName="click-row"
    :bordered="isPanelMode"
    @rowClick="onShow"
    @change="fetchRecords")
    a-table-column(title="#" dataIndex="_index" :width="60")
    a-table-column(title="流程单号" dataIndex="seq" :width="160")
      template(slot-scope="seq")
        .text-wrap {{ seq }}
    a-table-column(title="资金卡号" dataIndex="project.uid" :width="140")
    a-table-column(title="资金卡名" dataIndex="project.name" :width="140")
    a-table-column(title="金额" align="right" :width="220")
      template(slot-scope="record")
        .text-right 总金额：{{ record.amount | toCurrency }}
        .text-right 报销金额：{{ record.final_amount | toCurrency }}
        .text-right 劳务费个税：{{ (record.tax || 0) | toCurrency }}
    a-table-column(title="收款人信息" :width="160")
      template(slot-scope="scope")
        .text 名称：{{ scope.payee_meta && scope.payee_meta.name }}
        .text 部门：{{ scope.payee_meta && scope.payee_meta.department }}
        .text 支付方式：{{ scope.payee_meta && scope.payee_meta.payment_way }}
    a-table-column(title="事由备注" dataIndex="remark" :width="140")
      template(slot-scope="remark")
        a-tooltip(:title="remark")
          .text-ellipsis(style="max-width: 150px;")
            | {{ remark }}
    a-table-column(title="创建时间" dataIndex="created_at" :width="120")
      template(slot-scope="created_at")
        | {{ created_at | format }}
    a-table-column(title="类型" dataIndex="type" :width="100")
      template(slot-scope="type")
        | {{ store.voucherTypeText[type] }}
    a-table-column(title="状态" :filters="stateOptions" key="state" :width="80")
      template(slot-scope="record")
        span(v-if="record.type === 'Finance::LoanVoucher'" :class="store.loanStateMap[record.state].class")
          | {{ store.loanStateMap[record.state].text }}
        span(v-else :class="store.stateMap[record.state].class")
          | {{ store.stateMap[record.state].text }}
    a-table-column(:width="100" v-if="operable")
      template(slot-scope="record")
        .text-right.table-hover-col(v-if="record.state === 'created' && currentUser.id === record.teacher_id")
          IconTooltip(icon="edit" tips="编辑" @click="onEdit(record)")
          PopoverConfirm(
            title="删除"
            content="您确认删除此报销单据吗？"
            :loading="store.loading"
            @confirm="onDelete(record)")
            IconTooltip(icon="delete" tips="删除")
  slot
</template>

<style lang="stylus" scoped>
.voucher-table
  height 100%
  .header-item
    margin-left 10px
  .text-wrap
    white-space wrap
    word-break break-all
</style>
