<script lang="ts">
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import { Project, IAnasDetail, IProject } from '../../models/finance/project';

@Component
export default class ProjectAnasDialog extends Vue {
  @Model('change', { type: Boolean, default: false }) value!: boolean;
  @Prop({ type: Object, default: () => ({}) }) project!: IProject;
  @Prop({ type: String, default: 'teacher/own' }) role!: 'admin' | 'teacher/own';

  loading: boolean = false;
  records: IAnasDetail[] = [];
  page: number = 1;
  pageSize: number = 10;
  count: number = 0;

  get visible() {
    return this.value;
  }
  set visible(val: boolean) {
    this.$emit('change', val);
  }

  @Watch('visible')
  onOpen() {
    if (this.value) {
      this.fetchAnasList(1);
    }
  }

  async fetchAnasList(page: number = 1, query: IObject = {}, size: number = this.pageSize) {
    if (!this.project.id) return;
    this.loading = true;
    this.pageSize = size;
    const { data } = await new Project(this.role)
      .projectAnas(this.project.id, { page: 1, per_page: size, q: query })
      .finally(() => {
        this.loading = false;
      });
    this.records = data.project_anas.map(ana => ({
      ...ana,
      normal_ratio: this.getPercent(ana.normal_ratio),
      doing_ratio: this.getPercent(ana.doing_ratio),
      completed_ratio: this.getPercent(ana.completed_ratio),
    }));
    this.count = data.total_count;
    this.page = data.current_page;
  }

  getPercent(ratio: number | undefined) {
    return Math.round((ratio || 0) * 10000) / 100;
  }
}
</script>

<template lang="pug">
MainModal(v-model="visible" :title="`资金卡：${project.name} - 统计日志`")
  AdminTable(
    :data="records"
    :currentPage="page"
    :totalCount="count"
    :loading="loading"
    :bordered="true"
    :hideOnSinglePage="true"
    rowClassName="click-row"
    @change="fetchAnasList")
    a-table-column(title="月份" dataIndex="day" align="center")
      template(slot-scope="day")
        | {{ day | format('YYYY年 MM月') }}
    a-table-column(title="预计报销率" align="center")
      template(slot-scope="record")
        a-progress(
          type="dashboard"
          :width="64"
          :percent="record.doing_ratio + record.completed_ratio"
          strokeColor="#75C940")
    a-table-column(title="已完成报销率" dataIndex="completed_ratio" align="center")
      template(slot-scope="completed_ratio")
        a-progress(
          type="dashboard"
          :width="64"
          :percent="completed_ratio"
          strokeColor="#75C940")
    a-table-column(title="计划使用率" dataIndex="normal_ratio" align="center")
      template(slot-scope="normal_ratio")
        a-progress(
          type="dashboard"
          :width="64"
          :percent="normal_ratio"
          strokeColor="#75C940")
</template>

<style lang="stylus" scoped></style>
