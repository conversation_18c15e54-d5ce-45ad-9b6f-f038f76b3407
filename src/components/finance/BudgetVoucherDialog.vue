<script lang="ts">
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import { Voucher, IVoucher } from '@/models/finance/voucher';
import { VoucherStore } from '../../store/modules/finance/voucher.store';
import activityStore from '@/store/modules/finance/activity.store';
import VoucherInstanceDialog from '@/components/finance/VoucherInstanceDialog.vue';
import ComFinanceBudgetLockGrpsCard2 from '@/components/finance/ComFinanceBudgetLockGrpsCard2.vue';
import { financeTeacherBudgetLockGrpsStore } from '@/store/modules/finance/teacher/budget_lock_grps.store';
import { financeAdminBudgetLockGrpStore } from '@/store/modules/finance/admin/budget_lock_grp.store';

@Component({
  components: {
    VoucherInstanceDialog,
    ComFinanceBudgetLockGrpsCard2,
  },
})
export default class BudgetVoucherDialog extends Vue {
  @Model('change', { type: Boolean, default: false }) value!: boolean;
  @Prop({ type: Object, required: true, default: () => ({}) }) budget!: IObject;
  @Prop({ type: String, required: true, validator: val => ['admin', 'teacher/own', 'teacher/execute'].includes(val) })
  private role!: 'admin' | 'teacher/own' | 'teacher/execute'; // 确定 voucher 接口

  vouchers: IVoucher[] = [];
  totalCount: number = 0;
  currentPage: number = 1;
  loading: boolean = false;

  voucher: IVoucher = {};
  instanceVisible: boolean = false;
  get tabs() {
    return [
      { label: this.budget.name + ' —— 单据列表', key: this.budget.name },
      { label: '预算请购单', key: '预算请购单' },
    ];
  }
  BudgetLockGrpsStore: any = null;
  activeTabKey = '';
  get activityStore() {
    return activityStore;
  }
  get voucherModel() {
    return new Voucher({
      parentResource: 'budgets',
      role: this.role,
    });
  }
  get stateMap() {
    return VoucherStore.withActivityTeacher.stateMap;
  }
  get voucherTypeText() {
    return VoucherStore.withActivityTeacher.voucherTypeText;
  }
  get visible() {
    return this.value;
  }
  set visible(val: boolean) {
    this.$emit('change', val);
  }
  get teacherBudgetLockGrpsStore() {
    return financeTeacherBudgetLockGrpsStore;
  }
  get adminBudgetLockGrpsStore() {
    return financeAdminBudgetLockGrpStore;
  }

  @Watch('visible')
  onOpen() {
    if (this.value) {
      this.fetchVouchers(1);
      this.activeTabKey = this.tabs[0].key;
      this.storeInit();
    }
  }
  storeInit() {
    if (this.$route.path.includes('teacher/own')) {
      this.teacherBudgetLockGrpsStore.init({
        parents: [
          { type: 'own', id: null },
          { type: 'budgets', id: this.budget.id },
        ],
      });
      this.BudgetLockGrpsStore = this.teacherBudgetLockGrpsStore;
    } else if (this.$route.path.includes('teacher/execute')) {
      this.teacherBudgetLockGrpsStore.init();
      this.BudgetLockGrpsStore = this.teacherBudgetLockGrpsStore;
    } else {
      this.adminBudgetLockGrpsStore.init({
        parents: [{ type: 'budgets', id: this.budget.id }],
      });
      this.BudgetLockGrpsStore = this.adminBudgetLockGrpsStore;
    }
  }

  async fetchVouchers(page: number = 1) {
    if (this.budget.id) {
      this.loading = true;
      const { data } = await this.voucherModel
        .indexByParent(this.budget.id, {
          page,
          per_page: 10,
        })
        .finally(() => {
          this.loading = false;
        });
      this.vouchers = data.vouchers;
      this.currentPage = data.current_page;
      this.totalCount = data.total_count;
    }
  }
  async onExport() {
    try {
      const params = {
        parent_type: 'budget',
        parent_id: this.budget.id,
        activityId: this.$route.params.activityId,
        type: ({
          admin: '',
          'teacher/own': 'own',
          'teacher/execute': 'execute',
        } as any)[this.role],
        role: this.role === 'admin' ? 'admin' : 'teacher',
      };
      const { data } = await activityStore.vouchersExcel(params);
      this.$helper.exportExcel(data);
    } catch (error) {
      this.$message.error('导出失败');
    }
  }
  async onShow(record: any) {
    this.voucher = { ...record };
    this.instanceVisible = true;
  }
}
</script>

<template lang="pug">
MainModal(v-model='visible', width='1200px')
  template(slot='title')
    .flex-between(style='padding-right: 34px')
      TaTab.ta-tab(v-model='activeTabKey', :tabs='tabs')
      TextButton(
        v-if='activeTabKey == budget.name',
        icon='download',
        @click='onExport',
        :loading='activityStore.loading',
        :disabled='activityStore.loading'
      )
        | 导出
  AdminTable(
    v-if='activeTabKey == budget.name',
    :data='vouchers',
    :currentPage='currentPage',
    :totalCount='totalCount',
    :loading='loading',
    :bordered='true',
    :hideOnSinglePage='true',
    rowClassName='click-row',
    @rowClick='onShow',
    @change='fetchVouchers'
  )
    a-table-column(title='#', dataIndex='_index')
    a-table-column(title='流程单号', dataIndex='seq', :width='160')
      template(slot-scope='seq')
        .text-wrap {{ seq }}
    a-table-column(title='资金卡号', dataIndex='project.uid')
    a-table-column(title='资金卡名', dataIndex='project.name')
    a-table-column(title='金额', :width='200', align='right')
      template(slot-scope='record')
        .text-right 总金额：{{ record.amount | toCurrency }}
        .text-right 报销金额：{{ record.final_amount | toCurrency }}
        .text-right 劳务费个税：{{ (record.tax || 0) | toCurrency }}
    a-table-column(title='收款人信息', :width='140')
      template(slot-scope='scope')
        .text 名称：{{ scope.payee_meta && scope.payee_meta.name }}
        .text 部门：{{ scope.payee_meta && scope.payee_meta.department }}
        .text 支付方式：{{ scope.payee_meta && scope.payee_meta.payment_way }}
    a-table-column(title='事由备注', dataIndex='remark')
      template(slot-scope='remark')
        a-tooltip(:title='remark')
          .text-ellipsis(style='max-width: 150px')
            | {{ remark }}
    a-table-column(title='创建时间', dataIndex='created_at')
      template(slot-scope='created_at')
        | {{ created_at | format }}
    a-table-column(title='类型', dataIndex='type')
      template(slot-scope='type')
        | {{ voucherTypeText[type] }}
    a-table-column(title='状态', dataIndex='state', :width='100')
      template(slot-scope='state')
        span(:class='stateMap[state].class')
          | {{ stateMap[state].text }}
  ComFinanceBudgetLockGrpsCard2.table(v-else-if='activeTabKey == "预算请购单"', :store='BudgetLockGrpsStore')
  VoucherInstanceDialog(v-model='instanceVisible', :voucher='voucher', @instanceChange='fetchVouchers(currentPage)')
</template>

<style lang="stylus" scoped>
.ta-tab
  width 50%
.table
  margin 0 20px
</style>
