<script lang="ts">
import { Component, Vue, Prop, Model } from 'vue-property-decorator';
import InstanceDetail from '@/components/bpm/InstanceDetail.vue';
import { IVoucher } from '@/models/finance/voucher';
import instanceModel from '@/models/bpm/instance';
import BpmFlowableVoucher from '@/components/bpm/flowable/BpmFlowableVoucher.vue';
import { IInstance } from '@/models/bpm/instance';

@Component({
  components: {
    InstanceDetail,
    BpmFlowableVoucher,
  },
})
export default class FinanceVoucherInstanceDialog extends Vue {
  @Model('change', { type: Boolean, default: false }) readonly value!: boolean;
  @Prop({ type: Object, default: () => {} }) readonly voucher!: IVoucher;

  instanceChanged: boolean = false;

  get project() {
    return this.voucher.project || {};
  }

  onModalChange(visible: boolean) {
    this.$emit('change', visible);
    if (!visible) {
      this.$emit('close');
      if (this.instanceChanged) {
        this.$emit('instanceChange');
      }
    }
    this.instanceChanged = false;
  }
  onInstanceChanged() {
    this.instanceChanged = true;
  }
  print(instance: IInstance) {
    const { state, flowable_type } = instance;
    this.$utils.open(`/finance/teacher/vouchers/${this.voucher.id}/print?type=${flowable_type}&state=${state}`);
  }
  edit() {
    this.$router.push(`/finance/teacher/activities/${this.voucher.activity_id}/vouchers/${this.voucher.id}/edit`);
  }
}
</script>

<template lang="pug">
MainModal(
  :value="value"
  width="85%"
  :footer="false"
  :maskClosable="false"
  @change="onModalChange")
  template(slot="title")
    .empty empty
  //-   span(v-if="project.name") {{ project.name }} -
  //-   span 单据详情
  InstanceDetail(
    :instanceId="voucher.instance_id"
    @change="onInstanceChanged"
    @flowableEdit="edit"
    @print="print")
    template(#flowable="{ instance }")
      BpmFlowableVoucher(:instance="instance")
</template>

<style lang="stylus" scoped>
.empty
  opacity 0
</style>
