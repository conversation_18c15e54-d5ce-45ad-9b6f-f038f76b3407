<template lang="pug">
.component-page
  .module
    .module-top
      strong {{ title }}
    .module-middle
      a-row(:gutter='16')
        a-col(:span='12')
          a-form-item.form-label(label='收款单位/收款个人', :required='true')
            a-input(v-model='value.payee_meta.name', size='large', placeholder='请输入收款单位/收款个人')
        a-col(:span='12')
          a-form-item.form-label(label='部门', :required='true')
            a-input(v-model='value.payee_meta.department', size='large', placeholder='请输入部门')
        a-col(:span='24')
          a-form-item.form-label(:required='true')
            span(slot="label") 付款方式
              span(style="color: red;") (资金卡预算里“资产采购方式”为”集市采购、集中采购、政府采购“的，付款方式请选择”国库支付“。)
            a-select.block(
              :value='value.payee_meta.payment_way || undefined',
              size='large',
              placeholder='请选择付款方式',
              @change='onSelectPaymentWay'
            )
              a-select-option(v-for='o in paymentWays', :value='o.label', :key='o.label')
                | {{ o.label }}
        a-col(:span='12')
          a-form-item.form-label(label='联系方式', :required='true')
            a-input(v-model='value.payee_meta.phone', size='large', placeholder='请输入联系方式')
        a-col(:span='12')
          a-form-item.form-label(label='报销费用合计', :required='true')
            CurrencyInput(v-model='value.amount', placeholder='请输入金额', style='width: 100%', size='large')
            .price-uppercase
              | {{ amountUppercase }}
        a-col(:span='24')
          a-form-item.form-label(label='事由备注', :required='true')
            a-input(v-model='value.remark', size='large', placeholder='请输入内容')
  .module
    FileUploader.uploader(:value='attachments', @change='onFilesChange')
      .attachment-upload
        strong 单据附件
        a-button(icon='cloud-upload', type='primary', size='large') 上传附件
  slot
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit, PropSync } from 'vue-property-decorator';
import { Voucher, VoucherType } from '@/models/finance/voucher';
import { IFile } from '../../models/file';

@Component({
  components: {},
})
export default class VoucherAssetForm extends Vue {
  @Prop({ type: Object, default: () => Voucher.getInitialVoucher() }) private value?: any;
  formEditKey: number = 0;
  @Prop({ type: Boolean, default: true }) isFileAllSettled?: boolean;

  get voucherType() {
    return this.value.type || this.$route.query.type;
  }
  get VoucherType() {
    return VoucherType;
  }
  get title() {
    return ({
      [VoucherType.Routine]: '收款人信息',
      [VoucherType.Outside]: '出差人信息',
      [VoucherType.Loan]: '借款人信息',
    } as any)[this.voucherType];
  }
  get paymentWays() {
    return this.voucherType === VoucherType.Loan
      ? [{ label: '单位预付' }]
      : [
          { label: '代发（校内）' },
          { label: '代发（校外）' },
          { label: '转账' },
          { label: '国库支付' },
          { label: '公务卡' },
          { label: '支票' },
          { label: '集市采购' },
          { label: '集中采购' },
          { label: '托收' },
          { label: '加密支付' },
          { label: '学生代发' },
          { label: '抚恤金' },
          { label: '福利费' },
        ];
  }
  get metaKeys() {
    return Object.keys(this.value.meta) || [];
  }
  get amountUppercase() {
    return this.value.amount && this.$utils.digitUppercase(this.value.amount);
  }
  get attrKey() {
    return {
      route: {
        start_city: '',
        end_city: '',
        start_at: '',
        end_at: '',
        amount: '',
      },
      attribute: {
        number: '',
        days: '',
        amount: '',
      },
      summary: {
        content: '',
        amount: '',
      },
    };
  }
  get totalAmount() {
    const meta: any = this.value.meta || {};
    const dataKeys = Object.keys(meta);
    const totalAmount = (dataKeys || []).reduce((sum: number, key: string) => {
      const res =
        typeof meta[key].value !== 'object'
          ? meta[key].value
          : (meta[key].value || []).reduce(
              (keySum: number, item: any) => keySum + (typeof item.amount === 'number' ? Number(item.amount) : 0),
              0,
            );
      return Number(sum) + Number(res);
    }, 0);
    if (this.voucherType === VoucherType.Outside) {
      this.value.amount = totalAmount;
    }
    return totalAmount;
  }
  get attachments() {
    return (this.value.receipts_attributes || []).map((e: any) => e.attachment);
  }

  public addAttribute(val: string) {
    const key = ['route', 'summary'].includes(val) ? val : 'attribute';
    const attribute = {
      key: new Date().getTime(),
      ...(this.attrKey as any)[key],
    };
    this.value.meta[val].value.push(attribute);
    this.formEditKey = attribute.key;
  }

  public clearAttribute(key: string, index: number) {
    this.$confirm({
      title: '确定要继续执行此操作吗?',
      okText: '确定',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        this.value.meta[key].value.splice(index, 1);
      },
      onCancel: () => {},
    });
  }

  public formatAmount(val: any[]): string | number {
    if (!val || val.length === 0) return 0;
    const sum = (val || []).reduce(
      (sum: number, item: any) => sum + (typeof item.amount === 'number' ? Number(item.amount) : 0),
      0,
    );
    return this.$utils.toCurrency(sum);
  }

  public onFilesChange(fileItems: any[], statusFiles: any, isFileAllSettled: boolean) {
    this.$emit('update:isFileAllSettled', isFileAllSettled);
    this.value.receipts_attributes = (fileItems || []).map((item: any) => ({
      name: item.fileName,
      attachment: item,
    }));
  }

  public removeAttachment(val: any, index: number) {
    this.$confirm({
      title: '确定要继续执行此操作吗?',
      okText: '确定',
      okType: 'danger',
      cancelText: '取消',
      onOk: () => {
        this.attachments.splice(index, 1);
      },
      onCancel: () => {},
    });
  }

  onSelectPaymentWay(way: string) {
    this.$set(this.value.payee_meta, 'payment_way', way);
  }
}
</script>

<style lang="stylus" scoped>
.component-page
  padding-bottom 16px
  width 100%
  .module
    margin 0px auto
    padding-bottom 24px
    max-width 800px
    width 100%
    border-bottom 1px #e8e8e8 solid
    &:last-child
      border-bottom none
    .uploader
      width 100%
      .attachment-upload
        display flex
        justify-content space-between
        align-items center
        margin-top 24px
        width 100%
    .module-top
      display flex
      justify-content space-between
      align-items center
      margin-top 24px
      width 100%
    .module-title
      margin 24px 0 16px
    .module-middle
      width 100%
      .form-label
        margin-bottom 0
        padding 20px 0px 8px
        color #808080
        font-size 14px
        line-height 20px
      table, tr
        width 100%
        th, td
          box-sizing border-box
          padding 8px 20px 8px 0px
          color #808080
          font-size 14px
          line-height 20px
        .actions
          padding 8px 0px
          text-align right
      .price-uppercase
        margin-top 10px
        font-size 16px
        line-height 24px
      .block
        display block
        width 100%
label, span
  color #808080
  font-size 14px
  line-height 20px
strong
  color #383838
  font-size 16px
  line-height 20px
.black
  color #383838
  font-size 14px
  line-height 20px
</style>
