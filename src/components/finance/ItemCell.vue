<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class FinanceItemCell extends Vue {
  @Prop({ type: String }) value!: string;
}
</script>

<template lang="pug">
.finance-role-cell
  .name
    slot
      a-tooltip(:title="value")
        span {{ value }}
  .actions
    slot(name="actions")
</template>

<style lang="stylus" scoped>
.finance-role-cell
  padding 12px
  background rgba(250,250,250,1)
  border-radius 3px
  border 1px solid rgba(229,229,229,1)
  display flex
  height 44px
  overflow hidden
  &:hover
    border-color #3DA8F5
    cursor pointer
    .actions
      display block
  .name
    overflow hidden
    width 100%
    text-overflow ellipsis
    white-space nowrap
    height 20px
    line-height 20px
  .actions
    flex-shrink 0
    display none
    line-height 16px
</style>
