<script lang="ts">
import BudgetLockGrpsVoucherDialog from '@/components/finance/BudgetLockGrpsVoucherDialog.vue';
import ProjectAnasDialog from '@/components/finance/ProjectAnasDialog.vue';
import FinanceProjectTable from '@/components/finance/ProjectTable.vue';
import ImportExcel from '@/components/import_export/ImportExcel.vue';
import { IBudget } from '@/models/finance/budget';
import { Component, Prop, Vue } from 'vue-property-decorator';
import InstanceDetailDialog from '../bpm/InstanceDetailDialog.vue';

@Component({
  components: {
    FinanceProjectTable,
    ImportExcel,
    ProjectAnasDialog,
    BudgetLockGrpsVoucherDialog,
    InstanceDetailDialog,
  },
})
export default class ComFinanceBudgetLockGrpsCard extends Vue {
  @Prop({ type: Object }) store!: IObject;

  visibleDialog = false;
  instanceId = 0;
  showBudget: IBudget = {};
  voucherVisible: boolean = false;
  query: IObject = {};
  exportHeaders: IObject[] = [];
  tabs: object[] = [
    {
      label: '进行中',
      key: 'doing',
      query: { state_eq: 'doing' },
      mode: 'table',
    },
    {
      label: '已完成',
      key: 'done',
      query: { state_eq: 'done' },
      mode: 'table',
    },
    {
      label: '已取消',
      key: 'cancelled',
      query: { state_eq: 'cancelled' },
      mode: 'table',
    },
    {
      label: '全部',
      key: 'all',
      query: {},
      mode: 'table',
    },
  ];

  get config() {
    return {
      mode: 'custom',
      store: this.store,
      recordName: '资金请购单',
      tableConfig: {
        bordered: true,
      },
      searcherSimpleOptions: [
        { type: 'string', label: '名称', key: 'name' },
        { type: 'string', label: 'seq', key: 'seq' },
      ],
    };
  }

  get stateMap(): IObject {
    return {
      doing: { label: '进行中', type: 'warning', value: 'doing' },
      done: { label: '已完成', type: 'success', value: 'done' },
      cancelled: { label: '已取消', type: 'danger', value: 'cancelled' },
    };
  }
  show(record: IBudget) {
    this.showBudget = record;
    this.voucherVisible = true;
  }

  mounted() {
    this.store.init();
  }

  get isAdmin() {
    return this.$utils.hasPermission('finance', 'admin');
  }

  done(id: Number) {
    this.store.sendMemberAction({ id: id, action: 'done' });
  }

  visibleInstance(record: IObject) {
    if (record.instance_id) {
      this.instanceId = record.instance_id;
      this.visibleDialog = true;
    }
  }
}
</script>

<template lang="pug">
.com_finance_budget_lock_grps_card
  TaTitleHeader(:title='$route.meta.title')
  TaIndexView(ref='indexView', :tabs='tabs', :config='config', @onShow='show', :tabsLeftMargin='0')
    template(#table)
      a-table-column(title='名称', dataIndex='name', :width='140')
        template(slot-scope='scope, record')
          .info {{ `【${record.name}】` }}
      a-table-column(title='基本信息', :width='260')
        template(slot-scope='record')
          .info 资金卡名：{{ record.name }}
          .info 资金卡号：{{ record.seq }}
      a-table-column(title='总金额', dataIndex='amount', align='right', :width='140')
        template(slot-scope='amount')
          span {{ amount }}
      a-table-column(title='金额详情', :width='200')
        template(slot-scope='record')
          .info 已使用：{{ record.completed_payment_amount }}
          .info 已锁定：{{ record.locking_amount }}
          .info 请购中：{{ record.processing_payment_amount }}
      a-table-column(title='负责人', :width='140')
        template(slot-scope='record')
          .info {{ record.owner.name }}
      a-table-column(title='创建时间', :width='110', dataIndex='created_at')
        template(slot-scope='created_at')
          | {{ created_at | format }}
      a-table-column(title='状态', width='100px', align='center')
        template(slot-scope='record')
          TaTag(:type='stateMap[record.state].type', size='small')
            | {{ stateMap[record.state].label }}
      a-table-column(title='操作', width='150px', align='center')
        template(slot-scope='record')
          a-button(type='default' @click.stop='visibleInstance(record)') 查看请购流程
          .state-action(v-if='record.state === "doing"', @click.stop='')
            PopoverConfirm(
              title='完成采购',
              content='确认释放预算吗',
              @click.stop='',
              @confirm='done(record.id)'
            )
              a-button.btn-primary
                .line 完成采购
                .line （释放预算）
  BudgetLockGrpsVoucherDialog(v-model='voucherVisible', role='teacher/own', :budget='showBudget')
  InstanceDetailDialog(
    v-if='visibleDialog'
    title='审批详情',
    v-model='visibleDialog',
    :instanceId='instanceId',
  )
</template>

<style lang="stylus" scoped>
.com_finance_budget_lock_grps_card
  .state-action
    margin-top 10px
    .btn-primary
      height 50px
      .line
        font-size 12px
        display block
</style>
