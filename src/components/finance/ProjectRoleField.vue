<script lang="ts">
/**
 * 人员选择器
 */
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import UserSelectorDialog from '@/components/hr/UserSelectorDialog.vue';
import approvalRoleStore from '@/store/modules/finance/approval_role.store';
import { IApprovalRole } from '../../models/finance/approval_role';
import projectRoleModel, { IProjectRole } from '../../models/finance/project_role';

@Component({
  components: {
    UserSelectorDialog,
  },
})
export default class ProjectRoleField extends Vue {
  visible: boolean = false;
  selectedProjectRoles: IProjectRole[] = [];
  newMembers: IProjectRole[] = [];
  approvalRole: IApprovalRole = {};

  @Model('change', { required: true }) readonly value!: IProjectRole[];
  @Prop({ type: Array, default: () => [] }) readonly defaultMembers!: any[];
  @Prop({ type: String, default: '选择人员' }) readonly title!: string;
  @Prop({ type: Boolean, default: false }) readonly disabled!: boolean;

  get approvalRoles() {
    const selectedIds = this.selectedProjectRoles.map(o => o.approval_role_id);
    return approvalRoleStore.records.filter(o => !selectedIds.includes(o.id!));
  }

  @Watch('value', { deep: true, immediate: true })
  onValueChange() {
    if (this.value instanceof Array) {
      this.selectedProjectRoles = [...this.value];
    }
  }

  created() {
    approvalRoleStore.fetch({ page: 1 });
  }
  openSelector(role: IApprovalRole) {
    this.approvalRole = role;
    this.visible = true;
  }
  chooseMember(memberIds: number[], members: any[]) {
    if (this.disabled) {
      return;
    }
    this.newMembers = members.map(o => ({
      approval_role_id: this.approvalRole.id,
      approval_role_name: this.approvalRole.name,
      teacher_id: o.id,
      teacher_name: o.name,
    }));
    this.selectedProjectRoles.push(...this.newMembers);
    this.syncValue();
  }
  removeMember(index: number) {
    if (this.disabled) {
      return;
    }
    const role = this.selectedProjectRoles[index];
    if (role.id) {
      projectRoleModel.delete(role.id);
    }
    this.selectedProjectRoles.splice(index, 1);
    this.syncValue();
  }
  syncValue() {
    this.$emit('change', this.selectedProjectRoles, this.newMembers);
  }
}
</script>

<template lang="pug">
.operator-select
  .contact-role
    a-tag.tag(
      v-for="(role, index) in selectedProjectRoles"
      :key="role.approval_role_id"
      :closable="!disabled"
      @close="removeMember(index)")
      | {{role.approval_role_name}}: {{ role.teacher_name }}
    a-dropdown(v-if="approvalRoles.length > 0")
      a-menu(slot="overlay")
        a-menu-item(
          v-for="approvalRole in approvalRoles"
          :key="approvalRole.id"
          @click.native="openSelector(approvalRole)")
          | {{ approvalRole.name }}
      a-button(
        icon="plus"
        shape="circle"
        size="small"
        type="primary"
        :disabled="disabled")

  UserSelectorDialog(
    v-model="visible"
    :title="title"
    :multiple="false"
    @change="chooseMember")
</template>

<style lang="stylus" scoped>
.operator-select
  width 100%
  .contact-role
    display flex
    align-items center
    padding 4px 0
    flex-wrap wrap
    .tag
      .anticon-close
        margin-left 5px
</style>
