<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { IBudget } from '../../models/finance/budget';

@Component({
  components: {},
})
export default class FinanceBudgetTable extends Vue {
  @Prop({ type: Object, default: () => ({}) }) private store!: any;
  @Prop({ type: Boolean, default: false }) private operable!: boolean;
  @Prop({ type: Boolean, default: false }) private assignable!: boolean; // 教师端分配执行人
  @Prop({ type: Boolean, default: false }) private isSubBudget!: boolean; // 教师端分配执行人
  @Prop({ type: Object, default: null }) private rowSelection!: IObject;
  @Prop({ type: Boolean, default: false }) private visiblePay!: boolean;

  preRecordsLength: number = 0;
  catalogNameFilters: object[] = [];
  nameFilters: object[] = [];
  subjectNameFilters: object[] = [];
  originNameFilters: object[] = [];
  visible: boolean = false;
  executeBudgets: object[] = [];
  selectRecord: IBudget = {};

  @Watch('store.totalCount')
  onStoreChange() {
    if (this.store.totalCount >= this.preRecordsLength) {
      this.preRecordsLength = this.store.totalCount;
      this.catalogNameFilters = this.getColFilters('catalog_name');
      this.nameFilters = this.getColFilters('name');
      this.subjectNameFilters = this.getColFilters('subject_name');
      this.originNameFilters = this.getColFilters('origin_name', '资金统筹');
    }
  }

  getColFilters(dataIndex: string, defaultText: any = 'null', defaultValue: any = '') {
    return Object.keys(this.$utils.objectify(this.store.records, dataIndex)).map((key: string) => ({
      text: key && key !== 'null' ? key : defaultText,
      value: key && key !== 'null' ? key : defaultValue,
    }));
  }
  fetchRecords(...args: any) {
    this.$emit('change', ...args);
  }
  onShow(record: IBudget, index: number) {
    this.$emit('show', record, index);
  }
  onEdit(...args: any) {
    this.$emit('edit', ...args);
  }
  onDelete(...args: any) {
    this.$emit('delete', ...args);
  }
  getBalanceAmount(record: IBudget) {
    return this.$utils.toCurrency(
      Number(record.amount) -
        Number(record.processing_payment_amount) -
        Number(record.completed_payment_amount) -
        Number(record.locking_amount),
    );
  }
  openExectorSelector(record: IBudget) {
    this.$emit('assignExecutors', record);
  }
  openSubBudgetList(record: IBudget) {
    this.$emit('openSubBudgetList', record);
  }

  getLabel(item: any) {
    return `${item.name}(已报销${item.usedAmount}元):`;
  }

  showModal(record: IBudget) {
    if (record.execute_budgets && record.execute_budgets.length > 0) {
      this.selectRecord = record;
      this.executeBudgets = [];
      for (const { id, amount, teacher_id, used_amount } of record.execute_budgets) {
        const name = record.executors && record.executors.find(e => e.id === teacher_id)?.name;
        this.executeBudgets.push({
          id,
          amount,
          name,
          usedAmount: used_amount,
        });
      }
      this.visible = true;
    } else {
      this.$message.error('请先选择执行人');
    }
  }

  async update() {
    const params = this.executeBudgets.map((item: any) => ({
      id: item.id,
      amount: item.amount,
    }));
    const result = await this.store.update({
      id: this.selectRecord.id,
      execute_budgets_attributes: params,
    });
    this.$emit('change');
    this.visible = false;
    this.executeBudgets = [];
    this.$message.success('设置成功');
  }
}
</script>

<template lang="pug">
.info-budget
  AdminTable(
    :store="store"
    :hideOnSinglePage="true"
    :rowSelection="rowSelection"
    rowClassName="click-row"
    @rowClick="onShow"
    @change="fetchRecords")
    a-table-column(title="能否报销" dataIndex="can_pay" :width="150" v-if="visiblePay")
      template(slot-scope="can_pay")
        span.red(v-if="can_pay") {{ can_pay }}
        span.blue(v-else) 正常
    a-table-column(title="二级内容" dataIndex="catalog_name" :width="150" :filters="catalogNameFilters")
    a-table-column(title="三级内容" dataIndex="name" :width="130")
      template(slot-scope="name")
        a-tooltip(:title="name")
          .three-line(style="max-width: 130px")
            | {{ name }}
    a-table-column(title="科目" dataIndex="subject_name" :width="110" :filters="subjectNameFilters")
    a-table-column(title="来源" dataIndex="origin_name" :width="110" :filters="originNameFilters")
      template(slot-scope="origin_name")
        | {{ origin_name || '资金池统筹' }}
    a-table-column(title="金额" dataIndex="amount" :width="130" align="right")
      template(slot-scope="amount")
        span {{ amount | toCurrency }}
    a-table-column(title="金额详情" :width="180")
      template(slot-scope="record")
        .info 未锁定：{{ getBalanceAmount(record) }}
        .info 报销中：{{ record.processing_payment_amount | toCurrency }}
        .info 已报销：{{ record.completed_payment_amount | toCurrency }}
        .info 已锁定：{{ record.locking_amount | toCurrency }}
    a-table-column(title="实际使用率" :width="100" align="center")
      template(slot-scope="record")
        a-progress(
          type="dashboard"
          :width="64"
          :percent="$tools.getRate(record.completed_payment_amount, record.amount)"
          strokeColor="#75C940")
    a-table-column(title="预期使用率" :width="100" align="center")
      template(slot-scope="{ completed_payment_amount, checked_payment_amount, amount }")
        a-progress(
          type="dashboard"
          :width="64"
          :percent="$tools.getRate((+completed_payment_amount) + (+checked_payment_amount), amount)"
          strokeColor="#75C940")
    // a-table-column(title="数量" dataIndex="number" :width="60" align="right")
    // a-table-column(title="单价" dataIndex="unit_price" :width="100" align="right")
      template(slot-scope="unit_price")
        span {{ unit_price | toCurrency }}
    a-table-column(title="采购方式" dataIndex="purchase" :width="80")
      template(slot-scope="purchase")
        a-tooltip(:title="purchase")
          .text-ellipsis(style="max-width: 150px;")
            | {{ purchase }}
    a-table-column(title="支付方式" dataIndex="payment_way", :width="80")
    a-table-column(title="备注" dataIndex="remark" :width="80")
      template(slot-scope="remark")
        a-tooltip(:title="remark")
          .text-ellipsis(style="max-width: 150px;")
            | {{ remark }}
    a-table-column(title="分配执行人" dataIndex="executors" :width="100" align="center")
      template(slot-scope="executors")
        Popover(title="执行人" placement="bottomRight")
          template(#main)
            TaTag.teacher-tag(type="primary" v-for="teacher in executors" :key="teacher.id")
              | {{ teacher.name }}
          span.count-hover(@click.stop="")
            a-icon(type="eye")
            |  共 {{ executors.length }} 人
    //- 分配执行人
    a-table-column(title="操作" :width="70" align="center" v-if="assignable")
      template(slot-scope="record")
        .table-hover-col
          IconTooltip(icon="user-add" tips="分配执行人" @click="openExectorSelector(record)")
          IconTooltip(icon="dollar" tips="分配资金" @click="showModal(record)")
    a-table-column(title="子预算" :width="60" align="center" v-if='!isSubBudget' )
      template(slot-scope="record")
        .view(@click.stop='openSubBudgetList(record)') {{record.sub_budget_count}}
    //- 编辑模式
    a-table-column(:width="80" v-if="operable" align="center")
      template(slot-scope="record")
        .table-hover-col
          IconTooltip(icon="edit" tips="编辑" @click="onEdit(record)")
          PopoverConfirm(
            title="删除"
            content="您确认删除此预算吗？"
            :loading="store.loading"
            @confirm="onDelete(record)")
            IconTooltip(icon="delete" tips="删除")

  MainModal(v-model='visible' width="600px" title='资金分配')
    .form
      .tip
        | 温馨提示：如果不需要设置金额，请不要输入
      a-form-item(v-for="(item, index) in executeBudgets" :label="getLabel(item)" :required="true")
        a-input(v-model="item.amount" type="number" size="large" placeholder="请输入金额")
    template(#footer)
      .float-right
        a-button(type="danger" size="large" @click="visible = false")
          | 取消
        a-button(type="primary" size="large" @click="update")
          | 确认
</template>

<style lang="stylus" scoped>
.count-hover
  cursor pointer
  display inline-block
  height 32px
  padding 0 6px
  line-height 32px
  text-align center
  &:hover
    background rgba(237, 247, 255, 1)
    border-radius 3px
    cursor pointer
    color #3DA8F5
.teacher-tag
  margin-right 6px
  margin-bottom 6px

.red
  color red
.blue
  color #3b82f6

.form
  padding 20px
  .tip
    color red
.float-right
  float right
</style>
