<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { IProject, Project } from '@/models/finance/project';
import { IInstance } from '@/models/bpm/instance';
import InstanceDetailDialog from '../bpm/InstanceDetailDialog.vue';
import BaseStore from '../../store/BaseStore';
import projectStore from '@/store/modules/finance/project.store';
import ProjectAnasDialog from './ProjectAnasDialog.vue';

@Component({
  components: {
    InstanceDetailDialog,
    ProjectAnasDialog,
  },
})
export default class FinanceProjectTable extends Vue {
  @Prop({ type: Object, default: () => ({}) }) private store!: BaseStore;
  @Prop({ type: Boolean, default: false }) private show_sub_project!: boolean;

  visible: boolean = false;
  instance: IInstance = {};
  // teacher/own anas
  anasVisible: boolean = false;
  activeProject: IProject = {};

  get isTeacherOwn() {
    return this.store?.model?.namespace?.includes('teacher/own');
  }
  get isAdmin() {
    return this.$utils.hasPermission('finance', 'admin');
  }
  get userRole() {
    return this.isTeacherOwn ? 'teacher/own' : 'admin';
  }
  get stateMap() {
    return Project.stateMap;
  }
  get activityState() {
    return this.$store.state.financeTeacherActivity.record.state;
  }
  fetchRecords(...args: any) {
    this.$emit('change', ...args);
  }
  onShow(...args: any) {
    this.$emit('show', ...args);
  }
  onEdit(...args: any) {
    this.$emit('edit', ...args);
  }
  onDelete(...args: any) {
    this.$emit('delete', ...args);
  }
  getBalanceAmount(record: IProject) {
    return this.$utils.toCurrency(
      Number(record.amount) - Number(record.processing_voucher_amount) - Number(record.completed_voucher_amount),
    );
  }
  // teacher own
  isOwn(project: IProject) {
    return project.owner_id === this.$store.state.currentUser.id;
  }
  async createProjectInstance(project: IProject) {
    try {
      const { data } = await projectStore.createTeacherOwnProjectInstance(project.id!);
      this.showInstance(data);
    } catch (error) {
      this.$message.error('申请失败');
    }
  }
  showInstance(instance: IInstance) {
    this.instance = instance!;
    this.visible = true;
  }
  onInstanceChange() {
    this.fetchRecords(this.store.currentPage);
  }
  // 统计信息
  showAnasList(record: IProject) {
    this.activeProject = record;
    this.anasVisible = true;
  }
  typeFormat(str: string) {
    if (str == 'Finance::MainProject') {
      return '父资金卡';
    } else if (str == 'Finance::SubProject') {
      return '子资金卡';
    } else {
      return str;
    }
  }
}
</script>

<template lang="pug">
.project-table
  AdminTable(
    :store="store"
    :hideOnSinglePage="true"
    :bordered="true"
    rowClassName="click-row"
    @rowClick="onShow"
    @change="fetchRecords")
    a-table-column(title="一级项目" dataIndex="project_category_name" :width="140")
    a-table-column(title="基本信息" :width="260")
      template(slot-scope="record")
        .info 资金卡名：{{ record.name }}
        .info 资金卡号：{{ record.uid }}
        .info(v-if='record.parent_project_uid') 父资金卡号：{{ record.parent_project_uid }}
        .info 学校：{{ record.school_name }}
    a-table-column(title="总金额" dataIndex="amount" align="right" :width="140")
      template(slot-scope="amount")
        span {{ amount | toCurrency }}
    a-table-column(title="金额详情" :width="200")
      template(slot-scope="record")
        .info 可报销：{{ getBalanceAmount(record) }}
        .info 报销中：{{ record.processing_voucher_amount | toCurrency }}
        .info 已报销：{{ record.completed_voucher_amount | toCurrency }}
        .info(v-if='record.sub_project_amount > 0') 已分配：{{ record.sub_project_amount | toCurrency }}
    a-table-column(title="实际使用率" :width="80")
      template(slot-scope="record")
        a-progress(
          type="dashboard"
          :width="64"
          :percent="$tools.getRate(record.completed_voucher_amount, record.amount)"
          strokeColor="#75C940")
    a-table-column(title="预期使用率" :width="80")
      template(slot-scope="{ completed_voucher_amount, checked_voucher_amount, amount }")
        a-progress(
          type="dashboard"
          :width="64"
          :percent="$tools.getRate((+completed_voucher_amount) + (+checked_voucher_amount), amount)"
          strokeColor="#75C940")
    a-table-column(title="人员信息" :width="140")
      template(slot-scope="record")
        .info 负责人：{{ record.owner_name }}
        .info(v-for="role in record.project_roles" :key="role.id")
          | {{ role.approval_role_name }}：{{ role.teacher_name }}
    a-table-column(title="时间" :width="180")
      template(slot-scope="record")
        .info 开始日期：{{ record.start_at }}
        .info 结束日期：{{ record.end_at }}
    a-table-column(title="类型" v-if="isTeacherOwn" :width="90")
      template(slot-scope="record")
        .info(v-if="isOwn(record)") 我负责的
        .info(v-else) 我审批的
    a-table-column(title="创建时间" :width="110" dataIndex="created_at")
      template(slot-scope="created_at")
        | {{ created_at | format }}
    a-table-column(title="类别" :width="80" dataIndex="type" align='center' v-if='show_sub_project')
      template(slot-scope="type")
        | {{ typeFormat(type) }}
    a-table-column(title="子项目数", :width="80" ,dataIndex="sub_project_count",align='center', v-if='show_sub_project')
      template(slot-scope="sub_project_count")
        | {{ sub_project_count }}
    a-table-column(title="状态" width="100px" align="center" fixed="right")
      template(slot-scope="record")
        TaTag(:type="stateMap[record.state].type" size="small")
          | {{ stateMap[record.state].label }}
        .state-actions(v-if="isTeacherOwn" @click.stop="")
          PopoverConfirm(
            v-if="isOwn(record) && record.state === 'pending'"
            :disabled="activityState === 'doing' ? false:true"
            title="申请"
            content="您确认申请使用此资金卡吗？"
            type="primary"
            placement="bottomRight"
            :loading="store.loading"
            @confirm="createProjectInstance(record)")
            a-button(
              :disabled="activityState === 'doing' ? false:true"
              :class="activityState === 'doing' ? 'btn-warning':'btn-info' "
              size="small")
              | 发起申请
          a-button.btn-primary(
            v-else-if="record.instance"
            size="small"
            @click="showInstance(record.instance)")
            | 查看审批
        .state-actions(v-if="isTeacherOwn || isAdmin" @click.stop="")
          a-button.btn-info(
            size="small"
            @click="showAnasList(record)")
            | 统计信息

  InstanceDetailDialog(
    v-model="visible"
    title="资金卡申请"
    :instanceId="instance.id"
    @instanceChange="onInstanceChange")

  ProjectAnasDialog(
    v-model="anasVisible"
    :project="activeProject"
    :role="userRole")
</template>

<style lang="stylus" scoped>
.state-actions
  margin-top 10px
</style>
