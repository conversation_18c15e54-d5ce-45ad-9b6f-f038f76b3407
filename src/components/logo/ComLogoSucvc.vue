<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class ComLogoSucvc extends Vue {}
</script>

<template lang="pug">
.com-logo-stiei
  img.logo(src="@/assets/images/sucvc_logo.svg" height="30")
  .text
    .zh 上海城建职业学校
    .en Shanghai Urban Construction Vocational College
</template>

<style lang="stylus" scoped>
.com-logo-stiei
  display flex
  .text
    margin 5px 0 0 10px
    .zh
      font-size 12px
      line-height 14px
      letter-spacing 1px
      weight 500
    .en
      width 300px
      font-size 12px
      display inline-block
      -webkit-text-size-adjust none
      transform translateX(-75px) translateY(-5px) scale(0.5, 0.5)
</style>
