<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class ComFinanceBudgetThird extends Vue {
  @Prop({ type: String, default: '' }) title!: string;

  columns = [
    {
      title: '二级内容',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '三级内容',
      dataIndex: 'age',
      key: 'age',
    },
    {
      title: '科目',
      dataIndex: 'address',
      key: 'address',
    },
    {
      title: '来源',
      key: 'tags',
      dataIndex: 'tags',
    },
    {
      title: '金额',
      key: 'action',
      dataIndex: 'action',
    },
    {
      title: '金额详情',
      key: 'amount',
      dataIndex: 'amount',
    },
    {
      title: '实际使用率',
      key: 'amount',
      dataIndex: 'amount',
    },
  ];

  data = [
    {
      key: '1',
      name: '其他教学、行政杂项经费',
      age: '会议费',
      address: '差旅费',
      tags: '资金池统筹',
      action: '5,500.00',
    },
    {
      key: '1',
      name: '其他教学、行政杂项经费',
      age: '会议费',
      address: '差旅费',
      tags: '资金池统筹',
      action: '5,500.00',
    },
    {
      key: '1',
      name: '其他教学、行政杂项经费',
      age: '会议费',
      address: '差旅费',
      tags: '资金池统筹',
      action: '5,500.00',
    },
  ];

  visible = false;

  handleOk() {
    this.visible = false;
    this.$emit('ok');
  }

  show() {
    this.visible = true;
  }
}
</script>

<template lang="pug">
.component-container
  a-modal(v-model='visible', @ok='handleOk', centered, width='1000px')
    .header
    .title 选择三级内容
    a-table(:columns='columns', :data-source='data')
      a(slot='name', slot-scope='text') {{ text }}
      span(slot='customTitle')
        a-icon(type='smile-o') Name
      span(slot='tags', slot-scope='tags')
        a-tag(
          v-for='tag in tags',
          :key='tag',
          :color='tag === "loser" ? "volcano" : tag.length > 5 ? "geekblue" : "green"'
        )
          | {{ tag.toUpperCase() }}
      span(slot='action', slot-scope='text, record')
        a Invite 一 {{ record.name }}
</template>

<style lang="stylus" scoped>
.component-container
  display block
  .header
    display flex
    border-style solid
    .title
      width 50px
      border-style solid
      font-weight bold
</style>
