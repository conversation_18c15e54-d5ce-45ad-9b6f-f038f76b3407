<script lang="ts">
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import { IActivityMeeting } from '@/models/conference/activity_meeting';
import { TeacherActivityMeeting } from '@/models/conference/teacher_activity_meeting';
import { IMeetingRegister, MeetingRegister } from '@/models/conference/meeting_register';
import MeetingRegisterStore from '@/store/modules/conference/meeting_register.store';
import UserSelectorDialog from '@/components/hr/UserSelectorDialog.vue';
import TeacherActivityMeetingStore from '@/store/modules/conference/teacher_activity_meeting.store';
import { IApplicationForm, ApplicationForm } from '@/models/conference/application_form';
import ApplicationFormStore from '@/store/modules/conference/application_form.store';
import InstanceDetailDialog from '@/components/bpm/InstanceDetailDialog.vue';
import { MeetingTeacherRegisters } from '@/models/meeting/teacher/registers';
import { meetingTeacherRegistersStore } from '../../store/modules/meeting/teacher/registers.store';

@Component({
  components: {
    UserSelectorDialog,
    InstanceDetailDialog,
  },
})
export default class MemberStat extends Vue {
  @Prop({ type: Object, default: {}, required: true }) private meeting!: IActivityMeeting;

  stat: object = {};
  activeState: string = 'member';
  registers: IMeetingRegister[] = [];
  leavingApplicationForm: IApplicationForm[] = [];
  processingApplicationForm: IApplicationForm[] = [];
  visible: boolean = false;
  reviewVisible: boolean = false;
  memberIds: string[] = [];
  selectedInstanceId: number = -1;

  get steps() {
    const data = [
      { title: '参会人员', key: 'member', count: (this.stat as any).member_count },
      { title: '已请假', key: 'leaving', count: (this.stat as any).leaving_count },
      { title: '待审核', key: 'unreviewed', count: (this.stat as any).unreviewed_count },
    ];
    if (this.hasAuth) {
      return data;
    } else {
      return data.splice(0, 2);
    }
  }

  get hasAuth() {
    return this.meeting.is_moderator || this.meeting.reserver_id === this.$store.state.currentUser.id;
  }

  get store() {
    return meetingTeacherRegistersStore;
  }

  get meetingStore() {
    return TeacherActivityMeetingStore;
  }

  get applicationFormStore() {
    return ApplicationFormStore;
  }

  get registerIndexConfig() {
    return {
      recordName: '参会人员',
      store: this.store,
      mode: 'table',
    };
  }

  mounted() {
    this.fetchMeetingStat();
    this.fetchRegister();
  }

  onStepChange(key: string) {
    this.activeState = key;
    switch (key) {
      case 'member':
        this.fetchRegister();
        break;
      case 'leaving':
        this.fetchLeaving();
        break;
      case 'unreviewed':
        this.fetchProcessing();
        break;
    }
  }

  async fetchMeetingStat() {
    const { data } = await new TeacherActivityMeeting().stat(this.$route.params.id);
    this.stat = data;
  }

  async fetchRegister() {
    // const params = {
    //   parentId: this.meeting.id,
    //   page: 1,
    //   per_page: 999999,
    //   q: {
    //     not_special_role: true,
    //     state_in: ['unreviewed', 'reviewed'],
    //   },
    // };
    // const { data } = await this.store.fetchByParent(params);
    // this.registers = data.registers;
    this.store.init({
      parents: [{ type: 'activity_meetings', id: this.meeting.id }],
      params: {
        q: {
          not_special_role: true,
          state_in: ['unreviewed', 'reviewed'],
        },
      },
    });
  }

  startEdit() {
    this.visible = true;
  }

  onUpdate(memberIds: string[], members: IObject[], ransack: Object) {
    this.meetingStore
      .update({ id: this.meeting.id, user_ids: memberIds })
      .then(() => {
        this.$message.success('编辑成功');
        this.fetchRegister();
      })
      .catch(error => this.$message.error(error));
  }

  async fetchLeaving() {
    const params = {
      parentId: this.meeting.id,
      page: 1,
      per_page: 999999,
      q: {
        backup_reason_eq: '请假',
        state_eq: 'completed',
      },
    };
    await this.applicationFormStore.fetchByParent(params);
    this.leavingApplicationForm = this.applicationFormStore.records;
  }

  async fetchProcessing() {
    const params = {
      parentId: this.meeting.id,
      page: 1,
      per_page: 999999,
      q: {
        state_eq: 'processing',
      },
    };
    await this.applicationFormStore.fetchByParent(params);
    this.processingApplicationForm = this.applicationFormStore.records;
  }

  getComment(applicationForm: IApplicationForm) {
    if ((applicationForm.backup_user_names || []).length === 0) {
      return '-';
    } else {
      if (applicationForm.backup_reason === '请假') {
        return `替换人员：${(applicationForm.backup_user_names || []).join('、')}`;
      } else {
        return `邀请人员：${(applicationForm.backup_user_names || []).join('、')}`;
      }
    }
  }

  onReview(applicationForm: IApplicationForm) {
    this.selectedInstanceId = applicationForm.instance_id as number;
    this.reviewVisible = true;
  }
}
</script>

<template lang="pug">
  .member-stat
    .members
      StepToolbar.member-tabs(
        :value="activeState"
        :steps="steps"
        mode="tabs"
        @change="onStepChange"
      )
        //- template(#right v-if="activeState === 'member'")
        //-   .member-right-actions(@click="startEdit")
        //-     a-icon.icon(type="edit")
        //-     span 编辑人员
      .member-container(v-if="activeState === 'member'")
        //- a-table(:dataSource="registers" rowKey="id" v-loading="store.loading")
        TaIndexView(:config='registerIndexConfig')
          template(#table)
            a-table-column(key="code" :width="120")
              span(slot="title" style="color: #808080") 职工号
              template(slot-scope="scope")
                span.gray {{ scope.teacher.code }}
            a-table-column.gary(key="name")
              span(slot="title" style="color: #808080") 姓名
              template(slot-scope="scope")
                span {{ scope.teacher.name }}
            a-table-column.gary(
              key="department"
              :onFilter="(value, record) => record.teacher.department_path.join(' / ').includes(value)"
            )
              span(slot="title" style="color: #808080") 所属部门
              template(slot-scope="scope")
                span.gray {{ (scope.teacher.department_path || []).join(' / ') }}
            a-table-column.gary(key="remark")
              span(slot="title" style="color: #808080") 备注
              template(slot-scope="scope")
                span.gray {{ scope.meta.comment }}
      //- UserSelectorDialog(
      //-   title="编辑人员"
      //-   v-model="visible"
      //-   :multiple="true"
      //-   :userIds='meeting.user_ids'
      //-   @change="onUpdate"
      //- )
      .leaving-container(v-if="activeState === 'leaving'")
        a-table.gray(:dataSource="leavingApplicationForm" rowKey="id")
          a-table-column(key="code" :width="120" title='职工号')
            template(slot-scope="scope")
              span.gray {{ scope.register.teacher.code }}
          a-table-column(key="name" title='姓名')
            template(slot-scope="scope")
              span.gray {{ scope.register.teacher.name }}
          a-table-column(key="department" title='所属部门')
            template(slot-scope="scope")
              span.gray {{ (scope.register.teacher.department_path || []).join(' / ') }}
          a-table-column(key="backup_people" title='替换人员')
            template(slot-scope="scope")
              span.gray {{ scope.backup_user_names.length === 0 ? '-' : scope.backup_user_names.join('、') }}
      .unreviewed-container(v-if="activeState === 'unreviewed'")
        a-table.gray(:dataSource="processingApplicationForm" rowKey="id")
          a-table-column(key="name" title='申请人')
            template(slot-scope="scope")
              span.gray {{ scope.register.teacher.name }}
          a-table-column(key="code" :width="120" title='职工号')
            template(slot-scope="scope")
              span.gray {{ scope.register.teacher.code }}
          a-table-column(key="backup_reason" title='请求')
            template(slot-scope="scope")
              span.gray {{ scope.backup_reason }}
          a-table-column(key="comment" title='备注')
            template(slot-scope="scope")
              span.gray {{ getComment(scope) }}
          a-table-column(key="edit")
            template(slot-scope="scope")
              a-icon(type="edit" @click="onReview(scope)")
      InstanceDetailDialog(
        :instanceId="selectedInstanceId"
        title="审核请求"
        v-model="reviewVisible"
      )
</template>

<style lang="stylus" scoped>
.member-stat
  width 100%
  height 100%
  .members
    margin 10px 40px
    background-color white
    .member-tabs
      padding 0 20px
    .member-right-actions
      color #3DA8F5
      .icon
        margin-right 4px
    .member-container
      margin 10px 20px
      .gray
        color #808080
    .leaving-container
      margin 10px 20px
      .gray
        color #808080
    .unreviewed-container
      margin 10px 20px
      .gray
        color #808080
</style>
