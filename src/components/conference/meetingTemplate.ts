import { IFormTemplateItem } from '@/interfaces/formTemplate.interface';

export const meetingTemplate: IFormTemplateItem[] = [
  {
    key: 'title',
    name: '会议标题',
    layout: {
      component: 'input',
      required: true,
      type: 'string',
    },
    model: { attr_type: 'string' },
  },
  {
    key: 'date',
    name: '会议日期',
    layout: {
      component: 'date',
      required: true,
      type: 'string',
    },
    model: { attr_type: 'string' },
  },
  {
    key: 'begin_time',
    name: '会议开始时间',
    layout: {
      component: 'select',
      required: true,
      type: 'string',
      options: [],
    },
    model: { attr_type: 'string' },
  },
  {
    key: 'end_time',
    name: '会议结束时间',
    layout: {
      component: 'select',
      required: true,
      type: 'string',
      options: [],
    },
    model: { attr_type: 'string' },
  },
  {
    key: 'meeting_room_id',
    name: '会议地点',
    layout: {
      component: 'select',
      required: false,
      type: 'string',
      options: [],
    },
    model: { attr_type: 'number' },
  },
  {
    key: 'custom_meeting_room_name',
    name: '自定义会议地点',
    layout: {
      component: 'input',
      required: false,
      type: 'string',
      options: [],
    },
    model: { attr_type: 'string' },
  },
  {
    key: 'moderator_ids',
    name: '主持人',
    layout: {
      component: 'teacher',
      required: true,
      type: 'array',
      multiple: true,
    },
    model: { attr_type: 'array' },
  },
  // {
  //   key: 'organizer_ids',
  //   name: '发起人',
  //   layout: {
  //     component: 'teacher',
  //     required: false,
  //     type: 'array',
  //     multiple: true,
  //   },
  //   model: { attr_type: 'array' },
  // },
  {
    key: 'user_ids',
    name: '参会人',
    layout: {
      component: 'teacher',
      required: true,
      type: 'array',
      multiple: true,
    },
    model: { attr_type: 'array' },
  },
  {
    key: 'user_desc',
    name: '参会人描述',
    layout: {
      component: 'input',
      required: true,
      type: 'string',
      multiple: true,
    },
    model: { attr_type: 'string' },
  },
  {
    key: 'meeting_department_ids',
    name: '组织部门',
    layout: {
      component: 'department_new',
      required: true,
      type: 'array',
      multiple: true,
    },
    model: { attr_type: 'array' },
  },
  {
    key: 'desc',
    name: '会议主题',
    layout: {
      component: 'textarea',
      required: false,
      placeholder: '请输入',
      type: 'string',
    },
    model: { attr_type: 'string' },
  },
  {
    key: 'files',
    name: '会前资料',
    layout: {
      component: 'file',
      required: false,
      type: 'array',
    },
    model: { attr_type: 'array' },
  },
];
