<script lang="ts">
import { IActivityMeeting } from '@/models/conference/activity_meeting';
import { cloneDeep } from 'lodash-es';
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import TeacherRoomStore from '@/store/modules/conference/teacher_room.store';
import { meetingTemplate } from './meetingTemplate';
import { IFormTemplateItem } from '@/interfaces/formTemplate.interface';
import TemplateFormViewer from '../form/TemplateFormViewer.vue';
import TemplateForm from '@/components/form/TemplateForm.vue';

@Component({
  components: {
    TemplateForm,
    TemplateFormViewer,
  },
})
export default class ActivityMeetingForm extends Vue {
  @Prop({ type: Object, default: () => ({}) }) meeting!: IActivityMeeting;
  @Prop({ type: Object, default: () => ({}) }) timeRangeParams!: { start: string; end: string };
  @Prop({ type: Boolean, default: true }) editing!: boolean;

  formData: Partial<IActivityMeeting> = {};
  template: IFormTemplateItem[] = meetingTemplate;

  get roomStore() {
    return TeacherRoomStore;
  }

  get isAdmin() {
    return this.$utils.hasPermission('conference', 'admin');
  }

  @Watch('timeRangeParams', { deep: true, immediate: true })
  onTimeRangeParamsChange() {
    this.updateTemplate();
  }

  @Watch('meeting')
  initFormData() {
    this.formData = cloneDeep(this.meeting);
    if ((this.formData.meeting_department_ids || []).length < 1) {
      const { department_ids } = this.$store.state.currentUser;
      this.formData.meeting_department_ids = department_ids;
    }

    if (this.formData.begin_time) {
      this.formData.begin_time = this.$moment(this.formData.begin_time).format('HH:mm');
    }
    if (this.formData.end_time) {
      this.formData.end_time = this.$moment(this.formData.end_time).format('HH:mm');
    }
    this.formData.files = (this.formData.meta || {}).files;
    this.formData.meeting_type = (this.formData.meta || {}).meeting_type;
  }

  mounted() {
    this.updateTemplate();
  }

  async updateTemplate() {
    this.updateTemplateTimeRange();
    this.updateTemplateRoom();
    // this.removeUsersIfNecessary();
  }

  updateTemplateTimeRange() {
    const startTimeInt = parseInt((this.$route.query.startTimeInt as string | null) || '800');
    const endTimeInt = parseInt((this.$route.query.endTimeInt as string | null) || '2200');
    const iMax = (endTimeInt - startTimeInt) / 50;
    var result: { value?: string; label: string }[] = [];
    for (let i = 0; i <= iMax; i++) {
      // 10进制数字
      const num = startTimeInt + 50 * i;
      // 10进制半点减去 20
      const offset = num % 100 === 50 ? -20 : 0;
      result.push({ label: this.timeIntToTimeStr(num + offset) });
    }
    this.template.splice(2, 1, {
      key: 'begin_time',
      name: '会议开始时间',
      layout: {
        component: 'select',
        required: true,
        type: 'string',
        options: result,
      },
      model: { attr_type: 'string' },
    });
    this.template.splice(3, 1, {
      key: 'end_time',
      name: '会议结束时间',
      layout: {
        component: 'select',
        required: true,
        type: 'string',
        options: result,
      },
      model: { attr_type: 'string' },
    });
  }

  // removeUsersIfNecessary() {
  //   if (this.meeting.is_joined && !this.isAdmin) {
  //     this.template.splice(8, 1);
  //   }
  // }

  timeIntToTimeStr(timeInt: number) {
    return `${Math.floor(timeInt / 100)}:${this.minuteStr(timeInt % 100)}`;
  }

  minuteStr(num: number) {
    if ((num + '').length < 2) {
      return '0' + num;
    } else {
      return num + '';
    }
  }

  async updateTemplateRoom() {
    await this.roomStore.fetch({
      page: 1,
      per_page: 999999,
    });
    const options: { label: string; value: number | undefined | null }[] = this.roomStore.records.map(room => {
      return { label: room.name as string, value: room.id };
    });

    options.push({ label: '其他', value: null });

    this.template.splice(4, 1, {
      key: 'meeting_room_id',
      name: '会议室',
      layout: {
        component: 'select',
        placeholder: '请选择会议室',
        required: false,
        type: 'number',
        options,
      },
      model: { attr_type: 'number' },
    });
  }

  submit(initForm: Partial<IActivityMeeting> = {}, callbacks?: { success?: (result: IObject) => void }) {
    (this.$refs.form as TemplateForm).submit({
      success: (payload: IObject) => {
        let result: any = {};
        result = Object.assign(cloneDeep(this.formData), payload);
        result = Object.assign(result, initForm);
        result.id = this.formData.id;
        result.begin_time = this.$moment(`${this.$moment(result.date).format('YYYY-MM-DD')} ${result.begin_time}`);
        result.end_time = this.$moment(`${this.$moment(result.date).format('YYYY-MM-DD')} ${result.end_time}`);
        ['files', 'meeting_type'].forEach(column => {
          result.meta = Object.assign(result.meta || {}, { [column]: (result as any)[column] });
        });
        if (callbacks?.success) {
          callbacks.success(result);
        } else {
          this.$emit('submit', result);
        }
      },
      fail: () => {
        this.$message.error('请完善表单信息');
      },
    });
  }
}
</script>

<template lang="pug">
.activity-meeting-form
  TemplateForm.form(
    v-if='editing'
    ref="form"
    :template="template"
    :formData="formData"
  )
  TemplateFormViewer(
    v-else
    :template='template',
    :formData='formData',
    :border='false'
  )
</template>

<style lang="stylus" scoped></style>
