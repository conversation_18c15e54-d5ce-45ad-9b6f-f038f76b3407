<template lang="pug">
.activity-display-container
  .clock
    .time
      | {{ time | format('HH:mm') }}
    template(v-if="isScannerActived")
      .state.text-success(v-if="status === 'active'")
        | ∙ 进行中
      .state(v-else-if="status === 'inactive'")
        | ∙ 未开始
      .state.text(v-else-if="status === 'finish'")
        | ∙ 已结束
      .state.text-primary(v-else)
        | ∙ 即将开始
    template(v-else)
      .state.text-danger
        | ∙ 点击激活扫码枪
  .main
    .meeting
      .title {{ activity.title || '无安排' }}
      .info(v-if="activity.id")
        span 时间：{{ activity.begin_time | format('MM月DD日 HH:mm') }} - {{ activity.end_time | format('MM月DD日 HH:mm') }}
        span 地点：{{ activity.location }}
    .attendances
      transition-group.stage(tag="div" name="notification" v-if="activity.id")
        .notification(
          v-for="(item, index) in attendances"
          :key="item.id")
          .user
            span.name {{ item.user_name }}
            span {{ item.user_department_name || item.user_code }}
          .info
            span {{ item.signup_at | format('HH:mm') }}&nbsp;&nbsp;
            template(v-if="item.state === '已签到'")
              span 签到成功
              a-icon.icon.text-success(type="check-circle" theme="filled")
            template(v-else)
              a-icon.icon.text-danger(type="close-circle" theme="filled")
    .extra-info
      .statistic
        span 已到
        strong {{ activity.present_count || '0' }}
        span 人
        span.split-line |
        span 应到
        strong {{ activity.supposed_count || '0' }}
        span 人
      .footer(v-if="nextActivity.id")
        span
          | 下一场：
          strong {{ nextActivity.title || '无' }}
        span
          | {{ nextActivity.begin_time | format('MM月DD日 HH:mm') }}
          |  - {{ nextActivity.end_time | format('MM月DD日 HH:mm') }}

  //- 扫码枪
  input.scanner-input(
    id="scannerInput"
    v-model="scanIdCode"
    type="tel"
    autofocus
    autocomplete="off"
    @focus="onScannerFocus"
    @blur="isScannerActived = false"
    @keyup.enter.stop="handleScanCode")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { IActivity } from '@/models/meeting/activity';
import { IRegister } from '../../models/meeting/teacher/registers';
import { MeetingTeacherRegisters } from '@/models/meeting/teacher/registers';
import ActiveModel from '@/lib/ActiveModel';

type TypeStuaus = 'active' | 'inactive' | 'soon' | 'finish';

@Component
export default class ActivityMeetingScan extends Vue {
  time: Date = new Date();
  status: TypeStuaus = 'inactive';
  attendances: IRegister[] = [];
  clockTimer: any = null;
  activityTimer: any = null;

  // scan code
  isScannerActived: boolean = false;
  scanIdCode: string = '';
  notificationTimer: any = null;
  clearNotificationTimer: any = null;

  @Prop({ type: Object, default: () => ({}) }) activity!: IActivity;
  @Prop({ type: Object, default: () => ({}) }) nextActivity!: IActivity;
  @Prop({ type: Object, default: () => ({}) }) store!: IObject;
  @Prop({ type: Function, default: MeetingTeacherRegisters }) registerApi!: any;

  mounted() {
    this.initClock();
    this.initActivityTimer();
    this.$nextTick(this.resetScanner);
  }
  beforeDestroy() {
    clearInterval(this.clockTimer);
    clearInterval(this.notificationTimer);
    clearInterval(this.activityTimer);
  }
  @Watch('activity')
  onActivityChange() {
    this.resetScanner();
  }

  initActivityTimer() {
    this.activityTimer = setInterval(() => {
      if (this.activity.id) {
        this.store.find(this.activity.id);
      }
    }, 5000);
  }

  initClock() {
    this.time = new Date();
    this.clockTimer = setInterval(() => {
      this.time = new Date();
      // 处理状态
      if (this.activity.id) {
        const now = this.time.valueOf();
        const beginTime = new Date(this.activity.begin_time!).valueOf();
        const endTime = new Date(this.activity.end_time!).valueOf();
        if (beginTime - now > 0 && beginTime - now < 3600000) {
          this.status = 'soon';
        } else if (now < endTime && now > beginTime) {
          this.status = 'active';
        } else if (now > endTime) {
          this.status = 'finish';
        } else {
          this.status = 'inactive';
        }
      } else {
        this.status = 'inactive';
      }
      this.$emit('nextTick', this.time);
    }, 1000);
  }
  // 扫描签到二维码
  async handleScanCode(e: Event) {
    if (this.status === 'finish') {
      this.$message.info('会议已结束');
      return;
    }
    // if (this.status === 'inactive') {
    //   this.$message.info('请于会议开始后进行扫码签到');
    //   return;
    // }
    const target = e.target as HTMLInputElement;
    const code = `${target.value}`.trim();
    if (code) {
      // try {
      const { data } = await new this.registerApi({
        parents: [{ type: 'activity_meetings', id: this.activity.id! }],
      })
        .sendCollectionAction('sign_in', { params: { register: { qrcode: code } } })
        .finally(this.resetScanner);
      this.store.find(this.activity.id);
      this.addAttendance(data);
      this.$emit('change');
      // } catch (error) {
      //   this.addAttendance({
      //     id: Math.random(),
      //     signup_at: new Date().toISOString(),
      //     user_name: '请检查您是否已成功参加此会议',
      //     // state: undefined,
      //   });
      // }
    }
  }
  addAttendance(attendance: IRegister) {
    if (this.attendances.length === 4) {
      this.attendances.shift();
    }
    this.attendances.push(attendance);
    this.autoClearNotification();
  }
  autoClearNotification() {
    clearInterval(this.clearNotificationTimer);
    this.clearNotificationTimer = setInterval(() => {
      if (this.attendances.length > 0) {
        this.attendances.shift();
      }
    }, 6000);
  }
  resetScanner() {
    this.scanIdCode = '';
    this.$nextTick(() => {
      const input = document.getElementById('scannerInput') as HTMLInputElement;
      input.focus();
    });
  }
  onScannerFocus() {
    this.scanIdCode = '';
    this.isScannerActived = true;
  }
}
</script>

<style lang="stylus" scoped>
.activity-display-container
  position relative
  z-index 100
  overflow auto
  width 100%
  height 100%
  background center / cover url('~@/assets/images/meeting/meeting_home_bg.svg') no-repeat
  color #fff
  text-align center
  .clock
    position absolute
    top 40px
    right 40px
    z-index 1000
    width 154px
    .time
      margin-bottom 1px
      height 86px
      border-radius 12px 12px 4px 4px
      background rgba(32, 44, 54, 1)
      font-size 40px
      line-height 86px
    .state
      padding 14px 0
      border-radius 4px 4px 12px 12px
      background rgba(32, 44, 54, 1)
      text-align center
      font-weight 500
      font-size 18px
      line-height 24px
  .main
    position absolute
    top 270px
    bottom 0
    left 0
    width 100%
    .meeting
      .title
        margin auto
        width 1120px
        color rgba(255, 255, 255, 1)
        font-weight 300
        font-size 80px
        line-height 120px
      .info
        margin-top 40px
        height 24px
        color rgba(255, 255, 255, 0.8)
        font-weight 400
        font-size 24px
        line-height 24px
        span:first-child
          margin-right 60px
    .attendances
      margin 40px auto
      width fit-content
      height 254px
      .stage
        position relative
        width 100%
        height 100%
        perspective 2000px
        transform-style preserve-3d
        .notification
          display flex
          justify-content space-between
          align-items center
          margin-bottom 10px
          padding 0 24px
          width 500px
          height 56px
          border-radius 10px
          background rgba(255, 255, 255, 1)
          box-shadow 0px 16px 30px 0px rgba(45, 129, 255, 0.5)
          color rgba(166, 166, 166, 1)
          font-weight 500
          font-size 16px
          line-height 24px
          transition all 0.8s
          &:last-child
            margin-bottom 0
          .user
            .name
              margin-right 7px
              color rgba(56, 56, 56, 1)
          .icon
            margin-left 12px
            vertical-align bottom
            font-size 24px
        .notification-enter
          opacity 0
          transform translateY(40px)
        .notification-leave-to
          opacity 0
          transform translateY(-40px)
        .notification-leave-active
          position absolute
          transition all 0.5s
    .extra-info
      position absolute
      bottom 0
      left 50%
      transform translateX(-50%)
      margin 0 auto
      width 1120px
      .statistic
        margin 40px auto
        width 100%
        color rgba(255, 255, 255, 0.8)
        text-align center
        font-size 24px
        line-height 24px
        strong
          margin 0 14px
          color #ffffff
          font-size 40px
          line-height 56px
        .split-line
          margin 0 40px
      .footer
        display flex
        justify-content space-between
        align-items center
        padding 0 28px
        height 70px
        border-radius 20px 20px 0px 0px
        background linear-gradient(360deg, rgba(57, 126, 222, 0.2) 0%, rgba(57, 126, 222, 0.6) 100%)
        font-size 18px
  .scanner-input
    position absolute
    top 0
    left 0
    z-index 1000
    margin 0
    padding 0
    width 100%
    height 100%
    outline none
    border none
    line-height 0
    opacity 0
</style>
