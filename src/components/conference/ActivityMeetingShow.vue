<script lang="ts">
import { ActiveStore } from '@/lib/ActiveStore';
import { IActivityMeeting } from '@/models/conference/activity_meeting';
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import ActivityMeetingForm from './ActivityMeetingForm.vue';
import ComApplicationFormCreator from './ComApplicationFormCreator.vue';
import InstanceDetailDialog from '../bpm/InstanceDetailDialog.vue';

@Component({
  components: {
    ActivityMeetingForm,
    ComApplicationFormCreator,
    InstanceDetailDialog,
  },
})
export default class ActivityMeetingShow extends Vue {
  @Prop({ type: Object, required: true }) store!: ActiveStore<IActivityMeeting>;
  @Prop({ type: Number, required: true }) meetingId!: number;

  formData: IActivityMeeting = {};
  editing: boolean = false;
  meeting: IActivityMeeting = {};

  greenBackground: string = '#F0F9F2';
  greenFont: string = '#6DC37D';
  blueBackground: string = '#EDF7FF';
  blueFont: string = '#3DA8F5';
  orangeBackground: string = '#FFF7E6';
  orangeFont: string = '#FA8C15';
  grayBackground: string = '#F5F5F5';
  grayFont: string = '#A6A6A6';

  get haveAdminAuth() {
    return this.$utils.hasPermission('conference', 'admin');
  }

  get canEdit() {
    if (this.haveAdminAuth) {
      return true;
    }
    const { currentUser } = this.$store.state;
    const isReserver = this.meeting.reserver_id === currentUser.id && this.meeting.reserver_type === currentUser.type;
    const isOrganizer = this.meeting.organizer_ids?.includes(currentUser.id);
    if ((isReserver || isOrganizer) && (this.meeting.state === 'draft' || this.meeting.state === 'pending')) {
      return true;
    }
    return this.meeting.is_moderator;
  }

  async fetchRecord() {
    const { data } = await this.store.find(this.meetingId);
    this.meeting = data;
    this.$emit('afterShow', data);
  }

  @Watch('meetingId', { immediate: true })
  onMeetingIdChanged() {
    this.fetchRecord();
    if (this.meeting.is_over) {
      this.greenBackground = this.grayBackground;
      this.greenFont = this.grayFont;
      this.blueBackground = this.grayBackground;
      this.blueFont = this.grayFont;
      this.orangeBackground = this.grayBackground;
      this.orangeFont = this.grayFont;
    }
  }

  onUpdate(callbacks?: { success?: (result: IObject) => void }) {
    if (!this.canEdit) {
      this.$message.error('请确认是否有该会议编辑权限');
    }
    (this.$refs.formComponent as ActivityMeetingForm).submit({}, callbacks);
  }

  onSubmit(formData: IActivityMeeting) {
    return this.store
      .update(formData)
      .then(() => {
        this.$message.success('修改成功');
        this.$emit('refresh');
        this.fetchRecord();
        this.editing = false;
      })
      .catch(error => {
        this.$message.error(error);
      });
  }

  startEditing() {
    this.editing = true;
  }

  cancel() {
    this.editing = false;
    this.formData = {};
  }

  handIn() {
    this.store
      .update({ id: this.meeting.id, state: 'pending' })
      .then(() => {
        this.$message.success('提交成功');
        this.fetchRecord();
        this.$emit('refresh');
      })
      .catch(error => this.$message.error(error));
  }

  cancelMeeting() {
    return (
      this.store
        .delete(this.meeting.id)
        // .update({ id: this.meeting.id, state: 'rejected' })
        .then(() => {
          this.$message.success('撤销成功');
          // this.fetchRecord();
          this.$router.back();
          this.$emit('refresh');
        })
        .catch(error => this.$message.error(error))
    );
  }

  getBackground(flag: string) {
    switch (flag) {
      case '审核中':
        return this.orangeBackground;
      case '通过':
        return this.greenBackground;
      case '未通过':
        return this.grayBackground;
    }
    return this.grayBackground;
  }

  getFont(flag: string) {
    switch (flag) {
      case '审核中':
        return this.orangeFont;
      case '通过':
        return this.greenFont;
      case '未通过':
        return this.grayFont;
    }
    return this.grayBackground;
  }

  invitingStyle() {
    return {
      backgroundColor: this.getBackground(this.meeting.is_inviting as string),
      border: `${this.getFont(this.meeting.is_inviting as string)} 1px solid`,
    };
  }

  leavingStyle() {
    return {
      backgroundColor: this.getBackground(this.meeting.is_leaving as string),
      border: `${this.getFont(this.meeting.is_leaving as string)} 1px solid`,
    };
  }

  invitedOrReplacementStyle() {
    return {
      backgroundColor: this.orangeBackground,
      border: `${this.orangeFont} 1px solid`,
    };
  }

  visibleInstance = false;
  onShowInstance() {
    this.onUpdate({
      success: (result: IObject) => {
        this.onSubmit(result).then(() => {
          this.visibleInstance = true;
        });
      },
    });
  }
}
</script>

<template lang="pug">
.activity-meeting-show
  .title
    strong 基本信息
    .right-actions(v-show="editing === false")
      .hand-in(@click="handIn" v-if="meeting.state === 'draft'")
        a-icon.icon(type="export")
        span 提交申请
      .edit(@click="startEditing", v-if='canEdit')
        a-icon.icon(type="edit")
        span 编辑
      .i-want(v-if='meeting.state === "reviewed" && meeting.is_joined')
        ComApplicationFormCreator(backupReason="请假", :meeting='meeting')
          a-icon.icon(type="export")
          span 我要请假
      .i-want(v-if='meeting.state === "reviewed" && meeting.is_joined')
        ComApplicationFormCreator(backupReason="邀请", :meeting='meeting')
          a-icon.icon(type="user-add")
          span 我要邀请
      .i-want(v-if='meeting.state === "reviewed" && haveAdminAuth', @click='cancelMeeting')
        a-icon.icon(type="close-circle")
        span 撤销会议

  .content-body(v-show="editing === false")
    .notice-shell
      .notice
        .inviting(v-if="meeting.is_inviting !== null" :style="invitingStyle()")
          a-icon.notice-icon(type="info-circle" theme="filled" :style="{ color: getFont(meeting.is_inviting)}")
          .notice-text
            .text1 {{ `我要邀请-${meeting.is_inviting}` }}
            .text2 {{  meeting.last_application_form && meeting.last_application_form.comment }}
        .leaving(v-if="meeting.is_leaving !== null" :style="leavingStyle()")
          a-icon.notice-icon(type="info-circle" theme="filled" :style="{ color: getFont(meeting.is_leaving)}")
          .notice-text
            .text1 {{ `我要请假-${meeting.is_leaving}` }}
            .text2 {{ meeting.last_application_form && meeting.last_application_form.comment }}
        .invited-or-replacement(
          v-if="meeting.register && meeting.register.meta.comment !== null"
          :style="invitedOrReplacementStyle()"
        )
          a-icon.notice-icon(type="info-circle" theme="filled" :style="{ color: getFont('审核中')}")
          .notice-text
            .text1 {{ meeting.register && meeting.register.meta.comment }}
  ActivityMeetingForm.form(
    ref='formComponent',
    :editing='editing',
    :meeting='meeting',
    @submit='onSubmit',
  )
  .footer
    .form-footer(v-if='editing === true')
      .actions
        a-button(@click="cancel")
          | 取消
        a-button(type="primary" @click="onUpdate" :loading="store.loading")
          | 保存
        a-button(v-if='meeting.pre_review_instance_id', type="primary" @click="onShowInstance")
          | 发起会议审批

  InstanceDetailDialog(
    v-if='visibleInstance',
    v-model='visibleInstance',
    title='会议初审详情',
    :instanceId='meeting.pre_review_instance_id'
  )

</template>

<style lang="stylus" scoped>
.activity-meeting-show
  position relative
  display flex
  flex-direction column
  height 100%
.title
  flex-shrink 0
  padding 16px 0
  width 100%
  // height 100%
  border-bottom 1px solid #DDDDDD
  text-align center
  font-size 16px
  .icon
    padding-right 4px
.right-actions
  position absolute
  top 15px
  right 40px
  display flex
  justify-content flex-start
  color #A6A6A6
  font-size 15px
  .edit
    cursor pointer
  .i-want
    cursor pointer
    margin-left 10px
  .hand-in
    cursor pointer
    margin-right 10px
.content-body
  // flex-grow 1
  // height 0
  overflow auto
  margin 20px auto
  width 400px
  .inviting, .leaving, .invited-or-replacement
    display flex
    justify-content flex-start
    border-radius 3px
    .notice-icon
      margin 10px
    .notice-text
      margin 10px 0
      line-height 18px
      .text2
        margin-top 5px
        color #808080
  .label
    margin 5px 0px
    color #808080
  .content
    width 100%
    margin 5px 0px 20px 0px
    word-wrap: break-word
    .zhi
      margin 0px 12px 0px 12px
      color #808080
.form
  margin 20px auto
  width 400px
  // padding-bottom 60px
  flex-grow 1
  height 0
  overflow auto

.footer
  flex-shrink 0
  border-top 1px solid #DDDDDD
  .form-footer
    display flex
    justify-content flex-end
    // width 60%
    background-color white
    .actions
      display flex
      justify-content flex-end
      padding-top 10px
      width 400px
</style>
