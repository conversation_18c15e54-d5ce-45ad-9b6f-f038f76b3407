<template lang="pug">
  .activity-meeting-card(
    :class="classes"
  )
    .reserver
      a-popover(:title='null')
        template(#content)
          span 发起人：{{ record.reserver_name }}
        a-icon(type='user')
    span.is-over(v-if="record.is_over") 已结束
    span.lock(v-if="lockVisible && record.state == 'reviewed' && !record.is_over")
      a-icon(type="lock" theme="filled")
    span.delete(v-else-if='deleteVisible && isAdmin && record.state === "pending"')
      PopoverConfirm(
        title="删除会议"
        content="您确定删除该会议吗"
        placement="top"
        @confirm="onDelete"
      )
        a-icon(type="delete")

    .part1
      .header
        a-tooltip
          template(#title)
            | {{ record.title }}
          span.title
            | {{ record.title }}
      .content
        .time
          a-icon.icon(type="clock-circle")
          span.text
            | {{ $moment(record.date).format('MM/DD') }}
          span.text
            | {{ `${$moment(record.begin_time).format('HH:mm')} - ${$moment(record.end_time).format('HH:mm')}` }}
        .room(v-if="roomVisiable")
          a-icon.icon(type="environment")
          span.text
            | {{ record.meeting_room_name }}
    .part2
      .people
        .moderator
          span.text
            | {{ `主持人：${(record.moderator_names || []).join('、')}`}}
        .participant
          a-tooltip
            template(#title)
              .text {{ `参加者：${record.user_desc || participant || '' }` }}
            div.text.truncate
              | {{ `参加者：${record.user_desc || participant || '' }` }}
        .moderator
          span.text
            | {{ `组织部门：${record.department_names}`}}
      .footer(v-show="tagVisible")
        .tags
          slot(name="tags" :meeting="record")
            .moderator-tag(v-if="isModerator" :style="{ color: greenFont, background: greenBackground }")
              a-icon(type="team")
              span 我主持的
            .participant-tag(
              v-if="isJoined && (!isInvited || !isReplacement)"
              :style="{ color: greenFont, background: greenBackground }"
            )
              a-icon(type="team")
              span 我参与的
            .participant-invited-tag(v-if="isInvited" :style="{ color: greenFont, background: greenBackground }")
              a-icon(type="team")
              span 我参与的-受邀
            .participant-invited-tag(v-if="isReplacement" :style="{ color: greenFont, background: greenBackground }")
              a-icon(type="team")
              span 我参与的-替换
            .is-leaving-tag(v-if="isLeaveing" :style="{ color: leavingFont, background: leavingBackground }")
              a-icon(type="team")
              span {{ `我要请假-${isLeaveing}` }}
            .is-inviting-tag(v-if="isInviting" :style="{ color: invitingFont, background: invitingBackground }")
              a-icon(type="team")
              span {{ `我要邀请-${isInviting}` }}
        //- .space
        .more
          slot(name="more" :meeting="record")

</template>

<script lang="ts">
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import { IActivityMeeting, ActivityMeeting } from '@/models/conference/activity_meeting';
import { template } from '../../views/comm/admin/dynamic_columns/template';
import { meetingAdminActivityMeetingStore } from '@/store/modules/meeting/admin/activity_meeting.store';

@Component({
  components: {},
})
export default class ActivityMeetingCard extends Vue {
  @Model('input', { type: Boolean, default: false }) private value!: boolean;
  @Prop({ type: Object, default: () => ({}), required: true }) private record!: IActivityMeeting;
  @Prop({ type: Boolean, default: true }) private roomVisiable!: boolean;
  @Prop({ type: Boolean, default: true }) private tagVisible!: boolean;
  @Prop({ type: Boolean, default: true }) private lockVisible!: boolean;
  @Prop({ type: Boolean, default: false }) private deleteVisible!: boolean;

  greenBackground: string = '#F0F9F2';
  greenFont: string = '#6DC37D';
  blueBackground: string = '#EDF7FF';
  blueFont: string = '#3DA8F5';
  orangeBackground: string = '#FFF7E6';
  orangeFont: string = '#FA8C15';
  grayBackground: string = '#F5F5F5';
  grayFont: string = '#A6A6A6';

  get participant() {
    const str = (this.record.user_names || []).join('、');
    return str.length > 16 ? str.slice(0, 16) + '...' : str;
  }

  get isModerator() {
    return this.record.is_moderator;
  }

  get isJoined() {
    return this.record.is_joined;
  }

  get isInvited() {
    return this.record.is_invited;
  }

  get isReplacement() {
    return this.record.is_replacement;
  }

  get isLeaveing() {
    return this.record.is_leaving;
  }

  get isInviting() {
    return this.record.is_inviting;
  }

  get isAdmin() {
    return this.$utils.hasPermission('conference', 'admin');
  }

  get leavingBackground() {
    switch (this.isLeaveing) {
      case '审核中':
        return this.orangeBackground;
      case '通过':
        return this.greenBackground;
      case '未通过':
        return this.grayBackground;
    }
    return this.grayBackground;
  }

  get leavingFont() {
    switch (this.isLeaveing) {
      case '审核中':
        return this.orangeFont;
      case '通过':
        return this.greenFont;
      case '未通过':
        return this.grayFont;
    }
    return this.grayBackground;
  }

  get invitingBackground() {
    switch (this.isInviting) {
      case '审核中':
        return this.orangeBackground;
      case '通过':
        return this.greenBackground;
      case '未通过':
        return this.grayBackground;
    }
    return this.grayBackground;
  }

  get invitingFont() {
    switch (this.isInviting) {
      case '审核中':
        return this.orangeFont;
      case '通过':
        return this.greenFont;
      case '未通过':
        return this.grayFont;
    }
    return this.grayBackground;
  }

  get classes() {
    return {
      disable: this.record.is_over,
      enable: !this.record.is_over,
      pending: this.record.state === 'pending',
    };
  }

  mounted() {
    if (this.record.is_over) {
      this.greenBackground = this.grayBackground;
      this.greenFont = this.grayFont;
      this.blueBackground = this.grayBackground;
      this.blueFont = this.grayFont;
      this.orangeBackground = this.grayBackground;
      this.orangeFont = this.grayFont;
    }
  }

  async onDelete() {
    meetingAdminActivityMeetingStore.init();
    await meetingAdminActivityMeetingStore.delete({ id: this.record.id! });
    this.$emit('refresh');
  }
}
</script>

<style lang="stylus" scoped>
.activity-meeting-card
  overflow hidden
  width 100%
  border 1px solid #E8E8E8
  background-color white
  position relative
  .reserver
    position absolute
    right 10px
    bottom 10px
  .is-over
    float right
    margin 10px -35px
    width 100px
    background #E5E5E5
    color #A6A6A6
    text-align center
    font-size 10px
    transform rotateZ(45deg)
  .lock, .delete
    float right
    margin 10px 10px
    color red
  .delete
    display none
  .header
    display flex
    justify-content space-between
  .part1
    padding 8px 12px
    border-bottom 1px solid #E8E8E8
    .header
      padding-bottom 6px
      .title
        overflow hidden
        text-overflow ellipsis
        white-space nowrap
        font-weight bold
        font-size 16px
    .content
      .time
        display flex
        justify-content flex-start
        padding 3px 2px
  .part2
    padding 8px 12px
    .people
      .moderator
        padding 2px 0
      .participant
        padding 2px 0
    .footer
      display flex
      justify-content space-between
      padding-top 8px
      .tags
        display flex
        justify-content flex-start
        min-height 27px
        .moderator-tag, .participant-tag, .participant-invited-tag, .is-leaving-tag, .is-inviting-tag
          margin 0px 4px
          padding 3px 4px
          border-radius 3px
          span
            font-size 12px
      .more
        line-height 30px
  .icon
    margin 2px
    font-size 17px
  .text
    overflow hidden
    padding 4px 2px
    text-overflow ellipsis
    white-space nowrap
    // font-size 14px
    line-height 14px
  .more-icon
    display none
  &:hover
    .delete
      display block

.activity-meeting-card.disable
  .icon
    color #CCCCCC
  .text
    color #CCCCCC
  .title
    color #A6A6A6
  .reserver
    color #CCCCCC

.activity-meeting-card.enable:hover
  .header
    .title
      color #3DA8F5
  .more-icon
    display block
  .more-icon:hover
    color #3DA8F5
.activity-meeting-card.pending
  border 3px dashed #E8E8E8
  margin-top -3px

.truncate
  overflow hidden
  -o-text-overflow ellipsis
  text-overflow ellipsis
  white-space nowrap
</style>
