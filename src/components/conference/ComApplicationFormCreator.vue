<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { applicationFormTemplateLeaving, applicationFormTemplateInviting } from './applicationFormTemplate';
import ApplicationFormStore from '@/store/modules/conference/application_form.store';
import { IActivityMeeting } from '@/models/conference/activity_meeting';
import { IApplicationForm } from '@/models/conference/application_form';
import InstanceDetailDialog from '../bpm/InstanceDetailDialog.vue';
import TaFormDialog from '../global/TaFormDialog.vue';

@Component({
  components: {
    InstanceDetailDialog,
    TaFormDialog,
  },
})
export default class ComApplicationFormCreator extends Vue {
  @Prop({ type: String, required: true }) backupReason!: '请假' | '邀请';
  @Prop({ type: Object, required: true }) meeting!: IActivityMeeting;

  instanceId = -1;
  visible = false;
  instanceVisible = false;
  formData: Partial<IApplicationForm> = {};

  get applicationFormStore() {
    return ApplicationFormStore;
  }

  get template() {
    if (this.backupReason === '请假') {
      return applicationFormTemplateLeaving;
    } else {
      return applicationFormTemplateInviting;
    }
  }

  onShowDialog() {
    this.visible = true;
  }

  create(formData: IApplicationForm) {
    this.applicationFormStore
      .createByParent({
        parentId: this.meeting.id,
        backup_user_ids: formData.backup_user_ids,
        backup_user_type: 'Teacher',
        backup_reason: this.backupReason,
      })
      .then(data => {
        this.$message.success('申请成功');
        this.instanceId = data.data.instance_id;
        this.visible = false;
        this.instanceVisible = true;
      })
      .catch(error => this.$message.error(error));
  }
}
</script>

<template lang="pug">
.com-application-form-creator
  .clickable(@click='onShowDialog')
    slot
  TaFormDialog(
    v-model="visible"
    :template="template"
    :title="`我要${backupReason}`"
    :loading="applicationFormStore.loading"
    :formData="formData"
    @create="create"
  )
  InstanceDetailDialog(
    :instanceId="instanceId"
    :title="`我要${backupReason}`"
    v-model="instanceVisible"
  )
</template>

<style lang="stylus" scoped>
.clickable
  cursor pointer
</style>
