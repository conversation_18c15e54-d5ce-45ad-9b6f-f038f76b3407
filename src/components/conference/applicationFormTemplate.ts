import { IFormTemplateItem } from '@/interfaces/formTemplate.interface';

export const applicationFormTemplateLeaving: IFormTemplateItem[] = [
  {
    key: 'backup_user_ids',
    name: '请选择替补人员（选填）',
    layout: {
      component: 'contacts',
      required: false,
      type: 'array',
      multiple: true,
    },
    model: { attr_type: 'array' },
  },
];

export const applicationFormTemplateInviting: IFormTemplateItem[] = [
  {
    key: 'backup_user_ids',
    name: '请选择邀请人员',
    layout: {
      component: 'contacts',
      required: true,
      type: 'array',
      multiple: true,
    },
    model: { attr_type: 'array' },
  },
];
