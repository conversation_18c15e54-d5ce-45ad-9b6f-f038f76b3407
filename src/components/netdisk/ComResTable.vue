<script lang="ts">
import ObjectBlock from '../ObjectBlock.vue';
import ComBreadcrumb from '@/components/netdisk/ComBreadcrumb.vue';

import { Component, Vue, Prop } from 'vue-property-decorator';
import { TaIndexViewTabInterface } from '../global/TaIndex';

@Component({
  components: { ComBreadcrumb },
})
export default class ComResTabel extends Vue {
  @Prop({ type: Object, default: () => {} }) private store!: any;
  @Prop({ type: Array, default: () => [] }) private breadcrumbData!: [];
  @Prop({ type: Array, default: () => [] }) private searcherOptions!: [];
  @Prop({ type: Array, default: () => undefined }) private parentBreadcrumb!: [];

  private tabs: TaIndexViewTabInterface[] = [
    { label: '  ', key: 'ComResTabel', num: 0, background: '', color: '', mode: 'table' },
  ];
  private searcherSimpleOptions: Array<any> = [
    {
      key: 'name',
      label: '课程名称',
      type: 'string',
    },
    {
      key: 'code',
      label: '课程代码',
      type: 'string',
    },
  ];

  get path() {
    return this.$route.path;
  }

  get config() {
    return {
      store: this.store,
      recordName: '资源',
      showCount: false,
      searcherSimpleOptions: this.searcherSimpleOptions,
    };
  }

  private async onShow(val: IObject = {}) {
    if (this.$route.meta.title === '学院资源') {
      await this.$router.push({ path: `${this.path}/${val.id}/majors` });
    } else if (this.$route.meta.title === '专业资源') {
      // 课程
      await this.$router.push({ path: `${this.path}/${val.id}/items` });
    } else {
      await this.$router.push({ path: `${this.path}/${val.id}/items` });
    }
  }

  private auth(val: string) {
    switch (val) {
      case 'read_write':
        return '读写';
      case 'read_only':
        return '读';
      default:
        return '无权限';
    }
  }
}
</script>

<template lang="pug">
.com-res-tabel-container
  TaIndexView(:tabs="tabs" :config="config" @onShow="onShow")
    template(#header)
       ComBreadcrumb(:topBreadcrumb="parentBreadcrumb")
    template(slot="table")
      //- 学院资源
      template(v-if='this.$route.meta.title === "专业资源"')
        a-table-column(title='专业名称', :ellipsis="true" dataIndex='name', width='280px')
        a-table-column(title='专业代码', :ellipsis="true" dataIndex='code', width='160px')
        a-table-column(title='所属学院',dataIndex='college_name', :ellipsis="true" width='160px')
        a-table-column(title='专业负责人', dataIndex='teacher_name', :ellipsis="true" width='160px')
        a-table-column(title='权限', dataIndex='netdisk_permit', :ellipsis="true" width='160px' )
          template(slot-scope="scope")
            span {{ auth(scope) }}
        a-table-column(title='拥有项目目数', dataIndex='children_count', align='center', width='120px')
        a-table-column(title='资源数目', dataIndex='netdisk_item_count', align='center', width='120px')
      //- 课程资源
      template(v-if="this.$route.meta.title === '课程资源'")
        a-table-column(title='课程名称', :ellipsis="true" dataIndex='name', width='240px' )
        a-table-column(title='所属专业', :ellipsis="true" dataIndex='major_name', width='160px' )
        a-table-column(title='所属学院', :ellipsis="true" dataIndex='department_name', width='240px', )
        a-table-column(title='课程负责人', :ellipsis="true" dataIndex='teacher_name', width='160px' )
        a-table-column(title='课程类别', :ellipsis="true" dataIndex='course_type', width='160px' )
        a-table-column(title='权限', :ellipsis="true" dataIndex='netdisk_permit', width='120px' )
          template(slot-scope="scope")
            span {{ auth(scope) }}
        a-table-column(title='拥有项目目数', :ellipsis="true" dataIndex='netdisk_item_count', align='center', width='160px')
      //- 学院资源
      template(v-if="this.$route.meta.title === '学院资源'")
        a-table-column(title='#', width='50px')
          template(slot-scope='scope')
            span {{ scope._index }}
        a-table-column(title='学院名称', dataIndex='name', width='160px')
        a-table-column(title='学院代码', dataIndex='code', width='160px' )
        a-table-column(title='专业总数', dataIndex='major_count', width='160px' )
        a-table-column(title='权限', dataIndex='netdisk_permit', width='160px' )
          template(slot-scope="scope")
            span {{ auth(scope) }}
        a-table-column(title='拥有项目目数', dataIndex='children_count', align='center', width='200px')
        a-table-column(title='资源数目', dataIndex='netdisk_item_count', align='center', width='200px')
</template>

<style lang="stylus" scoped>
.com-res-tabel-container
  width 100%
  height 100%
</style>
