<script lang="ts">
import { IItem } from '@/types/model';
import { TaIndexViewTabInterface } from '../global/TaIndex';
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';

import title from '@/models/res/title';
import ComTitle from '@/components/netdisk/ComTitle.vue';
import ComResTable from '@/components/netdisk/ComResTable.vue';
import ComTable from '@/components/netdisk/personal/ComTable.vue';
import ComCheckbox from '@/components/netdisk/ComCheckbox.vue';
import ComBreadcrumb from '@/components/netdisk/ComBreadcrumb.vue';
import ComStudentList from '../teaching/teacher/ComStudentList.vue';

@Component({
  components: { ComTitle, ComResTable, ComCheckbox, ComTable, ComBreadcrumb },
})
export default class ComTotalView extends Vue {
  @Prop({ type: Object, default: () => {} }) private store!: IObject;
  @Prop({ type: String, default: () => '' }) private parendpath!: '';
  @Prop({ type: String, default: () => '' }) private sourceName!: '';
  @Prop({ type: Number, default: () => undefined }) private sourceId!: number;
  @Prop({ type: Object, default: () => undefined }) private parentStore!: IObject;
  @Prop({ type: Array, default: () => [] }) private parentBreadcrumb!: []; // 自定义文件面包屑第一层
  @Prop({ type: String, default: () => '' }) private readonly netdiskTitle!: string; // 资源标题
  @Prop({ type: Boolean, default: () => false }) private readonly hideDisplayButton!: boolean; // 隐藏或显示 创建文件夹按钮和上传按钮

  private fileFormData: IItem = {}; // 上传文件的信息
  private EditFileStaue: boolean = false;
  private fileIItemData: Array<IItem> = [];
  private typeQueryData: Array<string> = [];
  private path = '/netdisk/teacher/owneds/items';

  get title(): string {
    return this.$route.meta.title;
  }

  get parentId(): number | undefined {
    return +this.$route.query.parentId || undefined;
  }

  // 文件修改发送 请求
  private async fileEditContent(val: IItem): Promise<void> {
    val.attachment ? (val.attachment.description = val.desc) : '';
    const { data } = await this.store.update(val);
    this.$message.success('文件已修改');
    this.fileIItemData = data.items;
  }

  // 获取文件信息
  private async getEditFileInfo(val: IItem): Promise<void> {
    this.EditFileStaue = !this.EditFileStaue;
    this.fileFormData = JSON.parse(JSON.stringify(val));
  }

  // haeder
  private async onUploadFile(formData: any, data: any): Promise<void> {
    let parentId = this.parentId === this.sourceId ? undefined : this.parentId;
    data.forEach(async (item: any, index: number) => {
      await this.store.create({ formData: { ...item, parent_id: parentId }, config: { params: { sequence: index } } });
    });
    this.fileIItemData = JSON.parse(JSON.stringify(this.store.records));
    this.$message.success('文件上传成功');
  }
  // 创建文件夹
  private async onCreate(val: any, items: any): Promise<void> {
    let parentId = this.parentId === this.sourceId ? undefined : this.parentId;
    let param: any = { name: val.name, desc: val.desc, type: 'Netdisk::FolderItem', parent_id: parentId };
    const { data } = await this.store.create(param);
    this.$message.success('文件夹创建成功');
  }

  // table
  private async onDelete(id: number): Promise<void> {
    this.$confirm({
      title: '是否确定删除该文件?',
      okText: '确定',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        await this.store.delete(id);
        this.fileIItemData = JSON.parse(JSON.stringify(this.store.records));
        this.$message.success('删除成功');
      },
    });
  }
}
</script>

<template lang="pug">
.ComTotalView
  .header
    ComTitle(
      :store="store"
      @create="onCreate"
      :sourceId="sourceId"
      :netdiskTitle="netdiskTitle"
      :parentStore="parentStore"
      @uploadFile='onUploadFile'
      @fileEdit="fileEditContent"
      :fileFormData="fileFormData"
      :editFileStaue="EditFileStaue"
      :hideDisplayButton="hideDisplayButton"
    )
  .checkbox-lists
  .table
    ComTable(
      :parentPath="parendpath"
      @delete="onDelete"
      @fileEdit="getEditFileInfo"
      :store="store"
      :parentStore="parentStore"
      :sourceName="sourceName"
      :parentBreadcrumb="parentBreadcrumb"
      :sourceId.sync="sourceId"
    )

</template>

<style lang="stylus" scoped>
.ComTotalView
  width 100%
  height 100%
  // overflow scroll
  .table
    margin-top 20px
</style>
