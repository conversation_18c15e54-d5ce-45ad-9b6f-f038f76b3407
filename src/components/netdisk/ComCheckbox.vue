<script lang="ts">
import { NotVoid } from 'lodash';
import { Component, Vue, Prop } from 'vue-property-decorator';
type fileType = 'Netdisk::FolderItem' | 'Netdisk::VideoItem' | 'Netdisk::DocumentItem' | 'Netdisk::ImageItem';

export interface ITagType {
  name: String;
  type: fileType;
}

@Component({
  components: {},
})
export default class comcheckbox extends Vue {
  private visible: boolean = false;
  private typeSet: any = new Set();
  private tagTypeArray: Array<ITagType> = [
    {
      name: '文件夹',
      type: 'Netdisk::FolderItem',
    },
    {
      name: '文档',
      type: 'Netdisk::DocumentItem',
    },
    {
      name: '视频',
      type: 'Netdisk::VideoItem',
    },
    {
      name: '图片',
      type: 'Netdisk::ImageItem',
    },
  ];

  private onShow(): void {
    this.visible = !this.visible;
  }

  private setDelete(val: string): void {
    this.typeSet.delete(val);
    this.$emit('checkbox', Array.from(this.typeSet));
  }

  private setAdd(val: string): void {
    this.typeSet.add(val);
    this.$emit('checkbox', Array.from(this.typeSet));
  }

  private change(val: any): void {
    this.typeSet.has(val) ? this.setDelete(val) : this.setAdd(val);
  }
}
</script>

<template lang="pug">
.comcheckbox
  .checkbox-list
    .list-top
      //-  所属类型
      .left 
        span.title 文件类型:
        .checkbox-box(v-for="item in tagTypeArray")
          a-checkbox.checkbox(:key="item.type" :value='item.type' @change="change(item.type)") {{ item.name }}
      .right
        .Icon-box(v-if='visible', @click='onShow')
          span 收起
          a-icon.icon(type='up')
        .Icon-box(v-else, @click='onShow')
          span 展开
          a-icon.icon(type='down')
    .list-bottom(v-if='visible')
      //-  所属标签 
      span.title 所属标签:
      .checkbox-box
        a-checkbox.checkbox(value='name') 全部
        a-checkbox.checkbox(value='age') 标签1
</template>

<style lang="stylus" scoped>
.comcheckbox
  width 100%
  height 100%
  margin-top 20px
  .checkbox-list
    padding 0px 28px
    padding-top 16px
    border-radius 2px
    background #F5F5F5
    .list-top
      display flex
      flex-direction row
      justify-content space-between
      padding-bottom 15px
      .left
        display flex
        flex-direction row
        .title
          width 70px
          color #383838
          font-weight 400
          font-size 14px
          font-family PingFangSC-Regular, PingFang SC
        .checkbox-box
          display flex
          flex-direction row
          flex-wrap wrap
          .checkbox
            margin-right 33px
            // margin-bottom 11px
      .right
        .Icon-box
          color #3DA8F5
          font-weight 400
          font-size 14px
          font-family PingFangSC-Regular, PingFang SC
          cursor pointer
          .icon
            margin-left 4px
            font-size 12px
    .list-bottom
      display flex
      flex-direction row
      padding 16px 0px
      border-top 1px dashed #CCCCCC
      .title
        width 70px
        color #383838
        font-weight 400
        font-size 14px
        font-family PingFangSC-Regular, PingFang SC
      .checkbox-box
        display flex
        flex 1
        flex-direction row
        flex-wrap wrap
        .checkbox
          margin-right 33px
          margin-bottom 11px

.ant-checkbox-wrapper
  margin 0
</style>
