<script lang="ts">
import { TaIndexViewTabInterface } from '@/components/global/TaIndex';
import { Component, Vue, Prop } from 'vue-property-decorator';
import { netdiskOwnedItemStore } from '@/store/modules/netdisk/owned/item.store';
import { IItem } from '@/types/model';

// 资源审核 table
@Component({
  components: {},
})
export default class ComTable extends Vue {
  @Prop({ type: String, default: '' }) prop!: string;
  @Prop({ type: Array }) checkData!: [];
  @Prop({ type: String, default: '' }) tab!: '';

  private selectedTeacherIds: number[] = [];
  private selectedRowKeys: number[] = [];
  private tabs: TaIndexViewTabInterface[] = [
    { label: '待审核', key: 'execute', num: 0, background: '', color: '', mode: 'table' },
    { label: '已通过', key: 'owned', num: 0, background: '', color: '', mode: 'table' },
    { label: '未通过', key: 'following', num: 0, background: '', color: '', mode: 'table' },
  ];

  get netdiskItemStore() {
    return netdiskOwnedItemStore;
  }

  get config() {
    return {
      store: this.netdiskItemStore,
      recordName: '任务',
      showCount: false,
      searcherSimpleOptions: [],
    };
  }

  get rowSelection() {
    return { selectedRowKeys: this.selectedRowKeys, onChange: this.onSelectChange };
  }

  created() {
    this.netdiskItemStore.init();
  }
  onSelectChange(selectedRowKeys: any) {
    this.selectedRowKeys = selectedRowKeys;
  }
  onShow(val: IItem) {}
}
</script>

<template lang="pug">
.com-table-container
  TaIndexView(:tabs="tabs" :config="config" :data="checkData" @onShow="onShow")
    template(#table='{ record }')
      a-table-column(title='名称', ellipsis='true')
        template(slot-scope='scope')
          img.img(src='@/assets/images/file/folder.png')
          span {{ scope.name }}
      a-table-column(title='大小', dataIndex='file_size', :width='70', ellipsis='true')
      a-table-column(title='所属院系', dataIndex='college', :width='156', ellipsis='true')
      a-table-column(title='申请人', dataIndex='applicant', :width='130', ellipsis='true')
      a-table-column(title='申请同步至', dataIndex='synchronize', :width='150', ellipsis='true')
      //- 待审核
      a-table-column(:width='200', align='center', v-if='tab === "待审核"', ellipsis='true')
        template(slot-scope='scope')
          a-button 不通过
          a-button(type='primary') 通过
      //- 已通过 , 未通过
      a-table-column(title='状态', :width='150', align='center', v-else, ellipsis='true')
        template(slot-scope='scope')
          span.status(:class='{ pass: tab === "已通过" }')
            a-badge(status='success', v-if='tab === "已通过"')
            a-badge(status='default', v-else)
            | {{ tab }}
</template>

<style lang="stylus" scoped>
.status
  color #A6A6A6
  font-weight 400
  font-size 14px
  font-family PingFangSC-Regular, PingFang SC

.img
  position relative
  top -2px
  margin-right 5px
  width 22px
  height 20px

.pass
  color #6DC37D
</style>
