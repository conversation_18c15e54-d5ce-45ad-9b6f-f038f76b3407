<script lang="ts">
import { IItem } from '@/types/model';
import { Component, Vue, Prop, Model } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class ComNetdiskScheduleCourseItemPreviewer extends Vue {
  @Model('input', { type: Boolean, default: false }) visible!: boolean;
  @Prop({ type: Object, required: true }) netdiskItem!: IItem;

  get localVisible() {
    return this.visible;
  }

  set localVisible(val: boolean) {
    this.$emit('input', val);
  }
}
</script>

<template lang="pug">
.com-netdisk-schedule-course-item-previewer
  a-modal(v-model='localVisible', width='80vw')
    .table
      a-table(:dataSource='netdiskItem.attachment.payload', :bordered='true')
        a-table-column(
          v-for='item in netdiskItem.attachment.template',
          :dataIndex='item.key',
          :key='item.key',
          :title='item.name',
        )
          template(slot-scope='value')
            template(v-if='typeof value === "object"')
              Attachments(:attachments='value')
            template(v-else)
              | {{ value }}
</template>

<style lang="stylus" scoped>
.table
  padding 10px
</style>
