<script lang="ts">
import ComFileMoveList from '@/components/netdisk/ComFileMoveList.vue';

import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import { netdiskOwnedItemStore } from '@/store/modules/netdisk/owned/item.store';
import { netdiskTeacherCollegeStore } from '@/store/modules/netdisk/teacher/college.store';
import { netdiskTeacherCourseStore } from '@/store/modules/netdisk/teacher/course_set.store';
import { netdiskTeacherCourseDirsStore } from '@/store/modules/netdisk/teacher/course_dirs.store';
import { netdiskTeacherItemStore } from '@/store/modules/netdisk/teacher/item.store';
import { unwatchFile } from 'fs';

@Component({
  components: { ComFileMoveList },
})
export default class ConCourseFileMove extends Vue {
  @Model('change', { type: Boolean, default: () => false }) private visible!: boolean;

  @Prop({ type: String, default: '' }) private prop!: string;
  @Prop({ type: Object, default: () => ({}) }) private store!: any;
  @Prop({ type: Object, default: () => ({}) }) private parendStore!: any;
  @Prop({ type: String, default: () => '' }) private title!: string;
  @Prop({ type: Number, default: () => 800 }) private width!: number;

  @Watch('visible')
  onchangevisible() {}

  private levelOne: any = [];
  private RootArray: any = [];
  private FolderArray: any = [];
  private activeId: number = 0;
  private rootfileId: number = 0;
  private departmenId: number = 0;
  private childrenStore: object = {};
  private fileId: number | undefined = undefined;
  private bodyStyle: object = {
    margin: 0,
    padding: 0,
  };
  private parentFolderArray: any = [
    // {
    //   name: '学院资源',
    //   id: 1,
    // },
    {
      name: '课程资源',
      id: 2,
    },
    {
      name: '个人资源',
      id: 3,
    },
    // {
    //   name: '专业资源',
    //   id: 4,
    // },
  ];

  get ownedItemStore() {
    return netdiskOwnedItemStore;
  }

  get netdiskCollegeStore() {
    return netdiskTeacherCollegeStore;
  }

  // get netdiskCourseStore() {
  //   return netdiskTeacherCourseStore;
  // }

  get netdiskCourseStore() {
    return netdiskTeacherCourseDirsStore;
  }

  get netdiskItemStore() {
    return netdiskTeacherItemStore;
  }

  get parentId(): number | undefined {
    return +this.$route.params.ParentId || undefined;
  }

  created() {
    this.netdiskCollegeStore.init();
    this.netdiskCourseStore.init();
    this.netdiskItemStore.init();
    this.ownedItemStore.init();
    this.FolderArray = [];
    this.RootArray = [];
    this.levelOne = [];
  }

  mounted() {}

  private onOk(): void {
    this.$emit('sumbit', this.fileId);
  }

  private onCancel(): void {
    this.$emit('cancel');
  }

  private createFile(): void {
    this.$emit('create');
  }

  private selectChildrenFolder(id: number, childrendepth: number, type?: any): void {
    let depth = childrendepth;
    if (this.FolderArray.length < depth) {
      if (type === 'Netdisk::FolderItem') {
        this.setFolder(id);
        this.fileId = 0;
      } else {
        this.fileId = id;
      }
    } else if (depth < this.FolderArray.length) {
      if (type === 'Netdisk::FolderItem') {
        this.FolderArray.splice(depth);
        this.setFolder(id);
        this.fileId = 0;
      } else {
        this.fileId = id;
      }
    } else if (depth === this.FolderArray.length) {
      if (type === 'Netdisk::FolderItem') {
        this.FolderArray.splice(depth);
        this.setFolder(id);
        this.fileId = 0;
      } else {
        this.fileId = id;
      }
    }
  }

  private setFolder(id: number): void {
    if (this.departmenId === 3) {
      this.ownedItemStore.index({ page: 1, per_page: 1000, q: { children_of: id } }).then((res: any) => {
        const { data } = res;
        this.FolderArray.push(JSON.parse(JSON.stringify(data.items)));
      });
    } else {
      this.netdiskItemStore.index({ page: 1, per_page: 1000, q: { children_of: id } }).then((res: any) => {
        const { data } = res;
        this.FolderArray.push(JSON.parse(JSON.stringify(data.items)));
      });
    }
  }

  private async selectNetdiskType(item: any) {
    this.RootArray = [];
    this.levelOne = [];
    this.FolderArray = [];
    this.activeId = item.id;
    if (item.id === 1) {
      let { data } = await this.netdiskCollegeStore.index({ page: 1, per_page: 1000 });
      this.levelOne = data.departments;
      this.departmenId = 1;
      this.netdiskItemStore.init();
    } else if (item.id === 2) {
      let { data } = await this.netdiskCourseStore.index({ page: 1, per_page: 1000 });
      this.levelOne = data.course_dirs;
      this.departmenId = 2;
    } else if (item.id === 3) {
      let { data } = await this.ownedItemStore.index({ page: 1, per_page: 1000, q: { roots: true } });
      this.RootArray = data.items;
      this.departmenId = 3;
    }
  }

  private async selectDepartment(item: any) {
    // this.departmenId = item.id;
    this.RootArray = [];
    this.FolderArray = [];
    if (this.departmenId === 1) {
      this.netdiskItemStore.init({ parents: [{ type: 'colleges', id: item.id }] });
      const { data } = await this.netdiskItemStore.index({ page: 1, per_page: 1000, q: { roots: true } });
      this.RootArray = [];
      this.RootArray = data.items;
    } else if (this.departmenId === 2) {
      this.netdiskItemStore.init({ parents: [{ type: 'course_dirs', id: item.id }] });
      const { data } = await this.netdiskItemStore.index({ page: 1, per_page: 1000, q: { roots: true } });
      this.RootArray = [];
      this.RootArray = data.items;
    }
  }

  private selectRoot(item: any) {
    this.rootfileId = item.id;
    this.FolderArray = [];
    if (item.type === 'Netdisk::FolderItem') {
      this.fileId = 0;
      if (this.departmenId === 3) {
        this.ownedItemStore.index({ page: 1, per_page: 1000, q: { children_of: item.id } }).then((res: any) => {
          const { data } = res;
          this.FolderArray.push(JSON.parse(JSON.stringify(data.items)));
        });
      } else {
        this.netdiskItemStore.index({ page: 1, per_page: 1000, q: { children_of: item.id } }).then((res: any) => {
          const { data } = res;
          this.FolderArray.push(JSON.parse(JSON.stringify(data.items)));
          this.childrenStore = this.netdiskItemStore;
        });
      }
    } else {
      this.fileId = item.id;
    }
  }
}
</script>

<template lang="pug">
a-modal.modal-file-move(@ok='onOk',  
                        :title='title', 
                        :width='width',
                        :visible='visible', 
                        @cancel='onCancel'
                        :bodyStyle= 'bodyStyle'           
                        )
  .file-list
    .roots
      .title 资源库
      .root-list
        .box(v-for="item in parentFolderArray" :key="item.id" 
            :class="{active: item.id === activeId }"
            @click="selectNetdiskType(item)"
            ) {{ item.name }}
    .roots(v-if="departmenId !== 3")
      .title {{  activeId === 3 ? "部门" : "课程名"  }}
      .root-list
        .box(v-for="item in levelOne" :key="item.id" 
            :class="{active: item.id ===  departmenId }"
            @click="selectDepartment(item)"
            ) {{ item.name }}
    .roots
      .title 根文件
      .root-list
        .box(v-for="item in RootArray" :key="item.id" 
            :class="{active: item.id === rootfileId }"
            @click="selectRoot(item)"
            )
          template
            img.img(src="@/assets/images/file/image.png" v-if="item.type  === 'Netdisk::ImageItem'")
            img.img(src="@/assets/images/file/video.png" v-if="item.type  === 'Netdisk::VideoItem'")
            img.img(src="@/assets/images/file/webpage.png" v-if="item.type  === 'Netdisk::UrlItem'")
            img.img(src="@/assets/images/file/text.png" v-if="item.type  === 'Netdisk::OtherItem'")
            img.img(src="@/assets/images/file/folder.png" v-if="item.type  === 'Netdisk::FolderItem'")
            img.img(src="@/assets/images/file/word.png" v-if="item.type  === 'Netdisk::DocumentItem'")
            span {{ item.name }}
    template(v-for="item in FolderArray")
      ComFileMoveList(:store="childrenStore"
                      @create="createFile"
                      :childrenFolderMap="item"
                      @changeChildren="selectChildrenFolder"
                      )
</template>

<style lang="stylus" scoped>
.modal-file-move
  position #F5F5F5
  .file-list
    overflow scroll
    overflow-x scroll
    overflow-y scroll
    display flex
    flex-direction row
    height 558px
    .roots
      flex-shrink 0
      overflow hidden
      height 100%
      border-right 1px solid #E5E5E5
      .title
        width 220px
        padding 0px 16px
        width 220px
        height  40px
        line-height 50px
        font-size 12px
        font-family PingFangSC-Regular, PingFang SC
        font-weight 400
        color #808080
      .root-list
        width 260px
        overflow scroll
        overflow-x hidden
        overflow-y scroll
        height 100%
        .box
          overflow hidden
          text-overflow ellipsis
          white-space nowrap
          cursor pointer
          line-height 40px
          box-sizing border-box
          width 100%
          height  40px
          background #FFFFFF
          font-size 14px
          font-family PingFangSC-Regular, PingFang SC
          font-weight 400
          color #383838
          padding 0px 16px 0px 16px

.active
  background #F5F5F5 !important

.img
  height 20px
  width 20px
  margin-right 5px
</style>
