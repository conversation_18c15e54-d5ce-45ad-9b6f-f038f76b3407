<script lang="ts">
import ObjectBlock from '@/components/ObjectBlock.vue';
import { IItem } from '@/types/model';
import { path } from 'lodash/fp';
import { Component, Vue, Prop, Model } from 'vue-property-decorator';
import { FileTemplate } from './template';

export interface ITag {
  name?: String;
  id?: Number;
}

@Component({
  components: {},
})
export default class ComUploadFile extends Vue {
  @Model('change', { type: Boolean, default: false }) visible!: boolean;

  @Prop({ type: String, default: '' }) private readonly title!: '';
  @Prop({ type: Object, default: () => '' }) private formData!: IItem;
  @Prop({ type: String, default: '480px' }) private readonly width!: '';

  private form = {};
  private loading = false;
  private tags: ITag[] = [];
  private tagName: String = '';
  private visibleTag: boolean = false;

  get template() {
    return FileTemplate;
  }

  get titles(): string {
    return this.$route.meta.title;
  }

  private onCancel(): void {
    this.$emit('Cancel');
  }

  private onShow(): void {
    this.visibleTag = true;
  }

  private addTag(): void {
    let tag: ITag = { name: this.tagName };
    this.tags.push(tag);
    this.tagName = '';
    this.visibleTag = false;
  }

  private onEdit(): void {
    this.$emit('fileEdit', this.formData);
  }

  private deleteTag(index: number): void {
    this.tags.splice(index, 1);
  }

  private onOk(): void {
    if (!this.formData.name) {
      this.$message.error('请填写需创建的文件夹名');
      return;
    }
    this.$emit('Submit', this.formData);
  }

  onCreate() {}
}
</script>

<template lang="pug">
//- TaFormDialog(
//-     v-model='visible',
//-     title='上传文件',
//-     :formData='form',
//-     :loading='loading',
//-     :template='template',
//-     @create='onCreate'
//-   )
a-modal.modal(:visible='visible', :title='title', :width='width', @ok='onOk', @cancel='onCancel')
  a-form-model.form(ref='formData', :model='formData')
    a-row.row(:span='21')
      a-col.col(:span='21')
        span.title 文件夹属性:
        span {{titles}}
    //- 文件夹名称
    a-form-item(label='文件夹名称', prop='name')
      a-input(v-model='formData.name', type='text', key='name', placeholder='请输入文件夹名')
    //- 文件夹描述
    a-form-item(label='文件夹描述', prop='desc')
      a-textarea(v-model='formData.desc',  key='desc', placeholder='请输入文件夹描述' :rows="4")
  template(slot="footer")
    a-button(@click="onCancel") 取消
    a-button(type="primary" v-if="title === '创建文件'" @click="onOk") 确认
    a-button(type="primary" v-else @click="onEdit") 修改
    //- 文件夹标签
      a-row.row(:span='21')
        a-col.col(:span='21')
          span 文件夹标签
        a-col.col(:span='21')
          //- tag 标签
          .tags
            a-tag.tag(v-for='(item, index) in tags')
              | {{ item.name }}
              a-icon.icon-close(type='close-circle', theme='filled', @click='deleteTag(index)')
            a-icon.icon-add(type='plus-circle', theme='filled', @click='onShow')
          Popover.popover(v-model='visibleTag', title='添加文件标签')
            template(slot='main')
              .tag-title * 标签
              a-input(v-model='tagName', type='text', placeholder='请输入标签')
            template(slot='footer')
              a-button.tag-button(type='primary', @click='addTag') 添加
      //- 文件夹所属
      a-form-item(label='文件夹所属', prop='belonging')
        a-select(v-model='formData.belonging', placeholder='请选择文件夹所属')
          a-select-option(value='shanghai') shanghai
      //- 可见性
      .mode-box
        .title 可见性
        a-radio-group.box(v-model='formData.radio')
          .mode
            a-icon.icon(type='lock')
            .content
              p 公开模式
              p 所以专业可见
            .radio
              a-radio(value='私有')
          .mode
            a-icon.icon(type='lock')
            .content
              p 公开模式
              p 所以专业可见
            .radio
              a-radio(value='公有')
      //- 可见专业
      .visible-box
        .title 
          .left 可见专业
          .right 
            a-icon(type='setting')
            span 设置
        .content 无
</template>

<style lang="stylus" scoped>
.modal
  .form
    .row
      .col
        margin-bottom 14px
        .title
          color #3DA8F5
          font-size 14px
          font-family PingFangSC-Medium, PingFang SC

.visible-box
  margin-top 20px
  .title
    display flex
    flex-direction row
    justify-content space-between
    .right
      color #3DA8F5
      span
        margin-left 2px
  .content
    margin-top 8px
    color #CCCCCC
    font-weight 400
    font-size 14px
    font-family PingFangSC-Regular, PingFang SC

.mode-box
  width 100%
  height 100%
  .title
    margin-bottom 8px
    color #383838
    font-weight 400
    font-size 14px
    font-family PingFangSC-Regular, PingFang SC
  .box
    display flex
    flex-direction row
    justify-content space-between
    .mode
      display flex
      flex-direction row
      justify-content space-between
      align-items center
      width 200px
      height 56px
      border-radius 3px
      background #FAFAFA
      .icon
        // position absolute
        // top 5px
        margin 0 15px
        width 5px
      .content
        width 80%
        // border 1px solid #3333
        color #383838
        font-weight 400
        font-size 14px
        font-family PingFangSC-Regular, PingFang SC
        p:nth-child(2)
          color #A6A6A6
      .radio
        // margin-right 10px
        height 100%
        line-height 55px

.tags
  display flex
  flex-direction row
  align-items center
  color #3DA8F5
  span
    font-weight 400
    font-size 14px
    font-family PingFangSC-Regular, PingFang SC
  .tag
    display flex
    justify-content center
    align-items center
    margin-right 8px
    min-width 86px
    height 28px
    border 1px solid rgba(61, 168, 245, 0.1)
    border-radius 20px
    background rgba(61, 168, 245, 0.1)
    .icon-close
      margin-left 6px
      color #3DA8F5
      cursor pointer

.tag-title
  margin-bottom 10px

.tag-button
  width 100%
</style>
