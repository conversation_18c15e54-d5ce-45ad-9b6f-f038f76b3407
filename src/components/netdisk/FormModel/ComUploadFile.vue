<script lang="ts">
import ObjectBlock from '@/components/ObjectBlock.vue';
import { all, path } from 'lodash/fp';
import { Component, Vue, Prop, Model } from 'vue-property-decorator';
import FileUploader from '@/components/global/FileUploader.vue';
import { IFile } from '@/models/file';
import { error } from 'console';

import { FileTemplate } from './template';

export interface ITag {
  name?: String;
  id?: Number;
}

export interface IUploadFile {
  id?: Number;
  desc?: String;
  name?: String;
  type?: String;
  radio?: Boolean;
  attachment?: any;
}

@Component({
  components: { FileUploader },
})
export default class ComCreateFile extends Vue {
  @Model('change', { type: Boolean, default: false }) visible!: boolean;
  @Prop({ type: String, default: () => '' }) private readonly title!: '';
  @Prop({ type: String, default: () => '480px' }) private readonly width!: '';

  private data: any = [];
  private form = {};
  private tags: ITag[] = [];
  private files: IFile[] = [];
  private tagName: string = '';
  private loading: boolean = false;
  private visibleTag: boolean = false;
  private allSettled: boolean = false;
  private formData: any = {};
  private readonly FileTypeObject: any = {
    url: 'Netdisk::UrlItem',
    video: 'Netdisk::VideoItem',
    image: 'Netdisk::ImageItem',
    document: 'Netdisk::DocumentItem',
  };

  get template() {
    return FileTemplate;
  }

  get titles(): String {
    return this.$route.meta.title;
  }

  private onShow(): void {
    this.visibleTag = true;
  }

  private onCancel(): void {
    this.$emit('Cancel');
  }

  private statusChange(allSettled: any) {
    this.allSettled = allSettled;
  }

  private onOk(): void {
    if (!this.formData.desc || !this.files[0]) {
      this.$message.error('请输入文件描述');
      return;
    }
    if (this.allSettled) {
      this.setFormData();
      this.$emit('Submit', this.formData, this.data);
      this.data = [];
      this.formData = {};
      this.files = [];
    } else {
      this.$message.error('文件正在上传,请等待文件上传完成再提交');
    }
  }

  private setFormData(): void {
    this.files.map((item: any) => {
      let type: any = item.category_type;
      type = this.FileTypeObject.hasOwnProperty(type) ? this.FileTypeObject[type] : 'Netdisk::OtherItem';
      this.data.push({
        name: item.fileName,
        desc: this.formData.desc,
        attachment: {
          description: this.formData.desc,
          ...item,
        },
        type,
      });
    });
  }

  private addTag(): void {
    if (this.tagName) {
      let tag: ITag = { name: this.tagName };
      this.tags.push(tag);
    }
    this.tagName = '';
    this.visibleTag = false;
  }

  private deleteTag(index: number): void {
    this.tags.splice(index, 1);
  }
  private onSuccess(file: IFile) {
    this.allSettled ? this.files.push(file) : '';
  }

  private onCreate() {}
}
</script>

<template lang="pug">
a-modal.modal(:visible='visible', 
              :title='title', 
              :width='width',
              @ok='onOk', 
              @cancel='onCancel')
  a-form-model.form(ref='formData', :model='formData')
    a-row.row(:span='21')
      a-col.col(:span='21')
        span.title 文件属性:
        span.title {{ titles }}
    //- 文件上传
    .upload-file
      FileUploader(v-model='files'
                  @success='onSuccess',
                  @statusChange="statusChange"
                  :limitMb="800"
                  style='width: 100%')
    //- 文件夹描述
    a-form-item(label='文件描述', prop='desc')
      a-textarea(v-model='formData.desc', type='text', key='desc', placeholder='请输入  文件描述' :rows="4")

    //- 文件夹标签
      a-row.row(:span='21')
        a-col.col(:span='21')
          span 文件夹标签
        a-col.col(:span='21')
          .tags
            a-tag.tag(v-for='(item, index) in tags')
              | {{ item.name }}
              a-icon.icon-close(type='close-circle', theme='filled', @click='deleteTag(index)')
            a-icon.icon-add(type='plus-circle', theme='filled', @click='onShow')
          Popover.popover(v-model='visibleTag', title='添加文件标签')
            template(slot='main')
              .tag-title * 标签
              a-input(v-model='tagName', type='text', placeholder='请输入标签')
            template(slot='footer')
              a-button.tag-button(type='primary', @click='addTag') 添加
      //- 可见性
      .mode-box
        .title 可见性
        a-radio-group.box(v-model='formData.radio')
          .mode
            a-icon.icon(type='lock')
            .content
              p 公开模式
              p 所以专业可见
            .radio
              a-radio(value='私有')
          .mode
            a-icon.icon(type='lock')
            .content
              p 公开模式
              p 所以专业可见
            .radio
              a-radio(value='公有')
      //- 可见专业
      .visible-box
        .title 
          .left 可见专业
          .right 
            a-icon(type='setting')
            span 设置
        .content 无
      //- 同步
      .synchronize
        .title 同步至
        a-checkbox-group.checkbox-list
          a-checkbox(value='专业资源') 专业资源
          a-checkbox(value='课程资源') 课程资源
        .prompt
          a-icon.icon(type='exclamation-circle')
          .text 申请同步后，需管理员审核通过，可在文件详情中查看申请情况与位置。
</template>

<style lang="stylus" scoped>
.modal
  .form
    .row
      .col
        margin-bottom 14px
        .title
          margin-right 10px
          color #3DA8F5
          font-size 14px
          font-family PingFangSC-Medium, PingFang SC

.upload-file
  margin-bottom 20px

.synchronize
  margin-top 20px
  color #383838
  font-weight 400
  font-size 14px
  font-family PingFangSC-Regular, PingFang SC
  .title, .checkbox-list
    margin-bottom 8px
  .prompt
    display flex
    flex-direction row
    padding-top 8px
    width 100%
    height 56px
    border 1px solid #E8E8E8
    border-radius 3px
    background #F5F5F5
    .icon
      margin 0 10px
    span
      width 90%
      color #383838
      font-weight 400
      font-size 14px
      font-family PingFangSC-Regular, PingFang SC

.visible-box
  margin-top 20px
  .title
    display flex
    flex-direction row
    justify-content space-between
    .right
      color #3DA8F5
      span
        margin-left 2px
  .content
    margin-top 8px
    color #CCCCCC
    font-weight 400
    font-size 14px
    font-family PingFangSC-Regular, PingFang SC

.mode-box
  width 100%
  height 100%
  .title
    margin-bottom 8px
    color #383838
    font-weight 400
    font-size 14px
    font-family PingFangSC-Regular, PingFang SC
  .box
    display flex
    flex-direction row
    justify-content space-between
    .mode
      display flex
      flex-direction row
      justify-content space-between
      align-items center
      width 200px
      height 56px
      border-radius 3px
      background #FAFAFA
      .icon
        // position absolute
        // top 5px
        margin 0 15px
        width 5px
      .content
        width 80%
        // border 1px solid #3333
        color #383838
        font-weight 400
        font-size 14px
        font-family PingFangSC-Regular, PingFang SC
        p:nth-child(2)
          color #A6A6A6
      .radio
        // margin-right 10px
        height 100%
        line-height 55px

.tags
  display flex
  flex-direction row
  align-items center
  color #3DA8F5
  span
    font-weight 400
    font-size 14px
    font-family PingFangSC-Regular, PingFang SC
  .tag
    display flex
    justify-content center
    align-items center
    margin-right 8px
    min-width 86px
    height 28px
    border 1px solid rgba(61, 168, 245, 0.1)
    border-radius 20px
    background rgba(61, 168, 245, 0.1)
    .icon-close
      margin-left 6px
      color #3DA8F5
      cursor pointer

.tag-title
  margin-bottom 10px

.tag-button
  width 100%
</style>
