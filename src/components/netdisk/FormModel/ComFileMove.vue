<script lang="ts">
import ObjectBlock from '@/components/ObjectBlock.vue';
import ComFileMoveList from '@/components/netdisk/ComFileMoveList.vue';

import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import { netdiskOwnedFileMoveItemStore } from '@/store/modules/netdisk/owned/item.store';
import { netdiskTeacherItemFileMoveStore } from '@/store/modules/netdisk/teacher/item.store';

@Component({
  components: { ComFileMoveList },
})
export default class ComFileMove extends Vue {
  @Model('change', { type: Boolean, default: () => false }) private visible!: boolean;

  @Prop({ type: String, default: '' }) private prop!: string;
  @Prop({ type: Object, default: () => ({}) }) private store!: any;
  @Prop({ type: String, default: () => '' }) private title!: string;
  @Prop({ type: Number, default: () => 800 }) private width!: number;
  @Prop({ type: Number, default: () => 0 }) private sourceId!: number;
  @Prop({ type: String, default: () => 0 }) private sourceName!: string;

  @Watch('visible')
  async onchangevisible() {
    if (this.visible) {
      let { data } = await this.fileMoveStore.index({ q: { roots: true, type_in: ['Netdisk::FolderItem'] } });
      this.FolderArray = [];
      this.FolderArray.push(JSON.parse(JSON.stringify(data.items)));
    }
  }

  private id: number = 0;
  private depth: Number = 0;
  private activeId: Number = 0;
  private FolderArray: any = [];
  private parentFolderArray: any = [];
  private readonly bodyStyle: object = {
    margin: 0,
    padding: 0,
  };

  get pageTitle(): string | undefined {
    return this.$route.meta.title;
  }

  get OtherFileMoveStore(): IObject {
    return netdiskTeacherItemFileMoveStore;
  }

  get ownedFileMoveStore(): IObject {
    return netdiskOwnedFileMoveItemStore;
  }

  get parentId(): number | undefined {
    return +this.$route.params.ParentId || undefined;
  }

  get fileMoveStore(): IObject {
    if (this.pageTitle === '我上传的') {
      return this.ownedFileMoveStore;
    } else {
      return this.OtherFileMoveStore;
    }
  }

  private onOk(): void {
    this.$emit('sumbit', this.id);
  }

  private createFile(): void {
    this.$emit('create');
  }

  private onCancel(): void {
    this.$emit('cancel');
  }

  async mounted() {
    this.pageTitle === '我上传的'
      ? this.fileMoveStore.init({})
      : this.fileMoveStore.init({ parents: [{ type: this.sourceName, id: this.sourceId }] });
  }

  private selectChildrenFolder(id: number, childrendepth: number, type: string): void {
    let dom: any = window.document.querySelector('.file-list');
    dom.scrollLeft = dom.scrollWidth + 220;
    let depth = childrendepth + 1;
    if (this.FolderArray.length < depth) {
      this.setFolder(id);
    } else if (depth < this.FolderArray.length) {
      this.FolderArray.splice(depth);
      this.setFolder(id);
    } else if (depth === this.FolderArray.length) {
      this.FolderArray.splice(depth);
      this.setFolder(id);
    }
  }

  private setFolder(id: number): void {
    this.id = id;
    this.fileMoveStore
      .index({ page: 1, per_page: 1000, q: { children_of: id, type_in: 'Netdisk::FolderItem' } })
      .then((res: any) => {
        const { data } = res;
        this.FolderArray.push(JSON.parse(JSON.stringify(data.items)));
      });
  }
}
</script>

<template lang="pug">
a-modal.modal-file-move#move(@ok='onOk',  
                        :title='title', 
                        :width='width',
                        :visible='visible', 
                        @cancel='onCancel'
                        :bodyStyle='bodyStyle'           
                        )
  .file-list#file-list
    .roots(v-if="false")
      .title 课程列表
      .root-list
        .box(v-for="item in parentFolderArray" :key="item.id" 
            :class="{active: item.id === activeId }") {{ item.name }}
    template(v-for="item in FolderArray")
      ComFileMoveList(:store="store"
                      @create="createFile" 
                      :childrenFolderMap="item"
                      @changeChildren="selectChildrenFolder")
</template>

<style lang="stylus" scoped>
.modal-file-move
  .file-list
    direction ltr
    overflow scroll
    overflow-x scroll
    overflow-y scroll
    display flex
    flex-direction row
    height 558px
    .roots
      flex-shrink 0
      overflow hidden
      height 100%
      border-right 1px solid #E5E5E5
      .title
        width 220px
        padding 0px 16px
        width 220px
        height  40px
        line-height 50px
        font-size 12px
        font-family PingFangSC-Regular, PingFang SC
        font-weight 400
        color #808080
      .root-list
        width 220px
        overflow scroll
        overflow-x hidden
        overflow-y scroll
        height 100%
        .box
          overflow hidden
          text-overflow ellipsis
          white-space nowrap
          cursor pointer
          line-height 40px
          box-sizing border-box
          width 220px
          height  40px
          background #FFFFFF
          font-size 14px
          font-family PingFangSC-Regular, PingFang SC
          font-weight 400
          color #383838
          padding 0px 16px 0px 16px



.active
  background #F5F5F5 !important
</style>
