<script lang="ts">
import { IBreadcrumb } from '@/views/netdisk/course_set/Index.vue';
import { any, path } from 'lodash/fp';
import { isObject } from 'util';
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';

// 资源面包屑
@Component({
  components: {},
})
export default class ComBreadcrumb extends Vue {
  @Prop({ type: Object, default: () => {} }) private store!: any;
  @Prop({ type: Array, default: () => [] }) private topBreadcrumb!: [];
  @Prop({ type: Array, default: () => [] }) private breadcrumbData!: [];
  @Prop({ type: Array, default: () => undefined }) private parentBreadcrumb!: [];
  @Prop({ type: Object, default: () => undefined }) private parentStore!: IObject;

  get path(): string {
    return this.$route.path;
  }

  get breadcrumb(): Array<IBreadcrumb> {
    return this.topBreadcrumb.concat(this.breadcrumbData);
  }

  private setParentBreadcrumb(): void {
    this.breadcrumbData.splice(0);
    this.$router.push({});
  }

  private onShow(val: string, id: any, index: number): void {
    index === 0 ? this.setParentBreadcrumb() : this.$router.push({ query: { parentId: id } });
  }
}
</script>

<template lang="pug">
.comBreadcrumb-container
  a-breadcrumb.breadcrumb(separator='>')
    a-breadcrumb-item.breadcrumb-item(v-for='(item, index) in breadcrumb', 
                                      :key='item.name'
                                      @click="onShow(item.path, item.id)"
                                      )
      span(@click="onShow(item.path, item.id, index)") {{ item.name }}
</template>

<style lang="stylus" scoped>
.comBreadcrumb-container
  .breadcrumb
    height 100%
    width 100%
    // margin-top 20px
    .breadcrumb-item
      font-size 14px
      font-family PingFangSC-Medium, PingFang SC
      cursor pointer
      font-weight  400
      color #383838
</style>
