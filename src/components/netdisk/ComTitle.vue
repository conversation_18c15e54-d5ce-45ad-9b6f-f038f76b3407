<script lang="ts">
import { IItem } from '@/types/model';
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { IUploadFile } from '@/components/netdisk/FormModel/ComUploadFile.vue';

import ComStudentList from '../teaching/teacher/ComStudentList.vue';
import ThesisPreview from '@/views/pt/activity/practice/ThesisPreview.vue';
import ComCreateFile from '@/components/netdisk/FormModel/ComCreateFile.vue';
import ComUploadFile from '@/components/netdisk/FormModel/ComUploadFile.vue';

// 资源 标题
@Component({
  components: { ComCreateFile, ComUploadFile },
})
export default class ComTitle extends Vue {
  @Prop({ type: Number }) private sourceId!: number;
  @Prop({ type: Object, default: () => {} }) private store!: IObject;
  @Prop({ type: Object, default: () => {} }) private parentStore!: IObject;
  @Prop({ type: Object, default: () => {} }) private fileFormData!: object;
  @Prop({ type: Boolean, default: () => true }) private visibleShow!: boolean;
  @Prop({ type: Boolean, default: () => false }) private editFileStaue!: boolean;
  @Prop({ type: String, default: () => '' }) private readonly netdiskTitle!: string;
  @Prop({ type: Boolean, default: () => false }) private readonly hideDisplayButton!: boolean; // 隐藏或显示 创建文件夹按钮和上传按钮

  @Watch('editFileStaue')
  onChangEditFileStaue() {
    this.Dodaltitle = '修改文件';
    this.visible = !this.visible;
    this.formData = this.fileFormData;
  }

  private formData: IItem = {};
  private authority: string = '';
  private visible: boolean = false;
  private formDataFile: IObject = {};
  private Dodaltitle: String = '创建文件';
  private visibleUpload: boolean = false;

  get title(): string {
    return this.$route.meta.title;
  }

  get parentId(): number {
    return +this.$route.query.parentId;
  }

  mounted() {
    this.$parent.$on('changeAuthority', (val: string) => {
      this.authority = val;
    });
  }

  private onFileEdit(val: IItem): void {
    this.$emit('fileEdit', val);
    this.visible = false;
  }

  private onCancel(): void {
    this.visible = false;
    this.visibleUpload = false;
  }

  private async onSubmit(val: any): Promise<void> {
    await this.$emit('create', val);
    this.formData = {};
    this.visible = false;
  }

  private onUploadFile(val: IUploadFile, data: any): void {
    this.$emit('uploadFile', val, data);
    this.visibleUpload = false;
  }

  private onShow(): void {
    this.authority === 'read_only' ? this.$message.warning('无文件夹添加权限') : this.setCreatedFileState();
  }

  private setCreatedFileState() {
    this.formData = {};
    this.Dodaltitle = '创建文件';
    this.visible = true;
  }

  private onShowFileMode(): void {
    this.authority === 'read_only' ? this.$message.warning('无文件上传权限') : (this.visibleUpload = true);
  }
}
</script>

<template lang="pug">
.comtitle-container 
  .title {{ netdiskTitle || title }}
  .operating
    slot(name='query')
    span.Icon-box.between(@click='onShow' v-if="parentId || hideDisplayButton")
      a-icon.icon(type='plus-circle')
      span 创建文件夹
    span.Icon-box(@click='onShowFileMode' v-if="parentId || hideDisplayButton")
      a-icon.icon(type='cloud-upload')
      span 上传
  ComCreateFile(v-model='visible', 
                :title='Dodaltitle', 
                :formData='formData',
                @Submit='onSubmit', 
                @Cancel='onCancel'
                @fileEdit="onFileEdit"
              )
  ComUploadFile(v-model='visibleUpload', title='文件上传', @Submit='onUploadFile', @Cancel='onCancel')
</template>

<style lang="stylus" scoped>
.comtitle-container
  display flex
  flex-direction row
  justify-content space-between
  width 100%
  height 100%
  .title
    font-size 18px
    color #383838
    font-weight 500
    font-family PingFangSC-Medium, PingFang SC
  .operating
    .Icon-box
      display inline-block
      color #3DA8F5
      font-weight 500
      font-size 14px
      font-family PingFangSC-Medium, PingFang SC
      cursor pointer
      .icon
        margin-right 5px
    .between
      margin 0px 26px
</style>
