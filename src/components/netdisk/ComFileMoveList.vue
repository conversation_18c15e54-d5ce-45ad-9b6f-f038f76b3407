<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import ComCreateFile from '@/components/netdisk/FormModel/ComCreateFile.vue';
import { IItem } from '@/types/model';

@Component({
  components: { ComCreateFile },
})
export default class ComFileMoveList extends Vue {
  @Prop({ type: Object, default: () => {} }) private store!: object;
  @Prop({ type: Array, default: () => [] }) private childrenFolderMap!: [];
  @Prop({ type: Boolean, default: () => false }) private readonly loading!: boolean;

  private activeId: Number = 0;
  private formData: IItem = {};

  private selectRootFolder(id: Number, depth: Number, type: string): void {
    this.activeId = id;
    this.$emit('changeChildren', id, depth, type);
  }
}
</script>

<template lang="pug">
.com-file-move-list
  .title 文件夹
  .folder-list
    .folder-box(v-for="item in childrenFolderMap" 
                :key='item.id'
                :class="{active: item.id === activeId }"
                @click="selectRootFolder(item.id, item.depth,item.type)"
                )
      template
            img.img(src="@/assets/images/file/image.png" v-if="item.type  === 'Netdisk::ImageItem'")
            img.img(src="@/assets/images/file/video.png" v-if="item.type  === 'Netdisk::VideoItem'")
            img.img(src="@/assets/images/file/webpage.png" v-if="item.type  === 'Netdisk::UrlItem'")
            img.img(src="@/assets/images/file/text.png" v-if="item.type  === 'Netdisk::OtherItem'")
            img.img(src="@/assets/images/file/folder.png" v-if="item.type  === 'Netdisk::FolderItem'")
            img.img(src="@/assets/images/file/word.png" v-if="item.type  === 'Netdisk::DocumentItem'")
      a-tooltip(placement="topLeft")
        template(slot="title")
          span {{ item.name }}
        span {{item.name}}

</template>

<style lang="stylus" scoped>
.com-file-move-list
  flex-shrink 0
  overflow hidden
  width 260px
  height 100%
  border-right 1px solid #E5E5E5
  .Icon,.title
    width 100%
    height 30px
    width 100%
    line-height 40px
    font-size 18px
    padding 0px 17px
    color #808080
  .title
    font-size 12px
    font-family PingFangSC-Regular, PingFang SC
    font-weight 400
    margin-bottom 10px
  .folder-list
    cursor pointer
    overflow scroll
    overflow-x hidden
    overflow-y scroll
    width 100%
    height 100%
    // border 1px solid red
    .folder-box
      padding 0px 0px 0px 16px
      width 100%
      height 40px
      line-height 40px
      background #FFFFF
      // border 1px solid #dddd
      overflow hidden
      text-overflow ellipsis
      white-space nowrap
      span
        font-size 14px
        font-family PingFangSC-Regular, PingFang SC
        font-weight 400
        color #383838
        margin-left 12px
      .img
        width 19px
        height 19px

.active
  background #F5F5F5 !important
</style>
