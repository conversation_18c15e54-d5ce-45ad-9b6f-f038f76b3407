<script lang="ts">
import { IFile } from '@/models/file';
import { IItem } from '@/types/model';
import { Component, Vue, Prop, Model } from 'vue-property-decorator';
import ComNetdiskScheduleCourseItemPreviewer from './ComNetdiskScheduleCourseItemPreviewer.vue';
@Component({
  components: {
    ComNetdiskScheduleCourseItemPreviewer,
  },
})
export default class ComNetdiskItemPreviewer extends Vue {
  @Model('input', { type: Boolean, default: false }) visible!: boolean;
  @Prop({ type: Object, required: true }) netdiskItem!: IItem;

  get localVisible() {
    return this.visible;
  }

  set localVisible(val: boolean) {
    this.$emit('input', val);
  }
}
</script>

<template lang="pug">
.com-netdisk-item-previewer(v-if='visible')
  ComNetdiskScheduleCourseItemPreviewer(
    v-if='netdiskItem.type === "Netdisk::ScheduleCourseItem"',
    v-model='localVisible',
    :netdiskItem='netdiskItem',
  )
  FilePreviewer(
    v-else
    v-model="localVisible"
    :attachment="netdiskItem.attachment"
    :title="netdiskItem.attachment.fileName"
    :heidDispalyDownloadButton="netdiskItem.netdisk_permit === 'read_write' ? true : false"
  )
</template>

<style lang="stylus" scoped></style>
