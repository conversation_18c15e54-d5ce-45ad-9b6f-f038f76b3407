<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class ComFileItem extends Vue {
  @Prop({ type: String, default: '' }) prop!: string;
  @Prop({ type: Object, default: () => {} }) private readonly item!: IObject;

  private fileIconMap: Map<string, string> = new Map([
    ['Netdisk::OtherItem', '@/assets/images/file/text.png'],
    ['Netdisk::ImageItem', '@/assets/images/file/image.png'],
    ['Netdisk::VideoItem', '@/assets/images/file/video.png'],
    ['Netdisk::UrlItem', '@/assets/images/file/webpage.png'],
    ['Netdisk::FolderItem', '@/assets/images/file/folder.png'],
    ['Netdisk::DocumentItem', '@/assets/images/file/word.png'],
  ]);

  get src(): any {
    return this.fileIconMap.get(this.item.type) || '';
  }

  mounted() {
    this.fetchData();
  }

  fetchData() {}

  private click(): void {
    this.$emit('click', this.item);
  }
}
</script>

<template lang="pug">
.com-file-item(@click.stop="click")
  //- img.img(:src="src" v-if="src")
  template
    img.img(src="@/assets/images/file/image.png" v-if="item.type  === 'Netdisk::ImageItem'")
    img.img(src="@/assets/images/file/video.png" v-if="item.type  === 'Netdisk::VideoItem'")
    img.img(src="@/assets/images/file/webpage.png" v-if="item.type  === 'Netdisk::UrlItem'")
    img.img(src="@/assets/images/file/text.png" v-if="item.type  === 'Netdisk::OtherItem'")
    img.img(src="@/assets/images/file/folder.png" v-if="item.type  === 'Netdisk::FolderItem'")
    img.img(src="@/assets/images/file/word.png" v-if="item.type  === 'Netdisk::DocumentItem'")
  span.name {{ item.name }}
</template>

<style lang="stylus" scoped>
.com-file-item
  display flex
  flex-direction row
  align-items center
  width 220px
  height 40px
  color #383838
  font-size 14px
  cursor pointer
  font-weight 400
  white-space nowrap
  text-overflow ellipsis
  padding 0px 16px 0px 20px
  font-family PingFangSC-Regular, PingFang SC
  margin 2px 0px
  .img
    height 20px
    width 20px
    margin-right 5px
.com-file-item:hover
  background #efefef
</style>
