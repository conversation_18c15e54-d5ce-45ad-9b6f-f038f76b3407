<script lang="ts">
import { chownSync } from 'fs';
import { Component, Vue, Prop } from 'vue-property-decorator';
import ComFileItem from './ComFileItem.vue';

@Component({
  components: { ComFileItem },
})
export default class ComFileItemList extends Vue {
  @Prop({ type: String, default: '' }) prop!: string;
  @Prop({ type: Array, default: () => [] }) private readonly list!: [];
  @Prop({ type: String, default: () => '' }) private readonly title!: string;
  @Prop({ type: Number, default: () => 0 }) private readonly index!: number;
  state: string = '';

  private activeId: number = 0;

  mounted() {
    this.fetchData();
  }
  fetchData() {}

  private clickFileItem(item: any): void {
    this.activeId = item.id;
    item.type === 'Netdisk::FolderItem' || item.depth === 'root'
      ? this.$emit('clickFileItem', item, this.index)
      : this.$message.success('已选中当前文件');
  }
}
</script>

// 文件加
<template lang="pug">
.com-file-item-list
  .title
    span {{ title }}
  .content
    template(v-if="list.length")
      ComFileItem(
        v-for="item in list" 
        :key="item.id"  
        :item="item" 
        :class="{activeClass: item.id === activeId}"  
        @click="clickFileItem(item)"
      )
    template(v-else)
      Empty(desc="暂无相关资源")
</template>

<style lang="stylus" scoped>
.com-file-item-list
  height 100%
  min-width 220px
  border-right 1px solid #E5E5E5
  .title
    height 30px
    font-weight bold
    line-height  30px
    padding-left 20px
    border-bottom 1px solid #E5E5E5
  .content
    overflow scroll
.activeClass
  background  #efefef
  color #3DA8F5
</style>
