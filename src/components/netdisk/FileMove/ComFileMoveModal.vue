<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import ComFileItemList from './ComFileItemList.vue';

import { netdiskOwnedItemStore } from '@/store/modules/netdisk/owned/item.store';
import { netdiskTeacherCourseDirsStore } from '@/store/modules/netdisk/teacher/course_dirs.store';

@Component({
  components: { ComFileItemList },
})
export default class ComFileMoveModal extends Vue {
  @Prop({ type: String, default: '' }) prop!: string;
  @Prop({ type: String, default: () => '800px' }) private readonly width!: string;
  private visible: boolean = true;
  private bodyStyle: object = {
    margin: 0,
    padding: 0,
  };

  private nitdiskName: string = '';

  get courseDirsStore(): IObject {
    return netdiskTeacherCourseDirsStore;
  }

  get ownedItemStore(): IObject {
    return netdiskOwnedItemStore;
  }

  // @Watch('nitdiskName')
  // onchange() {
  //   console.log(this.nitdiskName);
  //   this.getRootInfo();
  // }

  get store(): IObject {
    switch (this.nitdiskName) {
      case '课程资源':
        return this.courseDirsStore;
      case '个人资源':
        return this.ownedItemStore;
      default:
        return {};
    }
  }

  private taskchunk: any = [];

  private FileList: any = [
    [
      {
        id: 1,
        name: '课程资源',
        depth: 'root',
      },
      {
        id: 3,
        name: '个人资源',
        depth: 'root',
      },
    ],
  ];

  mounted() {
    this.fetchData();
    this.ownedItemStore.init();
    this.courseDirsStore.init();
  }

  private async getRootInfo(): Promise<void> {
    let { data } = await this.store.index({ q: { roots: true } });
    this.FileList.push(data.items);
  }

  private async getItemInfo(parentsId: number): Promise<void> {
    let { data } = await this.store.index({ q: { children_of: parentsId } });
    this.FileList.push(data.items);
  }

  private handleOk(): void {
    this.visible = true;
  }

  private clickFileItem(item: any, index: number): void {
    this.FileList.splice(index + 1);
    index + 1 > this.taskchunk.length
      ? this.taskchunk.push(item)
      : index + 1 < this.taskchunk.length
      ? this.taskchunk.splice(index + 1)
      : this.taskchunk.splice(index, 1, item);
    this.nitdiskName = this.taskchunk[0].name;
    index + 1 === 1 ? this.getRootInfo() : this.getItemInfo(item.id);
  }

  fetchData() {}
}
</script>

<template lang="pug">
a-modal.modal(
  title="资源导入"
  :visible="visible"
  @ok="handleOk"
  @cancel=" visible = false"
  :width="width"
  :bodyStyle= 'bodyStyle'   
)
  .modal-content
    ComFileItemList(
      v-for="(item,index) in FileList" 
      :key="index" 
      :index="index"
      :title="index === 0 ? '资源库' : '文件夹'" 
      :list="item"
      @clickFileItem="clickFileItem"
      )
</template>

<style lang="stylus" scoped>
.modal
  .modal-content
    width 100%
    height 558px
    display flex
    overflow scroll
    flex-direction row
</style>
