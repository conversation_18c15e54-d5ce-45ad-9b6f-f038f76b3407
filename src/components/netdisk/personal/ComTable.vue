<script lang="ts">
import UserSelector from '@/components/hr/UserSelector.vue';
import FilePreviewer from '@/components/global/FilePreviewer.vue';
import ComBreadcrumb from '@/components/netdisk/ComBreadcrumb.vue';
import ComFileMove from '@/components/netdisk/FormModel/ComFileMove.vue';

import { IFile } from '@/models/file';
import { IBreadcrumb } from '@/views/netdisk/course_set/Index.vue';
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { TaIndexViewTabInterface } from '@/components/global/TaIndex';
import score from '@/models/access/score';
import { IItem } from '@/types/model';
import ComNetdiskItemPreviewer from '../ComNetdiskItemPreviewer.vue';

// 文件 table
@Component({
  components: { FilePreviewer, ComFileMove, ComBreadcrumb, UserSelector, ComNetdiskItemPreviewer },
})
export default class ComNetdiskTable extends Vue {
  @Prop({ type: Number }) private readonly sourceId!: number;
  @Prop({ type: Object, default: () => {} }) private store!: any;
  @Prop({ type: Array, default: () => [] }) private FolderMap!: [];
  @Prop({ type: String, default: () => '' }) private sourceName!: string;
  @Prop({ type: String, default: () => '' }) private readonly parentPath!: '';
  @Prop({ type: Array, default: () => undefined }) private parentBreadcrumb!: [];
  @Prop({ type: Object, default: () => {} }) private parentStore!: IObject;
  @Prop({ type: Boolean, default: false }) private editable!: boolean;

  @Watch('parentId')
  onchangParentId() {
    this.fetchData();
    // this.parentId ? this.getChildrenBaedcrumb() : this.getParentBaedcrumb();
    this.parentId ? this.getChildrenBaedcrumb() : '';
  }

  @Watch('sourceId')
  onchange() {
    this.fetchData();
  }

  private activeItem: Partial<IItem> = {};
  private id: number | string = '';
  private visibleInfo: boolean = false;
  private selectedTeacherIds: Array<number> = [];
  private visibleManageInfo: boolean = false;
  private selectedManageTeacherIds: Array<number> = [];
  private file: IItem = {};
  private breadcrumb: any = [];
  private authority: string = '';
  private visible: boolean = false;
  private activityRowId: Number = 0;
  private fileMoveVisible: Boolean = false;
  private fileViewVisible: boolean = false;
  private tabs: TaIndexViewTabInterface[] = [
    { label: '', key: 'execute', num: 0, background: '', color: '', mode: 'table' },
  ];
  private searcherSimpleOptions: Array<any> = [
    {
      key: 'name',
      label: '资源名称',
      type: 'string',
    },
    {
      key: 'desc',
      label: '资源描述',
      type: 'string',
    },
  ];

  get config() {
    return {
      store: this.store,
      recordName: '资源',
      searcherSimpleOptions: this.searcherSimpleOptions,
    };
  }

  get title(): String {
    return this.$route.meta.title;
  }

  get majorsId(): number | undefined {
    return +this.$route.params.majorsId || undefined;
  }

  get collegesId(): number | undefined {
    return +this.$route.params.collegeId || undefined;
  }

  get courseId(): number | undefined {
    return +this.$route.params.courseId || undefined;
  }

  get parentId(): number | undefined {
    return +this.$route.query.parentId || undefined;
  }

  mounted(): void {
    this.parentId ? this.getChildrenBaedcrumb() : '';
  }

  created(): void {
    this.fetchData();
  }

  private async getChildrenBaedcrumb(): Promise<void> {
    let { data } = await this.store.find(this.parentId);
    ({ netdisk_permit: this.authority } = data);
    this.$parent.$emit('changeAuthority', data.netdisk_permit);
    this.breadcrumb = this.store.record.complete_path;
  }

  private async getParentBaedcrumb() {
    let { data } = await this.parentStore.find(this.sourceId);
    this.$parent.$emit('changeAuthority', data.netdisk_permit);
  }

  private onCancel(): void {
    this.activityRowId = 0;
  }

  private onShow(val: any): void {
    this.visible = true;
    this.activityRowId = val.id;
  }

  private openMoveWindow(): void {
    this.fileMoveVisible = true;
    this.$emit('openFolderModel');
  }

  private cancelFileMoveModel() {
    this.fileMoveVisible = false;
  }

  private getChildrenInfo(id: number) {
    this.$emit('getChildrenInfo', id);
  }

  private EditFileInfo(record: IItem): void {
    this.activeItem = record;
    this.fileViewVisible = true;
  }

  // 文件移动
  private sumbitFileMoveInfo(id: number): void {
    this.fileMoveVisible = false;
    this.store.update({ parent_id: id, id: this.file.id });
    this.fetchData();
  }

  private fileSize(file: IFile): string {
    return !file ? '' : `${Math.ceil(file.fileSize / (1024 * 1024)).toFixed(1)} MB`;
  }

  private onDelete(val: IItem): void {
    val.read_only ? this.$message.error('该文件不允许删除') : this.$emit('delete', val.id);
    this.activityRowId = 0;
  }

  private clickRow(record: IItem): void {
    record.type !== 'Netdisk::FolderItem'
      ? this.EditFileInfo(record)
      : this.$router.push({ path: `${this.parentPath}`, query: { parentId: record.id } });
  }

  private onEdit(scope: IItem): void {
    this.activityRowId = 0;
    scope.netdisk_permit !== 'read_write'
      ? this.$message.error('该文件只能被阅读,不可修改')
      : this.$emit('fileEdit', scope);
  }

  // 文件移动
  private fileMoveShow(scope: IItem): void {
    this.file = scope;
    this.activityRowId = 0;
    scope.netdisk_permit === 'read_write' ? this.openMoveWindow() : this.$message.error('无操作权限');
  }

  private fetchData(): void {
    this.parentId ? this.setStoreInit() : this.setOwnStoreInit();
  }

  private setStoreInit(): void {
    this.store.init({
      parents: [{ type: this.sourceName, id: this.sourceId }],
      params: { q: { children_of: this.parentId } },
    });
  }

  // 初始化 我上传的 和共享我的页面 store
  private setOwnStoreInit(): void {
    this.sourceName
      ? this.store.init({ parents: [{ type: this.sourceName, id: this.sourceId }], params: { q: { roots: true } } })
      : this.store.init({ params: { q: { roots: true } } });
  }

  private auth(val: string): string {
    return val === 'read_write' ? '读写' : val === 'read_only' ? '只读' : '无权限';
  }

  private changeSelect(ids: Array<number>): void {
    this.selectedTeacherIds = ids;
  }

  private changeManageSelect(ids: Array<number>): void {
    this.selectedManageTeacherIds = ids;
  }

  private async onConfirm(): Promise<void> {
    this.store
      .update({
        id: this.id,
        shared_teacher_ids: this.selectedTeacherIds,
      })
      .then(() => {
        this.$message.success('成功共享资源');
      })
      .catch(() => {
        this.$message.success('共享资源失败');
      });
    this.visibleInfo = false;
  }

  private async onManageConfirm(): Promise<void> {
    this.store
      .update({
        id: this.id,
        manage_teacher_ids: this.selectedManageTeacherIds,
      })
      .then(() => {
        this.$message.success('成功设置资源管理者');
      })
      .catch(() => {
        this.$message.success('设置资源管理者失败');
      });
    this.visibleManageInfo = false;
  }

  private shared(val: any) {
    if (val.netdisk_permit === 'read_write') {
      this.id = val.id;
      this.selectedTeacherIds = val.shared_teacher_ids;
      this.activityRowId = 0;
      this.visibleInfo = true;
    } else {
      this.$message.warning('无权限共享');
      this.visibleInfo = false;
    }
  }

  private manage(val: any) {
    if (val.netdisk_permit === 'read_write') {
      this.id = val.id;
      this.selectedManageTeacherIds = val.manage_teacher_ids;
      this.activityRowId = 0;
      this.visibleManageInfo = true;
    } else {
      this.$message.warning('无权限进行管理');
      this.visibleManageInfo = false;
    }
  }
}
</script>

<template lang="pug">
.comTable-container
  TaIndexView(:tabs="tabs" :config="config" @onShow="clickRow ")
    template(#header)
       ComBreadcrumb(:breadcrumbData='breadcrumb' :topBreadcrumb="parentBreadcrumb")
       slot(name='header')
    template(slot="table")
      a-table-column(title='名称',  key="fileName" ellipsis="true" :width='240')
        template(slot-scope="scope")
          a-tooltip(:title="scope.name")
            img.img(src="@/assets/images/file/image.png" v-if="scope.type  === 'Netdisk::ImageItem'")
            img.img(src="@/assets/images/file/video.png" v-if="scope.type  === 'Netdisk::VideoItem'")
            img.img(src="@/assets/images/file/webpage.png" v-if="scope.type  === 'Netdisk::UrlItem'")
            img.img(src="@/assets/images/file/text.png" v-if="scope.type  === 'Netdisk::OtherItem'")
            img.img(src="@/assets/images/file/folder.png" v-if="scope.type  === 'Netdisk::FolderItem'")
            img.img(src="@/assets/images/file/word.png" v-if="scope.type  === 'Netdisk::DocumentItem'")
            span {{ scope.name }}
      a-table-column(title='描述', dataIndex="desc" :width='360' key="desc" ellipsis="true")
        template(slot-scope="desc")
          a-tooltip(:title="desc")
            span {{ desc }}
      a-table-column(title='大小', align='center' key="fileSize" :width='110')
        template(slot-scope="scope")
          span
            | {{scope.attachment&&fileSize(scope.attachment)||scope.children_count&&`${scope.children_count}个子项`||"-"}}
      a-table-column(
        title='创建人',
        align='center',
        dataIndex='creator_name',
        :width='120',
        v-if='title !== "我上传的"',
      )
      a-table-column(title='最近使用', key="date" ellipsis="true" :width='170')
        template(slot-scope="scope")
          span {{ scope.updated_at | format("YYYY-MM-DD") }}
      a-table-column(title='所属课程', dataIndex='source_name', key="cuurse" :width='170', v-if='title === "课程资源"',)
      a-table-column(title='所属标签', :width='170', dataIndex="tag" key="tag" v-if='title === "专业资源"',)
      a-table-column(title='引用', align='center', key="count" :width='130',)
        template(slot-scope='scope')
          .Icon
            a-icon(type='link')
            span {{  }}
      a-table-column(title='权限', dataIndex='netdisk_permit', :width='120' )
          template(slot-scope="scope")
            span {{ auth(scope) }}
      a-table-column( title='筛选', key="filter", :width='80')
        template(slot-scope='scope' v-if="scope.netdisk_permit === 'read_write'")
          .table-hover-col
            IconTooltip(icon='down', tips='' @click="onShow(scope)")
          Popover(:visible="activityRowId === scope.id" :key="scope.id" @onCancel="onCancel" title='文件夹菜单')
            .popover(slot='main')
              //- .list-item
              //-   a-icon(type='copy')
              //-   span 复制文件夹至
              .list-item(@click="shared(scope)")
                a-icon(type='link')
                span {{ `文件共享 (${scope.shared_teacher_count})`  }}
              .list-item(@click="manage(scope)")
                a-icon(type='link')
                span {{ `文件管理 (${scope.manage_teacher_count})`  }}
              .list-item(@click="fileMoveShow(scope)")
                a-icon(type='folder')
                span 文件移动至
              .list-item(@click="onEdit(scope)")
                a-icon(type='edit')
                span(v-if="scope.type === 'Netdisk::FolderItem'") 编辑文件夹
                span(v-else) 编辑文件
              .list-item(@click="onDelete(scope)")
                a-icon(type='delete')
                span(v-if="scope.type === 'Netdisk::FolderItem'") 删除文件夹
                span(v-else) 删除文件
              .border
              .list-item
                a-icon(type='lock')
                span 私有模式
  ComNetdiskItemPreviewer(v-model='fileViewVisible', :netdiskItem='activeItem')
  ComFileMove(v-model="fileMoveVisible",
              title="通过并添加文件夹至"
              @cancel="cancelFileMoveModel"
              @sumbit="sumbitFileMoveInfo"
              @getChildrenInfo="getChildrenInfo"
              v-bind:FolderMap="FolderMap"
              :store="store"
              :sourceName="sourceName"
              :sourceId="sourceId"
              )
  MainModal(
    v-model="visibleInfo"
    title="选择共享人员"
    :width="1200")
    .modal-row(slot="footer")
      .count
        span 已选择
        span.text-primary(style="margin: 0px 4px") {{ selectedTeacherIds.length || 0 }}
        span 人
      a-button(type="primary" size="large" style="width: 100px" @click="onConfirm") 确定
    UserSelector(
      ref="memberSelector"
      :userIds="selectedTeacherIds"
      style="padding: 0px 20px"
      @change="changeSelect")

  MainModal(
    v-model="visibleManageInfo"
    title="选择资源管理人员"
    :width="1200")
    .modal-row(slot="footer")
      .count
        span 已选择
        span.text-primary(style="margin: 0px 4px") {{ selectedManageTeacherIds.length || 0 }}
        span 人
      a-button(type="primary" size="large" style="width: 100px" @click="onManageConfirm") 确定
    UserSelector(
      ref="memberSelector"
      :userIds="selectedManageTeacherIds"
      style="padding: 0px 20px"
      @change="changeManageSelect")
</template>

<style lang="stylus" scoped>
.comTable-container
  width 100%
  // overflow scroll
.Icon
  color #3DA8F5
  span
    margin-left 5px
.list-item
  height 40px
  line-height 40px
  cursor pointer
  span
    margin-left 14px
.img
  position relative
  top -2px
  margin-right 5px
  width 22px
  height 20px
.list-item:active
  background #f0fbff
  color #00ABFC
.border
  margin-bottom 10px
  border-bottom 1px solid #E5E5E5
.pagination
  margin 20px 0px
</style>
