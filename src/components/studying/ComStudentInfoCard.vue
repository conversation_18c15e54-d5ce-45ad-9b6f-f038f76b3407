<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class ComStudentInfoCard extends Vue {
  @Prop({ type: String }) title!: string;
  @Prop({ type: Array }) userCardInfo!: IObject[];

  itemStyle(item: IObject) {
    if (item.isTopBorder) {
      return {
        borderTop: '1px solid #e3e3e3',
        marginTop: '20px',
      };
    }
  }
}
</script>

<template lang="pug">
.user-card-box
  TaTitleHeader.header(v-if='title', :title='title')
  .item-box
    slot(name='content')
      .item(v-for="item in userCardInfo" :style="itemStyle(item)")
        a-icon.a-icon(:type="item.icon")
        .item-title {{item.label}}
        .item-val {{item.val}}
</template>

<style lang="stylus" scoped>
.user-card-box
  background #FFFFFF
  box-shadow 0px 0px 10px 0px rgba(0, 0, 0, 0.08)
  border-radius 5px
  padding 12px 24px 22px 24px
.header
  border-bottom 1px solid #e3e3e3
.item-box
  .item
    padding-top 20px
    display flex
    align-content center
    .a-icon
      font-size 16px
      line-height 24px
      color #d8d8d8
      margin 2px 12px 0 0
    .item-title
      width 100px
      color #999
      font-size 16px
      margin-right 20px
    .item-val
      font-size 16px
      color #333
</style>
