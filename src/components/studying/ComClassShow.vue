<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { TaIndexViewConfigInterface } from '@/components/global/TaIndex';
import ComStudentTable from './ComStudentTable.vue';

@Component({
  components: {
    ComStudentTable,
  },
})
export default class ComClassShow extends Vue {
  @Prop({ type: String, default: '' }) prop!: string;
  @Prop({ type: String, default: '' }) role!: string;
  @Prop({ type: Object }) readonly ClassStore!: any;
  @Prop({ type: Object }) readonly StudentsStore!: any;

  private activeNaem: string = 'scoreDetails';

  private classArr = [
    { itemTitle: '所属学院', key: 'department_name' },
    { itemTitle: '专业名称', key: 'major_name' },
    { itemTitle: '班级名称', key: 'name' },
    { itemTitle: '班级人数', key: 'students_count' },
    { itemTitle: '辅导员', key: 'teacher_names' },
    { itemTitle: '所属年级', key: 'grade' },
  ];
  private status = [
    { text: '在校', value: '在校' },
    { text: '结业', value: '结业' },
    { text: '死亡', value: '死亡' },
    { text: '离校', value: '离校' },
    { text: '毕业', value: '毕业' },
    { text: '退学', value: '退学' },
    { text: '休学', value: '休学' },
    { text: '保留入学资格', value: '保留入学资格' },
    { text: '保留学籍', value: '保留学籍' },
  ];

  get steps() {
    const step = [{ title: '基本信息', key: 'scoreDetails' }];
    return step;
  }

  get breadcrumbs() {
    const bc = [
      { title: '班级管理', url: `/studying/${this.role}/classes` },
      { title: this.Class.name, url: '' },
    ];
    return bc;
  }

  get Class() {
    return this.ClassStore.record;
  }

  get config(): TaIndexViewConfigInterface {
    return {
      recordName: '',
      searcherSimpleOptions: [{ label: '名称', key: '', type: 'string' }],
      store: this.StudentsStore,
      mode: 'table',
      showPageSizeChanger: true,
    };
  }

  mounted() {
    this.ClassStore.init();
    this.ClassStore.find(this.$route.params.id);
    this.StudentsStore.reset();
  }
  onTaIndexViewMounted() {
    this.StudentsStore.init({
      params: { q: { adminclass_id_eq: this.$route.params.id } },
    });
  }
}
</script>

<template lang="pug">
.container
  StepToolbar.toolbar(
    :value='activeNaem',
    :breadcrumbs.sync='breadcrumbs',
    :steps='steps',
    :border='true',
    mode='tabs'
  )
  .class-card
    .header 班级详情
    .item-box
      .item-list(v-for='item in classArr')
        .item
          span.item-title {{ item.itemTitle }}
          span.item-val {{ Class[item.key] }}
  .table-class
    ComStudentTable(:store='StudentsStore', :role='role', :classStore='this.Class', @onMounted='onTaIndexViewMounted')
</template>

<style lang="stylus" scoped>
.toolbar {
  padding: 0 20px;
  background: #ffffff;
}

.table-class {
  width: calc(100% - 120px);
  margin: 0 auto;
  padding: 20px;
  background: #FFFFFF;
  margin-bottom: 20px;
}

.class-card {
  width: calc(100% - 120px);
  margin: 20px auto;
  padding: 20px;
  background: #FFFFFF;
  border-radius: 5px;
}

.item-box {
  display: flex;
  justify-content: space-between;
  align-content: center;
  flex-wrap: wrap;
  padding-top: 20px;
  border-top: 1px solid #e3e3e3;
  margin-top: 20px;
}

.item-list {
  width: 30%;
  margin-bottom: 20px;

  .item {
    font-size: 14px;
    color: #333;

    .item-title {
      color: #999;
      margin-right: 20px;
    }
  }
}
</style>
