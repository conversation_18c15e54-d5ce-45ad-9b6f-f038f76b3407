<script lang="ts">
import { ActiveStore } from '@/lib/ActiveStore';
import { Component, Vue, Prop } from 'vue-property-decorator';
import { TaIndexViewConfigInterface, TaIndexViewTabInterface } from '../global/TaIndex';
import ComSelectTime from '@/components/studying/ComSelectTime.vue';
import { Moment } from 'moment';
import TaIndexView from '../global/TaIndexView.vue';
import { IStudyingStudentDaily } from '@/types/model';

@Component({
  components: {
    ComSelectTime,
  },
})
export default class ComStatStudentDaily extends Vue {
  @Prop({ type: Object, required: true }) store!: ActiveStore;
  @Prop({ type: String, default: '记录列表' }) title!: string;

  summary: {
    coming_count: number | null;
    going_count: number | null;
    register_count: number | null;
  } = {
    coming_count: null,
    going_count: null,
    register_count: null,
  };

  get config(): TaIndexViewConfigInterface {
    return {
      recordName: '记录',
      searcherSimpleOptions: [
        { label: '学号', key: 'student_code', type: 'string' },
        { label: '姓名', key: 'student_name', type: 'string' },
        { label: '班级', key: 'student_adminclass_name', type: 'string' },
        { label: '学院', key: 'student_department_name', type: 'string' },
        { label: '专业', key: 'student_major_name', type: 'string' },
      ],
      searcherComplicatedOptions: [
        { label: '班级', key: 'student_adminclass_name', type: 'string' },
        { label: '学院', key: 'student_department_name', type: 'string' },
        { label: '专业', key: 'student_major_name', type: 'string' },
        { label: '性别', key: 'student_sex', type: 'string' },
        { label: '上课情况', key: 'register_count', type: 'number' },
        { label: '入校情况', key: 'coming_count', type: 'number' },
        { label: '出校情况', key: 'coming_count', type: 'number' },
      ],
      store: this.store,
      mode: 'table',
      showExport: true,
      showPageSizeChanger: true,
    };
  }

  initStore(start_at: Moment, end_at: Moment) {
    this.store.init({
      params: {
        q: {
          created_at_gteq: start_at.format('YYYY-MM-DD HH:mm:ss'),
          created_at_lteq: end_at.format('YYYY-MM-DD HH:mm:ss'),
        },
      },
    });
  }

  onTaIndexViewMounted() {
    this.initStore(this.$moment().startOf('day'), this.$moment().endOf('day'));
  }

  onTaIndexViewIndex() {
    this.store
      .sendCollectionAction({
        silence: true,
        action: 'summary',
        config: {
          params: {
            q: {
              ...(this.store.model.params || { q: {} }).q,
              ...(this.$refs.taIndexView as TaIndexView<IStudyingStudentDaily>).query,
            },
          },
        },
      })
      .then(res => {
        this.summary = res.data;
      });
  }

  timeChange(timeStr: string, mode: string, timeRange: Moment[]) {
    this.initStore(timeRange[0], timeRange[1]);
  }
  private tabs: TaIndexViewTabInterface[] = [
    { label: '全部', key: 'all', mode: 'table' },
    { label: '奉贤校区', key: 'fengxian', mode: 'table', query: { student_campus_eq: '奉贤校区' } },
    { label: '闵行校区', key: 'minhang', mode: 'table', query: { student_campus_eq: '闵行校区' } },
    { label: '普陀校区', key: 'putuo', mode: 'table', query: { student_campus_eq: '普陀校区' } },
  ];
}
</script>

<template lang="pug">
.com-stat-student-daily
  TaTitleHeader(:title='title')
  .header-box
    .title-box
      TaTitleHeader
        template(#title)
          span 统计数据
      ComSelectTime.time-selector(@change='timeChange')
    .number-box
      .item
        .item-title 入校人次
        .item-val {{ summary.coming_count }}
      .item
        .item-title 出校人次
        .item-val {{ summary.going_count }}
      .item
        .item-title 上课人次
        .item-val {{ summary.register_count }}
  .table-box
    TaIndexView(
      ref='taIndexView',
      :config='config',
      :tabs='tabs',
      :tabsLeftMargin='0',
      @mounted='onTaIndexViewMounted',
      @onIndex='onTaIndexViewIndex'
    )
      template(#table)
        a-table-column(title='日期', dataIndex='date')
        a-table-column(title='班级信息')
          template(slot-scope='scope, record')
            p 学院：{{ record.student.department_name }}
            p 专业：{{ record.student.major_name }}
            p 班级：{{ record.student.adminclass_name }}
        a-table-column(title='学生信息', dataIndex='student')
          template(slot-scope='student')
            p 姓名：{{ student.name }}
            p 学号：{{ student.code }}
            p 性别：{{ student.sex }}
        a-table-column(title='上课情况', dataIndex='register_count')
          template(slot-scope='register_count')
            span {{ register_count ? `${register_count}次` : "无 " }}
        a-table-column(title='入校情况', dataIndex='coming_count')
          template(slot-scope='coming_count')
            span {{ coming_count ? `${coming_count}次` : "无 " }}
        a-table-column(title='出校情况', dataIndex='going_count')
          template(slot-scope='going_count')
            span {{ going_count ? `${going_count}次` : "无 " }}
        a-table-column(title='驻留校区', dataIndex='student.campus')
        a-table-column(title='辅导员', dataIndex='student.adminclass_teacher_names')
</template>

<style lang="stylus" scoped>
.table-box {
  width: 100%;
  box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.08);
  margin-top: 20px;
  padding: 0 20px;
}

.header-box {
  padding: 5px 20px 10px 20px;
  width: 100%;
  box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.08);
  border: 1px solid #e8e8e8;

  .title-box {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #E8E8E8;

    .time-selector {
      margin-top: 10px;
    }
  }

  .number-box {
    height: 98px;
    display: flex;
    align-items: center;
    justify-content: space-around;

    .item {
      margin-top: 15px;
    }

    .item-title {
      margin-bottom: 10px;
      color: #a6a6a6;
      font-size: 12px;
      line-height: 14px;
      font-weight: 500;
    }

    .item-val {
      font-size: 28px;
      color: #383838;
      font-family: DINCond;
      line-height: 28x;
    }
  }
}

.table-box {
  border: 1px solid #E8E8E8;
}
</style>
