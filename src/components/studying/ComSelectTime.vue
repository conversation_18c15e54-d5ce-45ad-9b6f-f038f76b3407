<script lang="ts">
import MomentStatic, { Moment } from 'moment';
import { Component, Vue, Watch } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class ComSelectTime extends Vue {
  private time: Moment | string = '';
  private week: Moment | string = '';
  private month: Moment | string = '';
  private timeRange: Moment[] = [];
  private state: 'daily' | 'weekly' | 'monthly' | 'custom' = 'daily';

  mounted() {
    this.state = 'daily';
    this.time = MomentStatic().format('YYYY-MM-DD');
  }

  private changeWeek(time: Moment): void {
    this.week = time;
    this.state = 'weekly';
    this.time = this.getTime(time, 'week');
    this.timeRange = this.getTimeRange(time, 'week');
    this.$emit('change', this.time, this.state, this.timeRange);
  }

  private changeMonth(time: Moment): void {
    this.month = time;
    this.state = 'monthly';
    this.time = this.getTime(time, 'month');
    this.timeRange = this.getTimeRange(time, 'month');
    this.$emit('change', this.time, this.state, this.timeRange);
  }

  private changeDay(time: Moment): void {
    this.state = 'daily';
    this.time = this.$moment(time).format('YYYY-MM-DD');
    this.timeRange = this.getTimeRange(time, 'day');
    this.$emit('change', this.time, this.state, this.timeRange);
  }

  private changeCustomTimeRange(timeAry: Moment[], timeStrAry: string[]): void {
    this.state = 'custom';
    this.time = timeStrAry.join(' ~ ');
    this.timeRange = [timeAry[0].startOf('day'), timeAry[1].endOf('day')];
    this.$emit('change', this.time, this.state, this.timeRange);
  }

  private getTimeRange(time: any, mode: any): Moment[] {
    return [this.$moment(time).startOf(mode), this.$moment(time).endOf(mode)];
  }

  private getTime(time: any, mode: any): string {
    return this.getTimeRange(time, mode)
      .map(time => time.format('YYYY-MM-DD'))
      .join(' 至 ');
  }

  private changeTime(diff: number): void {
    this.state === 'weekly'
      ? this.changeWeek(this.$moment(this.week).add(diff, 'week'))
      : this.state === 'monthly'
      ? this.changeMonth(this.$moment(this.month).add(diff, 'month'))
      : this.changeDay(this.$moment(this.time).add(diff, 'day'));
  }
}
</script>

<template lang="pug">
.com-select-time
  .time
    .current-date 当前日期 {{ time }}
    .date-ranges
      a-date-picker.date-picker(:mode="'date'" @change="changeDay" )
        .date-range(:class="{active: state === 'daily'}")
          | 日
      a-week-picker.date-picker(@change="changeWeek")
        .date-range(:class="{active: state === 'weekly'}")
          | 周
      a-month-picker.date-picker(@change="changeMonth")
        .date-range(:class="{active: state === 'monthly'} ")
          | 月
      a-range-picker.date-picker(@change="changeCustomTimeRange")
        .date-range(:class="{active: state === 'custom'} ")
          | ...
    .date-actions(v-if='state !== "custom"')
      .prev-button
        a-icon.Icon(type="left" @click="changeTime(-1)" key="prevTime")
      .next-button
        a-icon.Icon(type="right" @click="changeTime(+1)" key="nextTime")
</template>

<style lang="stylus" scoped>
.com-select-time
  height 40px
  display flex
  padding 0px 10px
  align-items center
  flex-direction flex
  justify-content space-between
  border-bottom 1px solid #ededed
  .time
    display flex
    flex-direction row
    .current-date
      margin-right 20px
    .date-actions
      display flex
      flex-direction row
      .prev-button
        margin-right 20px
        cursor pointer
      .next-button
        cursor pointer
    .date-ranges
      margin-right 20px
      display flex
      flex-direction row
      .date-range
        width 25px
        height 25px
        margin 0 5px
        cursor pointer
        text-align center
        border-radius 5px
        border 1px solid #d0d0d0
.active
  background #0080ff
  color #ffffff
</style>
