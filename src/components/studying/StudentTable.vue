<template lang="pug">
.student-page
  AdminTable(
    :data="store.students"
    :totalCount="store.totalCount"
    :currentPage="store.currentPage"
    :totalPages="store.totalPages"
    :perPage="store.perPage"
    :showHeader="true"
    :isSelected="isSelected"
    :rowSelection="rowSelection"
    :loading="loading"
    @rowClick="onShow"
    @filter="onFilter"
    @paginate="onPaginate")
    template(slot="header")
      .table-header
        slot(name="header")
    a-table-column(
      v-for="(column, index) in configs" :key="column.key"
      :title="column.label"
      :filters="column.filters"
      :fixed="index === 0")
      template(slot-scope="scope")
        template(v-if="column.key === 'instance'")
          .flow-box
            .token-cell
              .point.bell(:class="getClass(scope.instance)")
              span {{ getStateZh(scope.instance) }}
        template(v-else-if="column.key === 'flow'")
          a-popover(trigger="hover" placement="rightTop")
            template(slot="content")
              .flow-tokens
                .token-cell(v-for="(item, key) in scope.tokens", :key="key")
                  .point.bell(:class="getClass(item)")
                  span {{ item.name }}
                  strong &nbsp-&nbsp{{ getStateZh(item) }}
            a-button.flow-box
              span.point(v-for="(item, key) in scope.tokens", :key="key" :class="getClass(item)")
        template(v-else)
          span(:class="{'title': column.key === 'name'}") {{ scope[column.key] }}
    //- extends
    slot
    a-table-column(:width="92" align="center")
      span(slot="title")
        .header-actions
          Popover(
            v-model="visibleSort"
            placement="bottom"
            title="排序与筛选")
            .popover(slot="main")
              span.popover-item 排序
              .popover-item(v-for="(item, index) in sorts" :key="index" @click="changeSort(item.value)")
                span {{ item.label }}
                a-icon(type="check" v-if="sortIndex === item.value")
            template(#footer)
              a-button(type="primary" block @click="")
                | 确认
            IconTooltip(icon="sort-ascending" tips="排序" @click="visibleSort=!visibleSort")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import studentStore from '@/store/modules/student.store';
import storeIndex from '@/store';

@Component({
  components: {},
})
export default class StudentTable extends Vue {
  // props
  @Prop({ type: Boolean, default: false }) readonly isSelect?: boolean;
  @Prop({ type: Array, default: () => [] }) readonly selectedRowKeys?: number[];
  @Prop({ type: String, default: '' }) readonly type?: string;
  @Prop({ type: Array, default: () => [] }) readonly columns!: string[];
  @Prop({ type: Object, default: () => ({}) }) readonly query?: IObject;
  @Prop({ type: Boolean, default: false }) readonly loading?: boolean;

  // data
  private queryObject: object = {};
  private visibleSort: boolean = false;
  private sortIndex: string = 'number desc';
  private sorts: object[] = [
    { label: '按职学号-降序', value: 'code desc' },
    { label: '按职学号-升序', value: 'code asc' },
    { label: '按姓名A-Z', value: 'name asc' },
    { label: '按姓名Z-A', value: 'name desc' },
  ];
  get isSelected() {
    return !!(this.selectedRowKeys as number[]).length;
  }
  get rowSelection() {
    return this.isSelect ? { selectedRowKeys: this.selectedRowKeys, onChange: this.onSelectChange } : null;
  }
  get store() {
    return studentStore || {};
  }
  get configs() {
    const cols: IObject[] = [
      { label: '姓名', key: 'name', filters: [] },
      {
        label: '性别',
        key: 'sex',
        filters: [
          { text: '男', value: '男' },
          { text: '女', value: '女' },
        ],
      },
      { label: '报道流程', key: 'flow', filters: [] },
      { label: '宿舍', key: 'dorm', filters: [] },
      { label: '学院', key: 'college', filters: [] },
      { label: '系', key: 'department_name', filters: [] },
      { label: '专业', key: 'major_name', filters: [] },
      { label: '专业代码', key: 'major_code', filters: [] },
      { label: '年级', key: 'grade', filters: [] },
      { label: '班级', key: 'aclass', filters: [] },
      { label: '准考证号', key: 'code', filters: [] },
      { label: '学号', key: 'number', filters: [] },
      { label: '手机号', key: 'phone', filters: [] },
      { label: '联系电话', key: 'tel', filters: [] },
      { label: '状态', key: 'instance', filters: [] },
      {
        label: '类型',
        key: 'state',
        filters: [
          { text: '新生', value: 'newcommer' },
          { text: '在读生', value: 'studying' },
          { text: '毕业生', value: 'graduated' },
        ],
      },
    ];
    return this.columns.length
      ? this.columns.map((key: string) => ({
          ...cols.find((e: IObject) => e.key === key),
        }))
      : cols;
  }

  @Watch('query', { deep: true })
  public watchChange() {
    Object.assign(this.queryObject, this.query);
    this.fetchData();
  }
  public mounted() {
    const role: string = this.$utils.hasPermission('studying', 'admin') ? 'admin' : 'teacher';
    studentStore.setRole(role);
    this.fetchData();
  }

  public fetchData(page: number = 1) {
    const params = {
      page,
      per_page: 10,
      q: {
        s: [`${this.sortIndex}`],
        ...this.query,
        ...this.queryObject,
      },
    };
    studentStore.fetch(params);
  }

  public async onShow(val: IObject = {}) {
    await studentStore.find(val.id);
    this.$emit('onShow', val);
  }

  public onSelectChange(keys: number[] = []): number[] {
    this.$emit('rowSelection', keys);
    return keys;
  }

  public onPaginate(page: number) {
    this.$emit('paginate', page);
    this.fetchData(page);
  }

  public onFilter(val: IObject = {}) {
    this.$emit('filter', val);
    Object.assign(this.queryObject, val);
    this.fetchData();
  }

  public changeSort(val: string = '') {
    if (this.sortIndex !== val) {
      this.sortIndex = val;
      this.fetchData();
    }
  }

  // flow state color
  public getClass(val: IObject = {}): string {
    switch (val.state) {
      case 'processing':
        return 'bg-blue';
      case 'completed':
        return 'bg-green';
      case 'done':
        return 'bg-green';
      default:
        return 'bg-gray';
    }
  }

  public getStateZh(val: IObject = {}): string {
    switch (val.state) {
      case 'processing':
        return '进行中';
      case 'completed':
        return '已完成';
      case 'done':
        return '已完成';
      default:
        return '未开始';
    }
  }
}
</script>

<style lang="stylus" scoped>
.student-page
  width 100%
  background #fff
  tr:hover
    .title
      color #3DA8F5
      font-size 14px
  .header-actions
    padding-left 16px
    width 82px
    height 20px
    border-left 1px #e6e6e6 solid
    button
      padding 0px
      width 20px
      height 20px
      border none
      color #808080
      font-weight 450
      &:hover
        color #3DA8F5

button
  background none

.flow-box, .flow-tokens
  padding 0px
  border none
  background none
  .point
    margin 0px 3px
    width 6px
    height 6px
    border-radius 50%
    background red
  .bg-gray
    background #A6A6A6
  .bg-green
    background #75C940
  .bg-blue
    background #3DA8F5
  .token-cell
    display flex
    align-items center
    line-height 28px
    .bell
      margin-right 4px

.flow-tokens
  padding 6px 14px
  color #808080
</style>
