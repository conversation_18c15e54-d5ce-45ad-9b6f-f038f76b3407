<script lang="ts">
import { IStudent } from '@/models/student';
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import ComStudentInfoCard from './ComStudentInfoCard.vue';
import TemplateFormViewer from '../form/TemplateFormViewer.vue';

@Component({
  components: {
    ComStudentInfoCard,
    TemplateFormViewer,
  },
})
export default class ComStudentInfo extends Vue {
  @Prop({ type: Object }) student!: IStudent;
  @Prop({ type: String }) activeTitle!: string;

  baseInfoTemplate: IObject[] = [
    { icon: 'user', name: '姓名', key: 'name', layout: { span: 12 } },
    { icon: 'man', name: '学号', key: 'code', layout: { span: 12 } },
    { icon: 'idcard', name: '出生日期', key: 'birth', layout: { span: 12 } },
    { icon: 'man', name: '性别', key: 'sex', layout: { span: 12 } },
    { icon: 'phone', name: '手机', key: 'tel', layout: {} },
    { icon: 'idcard', name: '身份证类型', key: 'idcard_type', layout: { span: 12 } },
    { icon: 'idcard', name: '证件号码', key: 'identity_id', layout: { span: 12 } },
    { icon: 'apartment', name: '院系', key: 'department_name', layout: {} },
    { icon: 'apartment', name: '专业', key: 'major_name', layout: {} },
    { icon: 'apartment', name: '班级', key: 'adminclass_name', layout: {} },
    {
      icon: 'user',
      name: '辅导员',
      key: 'adminclass_teachers',
      layout: {},
      processer: (vals: IObject[]) => {
        return vals.map(user => user.name).join('、');
      },
    },
    // { icon: 'team', name: '民族', key: 'nation', layout: {}  },
    // { icon: 'border', name: '家庭地址', key: 'home_address' },
  ];

  get source() {
    return this.student
      ? [
          { title: '基本信息', formData: this.student, template: this.baseInfoTemplate },
          { title: '学籍信息', formData: this.student.column2_key_value, template: this.student.column2_form.fields },
          { title: '个人信息', formData: this.student.column1_key_value, template: this.student.column1_form.fields },
        ]
      : [];
  }

  @Watch('activeTitle')
  onMenu() {
    this.$nextTick(() => {
      (this.$el.querySelector(`.${this.activeTitle}`) as Element).scrollIntoView();
    });
  }
}
</script>

<template lang="pug">
.com-studying-user-info
  .card-box(v-for='item in source', :key='item.title', :class='{ [item.title]: true }')
    TaTitleHeader.header(:title='item.title')
    TemplateFormViewer(
      :formData='item.formData',
      :template='item.template',
      :titleWidth='140',
      :showIcon='true',
      :border='false'
    )
  .empty
</template>

<style lang="stylus" scoped>
.com-studying-user-info
  .card-box
    background #FFFFFF
    box-shadow 0px 0px 10px 0px rgba(0, 0, 0, 0.08)
    border-radius 5px
    padding 12px 24px 22px 24px
    margin-bottom 12px
  .header
    border-bottom 1px solid #e3e3e3
    margin-bottom 14px
  .empty
    height 30px
</style>
