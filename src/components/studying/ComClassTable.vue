<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { TaIndexViewConfigInterface } from '@/components/global/TaIndex';

@Component({
  components: {},
})
export default class ComClassTable extends Vue {
  @Prop({ type: String, default: '' }) prop!: string;
  @Prop({ type: String, default: '' }) role!: string;
  @Prop({ type: Object }) readonly adminclasses!: any;

  private tabs: any = [
    { label: '未毕业', key: '1', query: { valid: true }, num: 0, background: '', color: '', mode: 'table' },
    { label: '已毕业', key: '2', query: { invalid: true }, num: 0, background: '', color: '', mode: 'table' },
    { label: '全部', key: '3', query: {}, num: 0, background: '', color: '', mode: 'table' },
  ];

  get config(): TaIndexViewConfigInterface {
    return {
      recordName: '',
      searcherSimpleOptions: [
        { label: '名称', key: 'name', type: 'string' },
        { label: '学院', key: 'department_name', type: 'string' },
        { label: '专业', key: 'major_name', type: 'string' },
      ],
      searcherComplicatedOptions: [
        { label: '学院', key: 'department_name', type: 'string' },
        { label: '专业', key: 'major_name', type: 'string' },
      ],
      store: this.adminclasses,
      mode: 'table',
      showPageSizeChanger: true,
    };
  }

  onTaIndexViewMounted() {
    this.adminclasses.init();
  }

  private onShow(val: any): void {
    this.$router.push({ path: `/studying/${this.role}/classes/${val.id}` });
  }
}
</script>

<template lang="pug">
.content
  TaIndexView(:tabs='tabs', :config='config', :tabsLeftMargin='0', @mounted='onTaIndexViewMounted', @onShow='onShow')
    template(#table)
      a-table-column(title='班级名称', dataIndex='name')
      a-table-column(title='辅导员', dataIndex='teacher_names')
      a-table-column(title='系院', dataIndex='department_name')
      a-table-column(title='专业', dataIndex='major_name')
      a-table-column(title='班级类型', dataIndex='std_type')
      a-table-column(title='人才培养方案', dataIndex='program_name')
      a-table-column(title='人数', dataIndex='students_count')
</template>
