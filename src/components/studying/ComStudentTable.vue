<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { TaIndexViewConfigInterface } from '@/components/global/TaIndex';

@Component({
  components: {},
})
export default class ComStudentTable extends Vue {
  @Prop({ type: Object, default: () => ({}) }) readonly store!: any;
  @Prop({ type: Array }) readonly tabs!: any;
  @Prop({ type: String, default: '' }) role!: string;
  @Prop({ type: Object }) classStore!: any;
  private activeTabs: IObject[] = [];
  private status = [
    { text: '在校', value: '在校' },
    { text: '结业', value: '结业' },
    { text: '死亡', value: '死亡' },
    { text: '离校', value: '离校' },
    { text: '毕业', value: '毕业' },
    { text: '退学', value: '退学' },
    { text: '休学', value: '休学' },
    { text: '保留入学资格', value: '保留入学资格' },
    { text: '保留学籍', value: '保留学籍' },
  ];

  get config(): TaIndexViewConfigInterface {
    return {
      recordName: '',
      showExport: true,
      showImport: true,
      searcherSimpleOptions: [
        { label: '姓名', key: 'name', type: 'string' },
        { label: '性别', key: 'sex', type: 'string' },
        { label: '学号', key: 'code', type: 'string' },
        { label: '班级', key: 'adminclass_name', type: 'string' },
        { label: '学院', key: 'department_name', type: 'string' },
        { label: '专业', key: 'major_name', type: 'string' },
      ],
      searcherComplicatedOptions: [
        { label: '班级', key: 'adminclass_name', type: 'string' },
        { label: '学院', key: 'department_name', type: 'string' },
        { label: '专业', key: 'major_name', type: 'string' },
      ],
      store: this.store,
      mode: 'table',
      showPageSizeChanger: true,
      showCount: true,
    };
  }

  created() {
    if (this.tabs) {
      this.activeTabs = this.tabs;
    }
  }

  onTaIndexViewMounted() {
    this.$emit('onMounted');
  }

  onShow(val: any): void {
    if (this.$route.path.includes('teacher')) {
      // 打开的方式更改为新的窗口打开
      const target = this.$router.resolve({ name: 'studying_teacher_students_show', params: { id: val.id } });
      window.open(target.href, '_blank');
    } else {
      const target = this.$router.resolve({ name: 'studying_admin_students_show', params: { id: val.id } });
      window.open(target.href, '_blank');
    }
    // 变更打开方式后，下面这个为了面包屑而设置的传参就用不到了。
    // if (this.classStore) {
    //   this.$router.push({
    //     path: `/studying/${this.role}/students/${val.id}`,
    //     query: { from: `${this.classStore.name}`, id: `${this.classStore.id}` },
    //   });
    // } else {
    //   this.$router.push({
    //     path: `/studying/${this.role}/students/${val.id}`,
    //   });
    // }
  }

  onTaIndexViewIndex(data: IObject) {
    this.$emit('onIndex', data, this.activeTabs);
  }

  getHandInStateColor(state: string) {
    return [
      { label: '未提交', color: '#808080' },
      { label: '已完成', color: '#75c940' },
      { label: '审核中', color: '#3da8f5' },
      { label: '已终止', color: '#eb9e05' },
      { label: '-', color: '#808080' },
    ].find(item => item.label === state)?.color;
  }
}
</script>

<template lang="pug">
.content
  TaIndexView(
    :tabs='activeTabs',
    :config='config',
    :tabsLeftMargin='0',
    @mounted='onTaIndexViewMounted',
    @onShow='onShow',
    @onIndex='onTaIndexViewIndex'
  )
    template(#header)
      slot(name='header')
    template(#table)
      a-table-column(title='姓名', dataIndex='name')
      a-table-column(title='学号', dataIndex='code', width='120px')
      a-table-column(title='性别', dataIndex='sex')
      a-table-column(title='院系', dataIndex='department_name')
      a-table-column(title='专业', dataIndex='major_name')
      a-table-column(title='年级', dataIndex='grade')
      a-table-column(title='班级', dataIndex='adminclass_name')
      a-table-column(title='民族', dataIndex='nation')
      a-table-column(dataIndex='study_stat', title='学籍状态', :filters='status')
      a-table-column(title='提交状态', dataIndex='hand_in_state')
        template(slot-scope='hand_in_state')
          span(:style='{ color: getHandInStateColor(hand_in_state) }') {{ hand_in_state }}

      //- a-table-column(:width='100')
      //-   template(slot-scope='record')
      //-     .text-right.table-hover-col
      //-       IconTooltip(icon='edit', tips='编辑', @click='onEdit(record)')
        <!-- PopoverConfirm(title='删除', content='您确认删除学生吗？', @confirm='onDelete(record)') -->
        <!--   IconTooltip(icon='delete', tips='删除') -->
</template>

<style lang="stylus" scoped></style>
