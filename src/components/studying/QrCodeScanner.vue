<script lang="ts">
import { Component, Vue, Prop, Model, Emit, Watch } from 'vue-property-decorator';

@Component
export default class QrCodeScanner extends Vue {
  active: boolean = false;
  inputValue: string | null = null;

  @Model('input', { type: Boolean, default: false }) private value!: boolean;
  @Prop({ type: Boolean, default: false }) private loading!: boolean;

  @Watch('value')
  onValueChange() {
    if (this.value) {
      this.resetInputAndFocus();
    }
  }

  @Emit('input')
  close() {
    return false;
  }

  onFocus() {
    this.active = true;
  }

  onInputChange(e: Event) {
    const target = e.target as HTMLInputElement;
    const code = `${target.value}`;
    const array = code.split('00'); // 00 为自定义分解符
    const schoolCode = Number(array[0]);
    const instanceCode = array[1];
    const isValidSchoolId = schoolCode && schoolCode > 47 && schoolCode < 58;
    const isValidInstanceCode = instanceCode && instanceCode.length % 2 === 0;
    if (isValidSchoolId && isValidInstanceCode) {
      const schoolId = `${schoolCode - 48}`;
      const cache = [];
      for (let i = 0; i < instanceCode.length; i += 2) {
        cache.push(Number(instanceCode.charAt(i) + instanceCode.charAt(i + 1)) - 48);
      }
      const instanceId = cache.join('');
      this.$emit('change', schoolId, instanceId, code);
    } else {
      this.$message.error('请使用正确的二维码');
    }
    this.resetInputAndFocus();
  }

  resetInputAndFocus() {
    this.inputValue = null;
    this.$nextTick(() => {
      const input = document.getElementById('scannerInput') as HTMLInputElement;
      input.focus();
    });
  }
}
</script>

<template lang="pug">
a-modal(
  :visible="value"
  :closable="false"
  :maskClosable="true"
  :footer="null"
  :width="400"
  :height="420"
  @cancel="close"
  :bodyStyle="{ padding: 0 }")
  .scanner(v-loading="loading")
    img.icon(src="@/assets/images/studying/scanner.png" height="140" width="140")
    .active(v-if="active")
      | 扫码枪已开启
    .disabled(v-else)
      | 扫码枪已关闭，点击开启
    .tips 页面已锁定，请提示学生出示二维码
    input.scanner-input(
      id="scannerInput"
      type="tel"
      v-model="inputValue"
      @focus="onFocus"
      @keyup.enter.stop="onInputChange")
</template>

<style lang="stylus" scoped>
.scanner
  position relative
  padding-top 100px
  width 400px
  height 420px
  background #ffffff
  text-align center
  .icon
    display inline-block
    margin-bottom 60px
  .active
    margin-bottom 12px
    height 26px
    color #3DA8F5
    font-size 16px
    line-height 26px
  .disabled
    margin-bottom 12px
    height 26px
    color red
    font-size 16px
    line-height 26px
  .tips
    height 26px
    color rgba(166, 166, 166, 1)
    font-size 16px
    line-height 26px
  .scanner-input
    position absolute
    top 0
    left 0
    z-index 1000
    margin 0
    padding 0
    width 100%
    height 100%
    outline none
    border none
    line-height 0
    opacity 0
</style>
