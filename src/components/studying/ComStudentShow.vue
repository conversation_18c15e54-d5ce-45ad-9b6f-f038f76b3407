<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import ComStudentInfo from '@/components/studying/ComStudentInfo.vue';

@Component({
  components: { ComStudentInfo },
})
export default class ComStudentShow extends Vue {
  @Prop({ type: String, default: '' }) prop!: string;
  @Prop({ type: String, default: '' }) role!: string;
  @Prop({ type: Object, default: '' }) readonly teacherStudentsStore!: any;
  private visibleModalCollege: boolean = false;
  private activeName: string = 'scoreDetails';
  private password: string = '';
  activeTitle = '基本信息';

  get steps() {
    const step = [{ title: '信息', key: 'scoreDetails' }];
    return step;
  }

  get breadcrumbs() {
    return this.$route.query.from
      ? [
          { title: '班级管理', url: `/studying/${this.role}/classes` },
          { title: `${this.$route.query.from}`, url: `/studying/${this.role}/classes/${this.$route.query.id}` },
          { title: this.teacherStudentsStore.record.name, url: '' },
        ]
      : [
          { title: '学生管理', url: `/studying/${this.role}/students` },
          { title: this.teacherStudentsStore.record.name, url: '' },
        ];
  }

  get student() {
    return this.teacherStudentsStore.record;
  }

  get menus() {
    return ['基本信息', '学籍信息', '个人信息', '修改密码'];
  }

  mounted() {
    this.teacherStudentsStore.init();
    this.teacherStudentsStore.find(this.$route.params.id);
  }
  cancel() {
    this.visibleModalCollege = false;
    this.activeTitle = '基本信息';
    this.password = '';
  }
  submit() {
    // const reg = /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,}$/;
    // if (!reg.test(this.password)) {
    //   this.$message.error('密码长度不小于6位，由数字和字母组成');
    // }

    this.teacherStudentsStore
      .update({
        id: this.$route.params.id,
        password_raw: this.password,
      })
      .then((res: any) => {
        console.log('res: ', res);
        this.password = '';
        this.activeTitle = '基本信息';
        this.visibleModalCollege = false;
      });
  }
  @Watch('activeTitle', { immediate: true })
  changeActiveTitle() {
    if (this.activeTitle == '修改密码') {
      this.visibleModalCollege = true;
    } else {
      this.visibleModalCollege = false;
    }
  }
}
</script>

<template lang="pug">
.container
  StepToolbar.toolbar(
    :value='activeName',
    :breadcrumbs.sync='breadcrumbs',
    :steps='steps',
    :border='true',
    mode='tabs'
  )
  .user-info-card-box
    .side-box
      Panel.info-card
        .card-header
          TaAvatar(:name='student.name', :sex='student.sex', :short='true', fontSize='24px', boxSize='60px')
          .info
            .name {{ student.name }}
            .code 学号 {{ student.code }}
        .card-middle
          .menu-item(
            v-for='(menu, index) in menus',
            :key='index',
            :class='{ "menu-item-active": activeTitle === menu }',
            @click='activeTitle = menu'
          )
            | {{ menu }}
    ComStudentInfo.content(ref='comStudentInfo', v-if='student.id', :student='student', :activeTitle='activeTitle')
    a-modal(v-model='visibleModalCollege', :width='480', @cancel='cancel', @ok='submit')
      .modal-row(slot='title')
        .title 密码修改
      .modal-middle
        a-input(v-model:value='password', placeholder='新的密码')
</template>

<style lang="stylus" scoped>
.container
  position relative
  .toolbar
    padding 0 20px
    background #ffffff
    position sticky
    top 0
    z-index 3
  .user-info-card-box
    position fixed
    top 114px
    left 60px
    width calc(100% - 120px)
    // margin 20px auto
    margin-bottom 20px
    .content
      margin-top 0
      padding-left 320px
      overflow-y scroll
      height calc(100vh - 98px)
    .side-box
      position fixed
      top 114px
      left 60px
      flex-shrink 0
      width 300px
      .info-card
        padding-bottom 8px
        width 100%
        background #fff
        .card-header
          display flex
          align-items center
          padding 24px 20px
          .info
            margin-left 12px
            .name
              color #383838
              font-weight 500
              font-size 16px
              line-height 24px
            .code
              margin-top 4px
              color #808080
              font-size 14px
              line-height 20px
        .card-middle
          width 100%
          .menu-item
            padding 14px 20px
            color #808080
            font-weight 500
            font-size 14px
            cursor pointer
          .menu-item-active
            position relative
            color #383838
            &:before
              position absolute
              top 0px
              left 0px
              width 4px
              height 48px
              background #3DA8F5
              content ''
</style>
