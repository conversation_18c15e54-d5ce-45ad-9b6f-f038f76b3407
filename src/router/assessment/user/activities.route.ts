import { IRoute } from '@/interfaces/route.interface';

const routes: IRoute[] = [
  {
    path: '/assessment/user/activities',
    name: 'user_assessment_activity_index',
    component: () =>
      import(
        /* webpackChunkName: "user_assessment_activity_index" */
        '@/views/assessment/user/activities/Index.vue'
      ),
    meta: {
      title: '人事考核',
      layout: 'menu',
    },
  },
  {
    path: '/assessment/user/activities/:id',
    name: 'user_assessment_activity_entry',
    component: () =>
      import(
        /* webpackChunkName: "user_assessment_activity_entry" */
        '@/views/assessment/user/activities/Entry.vue'
      ),
    meta: {
      title: '考核详情',
      layout: 'header',
    },
    children: [
      {
        path: 'info',
        name: 'user_assessment_activity_entry_info',
        component: () =>
          import(
            /* webpackChunkName: "user_assessment_activity_entry_info" */
            '@/views/assessment/user/activities/show/Form.vue'
          ),
        meta: {
          title: '基本信息',
          layout: 'header',
        },
      },
      {
        path: 'mine',
        name: 'user_assessment_activity_entry_mine',
        component: () =>
          import(
            /* webpackChunkName: "user_assessment_activity_entry_mine" */
            '@/views/assessment/user/activities/show/Mine.vue'
          ),
        meta: {
          title: '我的考核',
          layout: 'header',
        },
      },
      {
        path: 'scores',
        name: 'user_assessment_activity_entry_score_index',
        component: () =>
          import(
            /* webpackChunkName: "user_assessment_activity_entry_score_index" */
            '@/views/assessment/user/activities/show/scores/Index.vue'
          ),
        meta: {
          title: '考核',
          layout: 'header',
        },
      },
      {
        path: 'examinees',
        name: 'user_assessment_activity_entry_examinees_index',
        component: () =>
          import(
            /* webpackChunkName: "user_assessment_activity_entry_examinees_index" */
            '@/views/assessment/user/activities/show/examinees/Index.vue'
          ),
        meta: {
          title: '管理',
          layout: 'header',
        },
      },
    ],
  },
];

export default routes;
