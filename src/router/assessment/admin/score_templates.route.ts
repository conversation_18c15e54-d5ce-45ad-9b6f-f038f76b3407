import { IRoute } from '@/interfaces/route.interface';

const routes: IRoute[] = [
  {
    path: '/assessment/admin/score_templates',
    name: 'assessment_admin_score_template_index',
    component: () =>
      import(
        /* webpackChunkName: "assessment_admin_score_template_index" */
        '@/views/assessment/admin/score_templates/Index.vue'
      ),
    meta: {
      title: '评分表管理',
      layout: 'menu',
    },
  },
  {
    path: '/assessment/admin/score_templates/:id',
    name: 'assessment_admin_score_template_entry',
    component: () =>
      import(
        /* webpackChunkName: "assessment_admin_score_template_entry" */
        '@/views/assessment/admin/score_templates/Entry.vue'
      ),
    meta: {
      title: '评分表详情',
      layout: 'header',
    },
    children: [
      {
        path: 'edit',
        name: 'assessment_admin_score_template_entry_edit',
        component: () =>
          import(
            /* webpackChunkName assessment_admin_score_template_entry_edit*/
            '@/views/assessment/admin/score_templates/show/Form.vue'
          ),
        meta: {
          title: '评分表内容',
          layout: 'header',
        },
      },
    ],
  },
];

export default routes;
