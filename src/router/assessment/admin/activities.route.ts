import { IRoute } from '@/interfaces/route.interface';

const routes: IRoute[] = [
  {
    path: '/assessment/admin/activities',
    name: 'assessment_admin_activity_index',
    component: () =>
      import(/* webpackChunkName: "assessment_admin_activity_index" */ '@/views/assessment/admin/activities/Index.vue'),
    meta: {
      title: '考核管理',
      layout: 'menu',
    },
  },
  {
    path: '/assessment/admin/activities/new',
    name: 'assessment_admin_activity_new',
    component: () =>
      import(
        /* webpackChunkName: "assessment_admin_activity_new" */
        '@/views/assessment/admin/activities/New.vue'
      ),
    meta: {
      title: '创建考核',
      layout: 'header',
    },
  },
  {
    path: '/assessment/admin/activities/:id',
    name: 'assessment_admin_activity_entry',
    component: () =>
      import(
        /* webpackChunkName: "assessment_admin_activity_entry" */
        '@/views/assessment/admin/activities/Entry.vue'
      ),
    meta: {
      title: '考核详情',
      layout: 'header',
    },
    children: [
      // 抽奖管理
      {
        path: 'lottery',
        name: 'assessment_admin_activity_entry_lottery',
        component: () =>
          import(
            /* webpackChunkName assessment_admin_activity_entry_lottery*/
            '@/views/assessment/admin/activities/show/lottery/Index.vue'
          ),
        meta: {
          title: '抽奖管理',
          layout: 'header',
        },
      },
      {
        path: 'examinees',
        name: 'assessment_admin_activity_entry_examinees',
        component: () =>
          import(
            /* webpackChunkName assessment_admin_activity_entry_examinees*/
            '@/views/assessment/admin/activities/show/examinees/Index.vue'
          ),
        meta: {
          title: '被考核人',
          layout: 'header',
          keepAlive: true,
        },
      },
      {
        path: 'info',
        name: 'assessment_admin_activity_entry_info',
        component: () =>
          import(
            /* webpackChunkName assessment_admin_activity_entry_info*/
            '@/views/assessment/admin/activities/show/Form.vue'
          ),
        meta: {
          title: '考核详情',
          layout: 'header',
        },
      },
      {
        path: 'groups',
        name: 'assessment_admin_activity_entry_groups_index',
        component: () =>
          import(
            /* webpackChunkName assessment_admin_activity_entry_groups_index*/
            '@/views/assessment/admin/activities/show/groups/Index.vue'
          ),
        meta: {
          title: '考核分组',
          layout: 'header',
        },
      },
      {
        path: 'catalogs',
        name: 'assessment_admin_activity_entry_catalogs_index',
        component: () =>
          import(
            /* webpackChunkName assessment_admin_activity_entry_catalogs_index*/
            '@/views/assessment/admin/activities/show/catalogs/Index.vue'
          ),
        meta: {
          title: '考核分类',
          layout: 'header',
        },
      },
      {
        path: 'catalogs/:catalogId/dimensions',
        name: 'assessment_admin_activity_entry_catalogs_show',
        component: () =>
          import(
            /* webpackChunkName assessment_admin_activity_entry_catalogs_show*/
            '@/views/assessment/admin/activities/show/catalogs/show/dimensions/Index.vue'
          ),
        meta: {
          title: '考核分类',
          layout: 'header',
        },
      },
    ],
  },
  {
    path: '/assessment/admin/activities/:id/groups/:groupId',
    name: 'assessment_admin_activity_entry_groups_entry',
    component: () =>
      import(
        /* webpackChunkName assessment_admin_activity_entry_groups_entry*/
        '@/views/assessment/admin/activities/show/groups/Entry.vue'
      ),
    meta: {
      title: '考核分组详情',
      layout: 'header',
    },
    children: [
      {
        path: 'examinees',
        name: 'assessment_admin_activity_entry_groups_show_examinees',
        component: () =>
          import(
            /* webpackChunkName assessment_admin_activity_entry_groups_show_examinees*/
            '@/views/assessment/admin/activities/show/groups/show/examinees/Index.vue'
          ),
        meta: {
          title: '被考核人',
          layout: 'header',
        },
      },
      {
        path: 'examinees/:examineeId',
        name: 'assessment_admin_activity_entry_groups_show_examinees_show',
        component: () =>
          import(
            /* webpackChunkName assessment_admin_activity_entry_groups_show_examinees_show*/
            '@/views/assessment/admin/activities/show/groups/show/examinees/Show.vue'
          ),
        meta: {
          title: '被考核人详情',
          layout: 'header',
        },
      },
      {
        path: 'catalogs',
        name: 'assessment_admin_activity_entry_groups_show_catalogs',
        component: () =>
          import(
            /* webpackChunkName assessment_admin_activity_entry_groups_show_catalogs*/
            '@/views/assessment/admin/activities/show/groups/show/catalogs/Index.vue'
          ),
        meta: {
          title: '考核配置',
          layout: 'header',
        },
      },
      {
        path: 'catalogs/:catalogId',
        name: 'assessment_admin_activity_entry_groups_show_catalogs_show',
        component: () =>
          import(
            /* webpackChunkName assessment_admin_activity_entry_groups_show_catalogs_show*/
            '@/views/assessment/admin/activities/show/groups/show/catalogs/Show.vue'
          ),
        meta: {
          title: '考核子项',
          layout: 'header',
        },
      },
    ],
  },
];

export default routes;
