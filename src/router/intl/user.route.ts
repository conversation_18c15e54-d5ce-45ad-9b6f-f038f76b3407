// intl 下的 user 角色的 route
export default [
  {
    path: '/intl/user/activities/:activityId',
    name: 'intl_user_activities',
    component: () => import('@/views/intl/user/activities/Entry.vue'),
    meta: { title: '国际交流平台', role: '', layout: 'header' },
    children: [
      {
        path: 'users',
        name: 'intl_user_activities_users',
        component: () => import('@/views/intl/user/activities/users/Index.vue'),
        meta: {
          title: '国际交流平台',
          layout: 'header',
        },
      },
      {
        path: 'netdisk/items',
        name: 'intl_user_activities_netdisk',
        component: () => import('@/views/intl/user/activities/netdisk_items/Index.vue'),
        meta: {
          title: '资源管理',
          layout: 'header',
        },
      },
      {
        path: 'plans',
        name: 'intl_user_activities_plan',
        component: () => import('@/views/intl/user/activities/plan_detail/Index.vue'),
        meta: {
          title: '计划详情',
          layout: 'header',
        },
      },
      {
        path: 'financeBudgets',
        name: 'intl_user_activities_finance',
        component: () => import('@/views/intl/user/activities/finance_budgets/Index.vue'),
        meta: {
          title: '资金卡管理',
          layout: 'header',
        },
      },
      {
        path: 'reports',
        name: 'intl_user_activities_reports',
        component: () => import('@/views/intl/user/activities/reports/Index.vue'),
        meta: {
          title: '报告管理',
          layout: 'header',
        },
      },
      {
        path: 'reports/form',
        name: 'intl_user_activities_reports_form',
        component: () => import('@/views/intl/user/activities/reports/Form.vue'),
        meta: {
          title: '添加报告',
          layout: 'header',
        },
      },
      {
        path: 'reports/:reportsId/edit',
        name: 'intl_user_activities_reports_form',
        component: () => import('@/views/intl/user/activities/reports/Form.vue'),
        meta: {
          title: '报告修改',
          layout: 'header',
        },
      },
    ],
  },
];
