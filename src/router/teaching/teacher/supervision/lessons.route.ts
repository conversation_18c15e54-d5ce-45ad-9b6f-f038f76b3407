/* eslint-disable */
export default [
  {
    path: '/teaching/teacher/supervision/lessons',
    name: 'teachingTeacherSupervisionLessonsIndex',
    component: () =>
      import(
        /* webpackChunkName: "teachingTeacherSupervisionLessonsIndex" */ '@/views/teaching/teacher/supervision/lessons/Index.vue'
      ),
    meta: {
      title: '督导课程',
      layout: 'autoMenu',
    },
  },
  {
    path: '/teaching/teacher/supervision/week_schedule',
    name: 'teachingTeacherSupervisionWeekSchedule',
    component: () =>
      import(
        /* webpackChunkName: "teachingTeacherSupervisionLessonsIndex" */ '@/views/teaching/teacher/supervision/week_lessons_schedule_page/Index.vue'
      ),
    meta: {
      title: '日历视图',
      layout: 'autoMenu',
    },
  },
  // {
  //   path: '/teaching/teacher/supervision/lessons/:lessonId',
  //   name: 'teachingTeacherSupervisionLessonsShow',
  //   component: () =>
  //     import(
  //       /* webpackChunkName: "teachingTeacherSupervisionLessonsShow" */ '@/views/teaching/teacher/supervision/lessons/Show.vue'
  //     ),
  //   meta: {
  //     title: '督导课程',
  //     layout: 'header',
  //   },
  // },
  // {
  //   path: '/teaching/teacher/supervision/lessons/:lessonId/new',
  //   name: 'teachingTeacherSupervisionLessonsFormNew',
  //   component: () =>
  //     import(
  //       /* webpackChunkName: "teachingTeacherSupervisionLessonsFormNew" */ '@/views/teaching/teacher/supervision/lessons/Form.vue'
  //     ),
  //   meta: {
  //     title: 'teachingTeacherSupervisionLessonsFormNew',
  //   },
  // },
  // {
  //   path: '/teaching/teacher/supervision/lessons/:lessonId/edit',
  //   name: 'teachingTeacherSupervisionLessonsFormEdit',
  //   component: () =>
  //     import(
  //       /* webpackChunkName: "teachingTeacherSupervisionLessonsFormEdit" */ '@/views/teaching/teacher/supervision/lessons/Form.vue'
  //     ),
  //   meta: {
  //     title: 'teachingTeacherSupervisionLessonsFormEdit',
  //   },
  // },
];
