import { IRoute } from '@/interfaces/route.interface';

const routes: IRoute[] = [
  {
    path: '/comm/admin/dynamic_columns',
    name: 'comm_admin_dynamic_columns_index',
    component: () =>
      import(/* webpackChunkName: "comm_admin_dynamic_columns_index" */ '@/views/comm/admin/dynamic_columns/Index.vue'),
    meta: {
      title: '动态字段管理',
      layout: 'header',
    },
  },
];

export default routes;
