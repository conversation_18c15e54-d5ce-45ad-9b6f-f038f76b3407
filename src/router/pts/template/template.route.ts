import { IRoute, RouteMeta } from '@/interfaces/route.interface';

const route: IRoute[] = [
  {
    path: '/pts',
    name: 'pts',
    component: () => import(/* webpackChunkName: "pts" */ '@/views/pts/Entry.vue'),
    children: [
      {
        path: 'templates',
        name: 'pts_templates',
        component: () => import(/* webpackChunkName: "pts_templates" */ '@/views/pts/template/Index.vue'),
        meta: {
          title: '模板管理',
        } as RouteMeta,
      },
    ],
    meta: {
      title: '顶岗实习',
      layout: 'default',
    } as RouteMeta,
  },
  {
    path: '/pts/templates/:templateId',
    name: 'pts_templates_show',
    component: () => import(/* webpackChunkName: "pts_templates_show" */ '@/views/pts/template/Show.vue'),
    meta: {
      title: '模板项管理',
    } as RouteMeta,
    children: [
      {
        path: '',
        name: 'pts_templates_show_info',
        component: () => import(/* webpackChunkName: "pts_templates_show_info" */ '@/views/pts/template/Infos.vue'),
        meta: {
          title: '模板项管理',
        } as RouteMeta,
      },
    ],
  },
];

export default route;
