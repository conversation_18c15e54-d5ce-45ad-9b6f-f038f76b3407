import { IRoute, RouteMeta } from '@/interfaces/route.interface';

const route: IRoute[] = [
  {
    path: '/pts',
    name: 'pts',
    component: () => import(/* webpackChunkName: "pts" */ '@/views/pts/Entry.vue'),
    children: [
      {
        path: 'admin/activities',
        name: 'pts_admin_activities',
        component: () => import(/* webpackChunkName: "pts_admin_activities" */ '@/views/pts/admin/Index.vue'),
        meta: {
          title: '实习活动',
        } as RouteMeta,
        children: [],
      },
    ],
    meta: {
      title: '顶岗实习',
      layout: 'default',
    } as RouteMeta,
  },
  {
    path: '/pts/admin/activities/:activitiesId',
    name: 'pts_admin_activities_show',
    component: () => import(/* webpackChunkName: "pts_admin_activities_show" */ '@/views/pts/admin/Show.vue'),
    meta: {} as RouteMeta,
    children: [
      {
        path: '',
        name: 'pts_admin_activities_Index',
        component: () =>
          import(/* webpackChunkName: "pts_admin_activities_Index" */ '@/views/pts/admin/activity/Index.vue'),
        meta: {
          layout: 'header',
          title: '基本信息',
        } as RouteMeta,
      },
      {
        path: 'statistics',
        name: 'pts_admin_activities_statistics',
        component: () =>
          import(/* webpackChunkName: "pts_admin_activities_statistics" */ '@/views/pts/admin/activity/Statistics.vue'),
        meta: {
          layout: 'header',
          title: '实习统计',
        } as RouteMeta,
      },
      {
        path: 'practices',
        name: 'pts_admin_activities_practices',
        component: () =>
          import(/* webpackChunkName: "pts_admin_activities_practices" */ '@/views/pts/admin/activity/Practices.vue'),
        meta: {
          layout: 'header',
          title: '学生列表',
        } as RouteMeta,
      },
    ],
  },
  // 详情
  {
    path: '/pts/admin/activities/:activitiesId/practices/:practiceId',
    name: 'pts_admin_activities_practices_show',
    component: () =>
      import(
        /* webpackChunkName: "pts_admin_activities_practices_show" */

        '@/views/pts/admin/activity/practices/Show.vue'
      ),
    children: [
      {
        path: '',
        name: 'pts_admin_activities_practices_show_BasicInfo',
        component: () =>
          import(
            /* webpackChunkName: "pts_admin_activities_practices_show_BasicInfo" */

            '@/views/pts/admin/activity/practices/BasicInfo.vue'
          ),
        meta: {
          layout: 'header',
          title: '基本信息',
        } as RouteMeta,
      },
      {
        path: 'reports',
        name: 'pts_admin_activities_practices_show_reports',
        component: () =>
          import(
            /* webpackChunkName: "pts_admin_activities_practices_show_reports" */

            '@/views/pts/admin/activity/practices/Reports.vue'
          ),
        meta: {
          layout: 'header',
          title: '实习周记',
        } as RouteMeta,
      },
      {
        path: 'companies',
        name: 'pts_admin_activities_practices_show_companies',
        component: () =>
          import(
            /* webpackChunkName: "pts_admin_activities_practices_show_companies" */

            '@/views/pts/admin/activity/practices/Companies.vue'
          ),
        meta: {
          layout: 'header',
          title: '企业评价',
        } as RouteMeta,
      },
      {
        path: 'registers',
        name: 'pts_admin_activities_practices_show_registers',
        component: () =>
          import(
            /* webpackChunkName: "pts_admin_activities_practices_show_registers" */

            '@/views/pts/admin/activity/practices/Registers.vue'
          ),
        meta: {
          layout: 'header',
          title: '打卡情况',
        } as RouteMeta,
      },
      {
        path: 'thesis',
        name: 'pts_admin_activities_practices_show_thesis',
        component: () =>
          import(
            /* webpackChunkName: "pts_admin_activities_practices_show_thesis" */
            '@/views/pts/admin/activity/practices/Thesis.vue'
          ),
        meta: {
          layout: 'header',
          title: '实习报告',
        } as RouteMeta,
      },
    ],
    meta: {
      layout: 'header',
      title: '详情',
    } as RouteMeta,
  },
];

export default route;
