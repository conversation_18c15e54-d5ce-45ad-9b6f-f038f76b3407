import { I<PERSON>out<PERSON>, RouteMeta } from '@/interfaces/route.interface';
import { resolve } from 'path';

const route: IRoute[] = [
  {
    path: '/pts',
    name: 'pts',
    component: () => import(/* webpackChunkName: "pts" */ '@/views/pts/Entry.vue'),
    children: [
      {
        path: 'teacher/activities',
        name: 'pts_teacher_activities',
        component: () => import(/* webpackChunkName: "pts_teacher_activities" */ '@/views/pts/teacher/Index.vue'),
        meta: {
          title: '实习管理',
        } as RouteMeta,
      },
    ],
    meta: {
      title: '顶岗实习',
      layout: 'default',
    } as RouteMeta,
  },
  {
    path: '/pts/teacher/activities/:activitiesId',
    name: 'pts_teacher_activities_show',
    component: () => import(/* webpackChunkName: "pts_teacher_activities_show" */ '@/views/pts/teacher/Show.vue'),
    meta: {
      title: '',
      layout: 'header',
    } as RouteMeta,
    children: [
      {
        path: 'console',
        name: 'pts_teacher_activities_show',
        component: () =>
          import(/* webpackChunkName: "pts_teacher_activities_show" */ '@/views/pts/teacher/activity/Show.vue'),
        children: [
          {
            path: '',
            name: 'pts_teacher_activities_show_statistics',
            component: () =>
              import(
                /* webpackChunkName: "pts_teacher_activities_show_statistics_registers" */
                '@/views/pts/teacher/activity/statistics/Registers.vue'
              ),
            meta: {
              layout: 'header',
              title: '打卡详情',
            } as RouteMeta,
          },

          // reports
          {
            path: 'reports',
            name: 'pts_teacher_activities_statistics_reports_index',
            component: () =>
              import(
                /* webpackChunkName: "pts_teacher_activities_show_statistics_reports" */
                '@/views/pts/teacher/activity/statistics/reports/Index.vue'
              ),
            meta: {
              layout: 'header',
              title: '实习周记',
            } as RouteMeta,
          },
          {
            path: 'reports/:reportsId',
            name: 'pts_teacher_activities_show_statistics_reports_show',
            component: () =>
              import(
                /* webpackChunkName: "pts_teacher_activities_show_statistics_reports_show" */
                '@/views/pts/teacher/activity/statistics/reports/Show.vue'
              ),
            meta: {
              layout: 'header',
              title: '周记详情',
            } as RouteMeta,
          },

          // companies
          {
            path: 'companies',
            name: 'pts_teacher_activities_show_statistics_companies',
            component: () =>
              import(
                /* webpackChunkName: "pts_teacher_activities_show_statistics_companies" */
                '@/views/pts/teacher/activity/statistics/Companies.vue'
              ),
            meta: {
              layout: 'header',
              title: '企业评价',
            } as RouteMeta,
          },
          {
            path: 'companies/:companiesId',
            name: 'pts_teacher_activities_show_statistics_companies_index',
            component: () =>
              import(
                /* webpackChunkName: "pts_teacher_activities_show_statistics_companies_index" */
                '@/views/pts/teacher/activity/statistics/companies/Index.vue'
              ),
            meta: {
              layout: 'header',
              title: '企业评价详情',
            } as RouteMeta,
          },

          // theses
          {
            path: 'theses',
            name: 'pts_teacher_activities_show_statistics_theses',
            component: () =>
              import(
                /* webpackChunkName: "pts_teacher_activities_show_statistics_theses" */
                '@/views/pts/teacher/activity/statistics/Theses.vue'
              ),
            meta: {
              layout: 'header',
              title: '实习报告',
            } as RouteMeta,
          },
          {
            path: 'theses/:practiceId',
            name: 'pts_teacher_activities_show_statistics_theses_Index',
            // 懒加载 一
            // component: () =>
            //   import(
            //     /* webpackChunkName: "pts_teacher_activities_show_statistics_theses_Index" */
            //     // '@/views/pts/teacher/activity/statistics/theses/Index.vue'
            //     // '@/views/pt/activity/practice/Thesis.vue'
            //     '@/views/pts/teacher/activity/statistics/theses/Thesis.vue'
            //   ),
            // 懒加载 二
            // component: (resolve: any) =>
            //   require(['@/views/pts/teacher/activity/statistics/theses/Thesis.vue'], resolve),
            // 懒加载 三
            component: (resolve: any) =>
              require.ensure(
                [],
                () => resolve(require('@/views/pts/teacher/activity/statistics/theses/Show.vue')),
                'Thesis',
              ),
            meta: {
              layout: 'header',
              title: '实习报告详情',
            } as RouteMeta,
          },
          {
            path: 'replies',
            name: 'ptsTeacherActivityRepliesIndex',
            component: () =>
              import(
                // eslint-disable-next-line max-len
                /* webpackChunkName: "ptsTeacherActivityRepliesIndex" */ '@/views/pts/teacher/activity/replies/Index.vue'
              ),
            meta: {
              title: '顶岗答辩',
              layout: 'header',
            },
          },
        ],
        meta: {
          layout: 'header',
          title: '',
        } as RouteMeta,
      },
      {
        path: 'practices',
        name: 'pts_teacher_activities_practices',
        component: () =>
          import(
            /* webpackChunkName: "pts_teacher_activities_practices" */ '@/views/pts/teacher/activity/Practices.vue'
          ),
        meta: {
          layout: 'header',
          title: '指导学生',
        } as RouteMeta,
      },
      {
        path: 'practivity_replies',
        name: 'pts_teacher_activities_practivity_replies',
        component: () =>
          import(
            /* webpackChunkName: "PtTeacherActivitiesPractivityReplies" */
            '@/views/pts/teacher/activity/replies/PractivityReplies.vue'
          ),
        meta: {
          layout: 'header',
          title: '答辩任务',
        } as RouteMeta,
      },
    ],
  },
  //
  {
    path: '/pts/teacher/activities/:activitiesId/practices/:practiceId',
    name: 'pts_teacher_activities_show_practices_show',
    component: () =>
      import(
        /* webpackChunkName: "pts_teacher_activities_show_practices_show" */

        '@/views/pts/teacher/activity/practices/Show.vue'
      ),
    children: [
      {
        path: '',
        name: 'pts_teacher_activities_show_practices_show_basicInfo',
        component: () =>
          import(
            /* webpackChunkName: "pts_teacher_activities_show_practices_show_basicInfo" */
            '@/views/pts/teacher/activity/practices/BasicInfo.vue'
          ),
        meta: {
          layout: 'header',
          title: '基本信息',
        } as RouteMeta,
      },
      {
        path: 'reports',
        name: 'pts_teacher_activities_practices_reports_index',
        component: () =>
          import(
            /* webpackChunkName: "pts_teacher_activities_show_practices_show_reports" */
            '@/views/pts/teacher/activity/practices/reports/Index.vue'
          ),
        meta: {
          layout: 'header',
          title: '实习周记',
        } as RouteMeta,
      },
      {
        path: 'reports/:reportId',
        name: 'pts_teacher_activities_show_practices_reports_show',
        component: () =>
          import(
            /* webpackChunkName: "pts_teacher_activities_show_practices_show_reports" */
            '@/views/pts/teacher/activity/practices/reports/Show.vue'
          ),
        meta: {
          layout: 'header',
          title: '详情周记',
        } as RouteMeta,
      },
      {
        path: 'companies',
        name: 'pts_teacher_activities_show_practices_show_companies',
        component: () =>
          import(
            /* webpackChunkName: "pts_teacher_activities_show_practices_show_companies" */
            '@/views/pts/teacher/activity/practices/Companies.vue'
          ),
        meta: {
          layout: 'header',
          title: '企业报告',
        } as RouteMeta,
      },
      {
        path: 'thesis',
        name: 'pts_teacher_activities_show_practices_thesis_show',
        component: () =>
          import(
            /* webpackChunkName: "pts_teacher_activities_show_practices_thesis_show" */
            // '@/views/pt/activity/practice/Thesis.vue'
            '@/views/pts/teacher/activity/statistics/theses/Show.vue'
          ),
        meta: {
          layout: 'header',
          title: '实习报告',
        } as RouteMeta,
      },
      {
        path: 'registers',
        name: 'pts_teacher_activities_show_practices_show_registers',
        component: () =>
          import(
            /* webpackChunkName: "pts_teacher_activities_show_practices_show_registers" */
            '@/views/pts/teacher/activity/practices/Registers.vue'
          ),
        meta: {
          layout: 'header',
          title: '打卡情况',
        } as RouteMeta,
      },
      {
        path: 'reply',
        name: 'pts_teacher_activities_show_practices_show_reply',
        component: () =>
          import(
            /* webpackChunkName: "pts_teacher_activities_show_practices_show_reply" */
            '@/views/pts/teacher/activity/replies/Show.vue'
          ),
        meta: {
          layout: 'header',
          title: '答辩情况',
        } as RouteMeta,
      },
    ],
    meta: {
      layout: 'header',
      title: '学生列表',
    } as RouteMeta,
  },
];

export default route;
