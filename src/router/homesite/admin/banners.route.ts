export default [
  {
    path: '/homesite/admin/banners',
    name: 'homesiteAdminBannersIndex',
    component: () =>
      import(/* webpackChunkName: "homesiteAdminBannersIndex" */ '@/views/homesite/admin/banners/Index.vue'),
    meta: {
      title: '轮播图',
      layout: 'autoMenu',
    },
  },
  {
    path: '/homesite/admin/titles',
    name: 'homesiteAdminTitlesIndex',
    component: () =>
      import(/* webpackChunkName: "homesiteAdminBannersIndex" */ '@/views/homesite/admin/titles/Index.vue'),
    meta: {
      title: '主题图',
      layout: 'autoMenu',
    },
  },
  {
    path: '/homesite/admin/media',
    name: 'homesiteAdminMediaIndex',
    component: () =>
      import(/* webpackChunkName: "homesiteAdminBannersIndex" */ '@/views/homesite/admin/media/Index.vue'),
    meta: {
      title: '媒体关注图',
      layout: 'autoMenu',
    },
  },
  {
    path: '/homesite/admin/newsImages',
    name: 'homesiteAdminMediaIndex',
    component: () =>
      import(/* webpackChunkName: "homesiteAdminBannersIndex" */ '@/views/homesite/admin/newsImages/Index.vue'),
    meta: {
      title: '图片新闻图',
      layout: 'autoMenu',
    },
  },
  {
    path: '/homesite/admin/news',
    name: 'homesiteAdminNewsIndex',
    component: () => import(/* webpackChunkName: "homesiteAdminNewsIndex;" */ '@/views/homesite/admin/news/Index.vue'),
    meta: {
      title: '校园网新闻',
      layout: 'autoMenu',
    },
  },
];
