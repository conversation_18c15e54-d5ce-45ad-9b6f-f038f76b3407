import { I<PERSON>out<PERSON>, RouteMeta } from '@/interfaces/route.interface';
import { resolve } from 'path';

const routes: IRoute[] = [
  {
    path: '/compete/admin',
    name: 'compete_admin_entry',
    component: () => import(/* webpackChunkName compete_admin_entry*/ '@/views/compete/Entry.vue'),
    meta: {
      title: '护理平台',
      layout: 'default',
    },
    children: [
      {
        path: 'activities',
        name: 'compete_teacher_activities_index',
        component: () =>
          import(/* webpackChunkName compete_admin_activities_index*/ '@/views/compete/activities/Index.vue'),
        meta: {
          title: '比赛管理',
          layout: 'default',
        },
      },
    ],
  },
  {
    path: '/compete/admin/activities/form',
    name: 'compete_admin_activities_form',
    component: () => import(/* webpackChunkName compete_admin_activities_form*/ '@/views/compete/activities/Form.vue'),
    meta: {
      title: '基本信息',
      layout: 'header',
    },
  },
  {
    path: '/compete/admin/activities/:activitiesId',
    name: 'compete_teacher_activities_show',
    component: () =>
      import(/* webpackChunkName compete_teacher_activities_show*/ '@/views/compete/activities/Show.vue'),
    meta: {
      title: '比赛详情',
      layout: 'header',
    },
    children: [
      {
        path: '',
        name: 'compete_teacher_activities_show_content',
        component: () =>
          import(
            /* webpackChunkName compete_teacher_activities_show_content*/ '@/views/compete/activities/Content.vue'
          ),
        meta: {
          title: '比赛内容',
          layout: 'header',
        },
      },
      {
        path: 'personnel',
        name: 'compete_teacher_activities_show_personnel',
        component: () =>
          import(
            /* webpackChunkName compete_teacher_activities_show_personnel*/ '@/views/compete/activities/Personnel.vue'
          ),
        meta: {
          title: '人员列表',
          layout: 'header',
        },
      },
    ],
  },
  {
    path: '/compete/admin/activities/:activitiesId/personnel/:Id',
    name: 'compete_teacher_activities_show_personnel_show',
    component: (resolve: (...modules: any[]) => void) =>
      require(['@/views/compete/activities/personnel/Show.vue'], resolve),
    meta: {
      title: '子比赛详情',
      layout: 'header',
    },
    children: [
      {
        path: '',
        name: 'compete_teacher_activities_show_personnel_content',
        // component: () =>
        //   import(
        //     /* webpackChunkName compete_teacher_activities_show_personnel_content*/
        //     '@/views/compete/activities/personnel/Content.vue'
        //   ),
        component: (resolve: (...modules: any[]) => void) =>
          require(['@/views/compete/activities/personnel/Content.vue'], resolve),
        meta: {
          title: '子比赛内容',
          layout: 'header',
        },
      },
    ],
  },
];

export default routes;
