import { IRoute } from '@/interfaces/route.interface';

const routes: IRoute[] = [
  {
    path: '/oa/user/workflows',
    name: 'oa_user_workflows',
    component: () => import(/* webpackChunkName: "oa_user_workflows" */ '@/views/oa/user/workflows/Index.vue'),
    meta: { title: '业务申请', layout: 'autoMenu' },
  },
  {
    path: '/oa/user/instances',
    name: 'oa_user_instances',
    component: () => import(/* webpackChunkName: "oa_user_instances" */ '@/views/oa/user/instances/Index.vue'),
    meta: { title: '动态', layout: 'autoMenu' },
  },
  {
    path: '/oa/user/workflows/:id',
    name: 'oa_user_workflows_show',
    component: () => import(/* webpackChunkName: "oa_user_workflows_show" */ '@/views/oa/user/instances/Index.vue'),
    meta: { title: '流程详情', layout: 'header', role: 'user' },
  },
];

export default routes;
