import { IRoute, RouteMeta } from '@/interfaces/route.interface';

const routes: IRoute[] = [
  {
    path: '/netdisk/teacher',
    name: 'netdisk_resources_entry',
    component: () => import(/* webpackChunkName entry*/ '@/views/netdisk/Entry.vue'),
    meta: {
      title: '资源管理系统',
      layout: 'default',
    },
    children: [
      {
        path: '/netdisk/teacher/menus/:menusId/items',
        name: 'netdisk_teacher_menus_items',
        component: () => import(/*webpackChunkName netdisk_teacher_menus_items */ '@/views/netdisk/menus/Index.vue'),
        meta: {
          title: '资源',
        } as RouteMeta,
      },
      // 学院资源首页
      {
        path: '/netdisk/teacher/colleges',
        name: 'netdisk_resources_colleges_index',
        component: () =>
          import(/*webpackChunkName netdisk_resources_colleges_index */ '@/views/netdisk/college/Index.vue'),
        meta: {
          title: '学院资源',
        } as RouteMeta,
      },
      // 我上传的
      {
        path: '/netdisk/teacher/owneds/items',
        name: 'netdisk_resources_personal_index',
        component: () =>
          import(/*webpackChunkName netdisk_resources_personal_index */ '@/views/netdisk/personal/Index.vue'),
        meta: {
          title: '我上传的',
        } as RouteMeta,
      },
      // {
      //   path: '/netdisk/teacher/owneds/:folderId',
      //   name: 'netdisk_resources_personal_index_folderId',
      //   component: () =>
      //     import(/*webpackChunkName netdisk_resources_personal_index_folder */ '@/views/netdisk/personal/Index.vue'),
      //   meta: {
      //     title: '我上传的',
      //   } as RouteMeta,
      // },
      // 我共享的
      {
        path: '/netdisk/teacher/personal/shareds',
        name: 'netdisk_resources_personal_shared',
        component: () =>
          import(/*webpackChunkName netdisk_resources_personal_shared */ '@/views/netdisk/personal/Shared.vue'),
        meta: {
          title: '共享给我的',
        } as RouteMeta,
      },
      // 专业资源
      {
        path: '/netdisk/teacher/majors',
        name: 'netdisk_resources_profession_index',
        component: () =>
          import(/*webpackChunkName netdisk_resources_profession_index */ '@/views/netdisk/major/Index.vue'),
        meta: {
          title: '专业资源',
        } as RouteMeta,
      },
      // 课程资源
      {
        path: '/netdisk/teacher/course_dirs',
        name: 'netdisk_resources_course_index',
        component: () =>
          import(/*webpackChunkName res_resources_course_index */ '@/views/netdisk/course_set/Index.vue'),
        meta: {
          title: '课程资源',
        } as RouteMeta,
      },
      // 审核
      {
        path: '/netdisk/teacher/reviews',
        name: 'netdisk_resources_review_index',
        component: () => import(/*webpackChunkName res_resources_review_index */ '@/views/netdisk/review/Index.vue'),
        meta: {
          title: '课程审核',
        } as RouteMeta,
      },
      // 标签
      {
        path: '/netdisk/teacher/tags',
        name: 'netdisk_resources_tag_index',
        component: () => import(/*webpackChunkName res_resources_tag_index*/ '@/views/netdisk/tag/Index.vue'),
        meta: {
          title: '标签管理',
        } as RouteMeta,
      },
      // 公共资源
      {
        path: '/netdisk/teacher/public',
        name: 'netdisk_resources_public_index',
        component: () =>
          import(/*webpackChunkName netdisk_resources_public_index */ '@/views/netdisk/public/Index.vue'),
        meta: {
          title: '公共资源',
        } as RouteMeta,
      },
      // 技能大赛资源
      {
        path: '/netdisk/teacher/contest',
        name: 'netdisk_resources_contest_index',
        component: () =>
          import(/*webpackChunkName netdisk_resources_contest_index */ '@/views/netdisk/contest/Index.vue'),
        meta: {
          title: '技能大赛资源',
        } as RouteMeta,
      },
    ],
  },

  {
    path: '/netdisk/teacher/colleges/:collegeId',
    name: 'netdisk_resources_Index',
    component: () => import(/* webpackChunkName entry*/ '@/views/netdisk/Show.vue'),
    meta: {
      title: '资源详情',
      layout: 'default',
    },
    children: [
      //  学院资源详情
      {
        path: 'items',
        name: 'netdisk_resources_colleges_show',
        component: () =>
          import(/*webpackChunkName netdisk_resources_colleges_show */ '@/views/netdisk/college/Show.vue'),
        meta: {
          title: '学院资源',
          layout: 'header',
        } as RouteMeta,
      },
      // 资源列表
      {
        path: 'lists',
        name: 'netdisk_resources_colleges_list',
        component: () => import(/*webpackChunkName res_resources_list */ '@/views/netdisk/list.vue'),
        meta: {
          title: '资源列表',
          layout: 'header',
        } as RouteMeta,
      },
    ],
  },
  // 学院资源 show
  {
    path: '/netdisk/teacher/colleges/:collegeId',
    name: 'netdisk_resources_Index',
    component: () => import(/* webpackChunkName entry*/ '@/views/netdisk/Show.vue'),
    meta: {
      title: '资源详情',
      layout: 'default',
    },
    children: [
      //  学院资源详情
      {
        path: 'items',
        name: 'netdisk_resources_colleges_show',
        component: () =>
          import(/*webpackChunkName netdisk_resources_colleges_show */ '@/views/netdisk/college/Show.vue'),
        meta: {
          title: '学院资源',
          layout: 'header',
        } as RouteMeta,
      },
      // 资源列表
      {
        path: 'majors',
        name: 'netdisk_resources_colleges_majors',
        component: () => import(/*webpackChunkName netdisk_resources_colleges_majors */ '@/views/netdisk/list.vue'),
        meta: {
          title: '学院资源',
          layout: 'header',
        } as RouteMeta,
      },
    ],
  },
  // 课程资源show
  {
    path: '/netdisk/teacher/course_dirs/:courseId',
    name: 'netdisk_resources_Index',
    component: () => import(/* webpackChunkName entry*/ '@/views/netdisk/Show.vue'),
    meta: {
      title: '课程资源',
      layout: 'default',
    },
    children: [
      {
        path: 'items',
        name: 'netdisk_resources_courses_dirs_show',
        component: () =>
          import(/*webpackChunkName netdisk_resources_courses_dirs_show */ '@/views/netdisk/course_set/Show.vue'),
        meta: {
          title: '课程资源',
          layout: 'header',
        } as RouteMeta,
      },
    ],
  },
  // 专业资源 show
  {
    path: '/netdisk/teacher/majors/:majorsId',
    name: 'netdisk_resources_Index',
    component: () => import(/* webpackChunkName entry*/ '@/views/netdisk/Show.vue'),
    meta: {
      title: '资源详情',
      layout: 'default',
    },
    children: [
      {
        path: 'items',
        name: 'netdisk_resources_majors_show',
        component: () => import(/*webpackChunkName res_resources_profession_show */ '@/views/netdisk/major/Show.vue'),
        meta: {
          title: '专业资源',
          layout: 'header',
        } as RouteMeta,
      },
      {
        path: 'courses',
        name: 'netdisk_resources_majors_courses_sets',
        component: () => import(/*webpackChunkName res_resources_list */ '@/views/netdisk/list.vue'),
        meta: {
          title: '专业资源',
          layout: 'header',
        } as RouteMeta,
      },
    ],
  },
  {
    path: '/netdisk/admin/menus',
    name: 'netdisk_menus_form',
    component: () => import(/*webpackChunkName netdisk_teacher_menus_form*/ '@/views/netdisk/menus/Form.vue'),
    meta: {
      title: '菜单管理',
      layout: 'header',
    } as RouteMeta,
  },
];

export default routes;
