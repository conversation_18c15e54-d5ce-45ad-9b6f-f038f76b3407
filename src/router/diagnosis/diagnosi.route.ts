import { IRout<PERSON>, RouteMeta } from '@/interfaces/route.interface';

const routes: IRoute[] = [
  {
    path: '/:category/teacher',
    name: 'diagnosis_resources_entry',
    component: () => import(/* webpackChunkName diagnosis_resources_entry*/ '@/views/diagnosis/Entry.vue'),
    meta: {
      title: '诊改资源类型',
      layout: 'default',
    },
    children: [
      {
        path: 'menus/:menusId/items',
        name: 'diagnosis_resources_Index',
        component: () => import(/* webpackChunkName diagnosis_resources_Index*/ '@/views/diagnosis/Index.vue'),
        meta: {
          title: '复核材料',
          layout: 'default',
        },
      },
    ],
  },
];

export default routes;
