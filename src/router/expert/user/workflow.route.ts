import { IRoute, RouteMeta } from '@/interfaces/route.interface';

const Show = () => import(/* webpackChunkName: "expert_user_workflow_show" */ '@/views/expert/user/workflows/Show.vue');

export default [
  {
    path: '/expert/user/workflows/:id',
    name: 'expert_user_workflow_show',
    component: Show,
    meta: {
      title: '诊改流程',
      layout: 'autoMenu',
    } as RouteMeta,
  },
] as IRoute[];
