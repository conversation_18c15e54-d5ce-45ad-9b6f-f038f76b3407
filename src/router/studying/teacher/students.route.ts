import { IRoute } from '@/interfaces/route.interface';

export default [
  {
    path: '/studying/teacher/students',
    name: 'studying_teacher_students_index',
    component: () =>
      import(/* webpackChunkName: "studying_teacher_students_index" */ '@/views/studying/teacher/students/Index.vue'),
    meta: {
      title: '我的学生',
      layout: 'menu',
    },
  },
  {
    path: '/studying/teacher/students/:id',
    name: 'studying_teacher_students_show',
    component: () =>
      import(/* webpackChunkName: "studying_teacher_students_show" */ '@/views/studying/teacher/students/Show.vue'),
    meta: {
      title: '学生详情',
      layout: 'header',
    },
  },
  {
    path: '/studying/teacher/classes',
    name: 'studying_teacher_classes_index',
    component: () =>
      import(/* webpackChunkName: "studying_teacher_classes_index" */ '@/views/studying/teacher/classes/Index.vue'),
    meta: {
      title: '我的班级',
      layout: 'menu',
    },
  },
  {
    path: '/studying/teacher/classes/:id',
    name: 'studying_teacher_classes_show',
    component: () =>
      import(/* webpackChunkName: "studying_teacher_classes_show" */ '@/views/studying/teacher/classes/Show.vue'),
    meta: {
      title: '班级详情',
      layout: 'header',
    },
  },
  {
    path: '/studying/teacher/instances',
    name: 'studying_teacher_instances_index',
    component: () =>
      import(/* webpackChunkName: "studying_teacher_instances_index" */ '@/views/studying/teacher/instances/Index.vue'),
    meta: {
      title: '学生审批',
      layout: 'menu',
    },
  },
  {
    path: '/studying/teacher/records',
    name: 'studying_teacher_records_index',
    component: () => import(/* webpackChunkName: "records_manage" */ '@/views/studying/teacher/records/Index.vue'),
    meta: {
      title: '记录查询',
      layout: 'menu',
    },
  },
] as IRoute[];
