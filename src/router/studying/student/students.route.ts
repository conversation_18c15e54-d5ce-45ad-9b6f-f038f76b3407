import { IRoute, RouteMeta } from '@/interfaces/route.interface';

const AdminShow = () => import(/* webpackChunkName: "student_show" */ '@/views/studying/student/Show.vue');
const AdminApplication = () =>
  import(/* webpackChunkName: "student_application" */ '@/views/studying/student/instances/Index.vue');

export default [
  {
    path: '/studying/student/info',
    name: 'manage_admin_show',
    component: AdminShow,
    meta: {
      title: '我的信息',
      layout: 'menu',
    } as RouteMeta,
  },
  {
    path: '/studying/student/instances',
    name: 'manage_admin_index',
    component: AdminApplication,
    meta: {
      title: '提交申请',
      layout: 'menu',
    } as RouteMeta,
  },
] as IRoute[];
