import { I<PERSON>out<PERSON>, RouteMeta } from '@/interfaces/route.interface';

const StudyingAdminApproval = () => import(/* webpackChunkName: "student" */ '@/views/studying/admin/Approval.vue');
const StudyingAdminClassIndex = () =>
  import(/* webpackChunkName: "class_manage" */ '@/views/studying/admin/classes/Index.vue');
const StudyingAdminStudentIndex = () =>
  import(/* webpackChunkName: "student_manage" */ '@/views/studying/admin/students/Index.vue');
const StudyingAdminRecordsIndex = () =>
  import(/* webpackChunkName: "records_manage" */ '@/views/studying/admin/records/Index.vue');
const StudyingAdminClassShow = () =>
  import(/* webpackChunkName: "class_show" */ '@/views/studying/admin/classes/Show.vue');
const StudyingAdminStudentShow = () =>
  import(/* webpackChunkName: "StudentShow" */ '@/views/studying/admin/students/Show.vue');

export default [
  {
    path: '/studying/admin/classes',
    name: 'studying_admin_classes_index',
    component: StudyingAdminClassIndex,
    meta: {
      title: '班级管理',
      layout: 'menu',
      keepAlive: true,
    } as RouteMeta,
  },
  {
    path: '/studying/admin/approvals',
    name: 'studying_admin_approvals_index',
    component: StudyingAdminApproval,
    meta: {
      title: '学生审批',
      layout: 'menu',
    } as RouteMeta,
  },
  {
    path: '/studying/admin/students',
    name: 'studying_admin_students_index',
    component: StudyingAdminStudentIndex,
    meta: {
      title: '学生管理',
      layout: 'menu',
      keepAlive: true,
    } as RouteMeta,
  },
  {
    path: '/studying/admin/records',
    name: 'studying_admin_records_index',
    component: StudyingAdminRecordsIndex,
    meta: {
      title: '记录管理',
      layout: 'menu',
    } as RouteMeta,
  },
  {
    path: '/studying/admin/classes/:id',
    name: 'studying_admin_classes_show',
    component: StudyingAdminClassShow,
    meta: {
      title: '班级详情',
      layout: 'header',
    } as RouteMeta,
  },
  {
    path: '/studying/admin/students/:id',
    name: 'studying_admin_students_show',
    component: StudyingAdminStudentShow,
    meta: {
      title: '学生详情',
      layout: 'header',
    } as RouteMeta,
  },
] as IRoute[];
