import { IRout<PERSON>, RouteMeta } from '@/interfaces/route.interface';

export default [
  {
    path: '/prevention/admin/coming_and_goings',
    name: 'prevention_admin_coming_and_goings_index',
    component: () =>
      import(
        /* webpackChunkName: "prevention_admin_coming_and_goings_index" */
        '@/views/prevention/admin/coming_and_goings/Index.vue'
      ),
    meta: {
      title: '出入信息',
      layout: 'menu',
    } as RouteMeta,
  },
] as IRoute[];
