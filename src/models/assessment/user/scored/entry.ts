import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';
import { IAssessmentEntry } from '@/types/model';

export class AssessmentUserScoredEntry extends ActiveModel<IAssessmentEntry> {
  constructor(config?: IModelConfig) {
    super({
      namespace: '/assessment/user/scored',
      name: 'entry',
      dataIndexKey: 'records',
      actions: [
        { name: 'export', method: 'post', on: 'collection' },
        { name: 'export_headers', method: 'post', on: 'collection' },
      ],
      mode: 'shallow',
      ...config,
    });
  }
}
