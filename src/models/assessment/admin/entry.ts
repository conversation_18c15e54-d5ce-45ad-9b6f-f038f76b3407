import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';
import { IAssessmentEntry } from '@/types/model';

export class AssessmentAdminEntry extends ActiveModel<IAssessmentEntry> {
  constructor(config?: IModelConfig) {
    super({
      namespace: '/assessment/admin',
      name: 'entry',
      dataIndexKey: 'records',
      actions: [
        { name: 'batch_update', method: 'post', on: 'collection' },
        { name: 'export', method: 'post', on: 'collection' },
        { name: 'export_headers', method: 'post', on: 'collection' },
      ],
      mode: 'shallow',
      ...config,
    });
  }
}
