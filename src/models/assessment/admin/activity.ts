import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';
import { IAssessmentActivity } from '@/types/model';

export class AssessmentAdminActivity extends ActiveModel<IAssessmentActivity> {
  constructor(config?: IModelConfig) {
    super({
      namespace: '/assessment/admin',
      name: 'activity',
      dataIndexKey: 'records',
      actions: [
        { name: 'export_entries', method: 'post', on: 'member' },
        { name: 'export_compete_entries', method: 'post', on: 'member' },
        { name: 'init_scores', method: 'post', on: 'member' },
      ],
      ...config,
    });
  }
}
