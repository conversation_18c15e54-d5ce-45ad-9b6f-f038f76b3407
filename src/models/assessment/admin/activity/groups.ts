import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';
import { IAssessmentCatalog } from '@/types/model';
import { IDepartment } from '../../../department';

export interface IGroup {
  id: number;
  activity_id: number;
  name: string;
  meta: any;
  activity_name: string;
  created_at: Date;
  updated_at: Date;
  catalogs: IAssessmentCatalog[];

  departments: IDepartment[];
  paste_department_ids: number[];
}

export class AssessmentAdminActivityGroups extends ActiveModel<IGroup> {
  constructor(config?: IModelConfig) {
    super({
      name: 'group',
      namespace: '/assessment/admin',
      dataIndexKey: 'records',
      mode: 'shallow',
      ...config,
    });
  }
}
