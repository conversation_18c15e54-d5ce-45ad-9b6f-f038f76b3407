import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';
import { IEntry } from '@/models/access/entry';
import { IScore } from '@/models/access/score';

export class AssessmentAdminActivityScores extends ActiveModel<IScore> {
  constructor(config?: IModelConfig) {
    super({
      name: 'score',
      namespace: '/assessment/admin',
      mode: 'shallow',
      dataIndexKey: 'records',
      ...config,
    });
  }
}
