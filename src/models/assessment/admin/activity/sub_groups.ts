import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';

export interface ISubGroups {
  id: number;
  group_id: number;
  catalog_id: number;
  created_at: Date;
  updated_at: Date;
}
export class AssessmentAdminActivitySubGroups extends ActiveModel<ISubGroups> {
  constructor(config?: IModelConfig) {
    super({
      name: 'sub_group',
      namespace: '/assessment/admin',
      dataIndexKey: 'records',
      mode: 'shallow',
      ...config,
    });
  }
}
