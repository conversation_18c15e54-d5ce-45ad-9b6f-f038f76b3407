import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';

export interface IDimensions {
  id: number;
  catalog_id: number;
  weight: number;
  type: string;
  score_template_id: number;
  name: string;
  deleted_at: Date;
  created_at: Date;
  updated_at: Date;
}

export class AssessmentAdminActivityCatalogsDimensions extends ActiveModel<IDimensions> {
  constructor(config?: IModelConfig) {
    super({
      name: 'dimension',
      namespace: '/assessment/admin',
      mode: 'shallow',
      dataIndexKey: 'records',
      actions: [
        { name: 'upload_excel', method: 'post', on: 'collection' },
        { name: 'excel', method: 'post', on: 'collection' },
        { name: 'import_headers', method: 'post', on: 'collection' },
        { name: 'import', method: 'post', on: 'collection' },
      ],
      ...config,
    });
  }
}
