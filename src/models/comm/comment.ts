/*
################# index column ########
#
#  id                                    :bigint           not null, primary key
#  commentable_id                        :integer
#  commentable_type                      :string(255)
#  title                                 :string(255)
#  body                                  :text(65535)
#  subject                               :string(255)
#  user_id                               :integer
#  user_type                             :string(255)
#  parent_id                             :integer
#  lft                                   :integer
#  rgt                                   :integer
#  created_at                            :datetime         not null
#  updated_at                            :datetime         not null
#  attachments(评论的附件，可以添加多个) :json
#
*/

import BaseModel from '../BaseModel';

export interface IComment {
  id: number;
  title: string;
  body: string;
  subject: string;
  parent_id: number;
  noCompare?: boolean;
}

export interface IResponse {
  current_page: number;
  total_pages: number;
  total_count: number;
  comments: IComment[];
}

export class Comment extends BaseModel<IComment, IResponse> {
  constructor() {
    super({
      name: 'comment',
      resource: 'comments',
      namespace: '/comm',
      parentResource: 'instances',
      role: 'user',
    });
  }

  // ======= topics =====
  getTopicComments(
    { courseId, topicId, params }: { courseId: number; topicId: number; params: object },
    isDiscussMode: boolean = false,
  ) {
    return this.request.get<IResponse>(`/teaching/user/courses/${courseId}/topics/${topicId}/comments`, {
      params: {
        ...params,
        mode: isDiscussMode ? 'discuss' : null,
      },
    });
  }

  deleteTopicComment({ courseId, topicId, commentId }: { courseId: number; topicId: number; commentId: number }) {
    return this.request.delete(`/teaching/user/courses/${courseId}/topics/${topicId}/comments/${commentId}`);
  }

  // ======== report =========
  getReportComments({ reportId, params }: { reportId: number; params: object }) {
    return this.request.get<IResponse>(`/comm/user/reports/${reportId}/comments`, {
      params,
    });
  }

  // 可用路由：parentPath的格式: /teaching/user/topics/1
  indexByParent(params: any) {
    return this.request.get(`${params.parentPath}/comments`, { params });
  }
  // 可用路由：parentPath的格式: /teaching/user
  deleteByParent(params: any) {
    return this.request.delete(`${params.parentPath}/comments/${params.id}`);
  }
}

export default new Comment();
