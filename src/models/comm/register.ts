import BaseModel from '../BaseModel';
import { IFile } from '../file';

export interface IRegister {
  id?: number;
  created_at?: string;
  updated_at?: string;
  user_type?: string;
  user_id?: number;
  source_type?: string;
  source_id?: number;
  state?: 'done' | 'undo' | 'late';
  type?: string;
  lon?: any;
  lat?: any;
  student: {
    name: string;
    avatar: IFile;
  };
}

export interface IResponse {
  registers: IRegister[];
}

interface ILocation {
  address: string;
  city: string;
  district: string;
  lat: number;
  lon: number;
  province: string;
  status: 'successed' | 'failed';
}

export class Register extends BaseModel<IRegister, IResponse> {
  constructor() {
    super({
      name: 'register',
      resource: 'register',
      namespace: '/comm/user',
    });
  }

  // 学生获取扫码详情
  findByNonce(nonce: string) {
    return this.request.get<IRegister>(`/comm/user/register?nonce=${nonce}`);
  }

  getAddress(location: { lon?: number; lat?: number; ip?: string }) {
    return this.request.post<ILocation>('/comm/user/register/address', {
      ...location,
    });
  }
}

export default new Register();
