import BaseModel from '../BaseModel';

export interface ITopic {
  id?: number;
  school_id?: number;
  college_id?: number;
  major_id?: number;
  user_type?: string;
  user_id?: number;
  source_type?: string;
  source_id?: string;
  type?: string;
  title?: string;
  body?: string;
  state?: string;
  cover_image?: string;
  view_count?: number;
  like_count?: number;
  star_count?: number;
  meta?: IObject;
  operations?: string[];
  noCompare?: boolean;
}

export interface IResponse {
  topics: ITopic[];
}

export class Topic extends BaseModel<ITopic, IResponse> {
  constructor() {
    super({
      name: 'topic',
      resource: 'topics',
      namespace: '/teaching',
      role: 'user/courses/:courseId',
    });
  }

  indexByCourse(courseId: number, params: IObject) {
    return this.request.get(`/teaching/user/courses/${courseId}/topics`, { params });
  }

  // =============== deprecated =================
  // 可用路由：parentPath的格式: /teaching/user/courses/1
  indexByParent(params: any) {
    return this.request.get(`${params.parentPath}/topics`, { params });
  }
  // 可用路由：parentPath的格式: /teaching/user/courses/1
  findByParent(params: any) {
    return this.request.get(`${params.parentPath}/topics/${params.id}`, { params });
  }
  // 可用路由：parentPath的格式: /teaching/user
  deleteByParent(params: any) {
    return this.request.delete(`${params.parentPath}/topics/${params.id}`);
  }
}
