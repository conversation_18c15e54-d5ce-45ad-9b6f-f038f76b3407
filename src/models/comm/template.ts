/*
################# index column ########
#
#  id                     :bigint           not null, primary key
#  school_id(学校id)      :bigint
#  teacher_id(教师id)     :bigint
#  title(主题)            :string(255)
#  type(STI)              :string(255)
#  state(状态)            :integer          default("active")
#  meta(扩展字段)         :json
#  deleted_at(软删除标识) :datetime
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#
*/

import BaseModel from '../BaseModel';

export interface ITemplate {
  id?: number;
  school_id: number;
  teacher_id: number;
  title: string;
  type: string;
  state: string;
  meta: any;
  infos?: any[];
}

export interface IResponse {
  templates: ITemplate[];
}

export class Template extends BaseModel<ITemplate, IResponse> {
  constructor() {
    super({
      name: 'template',
      resource: 'templates',
      namespace: '/comm/admin',
    });
  }
}

export default new Template();
