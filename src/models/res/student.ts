import BaseModel from '../BaseModel';
import { IFile } from '../file';

export interface IStudent {
  adminclass_id?: number;
  adminclass_name?: string;
  avatar?: IFile;
  code?: string;
  college_id?: number;
  college_name?: string;
  id?: number;
  identity_id?: string;
  major_id?: number;
  major_name?: string;
  name?: string;
  nation?: string;
  phone?: string;
  sex?: string;
  building_name?: string;
  floor?: number;
  room?: string;
  bed?: string;
  admit_major_name?: string;
}

interface IResponse {
  students: IStudent[];
}

export class Student extends BaseModel<IStudent, IResponse> {
  constructor() {
    super({
      name: 'student',
      resource: 'students',
      namespace: '/res',
      role: 'teacher',
    });
  }
}
