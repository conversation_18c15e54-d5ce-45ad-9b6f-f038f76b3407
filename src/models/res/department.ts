/*
################# index column ########
#  id                         :bigint           not null, primary key
#  school_id(所属学校)        :bigint
#  name(名称)                 :string(255)
#  code(代码)                 :string(255)
#  short_name(简称)           :string(255)
#  ancestry(组织机构的树路径) :string(255)
#  depth(层级)                :integer
#  type(STI类型)              :string(255)
#  meta(扩展字段)             :json
#  created_at                 :datetime         not null
#  updated_at                 :datetime         not null
#
*/

import BaseModel from '../BaseModel';

export interface IDepartment {
  id: number;
  name?: string;
  code?: string;
  short_name?: string;
  parent_id?: number;
  type?: string;
  meta?: IObject;
  depth?: number;
  children_count?: number;
  children?: IDepartment[];
}

export interface IResponse {
  departments: IDepartment[];
}

export class Department extends BaseModel<IDepartment, IResponse> {
  constructor() {
    super({
      name: 'department',
      resource: 'departments',
      namespace: '/res/admin',
    });
  }

  tree(params: any) {
    return this.request.get<IResponse>(`/res/teacher/departments/tree`, { params });
  }

  static convertTreeData(departments: IDepartment[]): IDepartment[] {
    return departments.map(d => ({
      ...d,
      key: d.id,
      value: d.id,
      title: d.short_name,
      children: this.convertTreeData(d.children || []),
    }));
  }

  getDepartmentKeys(departments: any[] = [], keyword: string) {
    let parentKeys: any = [];
    (departments || []).forEach((node: any) => {
      if (node.name.indexOf(keyword) > -1) {
        parentKeys.push(node.key);
        if (node.children_count) {
          parentKeys = parentKeys.concat(this.getDepartmentKeys(node.children, keyword));
        }
      } else if (node.children_count) {
        parentKeys = parentKeys.concat(this.getDepartmentKeys(node.children, keyword));
      }
    });
    return parentKeys;
  }

  filterDepartments(departments: any[] = [], keyword: string) {
    const res = (departments || []).map((node: any) => {
      if (node.name.indexOf(keyword) > -1 || node.short_name.indexOf(keyword) > -1) {
        if (node.children_count) {
          node.children = this.filterDepartments(node.children, keyword);
        }
        return node;
      } else if (node.children_count) {
        node.children = this.filterDepartments(node.children, keyword);
        return node;
      }
    });
    return (res || []).filter((e: any) => e);
  }
}

export default new Department();
