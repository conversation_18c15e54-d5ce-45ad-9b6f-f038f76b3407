/**
 * 人才培养方案(program)
 *
 * belongs_to major(专业)
 *
 * name:	string            培养方案名称
   grade:	string            培养方案的年级
   department_id:	integer   所属部门
   duration:	float         年限
   study_type:	string      学习类型
   degree:	string          学位
   effective_on:	date      生效日期
   invalid_on:	date        时效日期
   remark:	text            说明
   meta:	json              扩展字段
 */

import BaseModel from '@/models/BaseModel';
import { IPlan } from '../teaching/plan';

export interface IProgram {
  id: number;
  created_at: string;
  updated_at: string;
  school_id: number;
  name: string;
  grade: string;
  department_id?: number;
  major_id: number;
  duration: number;
  study_type: string;
  degree: string;
  effective_on: string;
  invalid_on: string;
  remark: string;
  major_name: string;
  major_code: string;
  major_direction?: string;
  department_name?: string;
  department_code?: string;
  adminclasses_count: number;
  students_count: number;
  current_term: number;
  plan: IPlan;
  meta?: IObject;
}

interface IResponse {
  programs: IProgram[];
}

export class Program extends BaseModel<IProgram, IResponse> {
  constructor(role: 'admin' | 'teacher') {
    super({
      name: 'program',
      resource: 'programs',
      namespace: '/res',
      parentResource: 'majors',
      role,
    });
  }
}
