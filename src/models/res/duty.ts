/*
################# index column ########
#  id                                 :bigint           not null, primary key
#  department_id(职务对应的部门)      :bigint
#  is_manager(是否是这个部门的管理岗) :boolean          default(FALSE)
#  name(职务名称)                     :string(255)
#  deleted_at(软删除使用的字段)       :datetime
#  created_at                         :datetime         not null
#  updated_at                         :datetime         not null
#  school_id(学校ID)                  :integer
#
*/

import BaseModel from '../BaseModel';
import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';

export interface IDuty {
  id: number;
  school_id: number;
  department_id: number;
  is_manager: boolean;
  name: string;
  department_name?: number;
  department?: any;
  teacher_ids?: any;
}

export interface IResponse {
  duties: IDuty[];
}

export class Duty extends BaseModel<IDuty, IResponse> {
  constructor(role: 'admin') {
    super({
      name: 'duty',
      resource: 'duties',
      namespace: '/res',
      role,
    });
  }
}

// 新的Model定义 获取组织结构下的职务详情
export class AdminDuties extends ActiveModel<IDuty> {
  constructor(config?: IModelConfig) {
    super({
      name: 'duty',
      namespace: 'res/admin',
      ...config,
    });
  }
}

export default new Duty('admin');
