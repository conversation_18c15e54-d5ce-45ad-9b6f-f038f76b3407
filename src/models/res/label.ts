/*
################# index column ########
#  id               :bigint           not null, primary key
#  school_id(学校)  :bigint
#  teacher_id(教师) :bigint
#  name(名称)       :string(255)      default("")
#  type(STI)        :string(255)
#  flag(标识)       :integer          default("hr")
#  deleted_at       :datetime
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#
*/

import BaseModel from '../BaseModel';

export interface ILabel {
  id: number;
  school_id: number;
  teacher_id: number;
  name: string;
  type: string;
  flag: number;
}

export interface IResponse {
  labels: ILabel[];
}

export class Label extends BaseModel<ILabel, IResponse> {
  constructor() {
    super({
      name: 'label',
      resource: 'labels',
      namespace: '/comm/admin',
    });
  }
}

export default new Label();
