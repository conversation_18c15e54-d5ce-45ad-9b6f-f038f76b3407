/*
################# index column ########
#  id                   :bigint           not null, primary key
#  school_id(学校id)    :bigint
#  teacher_id(教师id)   :bigint
#  name(名称)           :string(255)      default("")
#  meta(扩展字段)       :json
#  deleted_at(软删标识) :datetime
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#
*/

import BaseModel from '../BaseModel';
import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';

export interface ITitleCategory {
  id: number;
  school_id: number;
  teacher_id: number;
  name: string;
  meta: any;
  titles_attributes?: any[];
  titles?: any[];
  rows?: any[];
  cols?: any[];
  noCompare?: boolean;
  title_category?: any;
}

export interface IResponse {
  title_categories: ITitleCategory[];
}

export class TitleCategory extends BaseModel<ITitleCategory, IResponse> {
  constructor() {
    super({
      name: 'title_category',
      resource: 'categories',
      namespace: '/res/admin',
    });
  }
}

// 新

export class TeacherTitleCategory extends ActiveModel<ITitleCategory> {
  constructor(config?: IModelConfig) {
    super({
      name: 'title_category',
      namespace: '/res/admin',
      pathIndexKey: 'categories',
      ...config,
    });
  }
}

export default new TitleCategory();
