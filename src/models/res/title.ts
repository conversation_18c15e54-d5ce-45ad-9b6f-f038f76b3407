/*
################# index column ########
#  id                     :bigint           not null, primary key
#  school_id(学校id)      :bigint
#  level(等级)            :string(255)      default(NULL)
#  name(职称名称)         :string(255)      default("")
#  series(职称系列)       :string(255)      default("")
#  deleted_at(软删除标识) :datetime
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#
*/

import BaseModel from '../BaseModel';

export interface ITitle {
  id: number;
  school_id: number;
  level: string;
  name: string;
  series: string;
}

export interface IResponse {
  titles: ITitle[];
}

export class Title extends BaseModel<ITitle, IResponse> {
  constructor() {
    super({
      name: 'title',
      resource: 'titles',
      namespace: '/res/admin',
    });
  }
}

export default new Title();
