/**
 * Major (专业)
 *
 * has_many programs (人才培养方案)
 *
 */
import BaseModel from '../BaseModel';

export interface IMajor {
  id?: number;
  created_at?: string;
  updated_at?: string;
  school_id?: number;
  code?: string;
  name?: string;
  short_name?: string;
  eng_name?: string;
  duration?: number;
  project?: string;
  meta?: any;
  department_id?: number;
  gb_code?: any;
  direction?: any;
  programs_count?: number;
}

export interface IResponse {
  majors: IMajor[];
}

export class Major extends BaseModel<IMajor, IResponse> {
  constructor(role: 'admin' | 'teacher') {
    super({
      name: 'major',
      namespace: '/res',
      resource: 'majors',
      role,
    });
  }
}
