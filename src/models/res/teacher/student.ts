import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';
import { IStudent } from '../../../service/student';

export class ResTeacherStudent extends ActiveModel<IStudent> {
  constructor(config?: IModelConfig) {
    super({
      namespace: '/res/teacher',
      name: 'student',
      actions: [
        {
          name: 'find_by_file',
          method: 'post',
          on: 'collection',
        },
        // { name: 'export', method: 'post', on: 'collection' },
        { name: 'upload_excel', method: 'post', on: 'collection' },
        { name: 'excel', method: 'post', on: 'collection' },
        { name: 'import_headers', method: 'post', on: 'collection' },
      ],
      ...config,
    });
  }
}
