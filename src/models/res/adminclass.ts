/**
 * adminclass (班级)
 *
 * belongs_to major (专业)
 */
import BaseModel from '../BaseModel';

export interface IAdminclass {
  id?: number;
  created_at?: string;
  updated_at?: string;
  code?: string;
  name?: string;
  short_name?: string;
  grade?: string;
  major_id?: number;
  std_type?: string;
  plan_count?: number;
  department_id?: any;
  program_id?: number;
  students_count?: number;
  major_name?: string;
  department_name?: any;
  program_name?: string;
}

export interface IResponse {
  adminclasses: IAdminclass[];
}

export class Adminclass extends BaseModel<IAdminclass, IResponse> {
  constructor(role: 'admin' | 'teacher' | 'student') {
    super({
      name: 'adminclass',
      namespace: '/res',
      resource: 'adminclasses',
      parentResource: 'majors',
      role,
    });
  }
}
