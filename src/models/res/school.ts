/*
################# index column ########
#  id            :bigint           not null, primary key
#  name(名称)    :string(255)
#  address(地址) :string(255)
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#
*/

import BaseModel from '../BaseModel';

export interface ISchool {
  id: number;
  name: string;
  address: string;
}

export interface IResponse {
  schools: ISchool[];
}

export class School extends BaseModel<ISchool, IResponse> {
  constructor() {
    super({
      name: 'school',
      resource: 'schools',
      namespace: '/res',
    });
  }
}

export default new School();
