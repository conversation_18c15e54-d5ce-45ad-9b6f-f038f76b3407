/*
################# index column ########
#  id                    :bigint           not null, primary key
#  school_id(学校ID)     :bigint
#  department_id(组织ID) :bigint
#  name(名称)            :string(255)      default("")
#  code(学院代码)        :string(255)      default("")
#  short_name(简称)      :string(255)      default("")
#  eng_name(英文简称)    :string(255)      default("")
#  ancestry(树形结构)    :string(255)
#  depth(树形深度)       :integer
#  meta(扩展字段)        :json
#  deleted_at(删除标识)  :datetime
#  created_at            :datetime         not null
#  updated_at            :datetime         not null
#
*/

import BaseModel from '../BaseModel';

export interface ICollege {
  id: number;
  school_id: number;
  department_id: string;
  name: string;
  code: string;
  short_name: string;
  eng_name: string;
  ancestry: string;
  depth: number;
  meta: any;
}

export interface IResponse {
  colleges: ICollege[];
}

export class College extends BaseModel<ICollege, IResponse> {
  constructor() {
    super({
      name: 'college',
      resource: 'colleges',
      namespace: '/res/admin',
    });
  }
}

export default new College();
