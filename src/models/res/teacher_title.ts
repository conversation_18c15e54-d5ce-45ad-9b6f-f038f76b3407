/*
################# index column ########
#  id                                :bigint           not null, primary key
#  teacher_id(教师id)                :bigint
#  title_id(职称id)                  :bigint
#  created_at                        :datetime         not null
#  updated_at                        :datetime         not null
#  state(状态)                       :string(255)
#  country_title_id(评定职称)        :integer
#  country_level(评定职称等级)       :string(255)
#  country_auth_at(评定职称认证时间) :date
#  school_title_id(聘用职称)         :integer
#  school_level(聘用职称等级)        :string(255)
#  school_auth_at(聘用职称认证时间)  :date
#  teacher_title_id(内部职称)        :integer
#  teacher_level(内部职称等级)       :string(255)
#  teacher_auth_at(内部职称认证时间) :date
#  title_category_id(职称类别)       :bigint
#  meta(扩展字段)                    :json
#
*/

import BaseModel from '../BaseModel';
import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';

export interface ITeacherTitle {
  id: number;
  title_id?: number;
  teacher_id: number;
  state?: string;
  country_title_id?: number;
  country_level?: string;
  country_auth_at?: string;
  school_title_id?: number;
  school_level?: string;
  school_auth_at?: string;
  teacher_title_id?: number;
  teacher_level?: string;
  teacher_auth_at?: string;
  title_category_id?: number;
  teacher_name?: string;
  teacher_code?: string;
  teacher_department_name?: string;
  title_category_name?: string;
  meta?: any;
}

export interface IResponse {
  title_categories: ITeacherTitle[];
}

export class TeacherTitle extends BaseModel<ITeacherTitle, IResponse> {
  constructor() {
    super({
      name: 'teacher_title',
      resource: 'teacher_titles',
      namespace: '/res/admin',
    });
  }
}

// 新
export class ResTeacherTitle extends ActiveModel<ITeacherTitle> {
  constructor(config: IModelConfig) {
    super({
      name: 'teacher_title',
      namespace: '/res/admin',
      ...config,
    });
  }
}

export default new TeacherTitle();
