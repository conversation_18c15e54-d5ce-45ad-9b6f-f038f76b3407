import BaseModel from './BaseModel';

export interface IImport {
  id?: number;
}
export interface IResponse {
  data?: IImport[];
}

export interface ISubmitQuestion {
  uid: string;
  titles: string[];
  primary_keys: string[];
}

export interface INotify {
  uid: string;
}

export interface ITableKey {
  type: string;
}

export interface IValid {
  uid: string;
  titles: string[];
  primary_keys: string[];
  source_type: string;
  targets: string;
}

export interface IExcelInfo {
  uid: string;
  page?: number;
  par_page?: number;
  q?: any;
}
export class ImportModel extends BaseModel<IImport, IResponse> {
  constructor() {
    super({
      name: 'import',
      resource: 'imports',
    });
  }
  // 获取导出选择的字段源
  tableKeys(params: ITableKey) {
    return this.request.post('/ex/admin/imports/titles', { ...params });
  }

  // 上传Excel文件
  importFile(file: any) {
    const formData = new FormData();
    formData.append('file', file);
    return this.request.post('/ex/admin/imports/read', formData);
  }

  // 通过上传Excel返回的uid查询Excel导入的信息， 有效期为1小时
  getExcelInfo(params: IExcelInfo) {
    return this.request.post('/ex/admin/imports/async', { ...params });
  }

  // 校验导入
  onValid(params: IValid) {
    return this.request.post('/ex/admin/imports/valid', { ...params });
  }

  // 查询进度
  onNotify(params: INotify) {
    return this.request.post('/ex/admin/imports/notify', { ...params });
  }

  // ######################## 各个模块提交方法 ##########################
  // 提交需要导入的信息
  submitExcelInfo(params: ISubmitQuestion) {
    return this.request.post('/ex/admin/imports/exam/questions', { ...params });
  }
}
