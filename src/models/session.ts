import BaseModel from './BaseModel';

export interface IAccount {
  _id?: string;
  type?: 'Teacher' | 'Student';
  name?: string;
  account?: string;
  token?: string;
  fileToken?: string;
  school?: string;
  password?: string;
}

export interface IAuthorizeData {
  appId: string; // appId
  response_type: 'code'; // 填写code
  redirect_url: string; // 注册应用时填写的回调地址
  state: string; // 返回时原样返回此参数,客户端可通过这个进行合法性验证
}

export interface IThirdAuthAccount {
  app_id: string;
  thirdAuthId: string;
  account: string;
  password: string;
}

export interface IAuthAccount {
  account: string;
  password: string;
}

export interface OAuthAuthorizeRequestQuery {
  clientId: string;
  responseType: string;
  redirectUri: string;
  scope: string;
  state: string;
}

export interface OAuthAuthorizeResponse {
  error?: string;
  status?: string;
  redirect_uri?: string;
}

export class Session extends BaseModel {
  constructor() {
    super({
      name: 'session',
      resource: 'session',
      namespace: '/auth',
      rootPath: '/soa-auth',
    });
  }

  // =============== oauth ===============
  // v1 token 验证
  // checkToken() {
  //   return this.request.post<IAccount>('/checkToken');
  // }
  // v2
  checkToken() {
    return this.request.post<IAccount>('/auth/session/check');
  }
  // 用户授权
  authorize(authorizeData: IAuthorizeData) {
    return this.request.post('/oauth2/authorize', authorizeData);
  }
  // =============== third auth ===============
  // v1
  // thirdSignIn(account: IThirdAuthAccount) {
  //   return this.request.post<IAccount>('/thirdauths/signin', account);
  // }
  // v2
  async thirdSignIn(account: IThirdAuthAccount) {
    const { status, data } = await this.request.post('/auth/session', {
      ...account,
      app_id: process.env.VUE_APP_ID,
      thirdAuthId: '5d2ee7620047759f710bce75',
    });
    return {
      status,
      data: {
        _id: data.code,
        type: data.type || data.account_type,
        name: data.name,
        account: data.code,
        token: data.token,
        fileToken: data.file_token,
        school: data.school,
      } as IAccount,
    };
  }
  // =============== account ===============
  // 个人信息
  // info() {
  //   return this.request.get<IAccount>('/account/me');
  // }
  // 修改密码
  setPassword(val: any) {
    return this.request.patch('/auth/password', {
      ...val,
    });
  }
  // 登录
  signIn(account: IAuthAccount) {
    return this.request.post<IAccount>('/account/signin', account);
  }
  // v1: 登出
  // signOut() {
  //   return this.request.get('/account/signout');
  // }
  // v2: 登出
  signOut() {
    return this.request.delete('/auth/session');
  }
  // 注册
  signUp(user: IAccount) {
    return this.request.post('/account/signup', user);
  }
  // 批量注册
  bulkSignUp(users: IAccount[]) {
    return this.request.post('/account/signup/bulk', {
      users,
    });
  }
  // v1: 获取个人身份二维码，有效期 15 秒
  // getIdCode() {
  //   return this.request.post<{ code: string }>('/account/idcode');
  // }
  // v2: 获取个人身份二维码，有效期 15 秒
  getIdCode() {
    return this.request.post<{ code: string }>('/user/id_code');
  }
  // v1: 验证个人身份二维码
  // checkIdCode(code: string) {
  //   return this.request.post<IAccount>('/account/idcode/check', {
  //     code,
  //   });
  // }
  // v2: 验证个人身份二维码
  checkIdCode(code: string) {
    return this.request.post<IAccount>('/user/id_code/check', {
      code,
    });
  }
  // 获取临时密码
  getTemporaryPassword() {
    return this.request.post<{ password: string }>('/account/temporary-password');
  }
  // 删除账号
  delete(account: string) {
    return this.request.delete(`/account/users/${account}`);
  }

  public oAuthAuthorize(token: string, oAuthQueryParams: OAuthAuthorizeRequestQuery) {
    return this.request.post<OAuthAuthorizeResponse>(
      '/oauth_2/authorize',
      {
        token: token,
        client_id: oAuthQueryParams.clientId,
        response_type: oAuthQueryParams.responseType,
        redirect_uri: oAuthQueryParams.redirectUri,
        scope: oAuthQueryParams.scope,
        state: oAuthQueryParams.state,
      },
      {
        validateStatus: () => true,
      },
    );
  }
}

export default new Session();
