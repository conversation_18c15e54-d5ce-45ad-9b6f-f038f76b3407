import utils from '@/utils';
import { sumBy } from 'lodash';
import BaseModel from '../BaseModel';

interface AnswerData {
  rate: number;
  number: number;
}

interface RoleData {
  register: { total: number; undo: number; done: number };
  question: {
    [key: string]: {
      [key: string]: AnswerData;
    };
  };
}

interface QuestionItem {
  key: string;
  title: string;
  options: Array<{ key: string; value: string }>;
}

interface IResponse {
  id: number;
  start_at: string;
  end_at: string;
  info: {
    student: RoleData;
    teacher: RoleData;
  };
  meta: {
    questions: QuestionItem[];
  };
}

function getPercent(count: number, total: number) {
  return Math.round((count / total) * 10000) / 100;
}

export class Statistic extends BaseModel<any, IResponse> {
  constructor(role: 'admin') {
    super({
      name: 'statistic',
      resource: 'statistics',
      namespace: '/ep',
      role,
    });
  }

  async statistic(params: IObject) {
    const { data } = await this.request.get<IResponse>('/ep/admin/statistics', { params });
    const { info, meta } = data;
    const { teacher, student } = info;
    const questions = meta.questions || [];
    const getRoleStatisticData = (roleData: RoleData) => {
      const { register, question: questionMap } = roleData;
      const { done, undo, total } = register;
      return {
        statistic: [
          { name: '应打卡', count: total },
          { name: '已打卡', count: done, percent: getPercent(done, total) },
          { name: '未打卡', count: undo, percent: getPercent(undo, total) },
        ],
        records: questions.map((q: QuestionItem) => {
          const answerMap = utils.objectify(q.options, 'key');
          const total = sumBy(Object.values(questionMap[q.key]), 'number');
          return {
            key: q.key,
            title: q.title,
            question: q,
            statistic: Object.entries(questionMap[q.key] || {}).map(item => {
              const answerKey: string = item[0] || '';
              const answerData: AnswerData = item[1] || {};
              return {
                name: answerMap[answerKey].value,
                count: answerData.number,
                percent: Math.round((answerData.number / total) * 10000) / 100,
              };
            }),
          };
        }),
      };
    };
    return {
      student: getRoleStatisticData(student),
      teacher: getRoleStatisticData(teacher),
    };
  }
}
