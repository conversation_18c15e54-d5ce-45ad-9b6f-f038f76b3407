/*
################# index column ########
#
#  id                   :bigint           not null, primary key
#  school_id(学校id)    :bigint
#  teacher_id(教师id)   :bigint
#  name(名称)           :string(255)      default("")
#  state(状态)          :string(255)
#  start_at(开始时间)   :datetime
#  end_at(结束时间)     :datetime
#  meta(扩展字段)       :json
#  deleted_at(软删标识) :datetime
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#
*/

import BaseModel from '../BaseModel';

export interface IActivity {
  id: number;
  school_id: number;
  teacher_id: number;
  name: string;
  start_at: string;
  end_at: string;
  state: string;
  meta: any;
  noCompare: boolean;
  teacher_count?: number;
  student_count?: number;
  infos?: any;
}

export interface IResponse {
  activities: IActivity[];
}

export class Activity extends BaseModel<IActivity, IResponse> {
  constructor(role: 'admin' | 'inspect') {
    super({
      name: 'activity',
      resource: 'activities',
      namespace: '/ep',
      role,
    });
  }
  // admin
  registers(params: any) {
    return this.request.get(`/ep/admin/activities/${params.parentId}/statistics/registers`, {
      params,
    });
  }
  questions(params: any) {
    return this.request.get(`/ep/admin/activities/${params.parentId}/questions`, {
      params,
    });
  }
  programs(params: any) {
    return this.request.get(`/ep/admin/activities/${params.parentId}/programs`, {
      params,
    });
  }
  export(params: any) {
    return this.request.post(`/ep/admin/statistics/export`, {
      ...params,
    });
  }
  // inspect
  inspectRegisters(params: any) {
    return this.request.get(`/st/teacher/ep/activity/${params.parentId}/registers`, {
      params,
    });
  }
  inspectQuestions(params: any) {
    return this.request.get(`/ep/inspect/activities/${params.parentId}/questions`, {
      params,
    });
  }
  inspectPrograms(params: any) {
    return this.request.get(`/ep/inspect/activities/${params.parentId}/programs`, {
      params,
    });
  }
  inspectExport(params: any) {
    return this.request.post(`/ep/inspect/activities/${params.parentId}/export`, {
      params,
    });
  }
  // 分配巡查教师
  adminAssignInspectTeachers(activityIds: number[], teacherIds: number[]) {
    return this.request.post(`/ep/admin/activities/batch_update`, {
      activity: {
        ep_inspect_teacher_ids: teacherIds,
        activity_ids: activityIds,
      },
    });
  }

  // 分配巡查教师
  adminAssignManageTeachers(activityIds: number[], teacherIds: number[]) {
    return this.request.post(`/ep/admin/activities/batch_assign_manage`, {
      activity: {
        manage_teacher_ids: teacherIds,
        activity_ids: activityIds,
      },
    });
  }
}
