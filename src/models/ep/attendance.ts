/*
################# index column ########
#
#  id                    :bigint           not null, primary key
#  activity_id(活动id)   :bigint
#  college_id(院系id)    :bigint
#  major_id(专业id)      :bigint
#  adminclass_id(班级id) :bigint
#  user_type             :string(255)
#  user_id(用户)         :bigint
#  state(状态)           :integer          default("active")
#  meta(扩展字段)        :json
#  deleted_at(软删标识)  :datetime
#  created_at            :datetime         not null
#  updated_at            :datetime         not null
#
*/

import BaseModel from '../BaseModel';

export interface IAttendance {
  id?: number;
  activity_id?: number;
  college_id?: number;
  major_id?: number;
  adminclass_id?: number;
  user_type?: string;
  user_id?: number;
  state?: string;
  meta?: any;
  user?: any;
  nested_infos?: any;
  info?: any;
  questions?: any[];
}

export interface IResponse {
  attendances: IAttendance[];
}

export class Attendance extends BaseModel<IAttendance, IResponse> {
  constructor(role: 'admin' | 'inspect' | 'user') {
    super({
      name: 'attendance',
      resource: 'attendances',
      namespace: '/ep',
      parentResource: 'activities',
      role,
    });
  }
}
