import BaseModel from './BaseModel';

export interface IPermitTeacher {
  id: number;
  name: string;
  mod: string;
}

export interface IResponse {
  teachers: IPermitTeacher[];
}

export class PermitTeacher extends BaseModel<IPermitTeacher, IResponse> {
  constructor() {
    super({
      name: 'teacher',
      resource: 'teachers',
      namespace: '/permit/admin',
    });
  }
}

export default new PermitTeacher();
