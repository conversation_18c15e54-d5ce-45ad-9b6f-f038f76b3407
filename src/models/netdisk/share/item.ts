import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';
import { IItem } from '@/types/model';

export class NetdiskShareItem extends ActiveModel<IItem> {
  constructor(config?: IModelConfig) {
    super({
      name: 'item',
      namespace: 'netdisk/share',
      ...config,
    });
  }
}

export class NetdiskSharedItem extends ActiveModel<IItem> {
  constructor(config?: IModelConfig) {
    super({
      name: 'item',
      namespace: 'netdisk/shared',
      ...config,
    });
  }
}
