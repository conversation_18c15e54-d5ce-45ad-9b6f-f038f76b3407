import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';

export interface IMajor {
  id: number;
  school_id?: number;
  code?: string;
  name?: string;
  short_name?: string;
  eng_name?: string;
  duration?: number;
  project?: string;
  meta?: any;
  created_at?: Date;
  updated_at?: Date;
  department_id?: number;
  ancestry?: string;
  depth?: number;
  direction?: string;
  gb_code?: string;
  effective_on?: Date;
  invalid_on?: Date;
  state?: string;
}

export class NetdiskTeacherMajor extends ActiveModel<IMajor> {
  constructor(config: IModelConfig) {
    super({
      name: 'major',
      namespace: 'netdisk/teacher',
      ...config,
    });
  }
}
