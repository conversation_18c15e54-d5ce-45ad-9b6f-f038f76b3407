import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';

export interface ICoures {
  id: number;
  course_set_id: number;
  start_week?: number;
  end_week?: number;
  period?: number;
  status?: string;
  no: number;
  grade?: string;
  name: string;
  std_count?: number;
  semester_id?: number;
  department_id?: string;
  meta?: any;
  created_at?: Date;
  updated_at?: Date;
  course_catalog_id?: number;
  sub_school?: string;
  teach_depart_id?: number;
}

export class NetdiskTeacherCourseDirs extends ActiveModel<ICoures> {
  constructor(config?: IModelConfig) {
    super({
      name: 'course_dirs',
      namespace: 'netdisk/teacher',
      ...config,
    });
  }
}
