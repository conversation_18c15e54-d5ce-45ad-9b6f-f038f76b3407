import BaseModel from '@/models/BaseModel';
import { IFile } from '../file';

export interface IAdminTofu {
  id?: number;
  name?: string;
  url?: string;
  mobile_url?: string;
  type?: string;
  icon?: string;
  image_obj?: IFile[];
  image?: string;
  super_tofu_id?: number;
  position?: number;
  top_tofu_name?: string;
  instance_count?: number;
  isEdit?: boolean;
  subs?: IObject[];
  stared?: boolean;
}

interface IResponse {
  apps: IAdminTofu[];
}

export class TofuAdminApp extends BaseModel<IAdminTofu, IResponse> {
  constructor() {
    super({
      name: 'app',
      resource: 'apps',
      namespace: '/tofu',
      role: 'admin',
    });
  }
}

export default new TofuAdminApp();
