import BaseModel from '@/models/BaseModel';

export interface ITofuWebsite {
  id?: number;
  name?: string;
  url?: string;
  icon?: string;
  image?: string;
  position?: number;
}

interface IResponse {
  websites: ITofuWebsite[];
}

export class TofuAdminWebsite extends BaseModel<ITofuWebsite, IResponse> {
  constructor() {
    super({
      name: 'website',
      resource: 'websites',
      namespace: '/tofu',
      role: 'admin',
    });
  }
}

export default new TofuAdminWebsite();
