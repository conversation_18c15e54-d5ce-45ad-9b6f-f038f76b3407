import BaseModel from '@/models/BaseModel';

export interface ITofuCustom {
  id?: number;
  name?: string;
  type?: string;
  url?: string;
  icon?: string;
  image?: string;
  position?: number;
}

interface IResponse {
  custom: ITofuCustom[];
}

export class TofuCustom extends BaseModel<ITofuCustom, IResponse> {
  constructor() {
    super({
      name: 'custom',
      resource: 'customs',
      namespace: '/tofu',
      role: 'user',
    });
  }
}

export default new TofuCustom();
