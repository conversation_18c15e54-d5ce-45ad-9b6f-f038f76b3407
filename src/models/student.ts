/*
################# index column ########
#  id                                                        :bigint           not null, primary key
#  school_id(所属学校)                                       :bigint
#  name(姓名)                                                :string(255)
#  code(学号/准考证号)                                       :string(255)
#  identity_id(身份证号)                                     :string(255)
#  sex(性别)                                                 :string(255)
#  nation(民族)                                              :string(255)
#  birth(生日)                                               :date
#  native_place(籍贯)                                        :string(255)
#  home_address(家庭地址)                                    :string(255)
#  tel(家庭电话)                                             :string(255)
#  phone(手机号码)                                           :string(255)
#  postcode(邮编)                                            :string(255)
#  tel2(其他常用联系方式)                                    :string(255)
#  high_school(毕业学校)                                     :string(255)
#  specialty(特长爱好)                                       :string(255)
#  entrance_records(升学考试成绩)                            :float(24)
#  meta(扩展字段)                                            :json
#  created_at                                                :datetime         not null
#  updated_at                                                :datetime         not null
#  state(学生现在的状态，例如：newcommer studying graduated) :string(255)
#  major_name(专业名称)                                      :string(255)
#  major_code(专业代码)                                      :string(255)
#  admission_category(录取类别)                              :string(255)
#  avatar(头像信息，json结构)                                :json
#
########   extends   #######
# resume 个人简历, json
# family 家庭情况, json
# reward_punish 奖惩情况, string
# high_school_job 担任工作，string
# high_school_score 录取成绩，float
# native_address 户籍地址，string
# department_name 所在系，string
# aclass 班级，string
# number 学号，string
*/

import BaseModel from './BaseModel';
import { IFormTemplateItem } from '../interfaces/formTemplate.interface';

export interface IStudent {
  id: number;
  name: string; // (姓名)
  code: string; // (学号/准考证号)
  identity_id: string; // (学号/准考证号)
  sex: string; // (性别)
  state: string; // (学生现在的状态，例如：newcommer studying graduated)
  nation: string; // (民族)
  birth: string; // (生日)
  native_place: string; // (籍贯)
  home_address: string; // (家庭地址)
  tel: string; // (家庭电话)
  phone: string; // (手机号码)
  postcode: string; // (邮编)
  tel2: string; // (其他常用联系方式)
  high_school: string; // (毕业学校)
  specialty: string; // (特长爱好)
  entrance_records: number; // (升学考试成绩)
  reward_punish: string; // 奖惩情况
  high_school_job: string; // 担任工作
  resume: any; // 个人简历
  family: any; // 家庭情况
  meta: any; // (扩展字段)
  department_name: string;
  major_name: string;
  adminclass_name: string;
  column1_form: { fields: IFormTemplateItem[] };
  column2_form: { fields: IFormTemplateItem[] };
  column1_key_value: IObject;
  column2_key_value: IObject;
  column1_instance_states: string[];
  hand_in_state: string;
  building_name?: string;
  floor?: number;
  room?: string;
  bed?: string;
}

export interface IResponse {
  students: IStudent[];
}

export class Student extends BaseModel<IStudent, IResponse> {
  constructor(role: 'admin' | 'teacher') {
    super({
      name: 'student',
      resource: 'students',
      namespace: '/studying',
      role,
    });
  }
}
