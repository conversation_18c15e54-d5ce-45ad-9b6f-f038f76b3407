import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';
import { IActivity } from '@/models/access/activity';
import { IPractice } from '../practice';

export class PtTeacherActivitiesPractices extends ActiveModel<IPractice> {
  constructor(config?: IModelConfig) {
    super({
      name: 'practice',
      namespace: 'pt/teacher',
      mode: 'shallow',
      actions: [
        { name: 'export', method: 'post', on: 'collection' },
        { name: 'upload_excel', method: 'post', on: 'collection' },
        { name: 'excel', method: 'post', on: 'collection' },
        { name: 'import_headers', method: 'post', on: 'collection' },
        { name: 'export_headers', method: 'post', on: 'collection' },
        { name: 'import', method: 'post', on: 'collection' },
      ],
      ...config,
    });
  }
}
