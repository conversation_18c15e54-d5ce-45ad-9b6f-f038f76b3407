import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';
import { IComment } from '@/models/comment';

export class PtTeacherComments extends ActiveModel<IComment> {
  constructor(config?: IModelConfig) {
    super({
      name: 'comment',
      namespace: 'pt/teacher',
      ...config,
    });
  }
}

// 用户评论
export class CommUserComments extends ActiveModel<IComment> {
  constructor(config?: IModelConfig) {
    super({
      name: 'comment',
      namespace: 'comm',
      ...config,
    });
  }
}
