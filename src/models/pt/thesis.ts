/*
################# index column ########
#
#  id                     :bigint           not null, primary key
#  user_type              :string(255)
#  user_id(用户)          :bigint
#  school_id(学校id)      :bigint
#  college_id(学院id)     :bigint
#  major_id(专业id)       :bigint
#  college_code(学院代码) :string(255)
#  major_code(专业代码)   :string(255)
#  source_type            :string(255)
#  source_id(目标)        :bigint
#  type(STI)              :string(255)
#  title(主题)            :string(255)      default("")
#  body(信息)             :text(65535)
#  state(状态)            :string(255)
#  attachments(附件)      :json
#  meta(扩展字段)         :json
#  score(分数)            :decimal(8, 2)    default(0.0)
#  deleted_at(软删标识)   :datetime
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#
*/

import BaseModel from '../BaseModel';

export interface IThesis {
  id: number;
  user_type: string;
  user_id: number;
  school_id: number;
  college_id: number;
  major_id: number;
  college_code: string;
  major_code: string;
  source_type: string;
  source_id: string;
  type: string;
  title: string;
  body: string;
  state: string;
  attachments: any;
  meta: any;
  score: number;
  thesis_infos_attributes?: any;
}

export interface IResponse {
  theses: IThesis[];
}

export class Thesis extends BaseModel<IThesis, IResponse> {
  constructor() {
    super({
      name: 'thesis',
      resource: 'theses',
      namespace: '/pt/admin',
      parentResource: 'practices',
    });
  }
}

export default new Thesis();
