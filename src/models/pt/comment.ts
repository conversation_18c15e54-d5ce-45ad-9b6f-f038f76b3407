/*
################# index column ########
#
#  id                                    :bigint           not null, primary key
#  commentable_id                        :integer
#  commentable_type                      :string(255)
#  title                                 :string(255)
#  body                                  :text(65535)
#  subject                               :string(255)
#  user_id                               :integer
#  user_type                             :string(255)
#  parent_id                             :integer
#  lft                                   :integer
#  rgt                                   :integer
#  created_at                            :datetime         not null
#  updated_at                            :datetime         not null
#  attachments(评论的附件，可以添加多个) :json
#
*/

import BaseModel from '../BaseModel';

export interface IComment {
  id: number;
  title: string;
  body: string;
  subject: string;
  parent_id: number;
  noCompare?: boolean;
}

export interface IResponse {
  comments: IComment[];
}

export class Comment extends BaseModel<IComment, IResponse> {
  constructor() {
    super({
      name: 'comment',
      resource: 'comments',
      namespace: '/pt/amin',
      parentResource: 'reports', //reports, theses
    });
  }
  // 可用路由：teacher(index, delete), admin(index, delete), student(index)
  deleteByParent(params: any) {
    return this.request.delete(`/pt/${params.role}/${params.parentResource}/${params.parentId}/comments/${params.id}`);
  }
}

export default new Comment();
