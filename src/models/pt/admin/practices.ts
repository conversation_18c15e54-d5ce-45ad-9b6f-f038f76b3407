import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';
import { IPractice } from '../practice';

export class PtAdminActivitiesPractices extends ActiveModel<IPractice> {
  constructor(config?: IModelConfig) {
    super({
      name: 'practice',
      namespace: 'pt/admin',
      mode: 'shallow',
      actions: [
        { name: 'export', method: 'post', on: 'collection' },
        { name: 'upload_excel', method: 'post', on: 'collection' },
        { name: 'excel', method: 'post', on: 'collection' },
        { name: 'import_headers', method: 'post', on: 'collection' },
        { name: 'export_headers', method: 'post', on: 'collection' },
        { name: 'import', method: 'post', on: 'collection' },
      ],
      ...config,
    });
  }
}
