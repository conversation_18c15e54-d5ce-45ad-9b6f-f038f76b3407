import BaseModel from '@/models/BaseModel';

export interface IAdvise {
  id?: number;
  type?: string;
  title?: string;
  content?: string;
  cover_image?: IObject[];
  banners?: IObject[];
  position?: number;
  inform_mod_id?: number;
  created_at?: string;
  updated_at?: string;
  catalog?: string;
  top?: boolean;
}

interface IResponse {
  advises: IAdvise[];
}

export class InformAdvise extends BaseModel<IAdvise, IResponse> {
  constructor() {
    super({
      name: 'advise',
      resource: 'advises',
      namespace: '/inform',
      role: 'user',
    });
  }
}

export default new InformAdvise();
