import { ITeacher } from './teacher';
import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';

// 获取普通用户基本信息
export class UserTeacher extends ActiveModel<ITeacher> {
  constructor(config?: IModelConfig) {
    super({
      name: 'info',
      mode: 'single',
      namespace: '/hr/teacher',
      ...config,
    });
  }
}

// 获取普通用户通讯录
export class UserTeacherTeacher extends ActiveModel<ITeacher> {
  constructor(config?: IModelConfig) {
    super({
      name: 'teacher',
      namespace: '/hr/teacher',
      dataIndexKey: 'teachers',
      actions: [
        { name: 'star', method: 'post', on: 'member' },
        { name: 'unstar', method: 'post', on: 'member' },
      ],
      ...config,
    });
  }
}

export class UserTeacherStarTeacher extends ActiveModel<ITeacher> {
  constructor(config?: IModelConfig) {
    super({
      name: 'star_teacher',
      namespace: '/hr/teacher',
      dataIndexKey: 'teachers',
      ...config,
    });
  }
}
