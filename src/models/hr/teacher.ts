/*
################# index column ########
#  id                      :bigint           not null, primary key
#  name(姓名)              :string(255)
#  name_pinyin(姓名拼音)   :string(255)
#  identity_id(证件号码)   :string(255)
#  identity_type(证件类型) :string(255)
#  code(工号)              :string(255)
#  birthday(生日)          :date
#  sex(性别)               :string(255)
#  phone(手机)             :string(255)
#  tel(电话)               :string(255)
#  email(邮箱)             :string(255)
#  meta(扩展属性)           :json
#  dynamic_attrs(动态属性) :json
#  deleted_at(软删除)      :datetime
#  created_at              :datetime         not null
#  updated_at              :datetime         not null
#  school_id(所属学校)     :bigint
#  department_id(所属部门) :bigint
#  college_id(学院ID)        :integer
#  college_code(学院代码)    :string(255)
#  major_id(专业id)          :integer
#  major_code(专业代码)      :string(255)
#  to_school_at(进校时间)    :datetime
#  leave_school_at(离校时间) :datetime
#  work_way(用人方式)        :string(255)
#  degree(学位) // { no: 'no', bachelor: 'bachelor', master: 'master', doctor: 'doctor', postdoctor: 'postdoctor' }
#  education(学历) // { high: 'high', other: 'other' }
#  state(状态)               :string(255)
*/

import BaseModel from '../BaseModel';
import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';

export interface ITeacher {
  id: number;
  name: string;
  name_pinyin: string;
  identity_id: string;
  identity_type: number;
  code: number;
  birthday: number;
  sex: number;
  phone: number;
  tel: number;
  email: string;
  meta: any;
  dynamic_attrs: any;
  titles: any[];
  duties: any[];
  department: any;
  departments: any[];
  school_id?: number;
  department_id?: number;
  college_id?: number;
  major_id?: number;
  college_code?: string;
  major_code?: string;
  to_school_at?: string;
  leave_school_at?: string;
  work_way?: string;
  degree?: string;
  education?: string;
}

export interface IResponse {
  teachers: ITeacher[];
}

export class Teacher extends BaseModel<ITeacher, IResponse> {
  constructor() {
    super({
      name: 'teacher',
      resource: 'teachers',
      namespace: '/hr/admin',
    });
  }

  // 获取导出选择的字段源
  teacherKeys(params: { key: string }) {
    return this.request.post('/ex/teacher/exports/titles', { ...params });
  }

  exportExcel(params: { titles: string[]; q?: any }) {
    return this.request.post('/ex/teacher/hr/teachers', { ...params });
  }

  // 上传Excel文件
  importFile(file: any) {
    const formData = new FormData();
    formData.append('file', file);
    return this.request.post('/ex/admin/imports/read', formData);
  }

  // 通过上传Excel返回的uid查询Excel导入的信息， 有效期为1小时
  getExcelInfo(params: { uid: string; page?: number; par_page?: number; q?: any }) {
    return this.request.post('/ex/admin/imports/async', { ...params });
  }

  // 校验导入
  onValid(params: { uid: string; titles: string[]; primary_keys: string[]; source_type: string; targets: string }) {
    return this.request.post('/ex/admin/imports/valid', { ...params });
  }

  // 查询进度
  onNotify(params: { uid: string }) {
    return this.request.post('/ex/admin/imports/notify', { ...params });
  }

  // 提交需要导入的信息
  submitExcelInfo(params: { uid: string; titles: string[]; primary_keys: string[] }) {
    return this.request.post('/ex/admin/hr/teachers', { ...params });
  }
}

// 新Model
export class AdminTeacher extends ActiveModel<ITeacher> {
  constructor(config?: IModelConfig) {
    super({
      name: 'teachers',
      namespace: '/hr/admin',
      ...config,
    });
  }
}

export default new Teacher();
