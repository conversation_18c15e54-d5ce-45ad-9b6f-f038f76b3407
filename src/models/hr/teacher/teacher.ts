import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';
import { ITeacher } from '../../res/teacher';

export class HrTeacherTeacher extends ActiveModel<ITeacher> {
  constructor(config?: IModelConfig) {
    super({
      namespace: '/hr/teacher',
      name: 'teacher',
      actions: [
        {
          name: 'find_by_file',
          method: 'post',
          on: 'collection',
        },
        // { name: 'export', method: 'post', on: 'collection' },
        { name: 'upload_excel', method: 'post', on: 'collection' },
        { name: 'excel', method: 'post', on: 'collection' },
        { name: 'import_headers', method: 'post', on: 'collection' },
        // { name: 'export_headers', method: 'post', on: 'collection' },
        // { name: 'import', method: 'post', on: 'collection' },
      ],
      ...config,
    });
  }
}
