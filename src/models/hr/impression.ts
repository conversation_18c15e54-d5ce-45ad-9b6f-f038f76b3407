import BaseModel, { IModelConfig } from '../BaseModel';

export interface IImpression {
  id: number;
  impressionable_type: string;
  impressionable_id: number;
  action_name: string;
  view_name: string;
  ip_address: string;
}

interface IResponse {
  impressions: IImpression[];
}

export class Impression extends BaseModel<IImpression, IResponse> {
  constructor(role: 'teacher' | 'admin') {
    super({
      name: 'impression',
      resource: 'impressions',
      namespace: '/hr',
      role,
    });
  }
}
