import BaseModel from '@/models/BaseModel';

export interface IExpert {
  id?: number;
  created_at?: string;
  updated_at?: string;
  school_id?: number;
  name?: string; // 编辑时只读
  phone?: string; // 编辑时只读
  company?: string;
  bank?: string;
  bank_account?: string;
  duty?: string;
  remark?: string;
  sex?: string;
  identity_type?: string;
  identity_id?: string;
  auth_account_id?: number; // 关联鉴权系统的auth_account id)
}

interface IResponse {
  experts: IExpert[];
}

export class Expert extends BaseModel<IExpert, IResponse> {
  constructor(role: 'admin' | 'teacher') {
    super({
      name: 'expert',
      resource: 'experts',
      namespace: '/hr',
      role,
    });
  }

  // 激活账号
  activate(expert: IExpert) {
    if (expert.auth_account_id) {
      throw new Error('专家账号已经激活');
    }
    return this.request.post(`/hr/admin/experts/${expert.id}/activate`);
  }

  // 关闭账号
  block(expert: IExpert) {
    if (!expert.auth_account_id) {
      throw new Error('专家账号还未激活');
    }
    return this.request.post(`hr/admin/experts/${expert.id}/block`);
  }
}
