import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';
import { ITeacher } from '@/models/teacher';

export class HrAdminTeacher extends ActiveModel<ITeacher> {
  constructor(config?: IModelConfig) {
    super({
      namespace: '/hr/admin',
      name: 'teacher',
      actions: [
        { name: 'export', method: 'post', on: 'collection' },
        { name: 'upload_excel', method: 'post', on: 'collection' },
        { name: 'excel', method: 'post', on: 'collection' },
        { name: 'import_headers', method: 'post', on: 'collection' },
        { name: 'export_headers', method: 'post', on: 'collection' },
        { name: 'import', method: 'post', on: 'collection' },
      ],
      ...config,
    });
  }
}
