import BaseModel from '../BaseModel';

export interface IAuthInfo {
  id?: number;
  name?: string;
  name_pinyin?: string;
  identity_id?: string;
  identity_type?: string;
  code?: string;
  birthday?: string;
  sex?: string;
  phone?: string;
  tel?: string;
  email?: string;
  school_id?: number;
  department_id?: number;
  role: string;
  meta: any;
  dynamic_attrs: any;
  titles: any[];
  duties: any[];
  department: any;
  college_id?: number;
  major_id?: number;
  college_code?: string;
  major_code?: string;
  to_school_at?: string;
  leave_school_at?: string;
  work_way?: string;
  degree?: string;
  education?: string;
}

interface IResponse {}

export class AuthInfo extends BaseModel<IAuthInfo, IResponse> {
  constructor(role: 'teacher' | 'student' | 'expert' = 'teacher') {
    super({
      name: 'teacher',
      resource: 'teacher',
      namespace: '/hr',
      role,
    });
  }

  public find() {
    return this.request.get(`${this.namespace}/${this.role}/info`);
  }
  public update(auth: IAuthInfo) {
    return this.request.patch(`${this.namespace}/${this.role}/info`, {
      [auth.role]: auth,
    });
  }
}

export default new AuthInfo();
/*
################# index column ########
#  id                                                        :bigint           not null, primary key
#  school_id(所属学校)                                       :bigint
#  name(姓名)                                                :string(255)
#  code(学号/准考证号)                                       :string(255)
#  identity_id(身份证号)                                     :string(255)
#  sex(性别)                                                 :string(255)
#  nation(民族)                                              :string(255)
#  birth(生日)                                               :date
#  native_place(籍贯)                                        :string(255)
#  home_address(家庭地址)                                    :string(255)
#  tel(家庭电话)                                             :string(255)
#  phone(手机号码)                                           :string(255)
#  postcode(邮编)                                            :string(255)
#  tel2(其他常用联系方式)                                    :string(255)
#  high_school(毕业学校)                                     :string(255)
#  specialty(特长爱好)                                       :string(255)
#  entrance_records(升学考试成绩)                            :float(24)
#  meta(扩展字段)                                            :json
#  created_at                                                :datetime         not null
#  updated_at                                                :datetime         not null
#  state(学生现在的状态，例如：newcommer studying graduated) :string(255)
*/
