import BaseModel, { IModelConfig } from '../BaseModel';

interface IPublic {
  id: number;
  current_page: number;
  total_pages: number;
  total_count: number;
}

interface IResponse {
  audits: IPublic[];
}

export class Public extends BaseModel<IPublic, IResponse> {
  constructor(config?: IModelConfig) {
    super({
      name: 'audit',
      resource: 'audits',
      namespace: '/hr/admin',
      ...config,
    });
  }

  statistics() {
    return this.request.get('/hr/admin/statistics');
  }

  impressions(params: any) {
    return this.request.get('/hr/admin/impressions', { params });
  }
}

export default new Public();
