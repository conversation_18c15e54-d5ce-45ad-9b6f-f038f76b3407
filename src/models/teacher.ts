/*
################# index column ########
#  id                      :bigint           not null, primary key
#  name(姓名)              :string(255)
#  name_pinyin(姓名拼音)   :string(255)
#  identity_id(证件号码)   :string(255)
#  identity_type(证件类型) :string(255)
#  code(工号)              :string(255)
#  birthday(生日)          :date
#  sex(性别)               :string(255)
#  phone(手机)             :string(255)
#  tel(电话)               :string(255)
#  email(邮箱)             :string(255)
#  meta(扩展属性)           :json
#  dynamic_attrs(动态属性) :json
#  deleted_at(软删除)      :datetime
#  created_at              :datetime         not null
#  updated_at              :datetime         not null
#  school_id(所属学校)     :bigint
#  department_id(所属部门) :bigint
#  college_id(学院ID)        :integer
#  college_code(学院代码)    :string(255)
#  major_id(专业id)          :integer
#  major_code(专业代码)      :string(255)
#  to_school_at(进校时间)    :datetime
#  leave_school_at(离校时间) :datetime
#  work_way(用人方式)        :string(255)
#  degree(学位) // { no: 'no', bachelor: 'bachelor', master: 'master', doctor: 'doctor', postdoctor: 'postdoctor' }
#  education(学历) // { high: 'high', other: 'other' }
#  state(状态)               :string(255)
*/

import BaseModel from './BaseModel';

export interface ITeacher {
  id: number;
  name: string;
  name_pinyin: string;
  identity_id: string;
  identity_type: number;
  code: number;
  birthday: number;
  sex: number;
  phone: number;
  tel: number;
  email: string;
  meta: any;
  dynamic_attrs: any;
}

interface IResponse {
  teachers: ITeacher[];
}

export class Teacher extends BaseModel<ITeacher, IResponse> {
  constructor(role: 'admin' | 'teacher' = 'teacher') {
    super({
      name: 'teacher',
      resource: 'teachers',
      namespace: '/hr',
      role,
    });
  }
}

export default new Teacher();
