import BaseModel, { IModelConfig } from '../BaseModel';

export interface IApplicationForm {
  id?: number;
  created_at?: string;
  updated_at?: string;
  state?: string;
  backup_user_ids?: number[];
  backup_user_names?: string[];
  backup_user_type?: string;
  backup_reason?: string;
  comment?: string;
  register_id?: string;
  instance_id?: number;
}

export interface IResponse {
  application_form: IApplicationForm[];
}

export class ApplicationForm extends BaseModel<IApplicationForm, IResponse> {
  constructor(config?: IModelConfig) {
    super({
      name: 'application_form',
      resource: 'application_forms',
      namespace: '/meeting/teacher',
      parentResource: 'activity_meetings',
      ...config,
    });
  }
}

export default new ApplicationForm();
