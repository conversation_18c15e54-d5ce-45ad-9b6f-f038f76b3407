import BaseModel, { IModelConfig } from '../BaseModel';

export interface IMeetingRegister {
  id?: number;
  created_at?: string;
  updated_at?: string;
  user_type?: string;
  user_id?: number;
  source_type?: string;
  source_id?: number;
  state?: string;
  type?: string;
  lon?: string;
  lat?: string;
  meta?: any;
}

export interface IResponse {
  registers: IMeetingRegister[];
}

export class MeetingRegister extends BaseModel<IMeetingRegister, IResponse> {
  constructor(config?: IModelConfig) {
    super({
      name: 'register',
      indexKey: 'registers',
      resource: 'registers',
      namespace: '/meeting/teacher',
      parentResource: 'activity_meetings',
      ...config,
    });
  }
}

export default new MeetingRegister();
