/*
################# index column ########
#
#  id                                       :bigint           not null, primary key
#  date(会议安排的日期)                     :date
#  title(会议的标题)                        :string(255)
#  desc(会议描述)                           :text(65535)
#  meeting_room_id(会议室)                  :bigint
#  begin_time(会议开始时间)                 :datetime
#  end_time(会议结束时间)                   :datetime
#  reserver_type                            :string(255)
#  reserver_id(会议预约人)                  :bigint
#  meta(会议其他的信息)                     :json
#  created_at                               :datetime         not null
#  updated_at                               :datetime         not null
#  type(STI类型)                            :string(255)
#  location(会议地点)                       :string(255)
#  school_id(所属学校)                      :bigint
#  published(活动是否已发布)                :boolean
#  meeting_room_name
#  registers
*/

import BaseModel, { IModelConfig } from '../BaseModel';
import { IActivityMeeting, IResponse } from './activity_meeting';

export class TeacherActivityMeeting extends BaseModel<IActivityMeeting, IResponse> {
  constructor(config?: IModelConfig) {
    super({
      name: 'activity_meeting',
      resource: 'activity_meetings',
      namespace: '/meeting/teacher',
      ...config,
    });
  }

  stat(id: string) {
    return this.request.get(`/meeting/teacher/activity_meetings/${id}/stat`, {});
  }

  stateStat() {
    return this.request.get(`/meeting/teacher/activity_meetings/state_stat`, {});
  }
}

export default new TeacherActivityMeeting();
