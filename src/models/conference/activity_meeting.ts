/*
################# index column ########
#
#  id                                       :bigint           not null, primary key
#  date(会议安排的日期)                     :date
#  title(会议的标题)                        :string(255)
#  desc(会议描述)                           :text(65535)
#  meeting_room_id(会议室)                  :bigint
#  begin_time(会议开始时间)                 :datetime
#  end_time(会议结束时间)                   :datetime
#  reserver_type                            :string(255)
#  reserver_id(会议预约人)                  :bigint
#  meta(会议其他的信息)                     :json
#  created_at                               :datetime         not null
#  updated_at                               :datetime         not null
#  type(STI类型)                            :string(255)
#  location(会议地点)                       :string(255)
#  school_id(所属学校)                      :bigint
#  published(活动是否已发布)                :boolean
#  meeting_room_name
#  registers
*/

import BaseModel, { IModelConfig } from '../BaseModel';

export interface IActivityMeeting {
  id?: number;
  created_at?: string;
  updated_at?: string;
  date?: string;
  title?: string;
  desc?: string;
  meeting_room_id?: string;
  begin_time?: string;
  end_time?: string;
  reserver_type?: string;
  reserver_id?: number;
  meta?: any;
  type?: string;
  location?: string;
  school_id?: number;
  published?: boolean;
  limit_count?: number;
  meeting_room_name?: string;
  state?: string;
  moderator_names?: string[];
  user_names?: string[];
  is_over?: boolean;
  is_moderator?: boolean;
  is_joined?: boolean;
  is_leaving?: boolean | string;
  is_inviting?: boolean | string;
  is_invited?: boolean | string;
  is_replacement?: boolean | string;
  moderator_ids?: number[];
  organizer_ids?: number[];
  user_ids?: any[];
  files?: any[];
  meeting_type?: string;
  meeting_department_ids?: number[];
}

export interface IResponse {
  activity_meetings: IActivityMeeting[];
}

export class ActivityMeeting extends BaseModel<IActivityMeeting, IResponse> {
  constructor(config?: IModelConfig) {
    super({
      name: 'activity_meeting',
      resource: 'activity_meetings',
      namespace: '/meeting/admin',
      ...config,
    });
  }
}

export default new ActivityMeeting();
