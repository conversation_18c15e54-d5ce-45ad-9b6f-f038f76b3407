import BaseModel, { IModelConfig } from '../BaseModel';

export interface IAdminRoom {
  id?: number;
  uid?: number;
  name?: string;
  state?: string;
  building?: string;
  meta?: object;
  created_at?: string;
  updated_at?: string;
}

export interface IResponse {
  meeting_rooms: IAdminRoom[];
}

export class AdminRoom extends BaseModel<IAdminRoom, IResponse> {
  constructor(config?: IModelConfig) {
    super({
      name: 'meeting_room',
      indexKey: 'meeting_rooms',
      resource: 'rooms',
      namespace: '/meeting/admin',
      ...config,
    });
  }
}

export default new AdminRoom();
