import BaseModel, { IModelConfig } from '../BaseModel';

export interface ITeacherRoom {
  id?: number;
  uid?: number;
  name?: string;
  state?: string;
  building?: string;
  meta?: object;
  created_at?: string;
  updated_at?: string;
}

export interface IResponse {
  meeting_rooms: ITeacherRoom[];
}

export class TeacherRoom extends BaseModel<ITeacherRoom, IResponse> {
  constructor(config?: IModelConfig) {
    super({
      name: 'meeting_room',
      indexKey: 'meeting_rooms',
      resource: 'rooms',
      namespace: '/meeting/teacher',
      ...config,
    });
  }
}

export default new TeacherRoom();
