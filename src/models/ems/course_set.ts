/**
 * 课程 (course_set)
 *
 * belongs_to course_dir
 */
import BaseModel from '@/models/BaseModel';
import { IInstance } from '../bpm/instance';

export interface ICourseSet {
  id?: number;
  created_at?: string;
  updated_at?: string;
  code?: string;
  credits?: number;
  enabled?: boolean;
  establish_on?: string;
  name?: string;
  project?: string;
  education?: string;
  category?: string;
  course_type?: string;
  school_id?: number;
  department_id?: number;
  exam_mode?: string;
  period?: number;
  meta?: string;
  course_hours?: any;
  department_name?: string;
  course_dir_id?: number;
  course_dir_name?: string;
  paste_major_count?: number;
  teacher_id?: number;
  teacher_code?: string;
  majors?: any[];
}

interface IResponse {
  course_sets: ICourseSet[];
}

export class CourseSet extends BaseModel<ICourseSet, IResponse> {
  constructor(role: 'admin' | 'user' | 'teacher_own') {
    super({
      name: 'course_set',
      resource: 'course_sets',
      parentResource: 'course_dirs',
      namespace: '/ems',
      role,
    });
  }
}
