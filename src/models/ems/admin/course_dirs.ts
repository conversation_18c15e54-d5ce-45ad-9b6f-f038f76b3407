import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';
import { IEmsCourseDirs } from '@/types/model';

export class EmsAdminCourseDirs extends ActiveModel<IEmsCourseDirs> {
  constructor(config?: IModelConfig) {
    super({
      namespace: '/ems/admin',
      name: 'course_dir',
      dataIndexKey: 'course_dirs',
      actions: [
        { name: 'export', method: 'post', on: 'collection' },
        { name: 'upload_excel', method: 'post', on: 'collection' },
        { name: 'excel', method: 'post', on: 'collection' },
        { name: 'import_headers', method: 'post', on: 'collection' },
        { name: 'export_headers', method: 'post', on: 'collection' },
        { name: 'import', method: 'post', on: 'collection' },
      ],
      ...config,
    });
  }
}
