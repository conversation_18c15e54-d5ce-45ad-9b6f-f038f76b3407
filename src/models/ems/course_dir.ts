/**
 * CourseDir (课程目录)
 *
 * has_many course_sets (课程)
 */
import BaseModel from '@/models/BaseModel';

export interface ICourseDir {
  id?: number;
  created_at?: string;
  updated_at?: string;
  name?: string;
  code?: string;
  school_id?: number;
  teacher_id?: number;
  department_id?: number;
  teacher_code?: string;
  teacher_name?: string;
  department_name?: string;
  department_code?: string;
  department_type?: string;
}

interface IResponse {
  course_dirs: ICourseDir[];
}

export class CourseDir extends BaseModel<ICourseDir, IResponse> {
  constructor(role: 'admin' | 'user') {
    super({
      name: 'course_dir',
      resource: 'course_dirs',
      namespace: '/ems',
      role,
    });
  }
}
