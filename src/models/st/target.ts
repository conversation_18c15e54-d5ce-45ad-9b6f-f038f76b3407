import createRequest from '@/utils/request';

const httpClient = createRequest();

interface ITargetArgs {
  role: string;
  namespace: string;
  resource: string;
  resourceId: number;
  target: string;
  params: object;
}

interface ITargetResponse {
  current_page: number;
  total_pages: number;
  total_count: number;
}

export default class Target {
  static fetchTargets<T>(args: ITargetArgs) {
    const { role, namespace, resource, resourceId, target, params } = args;
    return httpClient.get<T & ITargetResponse>(`/st/${role}/${namespace}/${resource}/${resourceId}/${target}`, {
      params,
    });
  }
}
