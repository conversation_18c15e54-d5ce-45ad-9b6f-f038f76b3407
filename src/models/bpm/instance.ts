import BaseModel from '../BaseModel';
import { PlaceActionType, WorkflowTypes } from './workflow';
import { IToken, TokenTypes } from './token';
import utils from '@/utils';
import { AxiosResponse } from 'axios';
import store from '@/store';

interface IResponse {
  instances: IInstance[];
}

export class Instance extends BaseModel<IInstance, IResponse> {
  constructor() {
    super({
      resource: 'instances',
      name: 'instance',
      namespace: '/bpm',
      parentResource: 'workflows',
      role: 'user',
    });
  }

  async find(id: number | string) {
    const res = await this.request.get<IInstance>(`${this.resourcePath}/${id}`);
    const { data } = res;
    // 可执行的操作
    const _enableActionsMap = (data.enable_actions || []).reduce(
      (obj: any, key: any) => ({
        ...obj,
        [key]: true,
      }),
      {},
    );
    // 可退回打回的节点
    const tokensMap = utils.objectify(data.tokens || [], 'place_id');
    const _historyPlaceOptions = (Object.values(tokensMap) as IToken[])
      .filter(
        (o: IToken) =>
          o.place_id && [TokenTypes.Submit, TokenTypes.Approval].includes(o.type!) && o.id !== data.current_token!.id,
      )
      .map((o: any) => ({ value: o.place_id, label: `【${o.operator_name}】${o.name}` }));
    // 默认退回或打回的节点
    const _defaultNextPlaceId = _historyPlaceOptions.length ? _historyPlaceOptions.concat()[0].value : 0;
    res.data = {
      ...res.data,
      _enableActionsMap,
      _historyPlaceOptions,
      _defaultNextPlaceId,
    };
    return res;
  }

  /**
   * 审批记录统计
   * @param type  统计的具体 instance 类型，空代表获取所有类型的数据
   * @param workflowId 统计某个工作流，不传，统计所有工作流
   * @param filterTypes 返回的所有类型统计，需要过滤的类型，只影响 statistic 的统计结果
   */
  async statistic(type?: InstanceType | string, workflowId?: number | string, filterTypes?: InstanceType[]) {
    const { data } = await this.request.post<IObject>(
      `/bpm/user/instances/statistic?workflow_id=${workflowId || ''}&type=${type || ''}`,
    );
    const statistic = Object.values(data || {})
      .filter((o: any) => (filterTypes ? filterTypes.includes(o.type) : true))
      .reduce((res, o) => {
        Object.keys(o).forEach((k: string) => {
          if (Number.isInteger(o[k])) {
            const stateKey = k.replace(/_count/, '');
            res[stateKey] = res[stateKey] || 0;
            res[stateKey] += o[k];
          }
        });
        return res;
      }, {});
    return {
      data: {
        ...data,
        statistic,
      } as IObject & { statistic: IObject },
    };
  }

  export(query: IObject) {
    return this.request.post<IObject>('/bpm/user/instances/export', {}, { params: query });
  }

  get stateMap() {
    return {
      all: { value: '', label: '全部' },
      created: { value: 'created', label: '待提交', type: 'info', color: '#CCCCCC' },
      preparing: { value: 'preparing', label: '待处理', type: 'info', color: '#CCCCCC' }, // 说明要指派审批人
      processing: { value: 'processing', label: '进行中', type: 'primary', color: '#1890ff' },
      completed: { value: 'completed', label: '已完成', type: 'success', color: '#15bc83' },
      checked: { value: 'checked', label: '审核通过', type: 'success', color: '#15bc83' },
      received: { value: 'received', label: '已收件', type: 'success', color: '#15bc83' },
      rejected: { value: 'rejected', label: '已打回', type: 'warning', color: '#f29851' },
      failed: { value: 'failed', label: '已退回', type: 'warning', color: '#f5222d' },
      canceled: { value: 'canceled', label: '已取消', type: 'warning', color: '#f5222d' },
      terminated: { value: 'terminated', label: '已终止', type: 'danger', color: '#f5222d' },
    } as IObject;
  }

  personTitle = (operatorId: number, operatorName: string) => {
    const { id } = store.state.currentUser;
    return operatorId === id ? '我' : operatorName;
  };

  operatorDesc = (instance: IInstance) => {
    if (!instance.tokens) {
      return '';
    }

    const operators = instance.tokens
      .filter(token => token.state === 'processing')
      .map(token => this.personTitle(Number(token.operator_id), token.operator_name!));

    if (operators.length === 0) {
      return '';
    }

    //“我” 排第一个
    if (operators.findIndex(name => name === '我') > 0) {
      operators.unshift('我');
    }

    // 去重
    const result = operators.filter((name, index) => operators.findIndex(n => n === name) === index);
    if (result.length > 3) {
      return `${result.slice(0, 3).join('、')}等${result.length}人`;
    }

    return result.join('、');
  };
}

export default new Instance();

export interface IInstance {
  id?: number | string;
  created_at?: string;
  updated_at?: string;
  workflow_id?: number;
  creator_name?: string;
  creator_type?: 'Teacher' | 'Student';
  creator_id?: number;
  creator_department_path?: string[];
  creator_department_name?: string;
  payload?: IObject;
  state?: string;
  seq?: string;
  type?: InstanceType;
  tokens?: IToken[];
  current_token?: IToken;
  last_token?: IToken;
  meta?: IObject;
  enable_actions?: PlaceActionType[];
  flowable_id?: number;
  flowable_type?: IFlowableType;
  flowable_info?: IObject;
  summary?: IObject;
  _enableActionsMap?: IObject; // 业务数据
  _historyPlaceOptions?: object[]; // 业务数据
  _defaultNextPlaceId?: number; // 业务数据
  flag?: string;
  storage?: IObject;
}

export enum InstanceType {
  Bpm = 'Bpm::Instance',
  Wechat = 'Wechat::Instance',
  FinanceVoucher = 'Finance::VoucherInstance',
  FinanceLoan = 'Finance::LoanVoucherInstance',
  FinanceProject = 'Finance::ProjectInstance',
  ExamActivity = 'Exam::ActivityInstance',
  Meeting = 'Meeting::ApplicationFormInstance',
  Welcome = 'Studying::Welcome::Instance',
  Hr = 'Hr::ModificationInstance',
  EmsCourseSet = 'Ems::CourseSetInstance',
  Inform = 'Inform::Instance',
  Teaching = 'Teaching::ScheduleCourseInstance',
  AssessmentSubmit = 'Assessment::SubmitInstance',
  AssessmentConfirm = 'Assessment::ConfirmInstance',
  Notice = 'Bpm::NoticeInstance',
}

export enum IFlowableType {
  Voucher = 'Finance::Voucher',
  LoanVoucher = 'Finance::LoanVoucher',
  FinanceProject = 'Finance::Project',
  ExamActivity = 'Exam::Activity',
  WelcomeEntry = 'Studying::Welcome::Entry',
  TeachingCourseSet = 'Teaching::CourseSet',
  AssessmentSubmit = 'Assessment::Entry',
  AssessmentConfirm = 'Assessment::Entry',
  MeetingActivity = 'Meeting::Activity',
}

export const workflowTypeToInstanceTypeMap: IObject = {
  [WorkflowTypes.Bpm]: InstanceType.Bpm,
  [WorkflowTypes.Wechat]: InstanceType.Wechat,
  [WorkflowTypes.FinanceVoucher]: InstanceType.FinanceVoucher,
  [WorkflowTypes.FinanceLoan]: InstanceType.FinanceLoan,
  [WorkflowTypes.FinanceProject]: InstanceType.FinanceProject,
  [WorkflowTypes.ExamActivity]: InstanceType.ExamActivity,
  [WorkflowTypes.Meeting]: InstanceType.Meeting,
  [WorkflowTypes.Welcome]: InstanceType.Welcome,
  [WorkflowTypes.Hr]: InstanceType.Hr,
  [WorkflowTypes.EmsCourseSet]: InstanceType.EmsCourseSet,
  [WorkflowTypes.AssessmentSubmit]: InstanceType.AssessmentSubmit,
  [WorkflowTypes.AssessmentConfirm]: InstanceType.AssessmentConfirm,
};
