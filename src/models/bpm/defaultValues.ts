import {
  TransitionTypes,
  PlaceTypes,
  IApiKeyAttr,
  IWorkflowCore,
  IWorkflow,
  WorkflowTypes,
  WorkflowState,
} from './workflow';
import { IPlaceMenuTemplate } from '@/components/flowTree/types';

const source: IObject = {
  get core() {
    return {
      tree: [{ source: { id: null, seq: 'start' }, target: { id: null, seq: 'end' } }],
      places: [
        {
          id: null,
          name: '开始',
          seq: 'start',
          type: PlaceTypes.Start,
          transition_type: TransitionTypes.Start,
          options: {},
          fields: { fields: [] },
        },
        {
          id: null,
          name: '结束',
          seq: 'end',
          type: PlaceTypes.End,
          transition_type: TransitionTypes.End,
          options: {},
          fields: { fields: [] },
        },
      ],
    };
  },
  get workflow() {
    return {
      id: null,
      type: WorkflowTypes.Bpm,
      state: WorkflowState.Todo,
      name: '',
      desc: '',
      core: this.core,
      form: {
        fields: [],
      },
      meta: {
        workflow_attributes: [],
        token_callbacks: [],
        workflow_callbacks: [],
        workflow_roles: [],
      },
    };
  },
  get places() {
    return [
      {
        name: '审批',
        desc: '配置审批节点',
        icon: '',
        type: PlaceTypes.Place,
        transition_type: TransitionTypes.ApprovalDirectManager,
        payload: {
          options: { manager: true },
        },
      },
      {
        name: '抄送',
        desc: '配置抄送节点',
        icon: '',
        type: PlaceTypes.Place,
        transition_type: TransitionTypes.NotifyDirectManager,
        payload: {
          options: { manager: true },
        },
      },
      {
        name: '文章发布',
        desc: '配置文章发送逻辑',
        icon: '',
        type: PlaceTypes.Place,
        transition_type: TransitionTypes.WechatDirectPublish,
      },
      { name: '条件', desc: '配置条件分支', icon: '', type: PlaceTypes.Route, isRoute: true },
      {
        name: '功能回调',
        desc: '配置内置的回调功能',
        icon: '',
        type: PlaceTypes.Place,
        transition_type: TransitionTypes.FlowableCallback,
        payload: {
          options: { name: '', callback_method: '' },
        },
      },
      {
        name: '接口回调',
        desc: '配置自定义接口回调',
        icon: '',
        type: PlaceTypes.Place,
        transition_type: TransitionTypes.ApiCallback,
        payload: {
          options: {
            method: 'post',
            url: '',
            headers: {},
            key_attrs: [] as IApiKeyAttr[],
          },
        },
      },
      {
        name: '服务方法回调',
        desc: '配置服务方法回调',
        icon: '',
        type: PlaceTypes.Place,
        transition_type: TransitionTypes.FunctionCallback,
        payload: {
          options: {
            function: '',
            class_name: '',
            key_attrs: [] as IApiKeyAttr[],
          },
        },
      },
      {
        name: '计算',
        desc: '配置属性计算',
        icon: '',
        type: PlaceTypes.Place,
        transition_type: TransitionTypes.Formula,
        payload: {
          options: {
            function: '',
            formula: '',
          },
        },
      },
      {
        name: '指标采集',
        desc: '配置指标采集',
        icon: '',
        type: PlaceTypes.Place,
        transition_type: TransitionTypes.PointCount,
        payload: {
          options: {
            function: '',
            formula: '',
          },
        },
      },
    ];
  },
};

export const defaultCore: IWorkflowCore = source.core;

export const defaultWorkflow: IWorkflow = source.workflow;

export const defaultMenuPlaces: IPlaceMenuTemplate[] = source.places;
