import utils from '@/utils';
import <PERSON>hem<PERSON>, { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ErrorList } from 'async-validator';
import { IFormTemplateItem } from '@/interfaces/formTemplate.interface';
import BaseModel, { IModelConfig } from '../BaseModel';
import tokenModel, { IToken } from './token';
import { IFile } from '../file';

export class Workflow extends BaseModel<IWorkflow, IResponse> {
  constructor(config?: IModelConfig | IObject) {
    super({
      resource: 'workflows',
      name: 'workflow',
      namespace: '/bpm',
      role: 'admin',
      ...config,
    });
  }

  clone(id: number | string) {
    return this.request.post(`${this.resourcePath}/${id}/clone`);
  }

  /**
   * 获取指定 token 赋权的工作流表单，无 token 取第一个节点
   * @param workflowId 工作流实例
   * @param tokenId 阶段 token
   */
  async findTokenForm(workflowId: any, tokenId?: number | string | null): Promise<ITokenFormResponse> {
    const { data } = await this.find(workflowId);
    const { fields } = data.form || { fields: [] };
    let token: IToken = {
      transition: (data.core!.places || []).find(o => o.type === PlaceTypes.Start),
    };
    if (tokenId) {
      const res = await tokenModel.find(tokenId);
      token = res.data;
    }
    const newFormFields = this.getAccessibilityFields(fields, token.transition!);
    return Promise.resolve<ITokenFormResponse>({
      workflow: data,
      token,
      formTemplate: newFormFields,
      formEditable: newFormFields.some(o => o.accessibility === 'read_and_write' || !o.accessibility),
    });
  }

  /**
   * template: workflow 的 form.fields.fields 定义
   * transition: 挡圈操作的 place 或 transition
   * return: 返回新的模板，增加了 具体节点的 accessibility 属性
   */
  getAccessibilityFields(template: any[], transition: IWorkflowCorePlace) {
    if (!transition) {
      return template;
    }
    const power = utils.objectify((transition.fields && transition.fields.fields) || [], 'key', 'accessibility');
    const defaultAccessibility = [TransitionTypes.Start, PlaceTypes.Start].includes(transition.type!)
      ? 'read_and_write'
      : 'readonly';
    return template.map(item => ({
      ...item,
      accessibility: item.accessibility === 'hidden' ? 'hidden' : power[item.key] || defaultAccessibility,
    }));
  }

  /**
   * 获取 template 对于表单的验证规则
   */
  getFormRules(template: IFormTemplateItem[]) {
    return template.reduce((rules: IObject, item: IFormTemplateItem) => {
      rules[item.key!] = {
        required: item.layout.required && item.accessibility === 'read_and_write',
        message: `请设置${item.name}`,
        type: Workflow.getRuleType(item),
      };
      return rules;
    }, {});
  }

  /**
   * 根据模板表单，验证 formData 合法性
   */
  validateForm(formData: IObject, template: IFormTemplateItem[], callbacks: ValidateCallbacks = {}) {
    const rules = this.getFormRules(template);
    const validator = new Schema(rules);
    validator.validate(formData, {}, (errors, errorFields) => {
      if (errors) {
        if (callbacks.fail) {
          callbacks.fail(errors, errorFields);
        }
      } else {
        if (callbacks.success) {
          callbacks.success(formData);
        }
      }
    });
  }

  /**
   * 获取表单项 async-validator type
   */
  static getRuleType(item: IFormTemplateItem) {
    const specialTypeMap: IObject = {
      text: 'string',
      json: 'object',
      table: 'array',
    };
    let modelAttrType = item.model ? item.model.attr_type : '';
    let type = specialTypeMap[modelAttrType] || modelAttrType;
    if (['wechat_articles'].includes(item.layout.component)) {
      // if (['date', 'datetime', 'time', 'wechat_articles'].includes(item.layout.component)) {
      type = 'object';
    } else if (['date', 'datetime', 'time'].includes(item.layout.component)) {
      type = 'string';
    } else if (['checkbox', 'file', 'contacts', 'record', 'table'].includes(item.layout.component)) {
      type = item.layout.multiple ? 'array' : type;
    }
    return type;
  }
}

export default new Workflow();

export const userWorkflow = new Workflow({ role: 'user' });

export interface IWorkflow {
  id: number | string | undefined;
  name?: string;
  state?: WorkflowState;
  desc?: string;
  type?: WorkflowTypes;
  core?: IWorkflowCore;
  form?: { fields: Array<IFormTemplateItem> };
  storage?: { fields: Array<IFormTemplateItem> };
  category_id?: number;
  category_name?: string;
  image?: IFile[];
  catalog?: string;
  meta?: {
    flag?: string;
    flag_name?: string;
    workflow_roles?: string[];
    workflow_callbacks?: Array<{ callback_method: string; name: string }>;
    workflow_attributes: Array<{ attr: string; name: string; attr_type: string }>;
    token_callbacks?: string[]; // 选择之后，更新 place.callback_options
  };
  print_component?: string;
  created_at?: string;
  updated_at?: string;
  permit_type?: string;
  paste_teacher_ids?: number[];
  paste_student_ids?: number[];
  modul?: string;
  model_define_id?: number;
  flag?: string;
  cm_model_setting?: {
    id?: number;
    model_define_id?: number;
    flag?: string;
  };
  cm_model_setting_attributes?: {
    id?: number;
    model_define_id?: number;
    flag?: string;
  };
}

export enum WorkflowState {
  Todo = 'todo',
  Done = 'done',
}

export enum WorkflowTypes {
  Bpm = 'Bpm::Workflow',
  Wechat = 'Wechat::Workflow',
  FinanceVoucher = 'Finance::VoucherWorkflow',
  FinanceLoan = 'Finance::LoanVoucherWorkflow',
  FinanceProject = 'Finance::ProjectWorkflow',
  ExamActivity = 'Exam::ActivityWorkflow',
  Meeting = 'Meeting::ApplicationFormWorkflow',
  Inform = 'Inform::Workflow',
  Welcome = 'Studying::Welcome::Workflow',
  Hr = 'Hr::ModificationWorkflow',
  EmsCourseSet = 'Ems::CourseSetWorkflow',
  // teaching
  // Teaching = 'Teaching::ScheduleCourseInstance',
  Teaching = 'Teaching::ScheduleCourseWorkflow',
  AssessmentSubmit = 'Assessment::SubmitWorkflow',
  AssessmentConfirm = 'Assessment::ConfirmWorkflow',
}

export const WorkflowTypeName: IObject = {
  'Bpm::Workflow': '通用流程',
  'Wechat::Workflow': '微信矩阵管理系统',
  'Finance::VoucherWorkflow': '资金卡审核流程',
  'Finance::LoanVoucherWorkflow': '资金卡暂借款流程',
  'Finance::ProjectWorkflow': '资金卡承诺书流程',
  'Hr::ModificationWorkflow': '人事审批流程',
  'Exam::ActivityWorkflow': '考试发布流程',
  'Meeting::ApplicationFormWorkflow': '会议人员申请流程',
  'Assessment::SubmitWorkflow': '人事考核提交流程',
  'Assessment::ConfirmWorkflow': '人事考核确认流程',
  'Inform::Workflow': '新闻公告申请流程',
  'Studying::Welcome::Workflow': '迎新活动',
  'Ems::TeachingetWorkflow': '课程标准审批流程',
  // 'Teaching::ScheduleCourseInstance': '教学计划审批流程',
  'Teaching::ScheduleCourseWorkflow': '教学计划审批流程',
};

export interface IWorkflowCore {
  places: IWorkflowCorePlace[];
  tree: IObject[];
}

export enum IWorkflowModul {
  BPM = '业务流程',
  OA = 'OA',
}

export interface IWorkflowCorePlace {
  id?: number | null;
  seq?: string;
  name?: string;
  type?: PlaceTypes;
  transition_type?: TransitionTypes;
  workflow_id?: number;
  options?: IObject;
  token?: IToken;
  fields?: {
    fields: IFormTemplateItem[];
  };
  position?: number;
  callback_options?: {
    callback_method?: string;
    callback_type?: 'token_callback';
    action_permits?: { [key: string]: boolean }; // 节点操作的显示权限
    action_alias?: PlaceActionConfig; // 节点操作的配置
  };
  place_form?: {
    fields: IFormTemplateItem[];
  }; // 节点自身表单
  kind?: 'condition'; // 业务属性
  place_meta?: {
    major?: string[];
  };
}

// 流程的 place 类型
export enum PlaceTypes {
  Start = 'Places::StartPlace', // 开始节点
  End = 'Places::EndPlace', // 结束节点
  Route = 'Places::Route', // 条件节点
  Place = 'Place', // 普通节点
}

export enum TransitionTypes {
  Transition = 'Transition', // 结束节点
  Start = 'Transitions::Submit', // 开始节点
  End = 'Transitions::Finish',
  Condition = 'Transitions::Condition', // 条件节点

  ApprovalUser = 'Transitions::Approval::User', // 直接指定人员
  ApprovalUserAll = 'Transitions::Approval::UserAll', // 预先设置人员，与签
  ApprovalUserAny = 'Transitions::Approval::UserAny', // 预先设置人员，或签
  ApprovalSelect = 'Transitions::Approval::ApprovalSelect', // 预先设置或签与签，审批人自选人员
  ApprovalSpecifyManager = 'Transitions::Approval::SpecifyManager', // 部门主管
  ApprovalDirectManager = 'Transitions::Approval::DirectManager', // 直接主管
  ApprovalResearchManager = 'Transitions::Approval::ResearchManager', // 科研分管领导
  ApprovalLevelManager = 'Transitions::Approval::LevelManager', // 学院主管
  ApprovalSponsorSelect = 'Transitions::Approval::SponsorSelect', // 发起人自选
  ApprovalSponsorSelf = 'Transitions::Approval::SponsorSelf', // 发起人自己
  ApprovalFlowableRole = 'Transitions::Approval::FlowableRole', // 角色分配

  NotifySpecifyManager = 'Transitions::Notify::SpecifyManager', // 部门主管
  NotifyDirectManager = 'Transitions::Notify::DirectManager', // 直接主管
  NotifyLevelManager = 'Transitions::Notify::LevelManager', // 学院主管
  NotifyTeacher = 'Transitions::Notify::Teacher', // 指定成员
  NotifyFlowableRole = 'Transitions::Notify::FlowableRole', // 角色分配
  NotifySponsorSelect = 'Transitions::Notify::SponsorSelect', // 发起人自选

  WechatDirectPublish = 'Wechat::Transitions::DirectPublish', // 公众号文章直接发送
  WechatTimedPublish = 'Wechat::Transitions::TimedPublish', // 公众号文章定时发送

  FlowableCallback = 'Transitions::FlowableCallback', // 方法回调
  FunctionCallback = 'Transitions::FunctionCallback', // 方法回调
  ApiCallback = 'Transitions::ApiCallback', // api 回调

  ApprovalTeacher = 'Transitions::Approval::Teacher', // 指定一人 （deprecated）
  ApprovalTeacherAny = 'Transitions::Approval::TeacherAny', // 任选一人（deprecated）

  Formula = 'Transitions::Formula',

  PointCount = 'Transitions::Point::Count',
  PointValue = 'Transitions::Point::Value',
}

// place enable_actions 节点自定义操作
export type PlaceActionType =
  | 'print'
  | 'submit'
  | 'edit' // 可以编辑
  | 'assign' // 指派人员
  | 'forward' //转发
  | 'accept' // 通过
  | 'recall' // 撤回
  | 'reject' // 打回，指定 next_place_id，退回后，提交还是到当前节点
  | 'fail' // 退回，指定 next_place_id，退回从新走流程
  | 'terminate'; // 终止

export interface PlaceActionConfig {
  [key: string]: {
    name: string;
    show: boolean;
    desc: string;
  };
}
// 节点操作配置
export const getDefaultPlaceActionConfig = (): PlaceActionConfig => ({
  print: { name: '打印', show: true, desc: '打印关联的数据' },
  submit: { name: '提交', show: true, desc: '当前流程需要您提交后，流程才可开始运行' },
  edit: { name: '编辑', show: true, desc: '编辑关联的数据' },
  assign: { name: '指派', show: true, desc: '选择节点处理人员' },
  forward: { name: '转办', show: true, desc: '选择其他转办人员' },
  accept: { name: '通过', show: true, desc: '接受当前节点，使流程可以转移到下一节点' },
  recall: { name: '撤回', show: true, desc: '撤回已执行的审批操作，并重新处理' },
  reject: { name: '驳回', show: true, desc: '驳回节点，需要重新走审批流程' },
  fail: { name: '退回', show: true, desc: '退回节点，不需要重新走流程' },
  terminate: { name: '终止', show: true, desc: '直接结束本次流程' },
  open: { name: '打开链接', show: false, desc: '打开指定的页面流程' },
});

interface IResponse {
  workflows: IWorkflow[];
}

type ValidateCallbacks = {
  success?: (formData: IObject) => void;
  fail?: (errors: ErrorList, errorFields: FieldErrorList) => void;
};

export interface ITokenFormResponse {
  workflow: IWorkflow;
  token: IToken;
  formTemplate: IFormTemplateItem[];
  formEditable: boolean;
}

// api callback
export interface IApiKeyAttr {
  key: string; // form里面定义的属性key
  key_name: string; // form里面原来属性的名称
  name: string; // 回调映射的属性名称
}
