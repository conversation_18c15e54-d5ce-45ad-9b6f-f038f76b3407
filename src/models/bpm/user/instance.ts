import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';
import { IInstance } from '../instance';

export class BpmUserInstance extends ActiveModel<IInstance> {
  constructor(config?: IModelConfig) {
    super({
      namespace: '/bpm/user',
      name: 'instance',
      actions: [
        { name: 'statistic', method: 'post', on: 'collection' },
        { name: 'three_level_stat', method: 'post', on: 'collection' },
        { name: 'export', method: 'post', on: 'collection' },
        { name: 'export_headers', method: 'post', on: 'collection' },
      ],
      ...config,
    });
  }
}
