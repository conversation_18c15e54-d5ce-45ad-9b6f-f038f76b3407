import BaseModel from '@/models/BaseModel';

export interface IFormTemplate {
  id?: number;
  created_at?: string;
  updated_at?: string;
  name?: string;
  form?: {
    fields: any[];
  };
  school_id?: number;
  creator_type?: string;
  creator_id?: number;
  type?: TemplateType;
  creator_name?: string;
}

interface IResponse {
  templates: IFormTemplate[];
}

export enum TemplateType {
  Voucher = 'Forms::VoucherTemplate',
  LoanVoucher = 'Forms::LoanVoucherTemplate',
  Teacher = 'Forms::TeacherTemplate',
}

export const typeOptions = [
  { label: '通用模板', value: '' },
  { label: '资金卡报销', value: TemplateType.Voucher },
  { label: '资金卡借款', value: TemplateType.LoanVoucher },
  { label: '人事花名册', value: TemplateType.Teacher },
];

export class FormTemplate extends BaseModel<IFormTemplate, IResponse> {
  constructor(role: 'admin' | 'teacher') {
    super({
      name: 'template',
      resource: 'templates',
      namespace: '/forms',
      role,
    });
  }
}
