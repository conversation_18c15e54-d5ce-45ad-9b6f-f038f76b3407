import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';
import { IActivityMeeting } from '../../conference/activity_meeting';

export class MeetingTeacherActivityMeeting extends ActiveModel<IActivityMeeting> {
  constructor(config?: IModelConfig) {
    super({
      namespace: '/meeting/teacher',
      name: 'activity_meeting',
      actions: [
        { name: 'export', method: 'post', on: 'collection' },
        { name: 'export_headers', method: 'post', on: 'collection' },
      ],
      ...config,
    });
  }
}
