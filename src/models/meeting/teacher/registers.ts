import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';

export interface IRegister {
  id?: number;
  user_type?: string;
  user_id?: number;
  source_type?: string;
  source_id?: string;
  state?: string;
  type?: string;
  lon?: string;
  lat?: string;
  user?: any;

  signup_at?: string;
  user_name?: string;
}

export class MeetingTeacherRegisters extends ActiveModel<IRegister> {
  constructor(config?: IModelConfig) {
    super({
      name: 'register',
      namespace: 'meeting/teacher',
      actions: [{ name: 'sign_in', method: 'patch', on: 'collection' }],
      ...config,
    });
  }
}
