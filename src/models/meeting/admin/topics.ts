import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';
import { MeetingTopic } from '@/types/model';

export class MeetingAdminTopics extends ActiveModel<MeetingTopic> {
  constructor(config?: IModelConfig) {
    super({
      namespace: '/meeting/admin',
      name: 'topics',
      dataIndexKey: 'records',
      actions: [
        { name: 'export', method: 'post', on: 'collection' },
        { name: 'excel', method: 'post', on: 'collection' },
        { name: 'export_headers', method: 'post', on: 'collection' },
      ],
      ...config,
    });
  }
}
