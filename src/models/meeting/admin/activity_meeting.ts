import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';
import { IActivityMeeting } from '../../conference/activity_meeting';

export class MeetingAdminActivityMeeting extends ActiveModel<IActivityMeeting> {
  constructor(config?: IModelConfig) {
    super({
      namespace: '/meeting/admin',
      name: 'activity_meeting',
      actions: [
        { name: 'export', method: 'post', on: 'collection' },
        { name: 'export_headers', method: 'post', on: 'collection' },
      ],
      ...config,
    });
  }
}
