import BaseModel, { IModelConfig } from '../BaseModel';
import { IActivity } from './activity';

export interface IAttendance {
  id?: number;
  meeting_activity?: IActivity;
  user_id?: number;
  user_type?: 'Teacher' | 'Student';
  user_name?: string;
  user_code?: string;
  state?: AttendanceStates;
  meeting_activity_id?: number;
  meta?: IObject;
  signup_at?: any;
  created_at?: string;
  updated_at?: string;
  user_department_path?: string[];
  user_department_name?: string;
}

// todo -> signed -> checked | canceled
export enum AttendanceStates {
  signed = '已报名',
  checked = '已签到',
  canceled = '已取消',
  todo = '待审批',
}

interface IResponse {
  meeting_attendances: IAttendance[];
}

export class Attendance extends BaseModel<IAttendance, IResponse> {
  constructor(config?: IModelConfig) {
    super({
      name: 'meeting_attendance',
      resource: 'attendances',
      namespace: '/meeting/admin',
      parentResource: 'activities',
      ...config,
    });
  }

  // User cancel
  cancel(id: number) {
    return this.request.post(`${this.namespace}/${this.resource}/${id}/cancel`);
  }
}

export default new Attendance();
