import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';
import { IBudget } from '@/models/finance/budget';

// intl/user/activities/:activitiesId/finance_budgets
export class IntlUserActivitiesFinanceBudgets extends ActiveModel<IBudget> {
  constructor(config?: IModelConfig) {
    super({
      name: 'finance_budget',
      namespace: 'intl/user',
      dataIndexKey: 'budgets',
      ...config,
    });
  }
}
