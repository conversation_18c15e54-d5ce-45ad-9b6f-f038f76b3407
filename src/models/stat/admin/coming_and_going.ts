import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';
import { IStatComingAndGoing } from '@/types/model';

export class StatAdminComingAndGoing extends ActiveModel<IStatComingAndGoing> {
  constructor(config?: IModelConfig) {
    super({
      namespace: '/stat/admin',
      name: 'coming_and_going',
      dataIndexKey: 'records',
      actions: [
        { name: 'export', method: 'post', on: 'collection' },
        { name: 'export_headers', method: 'post', on: 'collection' },
      ],
      ...config,
    });
  }
}
