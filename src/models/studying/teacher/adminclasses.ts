import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';
import { IAdminclasses } from '@/types/model';

export class StudyingTeacherAdminclasses extends ActiveModel<IAdminclasses> {
  constructor(config?: IModelConfig) {
    super({
      namespace: '/studying/teacher',
      name: 'adminclasses',
      actions: [{ name: 'export', method: 'post', on: 'collection' }],
      ...config,
    });
  }
}
