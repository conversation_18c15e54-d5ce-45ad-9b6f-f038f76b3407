import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';
import { IStudent } from '../../student';

export class StudyingTeacherStudents extends ActiveModel<IStudent> {
  constructor(config?: IModelConfig) {
    super({
      namespace: '/studying/teacher',
      name: 'students',
      actions: [
        { name: 'export', method: 'post', on: 'collection' },
        { name: 'excel', method: 'get', on: 'collection' },
        { name: 'export_headers', method: 'post', on: 'collection' },
      ],
      ...config,
    });
  }
}
