import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';
import { IAdminclasses } from '@/types/model';

export class StudyingAdminAdminclasses extends ActiveModel<IAdminclasses> {
  constructor(config?: IModelConfig) {
    super({
      namespace: '/studying/admin',
      name: 'adminclasses',
      actions: [{ name: 'export', method: 'post', on: 'collection' }],
      ...config,
    });
  }
}
