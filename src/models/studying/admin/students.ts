import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';
import { IStudent } from '../../student';

export class StudyingAdminStudents extends ActiveModel<IStudent> {
  constructor(config?: IModelConfig) {
    super({
      namespace: '/studying/admin',
      name: 'student',
      actions: [
        { name: 'export', method: 'post', on: 'collection' },
        { name: 'upload_excel', method: 'post', on: 'collection' },
        { name: 'excel', method: 'post', on: 'collection' },
        { name: 'import_headers', method: 'post', on: 'collection' },
        { name: 'export_headers', method: 'post', on: 'collection' },
        { name: 'import', method: 'post', on: 'collection' },
      ],
      ...config,
    });
  }
}
