import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';
import { IStudyingStudentDaily } from '@/types/model';

export class StudyingAdminStatStudentDaily extends ActiveModel<IStudyingStudentDaily> {
  constructor(config?: IModelConfig) {
    super({
      namespace: '/studying/admin/stat',
      name: 'student_daily',
      actions: [
        { name: 'summary', method: 'get', on: 'collection' },
        { name: 'export', method: 'post', on: 'collection' },
      ],
      dataIndexKey: 'records',
      ...config,
    });
  }
}
