import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';
import { IStudyingEntry } from '@/types/model';

export class StudyingAdminWelcomeEntries extends ActiveModel<IStudyingEntry> {
  constructor(config?: IModelConfig) {
    super({
      namespace: '/studying/admin/welcome',
      name: 'entries',
      actions: [
        { name: 'export', method: 'post', on: 'collection' },
        { name: 'upload_excel', method: 'post', on: 'collection' },
        { name: 'excel', method: 'post', on: 'collection' },
        { name: 'import_headers', method: 'post', on: 'collection' },
        { name: 'export_headers', method: 'post', on: 'collection' },
        { name: 'import', method: 'post', on: 'collection' },
      ],
      ...config,
    });
  }
}
