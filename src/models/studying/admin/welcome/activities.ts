import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';
import { IStudyingActivity } from '@/types/model';

export class StudyingAdminWelcomeActivities extends ActiveModel<IStudyingActivity> {
  constructor(config?: IModelConfig) {
    super({
      namespace: '/studying/admin/welcome',
      name: 'activities',
      actions: [
        { name: 'statistics', method: 'post', on: 'member' },
        { name: 'export_statistics', method: 'post', on: 'member' },
      ],

      ...config,
    });
  }
}
