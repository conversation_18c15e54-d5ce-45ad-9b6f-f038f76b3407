/*
################# index column ########
#
#  id                         :bigint           not null, primary key
#  teacher_id(教师id)         :bigint
#  access_activity_id(活动id) :bigint
#  total_score(理论分数)      :integer          default(0)
#  score(实际分数)            :integer          default(0)
#  meta(扩展字段)             :json
#  state(状态)                :string(255)
#  deleted_at(软删除标识)     :datetime
#  created_at                 :datetime         not null
#  updated_at                 :datetime         not null
#
*/

import BaseModel from '../BaseModel';
import { IQuestionSet } from './question_set';
import request from '@/utils/request';

export interface IEntryMeta {
  total: '优秀' | '良好' | '一般' | '较差';
  question_count?: number;
  duty: string;
  work: string;
}

export interface ScopeTeacherId {
  id: number;
  name: string;
  teacher_ids: number[];
}

export interface IEntry {
  id: number;
  access_activity_id: number;
  title: string;
  body?: string;
  state: string;
  type: string;
  start_at: string;
  end_at: string;
  readonly meta?: IObject;
  entry_meta?: IEntryMeta;
  exam?: IQuestionSet[]; // 显示题目
  parentId?: number;
  scope_teacher_ids?: ScopeTeacherId[];
}

export interface IResponse {
  entries: IEntry[];
}

export class Entry extends BaseModel<IEntry, IResponse> {
  constructor() {
    super({
      name: 'access_entry',
      resource: 'entries',
      namespace: '/access/admin',
      parentResource: 'activities',
    });
  }

  unactive_teachers(params: any) {
    return this.request.get(`${this.namespace}/entries/${params.parentId}/unactive_teachers`, { params });
  }

  // entry_activities
  entry(activityId: number) {
    return this.request.get(`/access/teacher/entry_activities/${activityId}/entry`);
  }

  update_entry(entry: IEntry) {
    return this.request.patch(`/access/teacher/entry_activities/${entry.parentId}/entry`, {
      access_entry: entry,
    });
  }
  // score_activities
  score_entries(params: any) {
    return this.request.get(`/access/teacher/score_activities/${params.parentId}/score_entries`, { params });
  }

  score_entry(id: number) {
    return this.request.get(`/access/teacher/score_entries/${id}`);
  }

  batch_update_scores(params: any) {
    return this.request.post(`/access/admin/entries/${params.entryId}/batch_update_scores`, {
      ...params,
    });
  }

  export(entryId: number) {
    return this.request.get(`/access/admin/entries/${entryId}/export`);
  }
}

export default new Entry();
