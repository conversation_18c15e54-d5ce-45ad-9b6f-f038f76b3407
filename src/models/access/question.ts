/*
################# index column ########
#
#  id                             :bigint           not null, primary key
#  access_question_set_id(题目id) :bigint
#  teacher_id(教师id)             :bigint
#  title(题干)                    :text(65535)
#  meta(扩展字段)                 :json
#  score(分数)                    :integer          default(100)
#  deleted_at(软删除标识)         :datetime
#  created_at                     :datetime         not null
#  updated_at                     :datetime         not null
#
*/

import BaseModel from '../BaseModel';

export interface IQuestion {
  id: number;
  access_question_set_id: number;
  teacher_id: number;
  title: number;
  score?: number;
  meta: any;
}

export interface IResponse {
  questions: IQuestion[];
}

export class Question extends BaseModel<IQuestion, IResponse> {
  constructor() {
    super({
      name: 'access_question',
      resource: 'questions',
      namespace: '/access/admin',
      parentResource: 'question_sets',
    });
  }
}

export default new Question();
