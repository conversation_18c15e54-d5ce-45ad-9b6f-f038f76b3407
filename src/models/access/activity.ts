/*
#
#  id                   :bigint           not null, primary key
#  school_id(学校)      :bigint
#  name(名称)           :string(255)      default("")
#  body(活动信息)       :text(65535)
#  state(状态)          :string(255)
#  attachments(附件)    :json
#  start_at(开始时间)   :datetime
#  end_at(结束时间)     :datetime
#  meta(扩展字段)       :json
#  type(STI)            :string(255)
#  deleted_at(删除标识) :datetime
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#
*/
import BaseModel, { IModelConfig } from '../BaseModel';
import { IEntryMeta } from './entry';
import { IFile } from '../file';

type ActivityTag = { type: 'primary' | 'default' | 'success'; text: string };

export interface IActivity {
  id?: number;
  type?: string;
  school_id?: number;
  name?: string;
  body?: string;
  state?: any;
  teacher_ids?: number[];
  attachments?: {
    documents: IFile[];
  };
  created_at?: string;
  updated_at?: string;
  deleted_at?: string;
  start_at?: string;
  end_at?: string;
  meta?: IObject;
  scope_names?: string[];
  entry_meta?: IEntryMeta;
  entry?: boolean; // 用户是被考核人员，需要自评，用于第一阶段
  score?: boolean; // 用户是考核人员，需要考核其他人，用于第二阶段
  score_count?: number; // 已考核人数
  total_entry_count?: number; // 总待考核人数
  stage?: number; // ## 业务字段：当前所处阶段：-1 0，1.5, 1，1.5, 2，2.5 [未开始，自评，间隙，互评，间隙，统计，结束]
  tag?: { type: 'primary' | 'default' | 'success'; text: string }; // ## 活动当前的状态描述标签
  isSelf?: boolean; // 是否是自评阶段
  isAssessment?: boolean; // 是否是考核阶段
  isStatistics?: boolean; // 是否是统计阶段
}

interface IResponse {
  meeting_activities: IActivity[];
}

export class Activity extends BaseModel<IActivity, IResponse> {
  constructor(config?: IModelConfig) {
    super({
      name: 'access_activity',
      resource: 'activities',
      namespace: '/access/admin',
      ...config,
    });
  }

  score_teachers(params: any) {
    return this.request.get(`${this.namespace}/activities/${params.parentId}/score_teachers`, { params });
  }

  statistics(activityId: number) {
    return this.request.get(`${this.namespace}/activities/${activityId}/statistics`);
  }

  ranks(activityId: number) {
    return this.request.get(`${this.namespace}/activities/${activityId}/ranks`);
  }

  export(activityId: number) {
    return this.request.get(`${this.namespace}/activities/${activityId}/export`);
  }

  static getActivityStageInfo(activity: IActivity) {
    if (!activity.id) {
      return { stage: -1, isSelf: false, isAssessment: false, isStatistics: false };
    }
    const {
      self_start_at,
      self_end_at,
      assessment_start_at,
      assessment_end_at,
      statistics_start_at,
      statistics_end_at,
    } = activity.meta!;
    const now = new Date(new Date()).valueOf();
    const ary = [
      new Date(self_start_at).setHours(0, 0, 0, 0),
      new Date(self_end_at).setHours(23, 59, 59, 999),
      new Date(assessment_start_at).setHours(0, 0, 0, 0),
      new Date(assessment_end_at).setHours(23, 59, 59, 999),
      new Date(statistics_start_at).setHours(0, 0, 0, 0),
      new Date(statistics_end_at).setHours(23, 59, 59, 999),
    ];
    let stage = -1;
    if (now >= ary[0]) stage = 0;
    if (now > ary[1]) stage = 0.5;
    if (now >= ary[2]) stage = 1;
    if (now > ary[3]) stage = 1.5;
    if (now >= ary[4]) stage = 2;
    if (now > ary[5]) stage = 2.5;
    return {
      isSelf: self_start_at && now >= ary[0] && now <= ary[1], // 是否是自评阶段
      isAssessment: assessment_start_at && now >= ary[2] && now <= ary[3], // 是否是考核阶段
      isStatistics: statistics_start_at && now >= ary[4] && now <= ary[5], // 是否是统计阶段
      stage, // 当前所处阶段：-1 0，1.5, 1，1.5, 2，2.5 [未开始，自评，间隙，互评，间隙，统计，结束]
    };
  }

  static getActivityExtra(activity: IActivity) {
    let tagConfig: ActivityTag = { type: 'default', text: '' };
    if (!activity.id) return { tag: tagConfig };

    const { stage, ...states } = this.getActivityStageInfo(activity);
    const {
      entry,
      score,
      entry_meta: entryMeta,
      score_count: scoreCount,
      total_entry_count: totalEntryCount,
    } = activity as any;
    const { total: selfTotal = 0, question_count: questionCount = 0 } = entryMeta || {};
    // type: 文本样式类型，text: 描述文本
    if (stage < 0) {
      // 未开始
      tagConfig = { type: 'default', text: '等到考核活动开启' };
    } else if (stage === 0) {
      // 自评阶段
      if (entry) {
        if (selfTotal && questionCount) tagConfig = { type: 'success', text: '已填写' };
        else if (selfTotal) tagConfig = { type: 'primary', text: '请完善工作内容' };
        else if (questionCount) tagConfig = { type: 'primary', text: '请完善总评' };
        else tagConfig = { type: 'primary', text: '请完善工作内容及总评' };
      } else if (score) {
        if (selfTotal && questionCount) tagConfig = { type: 'default', text: '等待考核阶段开启' };
        else tagConfig = { type: 'default', text: '等待被考核人自评' };
      } else {
        tagConfig = { type: 'default', text: '等待被考核人自评' };
      }
    } else if (stage < 1) {
      // 自评结束
      tagConfig = { type: 'default', text: '等待考核阶段开启' };
    } else if (stage === 1) {
      // 考核阶段
      tagConfig = { type: 'default', text: '等待考核' };
      if (entry) {
        tagConfig = { type: 'default', text: '等待考核' };
      }
      if (score) {
        if (scoreCount === totalEntryCount) {
          tagConfig = { type: 'success', text: `已考核 ${totalEntryCount} 人` };
        } else {
          tagConfig = { type: 'primary', text: `已考核 ${scoreCount} 人，待考核 ${totalEntryCount - scoreCount} 人` };
        }
      }
    } else if (stage < 2) {
      // 考核结束
      tagConfig = { type: 'default', text: '等待统计阶段开启' };
    } else if (stage === 2) {
      // 统计阶段
      tagConfig = { type: 'default', text: '等待统计' };
    } else if (stage > 2) {
      // 统计结束，活动结束
      tagConfig = { type: 'default', text: '考核活动结束' };
    }
    return {
      stage,
      tag: tagConfig,
      ...states,
    };
  }
}

export default new Activity();
