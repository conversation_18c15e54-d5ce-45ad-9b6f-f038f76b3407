/*
################# index column ########
#  id                         :bigint           not null, primary key
#  access_activity_id(活动id) :bigint
#  teacher_id(教师id)         :bigint
#  access_scope_id(维度id)    :bigint
#  created_at                 :datetime         not null
#  updated_at                 :datetime         not null
#
*/

import BaseModel from '../BaseModel';

export interface ITeacher {
  id: number;
  teacher_id: number;
  access_activity_id: number;
  access_scope_id: number;
}

export interface IResponse {
  teachers: ITeacher[];
}

export class Teacher extends BaseModel<ITeacher, IResponse> {
  constructor() {
    super({
      name: 'access_scope_teacher',
      resource: 'teachers',
      namespace: '/access/admin',
      parentResource: 'scopes',
    });
  }
}

export default new Teacher();
