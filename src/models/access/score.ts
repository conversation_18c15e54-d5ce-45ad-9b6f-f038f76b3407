/*
################# index column ########
#  id                              :bigint           not null, primary key
#  access_activity_id(活动id)      :bigint
#  teacher_id(考核人员id)          :bigint
#  user_id(被考核人员id)           :bigint
#  access_entry_id(报名id)         :bigint
#  access_scope_id(考核人员范围id) :bigint
#  score(分数)                     :decimal(8, 2)    default(0.0)
#  meta(扩展字段)                  :json
#  deleted_at(软删除标识)          :datetime
#  created_at                      :datetime         not null
#  updated_at
*/

import BaseModel from '../BaseModel';
import request from '@/utils/request';

export interface IScore {
  id: number;
  teacher_id: number;
  access_activity_id: number;
  user_id: number;
  access_entry_id: number;
  access_scope_id: number;
  readonly meta?: IObject; // 前端不要修改
  score: number; // 互评页面，总评分，一定要更新
  score_meta: { [id: number]: number }; // 用来存各项分数 { questionId: score }，领导可以不用更新这个，其他中层领导必须更新
  parentId?: number;
}

export interface IResponse {
  scores: IScore[];
}

export class Score extends BaseModel<IScore, IResponse> {
  constructor() {
    super({
      name: 'score',
      resource: 'scores',
      namespace: '/access/admin',
      parentResource: 'entries',
    });
  }
  update_score(score: IScore) {
    return this.request.patch(`/access/teacher/score_entries/${score.parentId}/score`, {
      access_score: score,
    });
  }
}

export default new Score();
