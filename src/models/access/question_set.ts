/*
################# index column ########
#
#  id                         :bigint           not null, primary key
#  access_activity_id(活动id) :bigint
#  title(标题)                :string(255)      default("")
#  body(内容)                 :text(65535)
#  state(状态)                :string(255)
#  type(STI)                  :string(255)
#  start_at(开始时间)         :datetime
#  end_at(结束时间)           :datetime
#  meta(扩展字段)             :json
#  deleted_at(软删除标识)     :datetime
#  created_at                 :datetime         not null
#  updated_at                 :datetime         not null
#
*/

import BaseModel from '../BaseModel';

export interface IQuestionSet {
  id: number;
  access_activity_id: number;
  title: string;
  body?: string;
  state: string;
  type: string;
  start_at: string;
  end_at: string;
  meta?: IObject & { max_question_count?: number; max_letter_count?: number };
}

export interface IResponse {
  question_sets: IQuestionSet[];
}

export class QuestionSet extends BaseModel<IQuestionSet, IResponse> {
  constructor() {
    super({
      name: 'access_question_set',
      resource: 'question_sets',
      namespace: '/access/admin',
      parentResource: 'activities',
    });
  }
}

export default new QuestionSet();
