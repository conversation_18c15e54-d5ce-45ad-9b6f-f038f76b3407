/*
################# index column ########
#
#  id                         :bigint           not null, primary key
#  access_activity_id(活动id) :bigint
#  weight(占比重)             :integer
#  name(名称)                 :string(255)
#  deleted_at(软删除标识)     :datetime
#  created_at                 :datetime         not null
#  updated_at                 :datetime         not null
#
*/

import BaseModel from '../BaseModel';

export interface IScope {
  id: number;
  access_activity_id: number;
  weight: number;
  name?: string;
}

export interface IResponse {
  scopes: IScope[];
}

export class Scope extends BaseModel<IScope, IResponse> {
  constructor() {
    super({
      name: 'access_scope',
      resource: 'scopes',
      namespace: '/access/admin',
      parentResource: 'activities',
    });
  }
}

export default new Scope();
