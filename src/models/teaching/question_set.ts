import BaseModel from '../BaseModel';

export class QuestionSet extends BaseModel<IQuestionSet, IResponse> {
  constructor() {
    super({
      name: 'question_set',
      resource: 'question_sets',
      namespace: '/teaching/admin',
    });
  }
}

export default new QuestionSet();

export interface IQuestionSet {
  id?: number;
  created_at?: string;
  updated_at?: string;
  type?: 'Teaching::EvaluateQuestionSet'; // STI type 类型
  name?: string; // 题库模板的名称
  category?: string; // 题库的分类
  meta?: object;
  school_id?: number;
}

interface IResponse {
  question_sets: IQuestionSet[];
}
