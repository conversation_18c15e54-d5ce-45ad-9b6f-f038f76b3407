import BaseModel from '../BaseModel';
import { IEvaluateScore } from './evaluate_score';

export class Question extends BaseModel<IQuestion, IResponse> {
  constructor() {
    super({
      name: 'question',
      resource: 'questions',
      namespace: '/teaching/admin',
      parentResource: 'question_sets',
    });
  }

  deleteEvaluateScore(id: number) {
    return this.request.post(`${this.resourcePath}/${id}/delete_evaluate_score`);
  }
}

export default new Question();

export interface IQuestion {
  id?: number;
  created_at?: string;
  updated_at?: string;
  type?: QuestionTypes; // 题目类型
  title?: string; // 题目标题
  choices: IQuestionChoices; // 题目的选项
  question_set_id?: number;
  position: number; // 题目顺序
  teaching_evaluate_scores: IEvaluateScore[];
}

export enum QuestionTypes {
  single = 'Question::SingleChoice',
  multiple = 'Question::MultipleChoice',
  fill = 'Question::FillBlank',
  essay = 'Question::Essay',
}

export interface IQuestionChoices {
  answer?: string; // 填空题
  options?: Array<{ key: string; value: string }>; // 单选题 | 多选题
}

interface IResponse {
  questions: IQuestion[];
}
