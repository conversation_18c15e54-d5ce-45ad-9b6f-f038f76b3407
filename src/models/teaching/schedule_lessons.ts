import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';

export interface IScheduleLessons {
  id?: number;
  schedule_course_id?: number;
  week?: number;
  date?: Date;
  start_unit?: number;
  end_unit?: number;
  start_section?: number;
  end_section?: number;
  unit_count?: number;
  created_at?: Date;
  updated_at?: Date;
}

export class ScheduleLessons extends ActiveModel<IScheduleLessons> {
  constructor(config?: IModelConfig) {
    super({
      name: 'schedule_lesson',
      namespace: '/teaching/teacher/current_semester',
      ...config,
    });
  }
}
