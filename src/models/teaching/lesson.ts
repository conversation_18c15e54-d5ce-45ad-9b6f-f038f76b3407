import utils from '@/utils';
import moment from 'moment';
import BaseModel from '../BaseModel';
import { IScrollTableRecord } from '../../components/teaching/scrollTablle.interface';
import { IStudent } from '../student';
import { IHomework } from './homework';
import { IAnswer } from './answer';
import { EvaluationState } from './evaluation';
import { RegisterState } from './register';

export class Lesson extends BaseModel<ILesson, IResponse> {
  constructor() {
    super({
      name: 'lesson',
      resource: 'lessons',
      namespace: '/teaching',
      parentResource: 'courses',
      role: 'teacher/current_semester',
    });
  }

  static canEdit(lesson: ILesson) {
    return lesson.teach_permit === 'teaching';
  }

  lessonDownload(lessonId: number) {
    return this.request.post(`/teaching/teacher/inspect/lessons/${lessonId}/download`);
  }

  // 巡课统计信息
  getLessonStatisticByRole(config: { params: object; role: 'admin/current_semester' | 'teacher/current_inspect' }) {
    return this.request.get<IGroupStateInfos>(`teaching/${config.role}/lessons/ana_info`, {
      params: config.params,
    });
  }

  // 生成学期课表数据
  // [{ key: '', head: {}, items: [] }, ...]
  static getScheduleRecords(originalLessons: ILesson[]) {
    type EntryItem = [string, ILesson];
    return Object.entries(utils.groupBy(originalLessons, 'date') as any[])
      .sort((a: EntryItem, b: EntryItem) => new Date(a[0]).valueOf() - new Date(b[0]).valueOf())
      .map((record: [string, ILesson[]]) => {
        const [date, lessons] = record;
        const dateObj = moment(date);
        const sortedLessons = lessons
          .filter(o => o.id)
          .sort((a: ILesson, b: ILesson) => Number(a.start_unit!) - Number(b.start_unit!));
        return {
          key: date,
          head: {
            moment: dateObj,
            date: dateObj.format('MM/DD'),
            week: utils.weekDay(dateObj.isoWeekday()),
            lessonCount: sortedLessons.reduce((sum: number, a: ILesson) => sum + (a.unit_count || 1), 0),
          },
          items: sortedLessons,
        };
      }) as IScrollTableRecord[];
  }
}

export default new Lesson();

// ============================= types =============================
interface IEvaluationAnswerStatistic {
  answer_count: number;
  question_count: number;
  question_infos: IQuestionStatInfo[];
}
interface IQuestionStatInfo {
  answer_count: number;
  title: string;
  type: string;
  stat_info: Array<{ key: string; value: string; count: number }>;
}
interface IRegisterStat {
  done: number;
  late: number;
  undo: number;
}
interface IEvaluationStat {
  done: number;
  doing: number;
  todo: number;
  count: number;
}
interface IReportStat {
  total: number;
  pending: number;
  scored: number;
  published: number;
}
interface ITopicStat {
  total: number;
  pending: number;
  published: number;
}

export interface ILesson {
  id?: number;
  created_at?: string;
  updated_at?: string;
  start_time?: string;
  end_time?: string;
  start_unit?: number;
  end_unit?: number;
  week?: number;
  weekday?: number;
  course_id?: number;
  course_activity_id?: number;
  semester_id?: number;
  teacher_id?: number;
  classroom_id?: any;
  date?: string;
  course_name?: string;
  course_set_name?: string;
  course_set_exam_mode?: string;
  teacher_name?: string;
  classroom_name?: any;
  unit_count?: number;
  course_std_count?: number;
  start_section?: number;
  end_section?: number;
  std_count?: number;
  sub_school?: number;
  teach_permit?: string; // 权限角色
  // student
  student_evaluation_state?: EvaluationState;
  register_state?: RegisterState;
  homework?: IHomework;
  answer?: IAnswer;
  // teacher
  evaluation_count?: number;
  register_count?: IRegisterStat;
  evaluation_answer_statistic?: IEvaluationAnswerStatistic;
  // admin/current_semester
  register_stat?: IRegisterStat;
  evaluation_stat?: IEvaluationStat;
  report_stat?: IReportStat;
  topic_stat?: ITopicStat;
  // 业务属性
  datetime?: string; // 业务属性
  students?: IStudent[];
}

export interface IGroupStateInfos {
  register_count: IRegisterStat;
  evaluation_count: IEvaluationStat;
  report_count: IReportStat;
  students_count: number;
  course_set_count: number;
  register_students_count: number;
  teachers_count: number;
  lessons_count: number;
}

interface IResponse {
  lessons: ILesson[];
}
