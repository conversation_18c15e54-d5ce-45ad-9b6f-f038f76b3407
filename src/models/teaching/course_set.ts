import BaseModel from '@/models/BaseModel';
import { IInstance } from '../bpm/instance';

export interface ICourseSet {
  id?: number;
  created_at?: string;
  updated_at?: string;
  code?: string;
  credits?: number;
  enabled?: boolean;
  establish_on?: string;
  name?: string;
  project?: string;
  education?: string;
  category?: string;
  course_type?: string;
  school_id?: number;
  department_id?: number;
  exam_mode?: string;
  period?: number;
  meta?: string;
}

interface IResponse {
  course_sets: ICourseSet[];
}

export class CourseSet extends BaseModel<ICourseSet, IResponse> {
  constructor(role: 'admin' | 'teacher/current_semester' | 'user') {
    super({
      name: 'course_set',
      resource: 'course_sets',
      namespace: '/teaching',
      role,
    });
  }

  //- 获取课程关联的课程标准 instance
  courseSetInstance(id: number) {
    return this.request.get<IInstance>(`/teaching/user/course_sets/${id}/course_set_instance`);
  }
}
