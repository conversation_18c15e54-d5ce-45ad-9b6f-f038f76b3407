import BaseModel from '../BaseModel';
import { ICourseSet } from './course_set';
import { ICourse } from './course';

export interface IPlanCourseSet {
  id?: number;
  created_at?: string;
  updated_at?: string;
  compulsory?: boolean;
  remark?: string;
  terms?: string;
  course_set_id?: number;
  department_id?: any;
  plan_course_set_group_id?: number;
  exam_mode?: string;
  course_set_name?: string;
  course_set_code?: string;
  course_set?: ICourseSet;
  courses?: ICourse[];
}

interface IResponse {
  plan_course_sets: IPlanCourseSet[];
}

export class PlanCourseSet extends BaseModel<IPlanCourseSet, IResponse> {
  constructor() {
    super({
      name: 'plan_course_set',
      resource: 'plan_course_sets',
      namespace: '/teaching',
      parentResource: 'plan_course_set_groups',
      role: 'admin',
    });
  }
}
