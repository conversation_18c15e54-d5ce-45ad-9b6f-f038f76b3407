import BaseModel from '../BaseModel';
import { IPlanCourseSet } from './plan_course_set';

export interface IPlanCourseSetGroup {
  id: number;
  created_at: string;
  updated_at: string;
  credits: number;
  relation: string;
  remark: string;
  term_credits: string;
  term_week_hours: string;
  course_type: string;
  position: number;
  plan_id: number;
  plan_course_sets: IPlanCourseSet[];
}

interface IResponse {
  plan_course_set_groups: IPlanCourseSetGroup[];
}

export class PlanCourseSetGroup extends BaseModel<IPlanCourseSetGroup, IResponse> {
  constructor() {
    super({
      name: 'plan_course_set_group',
      resource: 'plan_course_set_groups',
      namespace: '/teaching',
      parentResource: 'programs',
      role: 'admin',
    });
  }
}
