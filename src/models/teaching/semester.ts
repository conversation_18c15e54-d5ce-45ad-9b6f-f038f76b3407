import BaseModel from '../BaseModel';
import utils from '@/utils';
import { IModelConfig } from '../BaseModel';
import ActiveModel from '../../lib/ActiveModel';

type TSchedule = { [key: string]: number };
export interface ISemester {
  id?: number;
  created_at?: string;
  updated_at?: string;
  school_id?: number;
  begin_on?: string;
  end_on?: string;
  code?: string;
  name?: string;
  school_year?: string;
  weeks_count?: number;
  current_week?: number;
  schedule?: TSchedule;
  _scheduleColumns?: IScheduleColumn[];
}

interface IResponse {
  semesters: ISemester[];
}

export class Semester extends BaseModel<ISemester, IResponse> {
  constructor() {
    super({
      name: 'semester',
      resource: 'semesters',
      namespace: '/teaching/teacher',
    });
  }

  setRole(role: string = 'teacher') {
    this.setConfig({ namespace: `/teaching/${role}` });
    return this;
  }

  async current() {
    const { data } = await this.request.get<ISemester>(`${this.namespace}/${this.resource}/current`);
    return { data: { ...data, _scheduleColumns: Semester.getScheduleColumns(data.schedule) } };
  }

  static getScheduleColumns(schedule: TSchedule = {}) {
    const count = Math.round(Object.keys(schedule || {}).length / 2);
    return Array.from({ length: count }).map((_, i) => {
      const index = i + 1;
      let startTime = String(schedule[`start_time_${index}`] || '----');
      let endTime = String(schedule[`end_time_${index}`] || '----');
      startTime =
        startTime.length === 3 ? utils.stringSplice(startTime, 1, 0, ':') : utils.stringSplice(startTime, 2, 0, ':');
      endTime = endTime.length === 3 ? utils.stringSplice(endTime, 1, 0, ':') : utils.stringSplice(endTime, 2, 0, ':');
      return {
        index,
        title: `第 ${index} 节`,
        range: `${startTime}~${endTime}`,
      };
    });
  }
}

export default new Semester();

// 课程表 - 时间列
export interface IScheduleColumn {
  index: number;
  title: string;
  range: string;
}
// 课程表 - 默认列数据
export const defaultColumns: IScheduleColumn[] = [
  {
    index: 1,
    title: '第一节',
    range: '8:30-9:10',
  },
  {
    index: 2,
    title: '第二节',
    range: '9:10-9:50',
  },
  {
    index: 3,
    title: '第三节',
    range: '10:05-10:45',
  },
  {
    index: 4,
    title: '第四节',
    range: '10:45-11:25',
  },
  {
    index: 5,
    title: '第五节',
    range: '13:00-13:40',
  },
  {
    index: 6,
    title: '第六节',
    range: '13:40-14:20',
  },
  {
    index: 7,
    title: '第七节',
    range: '14:35-15:15',
  },
  {
    index: 8,
    title: '第八节',
    range: '15:15-15:55',
  },
  {
    index: 9,
    title: '第九节',
    range: '19:00-19:40',
  },
  {
    index: 10,
    title: '第十节',
    range: '19:40-20:20',
  },
  {
    index: 11,
    title: '第十一节',
    range: '20:40-21:20',
  },
  {
    index: 12,
    title: '第十二节',
    range: '21:20-22:00',
  },
];

export class TeachingTeacherCourse extends ActiveModel<ISemester> {
  constructor(config?: IModelConfig) {
    super({
      namespace: '/teaching/teacher',
      name: 'semesters',
      ...config,
    });
  }
}
