import BaseModel from '../BaseModel';
import { ITeacher } from '../teacher';
import { IProgram } from './program';
import { IStudent } from '../student';

import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';

export interface ICourse {
  id?: number;
  created_at?: string;
  updated_at?: string;
  course_set_id?: number;
  start_week?: number;
  end_week?: number;
  period?: number;
  status?: string;
  no?: string;
  grade?: string;
  name?: string;
  std_count?: number;
  semester_id?: number;
  department_id?: number;
  department_name?: string;
  meta?: IObject;
  teachers?: ITeacher[];
  course_activity_count?: number;
  programs?: IProgram[];
  students?: IStudent[];
  homework?: any;
  answer?: any;
  course_catalog_id?: number; // 关联的资源目录
  inspect_teachers?: ITeacher[]; // 巡课教师
  inspect_teacher_ids?: number[];
  teach_depart_name?: string; // 授课部门
  teach_depart_code?: string; // 授课部门编号
  teach_permit?: string; // 权限角色
}

interface IResponse {
  current_page: number;
  total_pages: number;
  total_count: number;
  courses: ICourse[];
}

type RoleType = 'admin' | 'admin/current_semester' | 'teacher/share' | 'student' | 'teacher/current_semester';

export class Course extends BaseModel<ICourse, IResponse> {
  constructor(role: RoleType) {
    super({
      name: 'course',
      resource: 'courses',
      namespace: '/teaching',
      parentResource: 'programs',
      role,
    });
  }

  static canEdit(course: ICourse) {
    return course.teach_permit === 'teaching';
  }

  // ================== teacher current_semester =================
  // 当前学期课程列表
  teacherCurrentSemesterCourses(params: IObject) {
    return this.request.get<IResponse>('/teaching/teacher/current_semester/courses', {
      params,
    });
  }

  // ================== admin =================
  // 当前学期课程列表
  adminCurrentSemesterCourses(params: IObject) {
    return this.request.get<IResponse>('/teaching/admin/current_semester/courses', {
      params,
    });
  }
  // 开课设置 - 分配巡课教师
  adminAssignInspectTeachers(courseIds: number[], teacherIds: number[]) {
    return this.request.post(`/teaching/admin/current_semester/courses/batch_update`, {
      courses: {
        teacher_ids: teacherIds,
        course_ids: courseIds,
      },
    });
  }
}

export class TeachingTeacherCurrentSemesterCourses extends ActiveModel<ICourse> {
  constructor(config?: IModelConfig) {
    super({
      name: 'courses',
      namespace: 'teaching/teacher/current_semester',
      ...config,
    });
  }
}

export class TeachingAdminCurrentSemesterCourses extends ActiveModel<ICourse> {
  constructor(config?: IModelConfig) {
    super({
      namespace: '/teaching/admin/current_semester',
      name: 'course',
      actions: [
        { name: 'batch_update', method: 'post', on: 'collection' },
        { name: 'upload_excel', method: 'post', on: 'collection' },
        { name: 'excel', method: 'post', on: 'collection' },
        { name: 'import_headers', method: 'post', on: 'collection' },
        { name: 'export_headers', method: 'post', on: 'collection' },
        { name: 'import', method: 'post', on: 'collection' },
      ],
      ...config,
    });
  }
}

export class TeachingTeacherCurrentInspectCourses extends ActiveModel<ICourse> {
  constructor(config?: IModelConfig) {
    super({
      name: 'courses',
      namespace: 'teaching/teacher/current_inspect',
      ...config,
    });
  }
}
