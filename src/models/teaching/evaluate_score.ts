import BaseModel from '../BaseModel';

export interface IEvaluateScore {
  id?: number;
  created_at?: string;
  updated_at?: string;
  question_id?: number;
  score?: number;
  evaluate_item_id?: number;
  value?: string; // 题目的答案
  title?: string;
  content?: string;
}

interface IResponse {
  teaching_evaluate_scores: IEvaluateScore[];
}

export class EvaluateScore extends BaseModel<IEvaluateScore, IResponse> {
  constructor() {
    super({
      name: 'teaching_evaluate_score',
      resource: 'evaluate_scores',
      namespace: '/teaching/admin',
      parentResource: 'questions',
    });
  }
}

export default new EvaluateScore();
