import BaseModel from '../BaseModel';

export interface IEvaluateItem {
  id?: number;
  created_at?: string;
  updated_at?: string;
  title?: string;
  content?: string;
  school_id?: number;
  position?: number;
}

interface IResponse {
  teaching_evaluate_items: IEvaluateItem[];
}

export class EvaluateItem extends BaseModel<IEvaluateItem, IResponse> {
  constructor() {
    super({
      name: 'teaching_evaluate_item',
      resource: 'evaluate_items',
      namespace: '/teaching/admin',
    });
  }
}

export default new EvaluateItem();
