/**
 * 课程资源目录
 */

import BaseModel from '@/models/BaseModel';
import { ITeacher } from '../hr/teacher';
import { ILessonPlansResponse, ILessonPlan } from './lesson_plan';

export interface ICourseCatalog {
  id?: number;
  created_at?: string;
  updated_at?: string;
  teacher_id?: any;
  course_set_id?: number;
  title?: string;
  view_permit?: string;
  edit_permit?: string;
  teacher_name?: any;
  can_view?: boolean;
  edit_teachers?: ITeacher[];
  view_teachers?: ITeacher[];
  origin_course_catalog_id?: string | number | null;
}

interface IResponse {
  course_catalogs: ICourseCatalog[];
}

export class CourseCatalog extends BaseModel<ICourseCatalog, IResponse> {
  constructor(role: 'admin' | 'teacher/current_semester') {
    super({
      name: 'course_catalog',
      resource: 'course_catalogs',
      namespace: '/teaching',
      parentResource: 'course_sets',
      role,
    });
  }

  fetchLessonPlans(id: number, params: object = {}) {
    return this.request.get<ILessonPlansResponse>(`${this.resourcePath}/${id}/lesson_plans`, {
      params: {
        page: 1,
        per_page: 200,
      },
    });
  }

  createLessonPlan(id: number, formData: ILessonPlan) {
    return this.request.post<ILessonPlan>(`${this.resourcePath}/${id}/lesson_plans`, formData);
  }
}
