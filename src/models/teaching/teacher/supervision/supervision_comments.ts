import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';
import { TeachingSupervisionComment } from '@/types/model';

export class TeachingTeacherSupervisionSupervisionComments extends ActiveModel<TeachingSupervisionComment> {
  constructor(config?: IModelConfig) {
    super({
      namespace: '/teaching/teacher/supervision',
      name: 'supervision_comment',
      dataIndexKey: 'records',
      ...config,
    });
  }
}
