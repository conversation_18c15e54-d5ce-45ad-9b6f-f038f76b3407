import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';
import { ITeachingLesson } from '@/types/model';

export class TeachingTeacherSupervisionLessons extends ActiveModel<ITeachingLesson> {
  constructor(config?: IModelConfig) {
    super({
      namespace: '/teaching/teacher/supervision',
      name: 'lesson',
      ...config,
      actions: [{ name: 'ana_info', method: 'get', on: 'collection' }],
    });
  }
}
