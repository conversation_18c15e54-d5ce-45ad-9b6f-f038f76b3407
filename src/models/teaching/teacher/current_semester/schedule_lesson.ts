import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';
import { ITeachingScheduleLesson } from '@/types/model';

export class TeachingTeachsrCurrentSemesterScheduleLesson extends ActiveModel<ITeachingScheduleLesson> {
  constructor(config?: IModelConfig) {
    super({
      namespace: '/teaching/teacher/current_semester',
      name: 'schedule_lesson',
      actions: [{ name: 'import', method: 'post', on: 'collection' }],
      ...config,
    });
  }
}
