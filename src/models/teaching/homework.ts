import request from '@/utils/fileRequest';
import BaseModel from '../BaseModel';
import Target from '../st/target';

export interface IHomework {
  id?: number;
  school_id?: number;
  user_type?: string;
  user_id?: number;
  source_type?: string;
  source_id?: string;
  title?: string;
  body?: string;
  state?: string;
  flag?: number;
  type?: string;
  attachments?: object;
  meta?: any;
  score?: number;
  windex?: number;
  comment_count?: number;
  user?: object;
  files?: object[];
  homework?: any[];
  noCompare?: boolean;
  info: ICountInfo;
  teach_permit?: string;
}

interface ICountInfo {
  total: number;
  pending: number;
  published: number;
  scored: number;
}

export interface IResponse {
  current_page: number;
  total_pages: number;
  total_count: number;
  reports: IHomework[];
}

export class TeacherHomework extends BaseModel<IHomework, IResponse> {
  constructor(role: 'lessons/:lessonId' | 'courses/:courseId') {
    super({
      name: 'report',
      indexKey: 'reports',
      resource: 'homeworks',
      namespace: role === 'lessons/:lessonId' ? '/teaching/teacher/inspect' : '/teaching/teacher/share',
      role,
    });
  }

  fetchCourseHomeworks(args: { courseId: number; params: object }) {
    return Target.fetchTargets<{ homeworks: IHomework[]; info: ICountInfo }>({
      role: 'teacher',
      namespace: 'teaching',
      resource: 'course',
      resourceId: args.courseId,
      target: 'homeworks',
      params: args.params,
    });
  }

  // 导出作业
  homeworkExport(courseId: number) {
    return this.request.post(`/ex/teacher/course/${courseId}/homeworks`);
  }
}

export class StudentHomework extends BaseModel<IHomework, IResponse> {
  constructor() {
    super({
      name: 'report',
      indexKey: 'reports',
      resource: 'homeworks',
      namespace: '/teaching',
      parentResource: 'lessons',
      role: 'student',
    });
  }

  fetchCourseHomeworks(args: { courseId: number; params: object }) {
    return Target.fetchTargets<{ homeworks: IHomework[] }>({
      role: 'student',
      namespace: 'teaching',
      resource: 'course',
      resourceId: args.courseId,
      target: 'homeworks',
      params: args.params,
    });
  }
}
