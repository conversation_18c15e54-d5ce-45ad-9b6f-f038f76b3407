import BaseModel from '@/models/BaseModel';

export interface IPlanCourseGroupSet {
  id?: number;
  title?: string;
}

interface IResponse {
  plan_course_group_sets: IPlanCourseGroupSet[];
}

export class PlanCourseGroupSet extends BaseModel<IPlanCourseGroupSet, IResponse> {
  constructor(role: 'admin' | 'teacher') {
    super({
      name: 'plan_course_group_set',
      resource: 'plan_course_group_sets',
      namespace: '/teaching',
      role,
    });
  }
}
