import BaseModel from '@/models/BaseModel';
import { IPlanCourseSet } from './plan_course_set';
import { ISemester } from './semester';

export interface ITermPlan {
  id?: number;
  term?: number;
  program_id?: number;
  program_name?: string;
  adminclass_name?: string;
  activate?: boolean;
  adminclass_count?: number;
  course_sets_count?: number;
  credits?: number;
  semester?: ISemester;
  plan_course_sets?: IPlanCourseSet[];
}

interface IResponse {
  term_plans: ITermPlan[];
}

export class TermPlan extends BaseModel<ITermPlan, IResponse> {
  constructor(role: 'teacher' | 'admin' | 'student') {
    super({
      name: 'term_plan',
      resource: 'term_plans',
      namespace: '/teaching',
      parentResource: 'programs',
      role,
    });
  }

  fetchStudentTermPlans(params: IObject = {}) {
    return this.request.get<IResponse>(`/teaching/student/program/term_plans`);
  }

  findStudentTermPlan(id: number) {
    return this.request.get<ITermPlan>(`/teaching/student/program/term_plans/${id}`);
  }
}
