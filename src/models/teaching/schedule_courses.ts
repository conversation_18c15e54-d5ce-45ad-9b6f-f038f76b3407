import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';
import { IScheduleLessons } from './schedule_lessons';
import { IInstance } from '@/models/bpm/instance';

// 教育计划

export interface IScheduleCourses {
  id?: number;
  course_id?: number;
  state?: string;
  teacher_id?: number;
  created_at?: Date;
  updated_at?: Date;
  schedule_lessons?: IScheduleLessons[];
  course_name?: string;
  instance?: IInstance;
}

export class ScheduleCourses extends ActiveModel<IScheduleCourses> {
  constructor(config?: IModelConfig) {
    super({
      name: 'schedule_course',
      namespace: '/teaching/teacher/current_semester',
      mode: 'single',
      ...config,
      actions: [{ name: 'approval', method: 'post', on: 'collection' }],
    });
  }
}

// namespace: share/courses/schedule_course

export class ShareScheduleCourses extends ActiveModel<IScheduleCourses> {
  constructor(config: IModelConfig) {
    super({
      name: 'schedule_course',
      namespace: '/teaching/teacher/share',
      mode: 'single',
      ...config,
    });
  }
}
