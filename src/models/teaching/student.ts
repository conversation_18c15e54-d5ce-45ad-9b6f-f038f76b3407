import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';

export interface IStudent {
  id?: number;
  name?: string;
  code?: string;
  identity_id?: number;
  sex?: string;
  adminclass_name?: string;
  major_name?: string;
  college_name?: string;
  college_id?: number;
  major_id?: number;
  adminclass_id?: string;
  avatar?: any;
  phone?: number;
  nation?: any;
  grade?: string;
}
export class Student extends ActiveModel<IStudent> {
  constructor(config?: IModelConfig) {
    super({
      name: 'student',
      namespace: 'teaching/teacher/share',
      ...config,
    });
  }
}
