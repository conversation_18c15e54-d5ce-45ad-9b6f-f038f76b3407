import BaseModel from '@/models/BaseModel';
import { IAnswerSet } from '../comm/answer_set';

export type EvaluationState = 'done' | 'doing' | 'todo';

export interface IEvaluation {
  id?: number;
  created_at?: string;
  updated_at?: string;
  student_id?: number;
  teacher_id?: number;
  lesson_id?: number;
  course_id?: number;
  course_set_id?: number;
  grade?: any;
  major_id?: number;
  adminclass_id?: number;
  department_id?: any;
  date?: string;
  school_id?: number;
  semester_id?: number;
  score?: number;
  score_meta?: {
    [key: string]: number;
  };
  state?: EvaluationState;
  question_set_id?: number;
  answer_set?: IAnswerSet;
}

interface IResponse {
  current_page: number;
  total_pages: number;
  total_count: number;
  teaching_evaluations: IEvaluation[];
}

export class Evaluation extends BaseModel<IEvaluation> {
  constructor(role: 'student' | 'teacher') {
    super({
      name: 'evaluation',
      resource: 'evaluation',
      namespace: '/teaching',
      role,
    });
  }

  // 教师评价管理
  teacherLessonEvaluations(lessonId: number, params: IObject) {
    return this.request.get<IResponse>(`/teaching/teacher/inspect/lessons/${lessonId}/evaluations`, {
      params,
    });
  }

  // 学生评价管理
  createEvaluation(lessonId: number) {
    return this.request.post<IEvaluation>(`/teaching/student/lessons/${lessonId}/evaluation`);
  }
  submitEvaluation(lessonId: number, answerAttributes: Array<{ id: number; value: string }>) {
    return this.request.patch(`/teaching/student/lessons/${lessonId}/evaluation`, {
      teaching_evaluation: {
        times: 2,
        state: 'done',
        answers_attributes: answerAttributes,
      },
    });
  }
  deleteEvaluation(lessonId: number) {
    return this.request.delete(`/teaching/student/lessons/${lessonId}/evaluation`);
  }
  findEvaluation(lessonId: number) {
    return this.request.get<IEvaluation>(`/teaching/student/lessons/${lessonId}/evaluation`);
  }

  static get stateMap() {
    return {
      done: { value: 'done', label: '已评价', type: 'success' },
      doing: { value: 'doing', label: '待提交', type: 'warning' },
      todo: { value: 'todo', label: '待评价', type: 'primary' },
    } as IObject;
  }
}
