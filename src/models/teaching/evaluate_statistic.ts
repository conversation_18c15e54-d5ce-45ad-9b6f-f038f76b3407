import BaseModel from '@/models/BaseModel';

export interface IEvaluateStatistic {
  id: number;
  created_at: string;
  updated_at: string;
  title: string;
  creator_type: string;
  creator_id: number;
  school_id: number;
  query_meta: {
    q: IObject;
  };
  creator_name: string;
  score_statistic: IScoreStatistic;
}

export interface IScoreStatistic {
  [key: string]: number;
  score_sum: number;
  score_count: number;
}

interface IResponse {
  EvaluateStatistics: IEvaluateStatistic[];
}

export class EvaluateStatistic extends BaseModel<IEvaluateStatistic, IResponse> {
  constructor() {
    super({
      name: 'evaluate_statistic',
      resource: 'evaluate_statistics',
      namespace: '/teaching',
      role: 'admin',
    });
  }
}

export default new EvaluateStatistic();
