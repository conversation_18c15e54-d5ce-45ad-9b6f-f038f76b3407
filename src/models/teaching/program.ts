/**
 * 人才培养方案（program）
 */
import BaseModel from '../BaseModel';
import { IPlan } from './plan';

export interface IProgram {
  id?: number;
  created_at?: string;
  updated_at?: string;
  school_id?: number;
  name?: string;
  grade?: string;
  department_id?: number;
  major_id?: number;
  duration?: number;
  study_type?: string;
  degree?: string;
  effective_on?: string;
  invalid_on?: string;
  remark?: string;
  meta?: IObject;
  major_name?: string;
  major_code?: string;
  major_direction?: string;
  department_name?: string;
  department_code?: string;
  adminclasses_count?: number;
  students_count?: number;
  current_term?: number;
  plan?: IPlan;
}

interface IResponse {
  programs: IProgram[];
}

export class Program extends BaseModel<IProgram, IResponse> {
  constructor(role: 'teacher' | 'admin' | 'student') {
    super({
      name: 'program',
      resource: 'programs',
      namespace: 'teaching',
      role,
    });
  }

  // 培养计划
  plan(id: number) {
    return this.request.get<IPlan>(`${this.resourcePath}/${id}/plan`);
  }

  studentPlan() {
    return this.request.get<IPlan>('/teaching/student/program/plan');
  }
}
