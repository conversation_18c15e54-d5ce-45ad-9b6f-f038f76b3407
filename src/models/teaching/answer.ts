import BaseModel from '../BaseModel';

export interface IAnswer {
  id?: number;
  school_id?: number;
  user_type?: string;
  user_id?: number;
  source_type?: string;
  source_id?: string;
  title?: string;
  body?: string;
  state?: string;
  flag?: number;
  type?: string;
  attachments?: any;
  meta?: any;
  score?: number | null;
  windex?: number;
  comment_count?: number;
  user?: any;
  files?: any[];
  homework?: any[];
  wait_score?: any;
  teach_permit?: string;
  noCompare?: boolean;
}

export interface IResponse {
  teach_permit: string;
  info: {
    total: number;
    pending: number;
    published: number;
    scored: number;
  };
  reports: IAnswer[];
}

export class Answer extends BaseModel<IAnswer, IResponse> {
  constructor(params: { lessonId: number }) {
    super({
      name: 'report',
      resource: 'answers',
      namespace: '/teaching/teacher/inspect',
      parentResource: 'homeworks',
      role: `lessons/${params.lessonId}`,
    });
  }
  // 导出课程作业包
  downloadHomeworks(lessonId: number) {
    return this.request.post(`/teaching/teacher/inspect/lessons/${lessonId}/download`);
  }
  // 导出作业
  homeworkExport(lessonId: number, homeworkId: number) {
    return this.request.post(`/teaching/teacher/inspect/lessons/${lessonId}/homeworks/${homeworkId}/export`);
  }
}
