import BaseModel from './BaseModel';
import { IFile } from './file';

export interface IComment {
  id?: number;
  title?: string;
  body?: string;
  subject?: string;
  commentable_id?: number;
  commentable_type?: string;
  user_id?: number;
  user_type?: string;
  parent_id?: number;
  reply_user_name?: string;
  reply_user_id?: string;
  reply_user_type?: string;
  lft?: number;
  rgt?: number;
  created_at?: string;
  updated_at?: string;
  attachments: {
    attachments?: IFile[];
    files?: IFile[];
  };
}

interface IResponse {
  comments: IComment[];
}

export class Comment extends BaseModel<IComment, IResponse> {
  constructor() {
    super({
      name: 'comment',
      resource: 'comments',
      namespace: '/comm',
      parentResource: 'instances',
      role: 'user',
    });
  }
}

export default new Comment();
