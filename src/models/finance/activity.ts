/**
 * 资金卡项目
 * has_many projects (资金卡)
 */
import BaseModel from '@/models/BaseModel';

export interface IActivity {
  id?: number;
  year?: number;
  school_id?: number;
  school_name?: string;
  state?: 'todo' | 'doing' | 'done';
  deleted_at?: string;
  created_at?: string;
  updated_at?: string;
  url?: any;
}

interface IResponse {
  activities: IActivity[];
}

export class Activity extends BaseModel<IActivity, IResponse> {
  constructor(role: 'admin' | 'teacher') {
    super({
      name: 'activity',
      namespace: '/finance',
      resource: 'activities',
      role,
    });
  }

  projectsExcel(params: any) {
    return this.request.post<IActivity>(`/ex/teacher/finance_activity/${params.activityId}/projects`, { ...params });
  }

  vouchersExcel(params: any) {
    return this.request.post<IActivity>(`/ex/teacher/finance_activity/${params.activityId}/vouchers`, { ...params });
  }

  budgetsExcel(params: any) {
    return this.request.post<IActivity>(`/ex/teacher/finance_project/${params.parent_id}/budgets`, { ...params });
  }

  instancesExcel(params: any) {
    return this.request.post<IActivity>(`/ex/teacher/finance/voucher_instances`, { ...params });
  }

  vouchersExport(params: any) {
    return this.request.post<IActivity>('/ex/teacher/finance/vouchers', { ...params });
  }
}
