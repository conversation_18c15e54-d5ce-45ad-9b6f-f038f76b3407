import BaseModel from '@/models/BaseModel';
import { IFile } from '../file';

export interface IReceipt {
  id?: number;
  created_at?: string;
  updated_at?: string;
  school_id?: number;
  type?: string;
  name?: string;
  attachment?: IFile;
  meta?: IObject;
  receiptable_type?: string;
  receiptable_id?: number;
  creator_type?: string;
  creator_id?: number;
}

interface IResponse {
  receipts: IReceipt[];
}

export class FinanceReceipt extends BaseModel<IReceipt, IResponse> {
  constructor() {
    super({
      name: 'receipt',
      resource: 'receipts',
      parentResource: 'vouchers',
      namespace: '/finance',
      role: 'teacher',
    });
  }
}

export default new FinanceReceipt();
