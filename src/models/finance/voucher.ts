/**
 * 报销单据
 * belongs_to budget
 * belongs_to project
 * belongs_to activity
 */
import BaseModel from '@/models/BaseModel';
import { IPayment } from './payment';
import { IReceipt } from './receipt';
import { IProject } from './project';
import { IInstance } from '../bpm/instance';
import { IModelConfig } from '../BaseModel';

export class Voucher extends BaseModel<IVoucher, IResponse> {
  constructor(config?: IModelConfig | IObject) {
    super({
      name: 'voucher',
      resource: 'vouchers',
      parentResource: 'activities', // budgets, projects
      namespace: '/finance',
      role: 'teacher', // teacher/own (我的资金卡), teacher/execute（我执行的）
      ...config,
    });
  }

  exportAll(params?: IObject) {
    return this.request.post(`${this.resourcePath}/export`, {
      page: 1,
      per_page: 10000,
      ...params,
    });
  }

  exportByParentResource(parentId: number, params?: IObject) {
    return this.request.post(`${this.parentResourcePath}/${parentId}/${this.resource}/export`, {
      page: 1,
      per_page: 10000,
      ...params,
    });
  }

  static getInitialVoucher(type: VoucherType = VoucherType.Routine) {
    const voucher: IVoucher = {
      amount: '',
      remark: '',
      type,
      payee_meta: {
        name: '',
        phone: '',
        department: '',
        payment_way: '',
        form: {
          templateId: null,
          name: '',
          formData: {},
          template: [],
        },
      },
      payments_attributes: [],
      receipts_attributes: [],
      meta: { name: '' },
    };
    if (type === 'Finance::OutsideVoucher') {
      voucher.meta = {
        route: {
          label: '行程',
          value: [],
          attrkey: [
            { label: '出发', key: 'start_city' },
            { label: '到达', key: 'end_city' },
            { label: '出发时间', key: 'start_at' },
            { label: '到达时间', key: 'end_at' },
            { label: '票价', key: 'amount' },
          ],
        },
        sleeper_subsidy: {
          label: '未乘卧铺补贴',
          value: [],
          attrkey: [
            { label: '人', key: 'number' },
            { label: '天', key: 'days' },
            { label: '金额', key: 'amount' },
          ],
        },
        accommodation: {
          label: '住宿费',
          value: [],
          attrkey: [
            { label: '人', key: 'number' },
            { label: '天', key: 'days' },
            { label: '金额', key: 'amount' },
          ],
        },
        service_subsidy: {
          label: '助勤补贴',
          value: [],
          attrkey: [
            { label: '人', key: 'number' },
            { label: '天', key: 'days' },
            { label: '金额', key: 'amount' },
          ],
        },
        city_traffic: {
          label: '市内交通费',
          value: '',
        },
        summary: {
          label: '摘要',
          value: [],
          attrkey: [
            { label: '内容', key: 'content' },
            { label: '金额', key: 'amount' },
          ],
        },
      };
    }
    return voucher;
  }
}

export namespace FinanceVoucher {
  export const withActivityAdmin = new Voucher({
    parentResource: 'activities',
    role: 'admin',
  });
  export const withProjectAdmin = new Voucher({
    parentResource: 'projects',
    role: 'admin',
  });
  export const withBudgetAdmin = new Voucher({
    parentResource: 'budgets',
    role: 'admin',
  });
  export const withActivityTeacher = new Voucher({
    parentResource: 'activities',
    role: 'teacher',
  });
  export const withProjectTeacherOwn = new Voucher({
    parentResource: 'projects',
    role: 'teacher/own',
  });
  export const withBudgetTeacherOwn = new Voucher({
    parentResource: 'budgets',
    role: 'teacher/own',
  });
  export const withBudgetTeacherExecute = new Voucher({
    parentResource: 'budgets',
    role: 'teacher/execute',
  });
}

export interface IVoucher {
  id?: number;
  school_id?: number;
  activity_id?: number;
  type?: VoucherType;
  project_id?: any;
  teacher_id?: number;
  state?: string;
  amount?: string;
  max?: string;
  final_amount?: number;
  remark?: string;
  payee_meta?: IVoucherPayeeMeta;
  meta?: IVoucherMeta | { name: string };
  payments_attributes?: IPayment[];
  receipts_attributes?: IReceipt[];
  created_at?: string;
  updated_at?: string;
  seq?: string;
  project?: IProject;
  projects?: IProject[];
  payments?: any[];
  receipts?: any[];
  instance?: IInstance;
  instance_id?: number;
}

export enum VoucherType {
  Routine = 'Finance::RoutineVoucher',
  Outside = 'Finance::OutsideVoucher',
  Loan = 'Finance::LoanVoucher',
  Asset = 'Finance::AssetVoucher',
}

interface IResponse {
  vouchers: IVoucher[];
}

export interface IVoucherPayeeMeta {
  name: string;
  phone: string;
  form: {
    templateId: number | null;
    name: string;
    formData: object;
    template: object[];
  };
  department: string;
  payment_way: string;
}

export interface IVoucherMeta {
  route: {
    label: string;
    value: any[];
    attrkey: [
      { label: '出发'; key: 'start_city' },
      { label: '到达'; key: 'end_city' },
      { label: '出发时间'; key: 'start_at' },
      { label: '到达时间'; key: 'end_at' },
      { label: '票价'; key: 'amount' },
    ];
  };
  sleeper_subsidy: {
    label: '未乘卧铺补贴';
    value: any[];
    attrkey: [{ label: '人'; key: 'number' }, { label: '天'; key: 'days' }, { label: '金额'; key: 'amount' }];
  };
  accommodation: {
    label: '住宿费';
    value: any[];
    attrkey: [{ label: '人'; key: 'number' }, { label: '天'; key: 'days' }, { label: '金额'; key: 'amount' }];
  };
  service_subsidy: {
    label: '助勤补贴';
    value: any[];
    attrkey: [{ label: '人'; key: 'number' }, { label: '天'; key: 'days' }, { label: '金额'; key: 'amount' }];
  };
  city_traffic: {
    label: '市内交通费';
    value: string;
  };
  summary: {
    label: '摘要';
    value: any[];
    attrkey: [{ label: '内容'; key: 'content' }, { label: '金额'; key: 'amount' }];
  };
}
