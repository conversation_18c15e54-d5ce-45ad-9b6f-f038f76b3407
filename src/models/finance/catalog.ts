/**
 * 资金卡 - 目录 (二级内容)
 * has_many vouchers（报销）
 **/
import BaseModel from '@/models/BaseModel';

export interface ICatalog {
  id?: number;
  project_id?: number;
  ancestry?: any;
  name?: string;
  depth?: number;
  position?: number;
  created_at?: string;
  updated_at?: string;
}

interface IResponse {
  catalogs: ICatalog[];
}

export class Catalog extends BaseModel<ICatalog, IResponse> {
  constructor() {
    super({
      namespace: '/finance',
      parentResource: 'projects',
      resource: 'catalogs',
      role: 'admin',
      name: 'catalog',
    });
  }
}

export default new Catalog();
