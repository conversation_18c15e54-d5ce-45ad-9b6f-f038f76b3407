import BaseModel from '@/models/BaseModel';

export interface IPayment {
  id?: number;
  created_at?: string;
  updated_at?: string;
  school_id?: number;
  project_id?: number;
  budget_id?: number;
  origin_id?: any;
  subject_id?: number;
  voucher_id?: number;
  amount?: number;
  activity_id?: number;
  project_name?: string;
  project_uid?: string;
  finance_uid?: string;
  origin_name?: any;
  subject_name?: string;
}

interface IResponse {
  payments: IPayment[];
}

export class FinancePayment extends BaseModel<IPayment, IResponse> {
  constructor() {
    super({
      name: 'payment',
      resource: 'payments',
      parentResource: 'vouchers',
      namespace: '/finance',
      role: 'teacher',
    });
  }
}

export default new FinancePayment();
