/**
 * 资金来源
 */
import BaseModel from '@/models/BaseModel';

export interface IOrigin {
  id?: number;
  created_at?: string;
  updated_at?: string;
  name?: string;
  code?: string;
  school_id?: number;
}

interface IResponse {
  origins: IOrigin[];
}

export class Origin extends BaseModel<IOrigin, IResponse> {
  constructor(role: 'admin' | 'teacher' = 'admin') {
    super({
      name: 'origin',
      resource: 'origins',
      namespace: '/finance',
      role,
    });
  }
}

export default new Origin();
