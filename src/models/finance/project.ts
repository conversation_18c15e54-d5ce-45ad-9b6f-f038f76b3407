/**
 * 资金卡
 * belongs_to activity (资金卡项目)
 * has_many project_roles (项目负责人)
 * has_many budgets (资金卡三季，预算)
 * has_many catalogs (资金卡二级，目录)
 */
import BaseModel from '@/models/BaseModel';
import { IInstance } from '../bpm/instance';
import { IProjectRole } from './project_role';

export type FinanceProjectState = 'pending' | 'using' | 'approving' | 'finished';

export interface IProject {
  id?: number;
  uid?: string;
  name?: string;
  start_at?: string;
  end_at?: string;
  state?: FinanceProjectState;
  remark?: string;
  department_code?: string;
  major_code?: string;
  owner_id?: number;
  project_roles_attributes?: IProjectRole[]; // 创建时，可以关联创建子项
  owner_name?: string;
  owner_code?: string;
  activity_id?: number;
  school_id?: number;
  school_name?: string;
  type?: string;
  parent_project_id?: number;
  project_category_id?: number;
  project_category_name?: string;
  amount?: string;
  processing_voucher_amount?: string;
  completed_voucher_amount?: string;
  completed_voucher_count?: number;
  checked_voucher_amount?: string;
  project_roles?: IProjectRole[];
  sub_project_count?: number;
  instance?: IInstance;
  deleted_at?: string;
  created_at?: string;
  updated_at?: string;
}

interface IResponse {
  projects: IProject[];
}

interface AnasResponse {
  current_page: number;
  total_pages: number;
  total_count: number;
  project_anas: IAnasDetail[];
}

export interface IAnasDetail {
  id?: number;
  created_at?: string;
  updated_at?: string;
  day?: string;
  completed_ratio?: number;
  doing_ratio?: number;
  normal_ratio?: number;
  project_id?: number;
}

export class Project extends BaseModel<IProject, IResponse> {
  constructor(role: 'admin' | 'teacher/own' | 'teacher/execute' = 'admin') {
    super({
      name: 'project',
      resource: 'projects',
      parentResource: 'activities',
      namespace: '/finance',
      role: 'admin', // teacher/own, teacher/execute
    });
  }

  static get stateMap(): IObject {
    return {
      pending: { label: '未承诺', type: 'danger', value: 'pending' },
      approving: { label: '审批中', type: 'warning', value: 'approving' },
      using: { label: '使用中', type: 'success', value: 'using' },
      finished: { label: '已结束', type: 'default', value: 'finished' },
    };
  }

  projectAnas(projectId: number, params: IObject) {
    return this.request.get<AnasResponse>(`/finance/${this.role}/projects/${projectId}/project_anas`, {
      params,
    });
  }

  anasDetail(anasId: number) {
    return this.request.get<IAnasDetail>(`/finance/${this.role}/project_anas/${anasId}`);
  }

  // 申请使用资金卡
  createTeacherOwnProjectInstance(projectId: number) {
    return this.request.post<IInstance>(`/finance/teacher/own/projects/${projectId}/approval`);
  }

  // 获取导出选择的字段源
  tableKeys(params: { type: string }) {
    return this.request.post('/ex/admin/imports/titles', { ...params });
  }

  // 上传Excel文件
  importFile(file: any) {
    const formData = new FormData();
    formData.append('file', file);
    return this.request.post('/ex/admin/imports/read', formData);
  }

  // 通过上传Excel返回的uid查询Excel导入的信息， 有效期为1小时
  getExcelInfo(params: { uid: string; page?: number; par_page?: number; q?: any }) {
    return this.request.post('/ex/admin/imports/async', { ...params });
  }

  // 校验导入
  onValid(params: { uid: string; titles: string[]; primary_keys: string[]; source_type: string; targets: string }) {
    return this.request.post('/ex/admin/imports/valid', { ...params });
  }

  // 查询进度
  onNotify(params: { uid: string }) {
    return this.request.post('/ex/admin/imports/notify', { ...params });
  }

  // 提交需要导入的信息
  submitExcelInfo(parentId: string, params: { uid: string; titles: string[]; primary_keys: string[] }) {
    return this.request.post(`/ex/admin/imports/finance_activity/${parentId}/projects`, { ...params });
  }
}

export default new Project();
