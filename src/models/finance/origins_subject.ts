/**
 * 中间表：资金来源（origin）- 资金科目（subject）
 */
import BaseModel from '@/models/BaseModel';

export interface IOriginsSubject {
  id?: number;
  created_at?: string;
  updated_at?: string;
  activity_id?: number;
  origin_id?: number;
  subject_id?: number;
  plan_amount?: number;
  position?: number;
  catalog?: string;
  origin_name?: string;
  subject_name?: string;
}

interface IResponse {
  origins_subjects: IOriginsSubject[];
}

export class OriginsSubject extends BaseModel<IOriginsSubject, IResponse> {
  constructor() {
    super({
      name: 'origins_subject',
      resource: 'origins_subjects',
      parentResource: 'activities',
      namespace: '/finance',
      role: 'admin',
    });
  }

  batchUpdate(activityId: number, originsSubjects: IOriginsSubject[]) {
    return this.request.post(`${this.parentResourcePath}/${activityId}/${this.resource}/batch_update`, {
      origins_subjects: originsSubjects,
    });
  }
}

export default new OriginsSubject();
