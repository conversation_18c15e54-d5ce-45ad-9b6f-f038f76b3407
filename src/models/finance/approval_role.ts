/**
 * 财务审批角色
 * belongs_to project_role
 */
import BaseModel from '@/models/BaseModel';

export interface IApprovalRole {
  id?: number;
  created_at?: string;
  updated_at?: string;
  name?: string;
  remark?: string;
  school_id?: number;
}

interface IResponse {
  approval_roles: IApprovalRole[];
}

export class ApprovalRole extends BaseModel<IApprovalRole, IResponse> {
  constructor() {
    super({
      name: 'approval_role',
      resource: 'approval_roles',
      namespace: '/finance',
      role: 'admin', // teacher
    });
  }
}

export default new ApprovalRole();
