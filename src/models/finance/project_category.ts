import BaseModel from '@/models/BaseModel';

export interface IProjectCategory {
  id?: number;
  created_at?: string;
  updated_at?: string;
  name?: string;
  position?: number;
  school_id?: number;
}

interface IResponse {
  project_categories: IProjectCategory[];
}

export class FinanceProjectCategory extends BaseModel<IProjectCategory, IResponse> {
  constructor() {
    super({
      name: 'project_category',
      resource: 'project_categories',
      namespace: '/finance',
      role: 'admin',
    });
  }
}

export default new FinanceProjectCategory();
