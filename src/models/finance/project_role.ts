/**
 * 项目负责人
 * 中间表
 * belongs_to project
 * has_many approval_roles
 */
import BaseModel from '@/models/BaseModel';

export interface IProjectRole {
  id?: number;
  approval_role_id?: number;
  teacher_id?: number;
  position?: number;
  project_id?: number;
  approval_role_name?: string;
  teacher_name?: string;
  created_at?: string;
  updated_at?: string;
}

interface IResponse {
  project_roles: IProjectRole[];
}

export class ProjectRole extends BaseModel<IProjectRole, IResponse> {
  constructor() {
    super({
      name: 'project_role',
      resource: 'project_roles',
      parentResource: 'projects',
      namespace: '/finance',
      role: 'admin',
    });
  }
}

export default new ProjectRole();
