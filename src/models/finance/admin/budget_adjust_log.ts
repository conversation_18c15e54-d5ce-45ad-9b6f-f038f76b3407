import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';
import { FinanceBudgetLog } from '@/types/model';

export class FinanceAdminBudgetAdjustLog extends ActiveModel<FinanceBudgetLog> {
  constructor(config?: IModelConfig) {
    super({
      namespace: '/finance/admin',
      name: 'budget_adjust_log',
      dataIndexKey: 'records',
      mode: 'shallow',
      actions: [
        { name: 'export', method: 'post', on: 'collection' },
        { name: 'export_headers', method: 'post', on: 'collection' },
      ],
      ...config,
    });
  }
}
