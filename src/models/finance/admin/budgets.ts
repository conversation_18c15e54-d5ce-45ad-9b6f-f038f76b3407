import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';
import { IBudget } from '@/types/model';

export class FinanceAdminBudgets extends ActiveModel<IBudget> {
  constructor(config?: IModelConfig) {
    super({
      namespace: '/finance/admin',
      name: 'activity_budget',
      dataIndexKey: 'budgets',
      actions: [
        { name: 'export', method: 'post', on: 'collection' },
        { name: 'export_headers', method: 'post', on: 'collection' },
      ],
      ...config,
    });
  }
}
