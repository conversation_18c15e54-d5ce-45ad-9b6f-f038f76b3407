import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';
import { FinanceBudgetLog } from '@/types/model';

export class FinanceAdminBudgetLog extends ActiveModel<FinanceBudgetLog> {
  constructor(config?: IModelConfig) {
    super({
      namespace: '/finance/admin',
      name: 'budget_log',
      dataIndexKey: 'records',
      actions: [
        { name: 'export', method: 'post', on: 'collection' },
        { name: 'export_headers', method: 'post', on: 'collection' },
      ],
      ...config,
    });
  }
}
