import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';
import { IProject } from '../project';

export class FinanceAdminProjects extends ActiveModel<IProject> {
  constructor(config?: IModelConfig) {
    super({
      namespace: '/finance/admin',
      name: 'projects',
      actions: [
        { name: 'export', method: 'post', on: 'collection' },
        { name: 'upload_excel', method: 'post', on: 'collection' },
        { name: 'excel', method: 'post', on: 'collection' },
        { name: 'import_headers', method: 'post', on: 'collection' },
        { name: 'export_headers', method: 'post', on: 'collection' },
        { name: 'import', method: 'post', on: 'collection' },
      ],
      ...config,
    });
  }
}
