/**
 * 科目
 */
import BaseModel from '@/models/BaseModel';

export interface ISubject {
  id?: number;
  school_id?: number;
  name?: string;
  code?: string;
  remark?: string;
  catalog?: string;
  created_at?: string;
  updated_at?: string;
}

interface IResponse {
  subjects: ISubject[];
}

export class Subject extends BaseModel<ISubject, IResponse> {
  constructor(role: 'admin' | 'teacher' = 'admin') {
    super({
      name: 'subject',
      resource: 'subjects',
      namespace: '/finance',
      role,
    });
  }
}

export default new Subject();
