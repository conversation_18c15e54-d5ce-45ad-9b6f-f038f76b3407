/**
 * 资金池
 * 单数资源
 */
import BaseModel from '@/models/BaseModel';
import { IOriginsSubject } from './origins_subject';

export interface IPool {
  id?: number;
  col_names: string[];
  catalog_rows: IPoolCatalog[];
}
export interface IPoolCatalog {
  catalog_name: string;
  plan_amount: number;
  subject_rows: IPoolCatalogSubject[];
  origin_cols: IPoolCatalogOrigin[];
}
export interface IPoolCatalogSubject {
  subject_id: number;
  subject_name: string;
  plan_amount: number;
  origins_subjects: IOriginsSubject[];
}
export interface IPoolCatalogOrigin {
  origin_id: number;
  origin_name: string;
  plan_amount: number;
}

export class Pool extends BaseModel<IPool> {
  constructor() {
    super({
      name: 'pool',
      resource: 'pool',
      parentResource: 'activities',
      namespace: '/finance',
      role: 'admin',
    });
  }

  findPool(activityId: number) {
    return this.request.get<IPool>(`${this.parentResourcePath}/${activityId}/pool`);
  }

  createPool(activityId: number) {
    return this.request.post<IPool>(`${this.parentResourcePath}/${activityId}/pool`);
  }
}

export default new Pool();
