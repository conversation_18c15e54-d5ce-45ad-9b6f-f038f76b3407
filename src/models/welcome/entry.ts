import BaseModel from '@/models/BaseModel';
import { IInstance } from '../bpm/instance';
import { IFile } from '../file';
import { IStudent } from '../res/student';

export interface IWelcomeEntry {
  id?: number;
  created_at?: string;
  updated_at?: string;
  activity_id?: number;
  student_id?: number;
  student?: IStudent;
  flowable_info?: IWelcomeEntryFlowableInfo;
  instance?: IInstance;
  admission?: IFile[]; // 学生上传的准考证附件
  receipt?: IFile[]; // 学生上传的付款收据
}

export interface IWelcomeEntryFlowableInfo {
  seq?: string;
  activity_name?: string;
  activity_start_at?: string;
  activity_end_at?: string;
  activity_state?: string;
  student_id?: number;
  instance_state?: string;
  student_code?: string;
  student_name?: string;
  instance_storage?: IObject;
}

interface IResponse {
  entries: IWelcomeEntry[];
}

export class WelcomeEntry extends BaseModel<IWelcomeEntry, IResponse> {
  constructor(role: 'admin' | 'student') {
    super({
      name: 'entry',
      resource: 'entries',
      namespace: `/studying/${role}/welcome`,
      parentResource: 'activities',
    });
  }

  studentEntry() {
    return this.request.get<IWelcomeEntry>('/studying/student/welcome/entry');
  }

  createEntries(activityId: number, ids: number[]) {
    return this.request.post(`/studying/admin/welcome/activities/${activityId}/entries/append`, {
      entry: {
        student_ids: ids,
      },
    });
  }
}
