/**
 * 迎新 - 活动
 */
import BaseModel from '@/models/BaseModel';

export interface IWelcomeActivity {
  id?: number;
  created_at?: string;
  updated_at?: string;
  name?: string;
  start_at?: string;
  end_at?: string;
  body?: any;
  workflow_id?: number;
  state?: StateEnum;
  entry_count?: number;
  entry_state_group?: {
    created: number;
  };
  student_ids?: number[];
  fee?: IObject;
}

export enum StateEnum {
  todo = '草稿箱',
  doing = '已发布',
  done = '已过期',
}

interface IResponse {
  activities: IWelcomeActivity[];
}

export class WelcomeActivity extends BaseModel<IWelcomeActivity, IResponse> {
  constructor(role: 'admin') {
    super({
      name: 'activity',
      resource: 'activities',
      namespace: `/studying/${role}/welcome`,
    });
  }

  clone(id: number) {
    return this.request.post(`/studying/admin/welcome/activities/${id}/clone`);
  }
}
