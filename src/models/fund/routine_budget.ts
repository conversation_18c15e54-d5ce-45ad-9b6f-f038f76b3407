/*
################# index column ########
#  id(记录号)                       :string(32)       not null, primary key
#  zjkRoutineBaseId(记录号(日常资金卡表)) :string(32)
#  budgetName(预算名称)              :string(200)
#  proportion(比例)                :float(53)
#  amountOfMoney(金额)             :float(53)        default(0.0)
#  expenseAmount(已报销金额)          :float(53)        default(0.0)
#  freezeAmount(已冻结金额)           :float(53)        default(0.0)
#  personInCharge(负责人)           :string(32)
#  insert_u(插入者)                 :string(32)
#  insert_d(插入日期)                :date
#  update_u(更新者)                 :string(32)
#  update_d(更新日期)                :date
#  isDelete(删除标记)                :string(32)
*/

import BaseModel from '../BaseModel';

export interface IRoutineBudget {
  id: string;
  budgetName: string;
  proportion: string;
  amountOfMoney: number;
  expenseAmount: number;
  freezeAmount: number;
  personInCharge: string;
  zjkRoutineBaseId: string;
}

export interface IResponse {
  routine_budgets: IRoutineBudget[];
}

export class RoutineBudget extends BaseModel<IRoutineBudget, IResponse> {
  constructor() {
    super({
      name: 'routine_budget',
      resource: 'routine_budgets',
      namespace: '/fund/admin',
      parentResource: 'routine_bases',
    });
  }

  execute(params: any = {}) {
    return this.request.get('/fund/teacher/execute_routine_budgets', { params });
  }
}

export default new RoutineBudget();
