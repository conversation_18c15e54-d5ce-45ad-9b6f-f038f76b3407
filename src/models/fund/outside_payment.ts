/*
################# index column ########
#  id(记录号)                           :string(32)       default(""), not null, primary key
#  zjkBudgetId(记录号（资金卡预算表）)          :string(32)
#  zjkRoutineBudgetId(记录号（日常资金卡预算表）) :string(32)
#  school(学校)                        :string(32)
#  codeNumber(二维码号)                  :string(32)
#  reimbursementDate(报销日期)           :datetime
#  numberOfDocuments(单据张数)           :integer
#  payee(收款人)                        :string(100)
#  payeeOrg(付款人组织机构（单位）)             :string(200)
#  department(部门)                    :string(32)
#  businessPerson(出差人姓名)             :string(100)
#  reason(事由)                        :string(3000)
#  reimbursementAmount(报销金额)         :float(53)
#  originalCash(原借支)                 :float(53)
#  verification(核销)                  :float(53)
#  retrieve(退补)                      :float(53)        unsigned
#  paymentPurpose(付款用途)              :string(32)
#  paymentType(付款方式)                 :string(32)
#  paymentTypeComment(付款方式说明)        :string(3000)
#  zjkPrepaymentsRoutineId(解冻号)      :string(32)
#  thawFunds(解冻金额)                   :float(53)        default(0.0)
#  type                              :string(32)
#  state                             :string(32)
#  isHrCashPool(人事资金池)               :string(2)
#  insert_u(插入者)                     :string(32)
#  insert_d(插入日期)                    :datetime
#  update_u(更新者)                     :string(32)
#  update_d(更新日期)                    :datetime
#  isDelete(删除标记)                    :string(32)
#  moneyd(金额大写)                      :string(200)
#  is_sign(是否需要院长签字)                 :string(32)
#  transferCode(转移码)                 :string(32)
#
#  #################.payment_check#############################
#
#  id(记录号)                                     :string(32)       default(""), not null, primary key
#  zjkPaymentId(记录号(日常资金卡报销付款凭证表)) :string(32)
#  checkResult(审核结果)                          :string(32)
#  checker(审核人)                                :string(32)
#  checkDate(审核日期)                            :date
#  checkType(审核类别)                            :string(32)
#  opinion(意见)                                  :string(500)
#  disagreeType(不同意的类型)                     :string(32)
#  attachmentId(附件ID)                           :string(32)
#  insert_u(插入者)                               :string(32)
#  insert_d(插入日期)                             :datetime
#  update_u(更新者)                               :string(32)
#  update_d(更新日期)                             :datetime
#  isDelete(删除标记)                             :string(32)
#  underwrite_id(签字ID)                          :string(32)
#
#  #################.outside#############################
#  id                                                                     :string(32) default(""), not null, primary key
#  sequence(序号)                                                               :integer
#  zjkOutsidePaymentId(外阜Id)                                                  :string(32)
#  zjkReimbursementOutsidepaymentId(记录号（日常资金卡报销外阜交通付款凭证表）) :string(32)
#  startDate(起始时间)                                                          :datetime
#  endDate(截止时间)                                                            :datetime
#  startPlace(启程)                                                             :string(100)
#  endPlace(到达地)                                                             :string(100)
#  ticket(车船牌飞机票)                                                         :string(100)
#  ticketAmount(车船票飞机票金额)                                               :float(53)
#  subsidyPerson(未乘卧铺补贴（人）)                                            :integer
#  subsidyDate(未乘卧铺补贴（天）)                                              :integer
#  subsidyAmount(未乘卧铺补贴（金额）)                                          :float(53)
#  accommodationPerson(住宿费(人))                                              :integer
#  accommodationDate(住宿费(天))                                                :integer
#  accommodationAmount(住宿费(金额))                                            :float(53)
#  LocalTransportationCharges(市内交通费)                                       :float(53)        unsigned
#  livingallowancePerson(出勤补贴(人))                                          :integer
#  livingallowanceDate(出勤补贴(天))                                            :integer
#  livingallowanceAmount(出勤补贴(金额))                                        :float(53)
#  summary(摘要)                                                                :string(500)
#  money(金额)                                                                  :float(53)
#  insert_u                                                                     :string(32)
#  insert_d(插入日期)                                                           :datetime
#  update_u(更新者)                                                             :string(255)
#  update_d(更新日期)                                                           :datetime
#  isDelete(删除标记)                                                           :string(32)
#
*/

import BaseModel from '../BaseModel';

export interface IOutsidePayment {
  id: string;
  zjkRoutineBudgetId: string;
  payee: string;
  payeeOrg: string;
  numberOfDocuments: number;
  reimbursementAmount: number;
  update_d: string;
  paymentPurpose: string;
  state_dic_name: string;
  businessPerson: string;
  reason: string;
}

export interface IResponse {
  outside_payments: IOutsidePayment[];
}
export interface IPayment {
  id: string;
  event: string;
  opinion: string;
}

export class OutsidePayment extends BaseModel<IOutsidePayment, IResponse> {
  constructor() {
    super({
      name: 'outside_payment',
      resource: 'outside_payments',
      namespace: '/fund/admin',
    });
  }

  fire(payment: IPayment) {
    return this.request.post(`/fund/teacher/outside_payments/${payment.id}/fire`, {
      payment,
    });
  }

  export(params: any = {}, role: string = 'admin') {
    return this.request.post(`/fund/${role}/outside_payments/export`, {
      params,
    });
  }

  exportByParent(params: any = {}, roleUrl: string) {
    // roleUrl的值如： // admin/outside_payments/id
    return this.request.post(`/fund/${roleUrl}/outside_payments/export`, {
      params,
    });
  }
}

export default new OutsidePayment();
