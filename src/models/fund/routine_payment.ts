/*
################# index column ########
#  id(记录号)                           :string(32)       default(""), not null, primary key
#  zjkRoutineBudgetId(记录号（日常资金卡预算表）) :string(32)
#  school(学校)                        :string(32)
#  codeNumber(二维码号)                  :string(32)
#  payee(收款人)                        :string(200)
#  payeeOrg(收款人组织机构)                 :string(200)
#  reimbursementDate(报销日期)           :datetime
#  numberOfDocuments(单据张数)           :integer
#  reimbursementAmount(报销金额)         :float(53)
#  paymentPurpose(付款用途)              :string(3000)
#  paymentType(付款方式)                 :string(32)
#  paymentTypeComment(付款方式说明)        :string(3000)
#  zjkPrepaymentsRoutineId(解冻资金号)    :string(32)
#  thawFunds(解冻资金)                   :float(53)
#  type(分类)                          :string(32)
#  state(状态)                         :string(32)
#  isHrCashPool(人事资金池)               :string(2)
#  insert_u(插入者)                     :string(32)
#  insert_d(插入日期)                    :datetime
#  update_u(更新者)                     :string(32)
#  update_d(更新日期)                    :datetime
#  isDelete(删除标记)                    :string(32)
#  moneyd(资金大写)                      :string(200)
#  is_sign(是否需要院长签字)                 :string(32)
#  transferCode(转移码)                 :string(32)
#
#  #################.payment_check#############################
#
#  id(记录号)                                     :string(32)       default(""), not null, primary key
#  zjkPaymentId(记录号(日常资金卡报销付款凭证表)) :string(32)
#  checkResult(审核结果)                          :string(32)
#  checker(审核人)                                :string(32)
#  checkDate(审核日期)                            :date
#  checkType(审核类别)                            :string(32)
#  opinion(意见)                                  :string(500)
#  disagreeType(不同意的类型)                     :string(32)
#  attachmentId(附件ID)                           :string(32)
#  insert_u(插入者)                               :string(32)
#  insert_d(插入日期)                             :datetime
#  update_u(更新者)                               :string(32)
#  update_d(更新日期)                             :datetime
#  isDelete(删除标记)                             :string(32)
#  underwrite_id(签字ID)                          :string(32)
*/

import BaseModel from '../BaseModel';

export interface IRoutinePayment {
  id: string;
  zjkRoutineBudgetId: string;
  payee: string;
  payeeOrg: string;
  numberOfDocuments: number;
  reimbursementAmount: number;
  update_d: string;
  paymentPurpose: string;
  state_dic_name: string;
}
export interface IPayment {
  id: string;
  event: string;
  opinion: string;
}

export interface IResponse {
  routine_payments: IRoutinePayment[];
}

export class RoutinePayment extends BaseModel<IRoutinePayment, IResponse> {
  constructor() {
    super({
      name: 'routine_payment',
      resource: 'routine_payments',
      namespace: '/fund/admin',
    });
  }

  fire(payment: IPayment) {
    return this.request.post(`/fund/teacher/routine_payments/${payment.id}/fire`, {
      payment,
    });
  }

  export(params: any = {}, role: string = 'admin') {
    return this.request.post(`/fund/${role}/routine_payments/export`, {
      params,
    });
  }

  exportByParent(params: any = {}, roleUrl: string) {
    // roleUrl的值如： // admin/routine_bases/id
    return this.request.post(`/fund/${roleUrl}/routine_payments/export`, {
      params,
    });
  }
}

export default new RoutinePayment();
