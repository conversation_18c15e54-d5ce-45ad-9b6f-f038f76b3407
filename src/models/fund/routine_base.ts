/*
################# index column ########
#  id(记录号)                    : string(32)       not null, primary key
#  capitalCardNumber(资金卡号)    : string(32)
#  financeNumber(财务编号)        : string(32)
#  moneyCardName(资金卡名称)       : string(200)
#  school(学校)                 : string(32)
#  amountOfMoney(金额)          : float(53)        default (0.0)
#  expenseAmount(已报销金额)       : float(53)        default (0.0)
#  freezeAmount(已冻结金额)        : float(53)        default (0.0)
#  personInCharge(负责人)        : string(32)
#  department(二级院系)           : string(32)
#  departmentLeadership(部门领导) : string(32)
#  manger(分管领导)               : string(32)
#  startDate(开始日期)            : date
#  endDate(结束日期)              : date
#  state(状态)                  : string(32)
#  insert_u(插入者)              : string(32)
#  insert_d(插入日期)             : date
#  update_u(更新者)              : string(32)
#  update_d(更新日期)             : string(255)
#  isDelete(删除标记)             : string(32)
#  remark(备注)                 : string(200)
#  zjkType(资金卡类别)             : string(32)
#  is_needbook(是否需要院办下发承若书)   : string(32)
#  is_sign(是否需要院长签字)          : string(32)
#  b_file_id(文件存放)            : string(50)
#  dean(院长)                   : string(32)
*/

import BaseModel from '../BaseModel';

export interface IRoutineBase {
  id: string;
  capitalCardNumber: string;
  moneyCardName: string;
  financeNumber: string;
  school_dic_name: string;
  amountOfMoney: string;
  avalible_amount: string;
  routine_budgets: IObject[];
  routine_thirds: IObject[];
}

export interface IResponse {
  routine_bases: IRoutineBase[];
}

export class RoutineBase extends BaseModel<IRoutineBase, IResponse> {
  constructor() {
    super({
      name: 'routine_base',
      resource: 'routine_bases',
      namespace: '/fund/admin',
    });
  }
}

export default new RoutineBase();
