/*
################# index column ########
id(主键)                       :string(32)       default(""), not null, primary key
#  zjk_budget_id(记录号关联二级)       :string(32)
#  routineBase_third_name(三级名称) :string(200)
#  amount_money(金额)             :float(53)        default(0.0)
#  expenseAmount(已报销金额)         :float(53)        default(0.0)
#  freezeAmount(已冻结金额)          :float(53)        default(0.0)
#  quantity(数量)                 :float(53)
#  unit(单位)                     :string(32)
#  unit_price(单价)               :float(53)
#  is_equiment(是否设备)            :string(32)
#  procurement_type(采购类型)       :string(32)
#  specifications(规格)           :string(200)
#  persion_charge(负责人)          :string(32)
#  insert_u(插入者)                :string(32)
#  insert_d(插入时间)               :datetime         not null
#  update_u(修改人)                :string(32)
#  update_d(修改时间)               :datetime         default(NULL), not null
#  is_delete(删除标记)              :string(32)
*/

import BaseModel from '../BaseModel';

export interface IRoutineThird {
  id: string;
  zjk_budget_id: string;
  routineBase_third_name: string;
  amount_money: number;
  expenseAmount: number;
  freezeAmount: number;
  quantity: number;
  unit: string;
  unit_price: number;
}

export interface IResponse {
  routine_thirds: IRoutineThird[];
}

export class RoutineThird extends BaseModel<IRoutineThird, IResponse> {
  constructor() {
    super({
      name: 'routine_third',
      resource: 'routine_thirds',
      namespace: '/fund/admin',
      parentResource: 'routine_budgets',
    });
  }
}

export default new RoutineThird();
