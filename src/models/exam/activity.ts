/**
 * 开始管理 - 考试活动
 */
import BaseModel from '@/models/BaseModel';
import { IQuestionSet } from '@/service/question_set';
import { IAnswerSet } from '@/service/answer_set';
import tools from '@/utils/tools';
import registerModel from '@/models/comm/register';
import { IInstance } from '../bpm/instance';

interface Student {
  id: number;
  name: string;
  code: string;
}

export interface IActivity {
  id?: number;
  created_at?: string;
  updated_at?: string;
  creator_type?: string;
  creator_id?: number;
  creator_name?: string;
  creator_code?: string;
  title?: string;
  duration_in_min?: number;
  start_at?: string;
  state?: 'todo' | 'doing' | 'done' | 'approving';
  department_name?: string;
  instance?: IInstance;
  student_count?: number;
  students?: Student[];
  paste_student_ids?: number[];
  question_set?: IQuestionSet;
  question_set_id?: number;
  own_answer_sets?: IAnswerSet;
  answer_set_group_count?: {
    todo: number;
    doing: number;
    done: number;
    checked: number;
  };
  time_infos?: {
    to_start: number;
    to_start_off: number;
    to_end: number;
  };
}

interface IResponse {
  activities: IActivity[];
}

export enum StateText {
  todo = '未发布',
  doing = '已发布',
  done = '已结束',
  approving = '审核中',
}

export class Activity extends BaseModel<IActivity, IResponse> {
  constructor(role: 'admin' | 'teacher' | 'student') {
    super({
      name: 'activity',
      resource: 'activities',
      namespace: '/exam',
      role,
    });
  }

  // teacher
  // TODO: 考试发布申请
  requestApproval(id: number) {
    return this.request.post(`/exam/teacher/activities/${id}/approval`);
  }

  // student
  async setLog(id: number) {
    const { cip } = tools.getIpInfo();
    const { data } = await registerModel.getAddress({ ip: cip });
    return this.request.post(`/exam/student/activities/${id}/log`, {
      activity: {
        address: data.address,
        ip: cip,
      },
    });
  }
}
