import { BaseService } from '../BaseService';
import { IAnswer } from './answer';

export interface IAnswerSet {
  id?: number;
  created_at?: string;
  updated_at?: string;
  type?: any;
  state: 'todo' | 'doing' | 'done' | 'checked';
  question_set_id?: number;
  meta?: any;
  answerable_type?: string;
  answerable_id?: number;
  creator_type?: string;
  creator_id?: number;
  answers?: IAnswer[];
  answers_attributes?: Array<{
    id: number;
    value: string;
    meta: IObject;
  }>;
}

export interface IResponse {
  current_page: number;
  total_pages: number;
  total_count: number;
  answer_sets: IAnswerSet[];
}

export class TeacherAnswerSet extends BaseService {
  fetch(params: object) {
    return this.request.get<IResponse>('/exam/teacher/answer_sets', {
      params,
    });
  }
  fetchByActivity(activityId: number, params: object) {
    return this.request.get<IResponse>(`/exam/teacher/activities/${activityId}/answer_sets`, {
      params,
    });
  }
  find(id: number) {
    return this.request.get<IAnswerSet>(`/exam/teacher/answer_sets/${id}`);
  }
  findByActivity(activityId: number, id: number) {
    return this.request.get<IAnswerSet>(`/exam/teacher/activities/${activityId}/answer_sets/${id}`);
  }
  create(answerSet: IAnswerSet) {
    return this.request.post<IAnswerSet>(`/exam/teacher/answer_sets`, {
      answer_set: answerSet,
    });
  }
  update(answerSet: IAnswerSet) {
    return this.request.patch(`/exam/teacher/answer_sets/${answerSet.id}`, {
      answer_set: answerSet,
    });
  }
  delete(id: number) {
    return this.request.delete(`/exam/teacher/answer_sets/${id}`);
  }
}

export namespace AnswerSetService {
  export const Teacher = new TeacherAnswerSet();
}
