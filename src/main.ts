import '@/assets/styles/ant-design.less';
import 'normalize.css';
import '@/assets/styles/elderly-mode.less';
/* eslint-disable-next-line */
import 'video.js/dist/video-js.css';
import 'viewerjs/dist/viewer.css';
import '@/assets/styles/global/index.styl';
import '@/assets/styles/cover.styl';
// 判断mac win导入滚动条样式
if (navigator.platform.toUpperCase().indexOf('MAC') === -1) {
  require('@/assets/styles/global/scrollbar.styl');
}
import 'babel-polyfill';
import Vue from 'vue';
import moment from 'moment';
import App from '@/App.vue';
import router from '@/router';
import store from '@/store';
import '@/helpers/directives';
import i18n from './i18n';
import '@/helpers/filters';
import '@/plugins';
import '@/components/global';
import tools from '@/utils/tools';
import utils from '@/utils';
import helper from '@/helpers/helper';
import speechService from '@/utils/speechService';
import { installSpeechMixin } from '@/utils/speechMixin';
import infiniteScroll from 'vue-infinite-scroll';
import RichEditor from './components/rich-editor';

moment.locale('zh-cn');

Vue.config.productionTip = false;
Vue.prototype.$moment = moment;
Vue.prototype.$tools = tools;
Vue.prototype.$utils = utils;
Vue.prototype.$helper = helper;
Vue.prototype.$speech = speechService;
Vue.use(infiniteScroll);
Vue.use(RichEditor);

// 安装语音混入（可选，提供更便捷的语音方法）
// installSpeechMixin();

new Vue({
  router,
  store,
  i18n,
  render: h => h(App),
}).$mount('#app');
