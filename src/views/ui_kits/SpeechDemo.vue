<template lang="pug">
.speech-demo
  a-card(title="语音播报功能演示")
    .demo-section
      h3 基础语音播报
      .demo-controls
        a-button(
          type="primary"
          @click="testBasicSpeech"
          v-speech:hover="'点击测试基础语音播报'"
        ) 测试基础播报
        
        a-button(
          @click="testSuccessMessage"
          v-speech:hover="'点击播报成功消息'"
        ) 成功消息
        
        a-button(
          @click="testErrorMessage"
          v-speech:hover="'点击播报错误消息'"
        ) 错误消息
        
        a-button(
          @click="testWarningMessage"
          v-speech:hover="'点击播报警告消息'"
        ) 警告消息

    .demo-section
      h3 表单元素语音播报
      a-form(:form="form" layout="vertical")
        a-form-item(label="姓名")
          a-input(
            v-decorator="['name']"
            placeholder="请输入您的姓名"
            v-speech:focus="'姓名输入框'"
          )
        
        a-form-item(label="邮箱")
          a-input(
            v-decorator="['email']"
            placeholder="请输入邮箱地址"
            v-speech:focus="'邮箱输入框'"
          )
        
        a-form-item(label="城市")
          a-select(
            v-decorator="['city']"
            placeholder="请选择城市"
            v-speech:focus="'城市选择框'"
          )
            a-select-option(value="beijing" v-speech:hover="'北京'") 北京
            a-select-option(value="shanghai" v-speech:hover="'上海'") 上海
            a-select-option(value="guangzhou" v-speech:hover="'广州'") 广州
        
        a-form-item
          a-button(
            type="primary"
            @click="handleSubmit"
            v-speech:hover="'提交表单'"
          ) 提交
          a-button(
            style="margin-left: 8px"
            @click="handleReset"
            v-speech:hover="'重置表单'"
          ) 重置

    .demo-section
      h3 导航和链接
      .demo-nav
        a-menu(mode="horizontal" :selectedKeys="[]")
          a-menu-item(
            key="home"
            v-speech:hover="'首页'"
          ) 首页
          a-menu-item(
            key="about"
            v-speech:hover="'关于我们'"
          ) 关于我们
          a-menu-item(
            key="contact"
            v-speech:hover="'联系我们'"
          ) 联系我们
      
      .demo-links
        a(
          href="#"
          @click.prevent="handleLinkClick('帮助文档')"
          v-speech:hover="'帮助文档链接'"
        ) 帮助文档
        a(
          href="#"
          @click.prevent="handleLinkClick('用户指南')"
          v-speech:hover="'用户指南链接'"
        ) 用户指南

    .demo-section
      h3 数据表格
      a-table(
        :columns="tableColumns"
        :dataSource="tableData"
        :pagination="false"
        size="small"
      )
        template(slot="action" slot-scope="text, record")
          a(
            @click="handleEdit(record)"
            v-speech:hover="`编辑${record.name}`"
          ) 编辑
          a-divider(type="vertical")
          a(
            @click="handleDelete(record)"
            v-speech:hover="`删除${record.name}`"
          ) 删除

    .demo-section
      h3 状态反馈
      .demo-controls
        a-button(@click="simulateLoading") 模拟加载
        a-button(@click="simulateUpload") 模拟上传
        a-button(@click="simulateSearch") 模拟搜索
        
      .demo-status
        a-spin(:spinning="loading" tip="加载中...")
          .content-placeholder(v-if="!loading") 内容区域
        
        a-progress(
          v-if="uploadProgress > 0"
          :percent="uploadProgress"
          :status="uploadProgress === 100 ? 'success' : 'active'"
        )

    .demo-section
      h3 快捷操作
      .demo-controls
        a-button(@click="speakCurrentTime") 播报当前时间
        a-button(@click="speakPageInfo") 播报页面信息
        a-button(@click="speakShortcuts") 播报快捷键
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';

@Component({
  name: 'SpeechDemo',
})
export default class SpeechDemo extends Vue {
  form = this.$form.createForm(this);
  loading = false;
  uploadProgress = 0;

  tableColumns = [
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '年龄',
      dataIndex: 'age',
      key: 'age',
    },
    {
      title: '城市',
      dataIndex: 'city',
      key: 'city',
    },
    {
      title: '操作',
      key: 'action',
      scopedSlots: { customRender: 'action' },
    },
  ];

  tableData = [
    { key: '1', name: '张三', age: 28, city: '北京' },
    { key: '2', name: '李四', age: 32, city: '上海' },
    { key: '3', name: '王五', age: 25, city: '广州' },
  ];

  testBasicSpeech() {
    this.$speech.speak('这是一个基础的语音播报测试，功能正常运行中。');
  }

  testSuccessMessage() {
    this.$speech.speakSuccess('操作已成功完成！');
  }

  testErrorMessage() {
    this.$speech.speakError('操作失败，请检查输入信息。');
  }

  testWarningMessage() {
    this.$speech.speakWarning('请注意，这是一个警告信息。');
  }

  handleSubmit() {
    this.form.validateFields((err: any, values: any) => {
      if (!err) {
        this.$speech.speakSuccess('表单提交成功！');
        console.log('表单数据:', values);
      } else {
        this.$speech.speakError('表单验证失败，请检查输入信息。');
      }
    });
  }

  handleReset() {
    this.form.resetFields();
    this.$speech.speak('表单已重置');
  }

  handleLinkClick(linkName: string) {
    this.$speech.speak(`点击了${linkName}`);
  }

  handleEdit(record: any) {
    this.$speech.speak(`编辑${record.name}的信息`);
  }

  handleDelete(record: any) {
    this.$speech.speakWarning(`确认删除${record.name}吗？`);
  }

  simulateLoading() {
    this.loading = true;
    this.$speech.speak('开始加载数据');
    
    setTimeout(() => {
      this.loading = false;
      this.$speech.speakSuccess('数据加载完成');
    }, 3000);
  }

  simulateUpload() {
    this.uploadProgress = 0;
    this.$speech.speak('开始上传文件');
    
    const interval = setInterval(() => {
      this.uploadProgress += 10;
      
      if (this.uploadProgress >= 100) {
        clearInterval(interval);
        this.$speech.speakSuccess('文件上传完成');
      } else if (this.uploadProgress % 30 === 0) {
        this.$speech.speak(`上传进度：${this.uploadProgress}%`);
      }
    }, 200);
  }

  simulateSearch() {
    this.$speech.speak('正在搜索');
    
    setTimeout(() => {
      const count = Math.floor(Math.random() * 100) + 1;
      this.$speech.speak(`搜索完成，找到${count}条结果`);
    }, 1500);
  }

  speakCurrentTime() {
    const now = new Date();
    const timeStr = now.toLocaleString('zh-CN');
    this.$speech.speak(`当前时间：${timeStr}`);
  }

  speakPageInfo() {
    this.$speech.speak('当前页面：语音播报功能演示页面，包含多种语音播报示例');
  }

  speakShortcuts() {
    this.$speech.speak('快捷键提示：按Tab键可以在表单元素间切换，按回车键可以激活按钮');
  }
}
</script>

<style lang="stylus" scoped>
.speech-demo
  padding 24px
  max-width 1200px
  margin 0 auto

.demo-section
  margin-bottom 32px
  
  h3
    margin-bottom 16px
    color #1890ff
    border-bottom 1px solid #e8e8e8
    padding-bottom 8px

.demo-controls
  margin-bottom 16px
  
  .ant-btn
    margin-right 8px
    margin-bottom 8px

.demo-nav
  margin-bottom 16px

.demo-links
  a
    margin-right 16px
    color #1890ff
    text-decoration none
    
    &:hover
      text-decoration underline

.demo-status
  margin-top 16px

.content-placeholder
  height 100px
  background #f5f5f5
  display flex
  align-items center
  justify-content center
  color #999
</style>
