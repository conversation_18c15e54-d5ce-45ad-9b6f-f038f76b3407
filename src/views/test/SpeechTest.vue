<template lang="pug">
.speech-test
  .header
    h1(v-speech="'语音播报测试页面'") 语音播报测试页面
    p(v-speech="'这是一个用于测试语音播报功能的页面'") 这是一个用于测试语音播报功能的页面
  
  .content
    .section
      h2(v-speech="'基本控件测试'") 基本控件测试
      .controls
        a-button(type="primary" v-speech="'主要按钮'" @click="testBasicSpeech") 测试基本语音
        a-button(v-speech="'普通按钮'" @click="testPageContent") 播报页面内容
        a-button(type="danger" v-speech="'危险按钮'" @click="testError") 测试错误提示
    
    .section
      h2(v-speech="'表单元素测试'") 表单元素测试
      a-form(:form="form" layout="vertical")
        a-form-item(label="姓名")
          a-input(v-decorator="['name']" placeholder="请输入姓名" v-speech="'姓名输入框'")
        a-form-item(label="邮箱")
          a-input(v-decorator="['email']" placeholder="请输入邮箱" v-speech="'邮箱输入框'")
        a-form-item(label="性别")
          a-select(v-decorator="['gender']" placeholder="请选择性别" v-speech="'性别选择框'")
            a-select-option(value="male" v-speech="'男性选项'") 男
            a-select-option(value="female" v-speech="'女性选项'") 女
    
    .section
      h2(v-speech="'链接测试'") 链接测试
      .links
        a(href="#" v-speech="'首页链接'" @click.prevent) 首页
        a(href="#" v-speech="'关于我们链接'" @click.prevent) 关于我们
        a(href="#" v-speech="'联系我们链接'" @click.prevent) 联系我们
    
    .section
      h2(v-speech="'内容区域测试'") 内容区域测试
      .content-area
        p(v-speech="'这是第一段测试内容'") 这是第一段测试内容，用于验证段落文本的语音播报功能。
        p(v-speech="'这是第二段测试内容'") 这是第二段测试内容，包含更多的文字信息，用于测试较长文本的播报效果。
        
        .card(v-speech="'信息卡片'")
          h3 信息卡片
          p 这是一个信息卡片，包含标题和内容。
    
    .section
      h2(v-speech="'自动播报测试'") 自动播报测试
      .auto-speech-area
        .item(v-speech.auto="'自动播报项目1'") 自动播报项目1
        .item(v-speech.auto="'自动播报项目2'") 自动播报项目2
        .item(v-speech.auto="'自动播报项目3'") 自动播报项目3
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import speechService from '@/utils/speechService';

@Component({})
export default class SpeechTest extends Vue {
  form = this.$form.createForm(this);

  testBasicSpeech(): void {
    speechService.speak('这是一个基本的语音播报测试', { priority: 'high' });
  }

  testPageContent(): void {
    speechService.speakPageContent();
  }

  testError(): void {
    speechService.speakError('这是一个错误提示的语音播报测试');
  }
}
</script>

<style lang="stylus" scoped>
.speech-test
  padding 20px
  max-width 800px
  margin 0 auto

.header
  text-align center
  margin-bottom 40px
  
  h1
    color #1890ff
    margin-bottom 10px
  
  p
    color #666
    font-size 16px

.section
  margin-bottom 30px
  padding 20px
  border 1px solid #e8e8e8
  border-radius 6px
  
  h2
    color #333
    margin-bottom 15px
    border-bottom 1px solid #e8e8e8
    padding-bottom 10px

.controls
  display flex
  gap 10px
  flex-wrap wrap

.links
  display flex
  gap 20px
  
  a
    color #1890ff
    text-decoration none
    padding 5px 10px
    border-radius 4px
    
    &:hover
      background-color #f0f8ff

.content-area
  p
    margin-bottom 15px
    line-height 1.6
    color #333

.card
  background #f9f9f9
  padding 15px
  border-radius 6px
  margin-top 15px
  
  h3
    margin-bottom 10px
    color #333

.auto-speech-area
  .item
    padding 10px
    margin 10px 0
    background #e6f7ff
    border-radius 4px
    border-left 4px solid #1890ff
</style>
