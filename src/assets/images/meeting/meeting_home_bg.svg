<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920px" height="1080px" viewBox="0 0 1920 1080" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 56.3 (81716) - https://sketch.com -->
    <title>背景@3x</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <linearGradient x1="100%" y1="66.6689576%" x2="17.7107241%" y2="31.6107294%" id="linearGradient-1">
            <stop stop-color="#5EC7FF" offset="0%"></stop>
            <stop stop-color="#3A7EFC" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="94.8579152%" y1="82.2109627%" x2="0%" y2="56.4542533%" id="linearGradient-2">
            <stop stop-color="#3DA8F5" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#58FFF8" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="-7.15951068%" y1="39.4870929%" x2="82.3113569%" y2="54.4275046%" id="linearGradient-3">
            <stop stop-color="#57B8FF" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#42E2FF" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="背景" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <rect fill="#191C20" x="0" y="0" width="1920" height="1080"></rect>
        <rect id="Rectangle" fill="url(#linearGradient-1)" fill-rule="nonzero" x="0" y="0" width="1920" height="1080"></rect>
        <circle id="Oval" fill="url(#linearGradient-2)" fill-rule="nonzero" cx="91" cy="99" r="320"></circle>
        <circle id="Oval-Copy" fill="url(#linearGradient-3)" fill-rule="nonzero" cx="1731" cy="1045" r="350"></circle>
    </g>
</svg>