// 适老化模式样式
.elderly-mode {
  // 基础字体大小
  font-size: 18px !important;
  line-height: 1.5 !important;

  // 标题字体
  h1, h2, h3, h4, h5, h6 {
    line-height: 1.4 !important;
  }
  h1 { font-size: 32px !important; }
  h2 { font-size: 28px !important; }
  h3 { font-size: 24px !important; }
  h4 { font-size: 20px !important; }
  h5 { font-size: 18px !important; }
  h6 { font-size: 16px !important; }

  // 表单元素
  .ant-input,
  .ant-input-affix-wrapper,
  .ant-select-selector,
  .ant-picker,
  .ant-input-number {
    font-size: 18px !important;
    height: 44px !important;
    line-height: 44px !important;
    padding: 0 15px !important;
  }

  // 按钮
  .ant-btn {
    font-size: 18px !important;
    height: 44px !important;
    padding: 0 20px !important;
    min-width: 88px !important;
  }

  // 表格
  .ant-table {
    font-size: 18px !important;
    th {
      font-size: 18px !important;
      padding: 16px !important;
    }
    td {
      padding: 16px !important;
    }
  }

  // 卡片
  .ant-card {
    .ant-card-head {
      font-size: 20px !important;
      padding: 16px 24px !important;
    }
    .ant-card-body {
      font-size: 18px !important;
      padding: 24px !important;
    }
  }

  // 弹窗
  .ant-modal {
    .ant-modal-title {
      font-size: 24px !important;
    }
    .ant-modal-body {
      font-size: 18px !important;
      padding: 24px !important;
    }
    .ant-modal-footer {
      padding: 16px 24px !important;
    }
  }

  // 导航菜单
  .ant-menu {
    font-size: 18px !important;
    .ant-menu-item {
      height: 50px !important;
      line-height: 50px !important;
    }
  }

  // 标签页
  .ant-tabs {
    font-size: 18px !important;
    .ant-tabs-tab {
      font-size: 18px !important;
      padding: 12px 16px !important;
    }
  }

  // 下拉菜单
  .ant-dropdown-menu {
    font-size: 18px !important;
    .ant-dropdown-menu-item {
      line-height: 44px !important;
      padding: 0 24px !important;
    }
  }

  // 分页
  .ant-pagination {
    font-size: 18px !important;
    .ant-pagination-item {
      height: 44px !important;
      line-height: 44px !important;
    }
  }

  // 高对比度
  .ant-btn-primary {
    background-color: #0050b3 !important;
    border-color: #0050b3 !important;
  }

  .ant-btn-primary:hover {
    background-color: #003a8c !important;
    border-color: #003a8c !important;
  }

  // 链接样式
  a {
    color: #0050b3 !important;
    text-decoration: underline !important;
  }

  // 表单标签
  .ant-form-item-label > label {
    font-size: 18px !important;
    height: 44px !important;
    line-height: 44px !important;
  }

  // 错误提示
  .ant-form-item-explain {
    font-size: 16px !important;
    line-height: 1.5 !important;
  }

  .ant-menu-vertical .ant-menu-item,
  .ant-menu-vertical-left .ant-menu-item,
  .ant-menu-vertical-right .ant-menu-item,
  .ant-menu-inline .ant-menu-item,
  .ant-menu-vertical .ant-menu-submenu-title,
  .ant-menu-vertical-left .ant-menu-submenu-title,
  .ant-menu-vertical-right .ant-menu-submenu-title,
  .ant-menu-inline .ant-menu-submenu-title {
    font-size: 18px !important;
  }

  .text-button {
    font-size: 18px !important;
  }

  .ant-select {
    font-size: 18px !important;
  }

  .searcher {
    width: 150px !important;
  }

  .searcher .search-input {
    font-size: 18px !important;
  }

  // 图标
  .anticon {
    font-size: 20px !important;
  }
}