@import './colors'

@font-face
  font-family 'DINCond'
  src url('../../fonts/DINCond.otf')
@font-face
  font-family 'PangMenZhengDao'
  src url('../../fonts/庞门正道标题体.TTF')

*, *:after, *:before
  box-sizing border-box

html, body
  margin 0
  padding 0
  height 100%
  background #fff
  font-family -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'

h1, h2, h3, h4, h5, h6, p
  margin 0px
  padding 0px

h1
  font-size 22px

h2
  font-size 20px

h3
  font-size 18px

// layout
.block
  display block
  width 100%

.flex
  display flex
  align-items center
  width 100%

.flex-center
  display flex
  justify-content center
  align-items center
  width 100%

.flex-around
  display flex
  justify-content space-around
  align-items center
  width 100%

.flex-between
  display flex
  justify-content space-between
  align-items center
  width 100%

.flex-end
  display flex
  justify-content flex-end
  align-items center
  width 100%

// text colors
.text-white
  color white !important

.text-gray
  color #808080 !important

.text-danger
  color #e50114 !important

.text-warning
  color #eb9e05 !important

.text-primary
  color #3DA8F5 !important

.text-success
  color #75C940 !important

.text-black
  color #212121 !important

.link
  color #3DA8F5
  text-decoration underline
  cursor pointer

.text-center
  text-align center

.text-right
  text-align right

.text-pre
  width 100%
  white-space pre-wrap
  word-break break-all

.text-ellipsis
  overflow hidden
  width 100%
  text-overflow ellipsis
  white-space nowrap

.two-line-text
  display -webkit-box
  overflow hidden
  -webkit-box-orient vertical
  -webkit-line-clamp 2

button + button
  margin-left 16px

.btn
  margin 0
  padding 0
  outline none
  border none
  background none

.btn-warning
  border-color $warningColor !important
  background-color $warningColor !important
  color #ffffff !important

.btn-danger
  border-color $dangerColor !important
  background-color $dangerColor !important
  color #ffffff !important

.btn-success
  border-color $successColor !important
  background-color $successColor !important
  color #ffffff !important

.btn-primary
  border-color $primaryColor !important
  background-color $primaryColor !important
  color #ffffff !important

.btn-info
  border-color $infoColor !important
  background-color $infoColor !important
  color #ffffff !important

.btn-dark
  border-color $darkColor !important
  background-color $darkColor !important
  color #ffffff !important

.ta-tag
  display inline-block
  padding 6px 8px
  border-radius 4px
  background #F5F5F5
  color #A6A6A6
  font-size 14px
  line-height 20px

.ta-tag-primary
  @extend .ta-tag
  background rgba(237, 247, 255, 1)
  color #3DA8F5

.ta-tag-success
  @extend .ta-tag
  background #F0F9F2
  color #6DC37D

.ta-tag-warning
  @extend .ta-tag
  background #FFF7E6
  color #FA8C15

.ta-tag-danger
  @extend .ta-tag
  background rgba(255, 79, 62, 0.1)
  color #e50114

.flex-table-cell
  display flex
  justify-content space-around

// 原生表格初始化样式
.native-table
  width 100%
  border-right 1px solid #E5E5E5
  border-bottom 1px solid #E5E5E5
  tr
    th, td
      padding 13px 16px
      border-top 1px solid #E5E5E5
      border-left 1px solid #E5E5E5

.emphasize-ant-table
  .ant-table-thead
    background #FAFAFA
  .group-cell
    background #FAFAFA

table
  // 表格悬停行，显示具体的列，可用于表格最后一列的操作列
  .table-hover-col
    visibility hidden !important
  .ant-table-thead
    th:first-child
      padding-left 0 !important
  .ant-table-row
    td:first-child
      padding-left 0 !important
    &:hover
      .table-hover-col
        visibility visible !important
  .click-row
    cursor pointer

.ant-table-bordered
  .ant-table-thead
    th:first-child
      padding-left 8px !important
  .ant-table-row
    td:first-child
      padding-left 8px !important

// 覆盖 ant-table fixed列时出现断点
.ant-table-scroll table .ant-table-fixed-columns-in-body
  visibility visible !important

// 覆盖 ant-table 默认样式
.ant-table-header
  background transparent !important

.ant-table-pagination
  margin 12px 0 !important

.ant-table-thead>tr>th
  white-space nowrap

// TreeSlelect css
.ant-select-tree-dropdown .ant-select-dropdown-search .ant-select-search__field
  height 40px

// modal 自定义
.modal-row
  display flex
  justify-content space-between
  align-items center
  width 100%
  .search
    margin -8px 24px
  .title
    color rgba(0, 0, 0, 0.85)
    font-weight 500
    font-size 16px
    line-height 22px
  .count
    color #808080
    font-weight 500
    font-size 14px

.popover
  padding 4px 0px
  .popover-item
    display flex
    justify-content space-between
    align-items center
    width 100%
    height 40px
    border none
    cursor pointer
    &:hover
      opacity 0.6

.icon-18
  width 18px
  height 18px

.icon-16
  width 16px
  height 16px

.bold
  color #000 !important
  font-weight bold

.hover
  cursor pointer
  &:hover
    opacity 0.8

// 富文本css
.ck-content
  width 100%
  word-break break-word
  h1, h2, h3, h4, h5
    color #383838
    font-weight 500
    line-height 1.6
  h1
    font-size 22px
  h2
    font-size 20px
  h3
    font-size 18px
  h4
    font-size 16px
  p
    color #383838
    font-size 14px
    line-height 24px
  img
    max-width 100%
  figure
    margin 0
    padding 10px
    text-indent 0
  .table
    table
      td
        padding 0.6em 1em !important
        border-width 1px
        border-style solid

tr
  &:hover
    .title
      color #3DA8F5
      font-size 14px
    .operations
      display flex
      justify-content space-around
      align-items center
      width 100%
  .operations
    display none

.three-line
  display -webkit-box
  overflow hidden
  -webkit-box-orient vertical
  -webkit-line-clamp 3

.two-line
  display -webkit-box
  overflow hidden
  -webkit-box-orient vertical
  -webkit-line-clamp 2

.one-line
  display -webkit-box
  overflow hidden
  -webkit-box-orient vertical
  -webkit-line-clamp 1

.clickable
  cursor pointer
