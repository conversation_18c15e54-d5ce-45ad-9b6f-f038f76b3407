/**
 * 全局语音播报服务
 * 基于 Web Speech API 的 SpeechSynthesisUtterance
 */

export interface SpeechSettings {
  enabled: boolean;
  rate: number; // 语速 0.1-10
  pitch: number; // 音调 0-2
  volume: number; // 音量 0-1
  voice: SpeechSynthesisVoice | null; // 选择的语音
  lang: string; // 语言
}

export interface SpeechQueueItem {
  text: string;
  priority: 'low' | 'normal' | 'high';
  interrupt?: boolean; // 是否中断当前播放
}

class SpeechService {
  private synth: SpeechSynthesis;
  private currentUtterance: SpeechSynthesisUtterance | null = null;
  private queue: SpeechQueueItem[] = [];
  private isPlaying = false;
  private settings: SpeechSettings;
  private voices: SpeechSynthesisVoice[] = [];

  constructor() {
    this.synth = window.speechSynthesis;
    
    // 默认设置
    this.settings = {
      enabled: false,
      rate: 1,
      pitch: 1,
      volume: 0.8,
      voice: null,
      lang: 'zh-CN'
    };

    this.loadSettings();
    this.initVoices();
    
    // 监听语音列表变化
    if (this.synth.onvoiceschanged !== undefined) {
      this.synth.onvoiceschanged = () => {
        this.initVoices();
      };
    }
  }

  /**
   * 初始化可用语音列表
   */
  private initVoices(): void {
    this.voices = this.synth.getVoices();
    
    // 如果没有设置语音，尝试选择中文语音
    if (!this.settings.voice && this.voices.length > 0) {
      const chineseVoice = this.voices.find(voice => 
        voice.lang.includes('zh') || voice.lang.includes('CN')
      );
      this.settings.voice = chineseVoice || this.voices[0];
    }
  }

  /**
   * 从 localStorage 加载设置
   */
  private loadSettings(): void {
    try {
      const saved = localStorage.getItem('speech-settings');
      if (saved) {
        const savedSettings = JSON.parse(saved);
        this.settings = { ...this.settings, ...savedSettings };
      }
    } catch (error) {
      console.warn('Failed to load speech settings:', error);
    }
  }

  /**
   * 保存设置到 localStorage
   */
  private saveSettings(): void {
    try {
      // 保存时排除 voice 对象，只保存 voice 的基本信息
      const settingsToSave = {
        ...this.settings,
        voice: this.settings.voice ? {
          name: this.settings.voice.name,
          lang: this.settings.voice.lang,
          voiceURI: this.settings.voice.voiceURI
        } : null
      };
      localStorage.setItem('speech-settings', JSON.stringify(settingsToSave));
    } catch (error) {
      console.warn('Failed to save speech settings:', error);
    }
  }

  /**
   * 获取当前设置
   */
  getSettings(): SpeechSettings {
    return { ...this.settings };
  }

  /**
   * 更新设置
   */
  updateSettings(newSettings: Partial<SpeechSettings>): void {
    this.settings = { ...this.settings, ...newSettings };
    this.saveSettings();
  }

  /**
   * 获取可用语音列表
   */
  getVoices(): SpeechSynthesisVoice[] {
    return this.voices;
  }

  /**
   * 启用/禁用语音播报
   */
  setEnabled(enabled: boolean): void {
    this.settings.enabled = enabled;
    if (!enabled) {
      this.stop();
    }
    this.saveSettings();
  }

  /**
   * 检查是否支持语音合成
   */
  isSupported(): boolean {
    return 'speechSynthesis' in window;
  }

  /**
   * 播放文本
   */
  speak(text: string, options: Partial<SpeechQueueItem> = {}): void {
    if (!this.settings.enabled || !this.isSupported() || !text.trim()) {
      return;
    }

    const queueItem: SpeechQueueItem = {
      text: text.trim(),
      priority: 'normal',
      interrupt: false,
      ...options
    };

    // 高优先级或需要中断时，清空队列
    if (queueItem.priority === 'high' || queueItem.interrupt) {
      this.stop();
      this.queue = [];
    }

    this.queue.push(queueItem);
    this.processQueue();
  }

  /**
   * 处理播放队列
   */
  private processQueue(): void {
    if (this.isPlaying || this.queue.length === 0) {
      return;
    }

    const item = this.queue.shift()!;
    this.playText(item.text);
  }

  /**
   * 播放单个文本
   */
  private playText(text: string): void {
    if (!text.trim()) {
      this.processQueue();
      return;
    }

    this.currentUtterance = new SpeechSynthesisUtterance(text);
    
    // 应用设置
    this.currentUtterance.rate = this.settings.rate;
    this.currentUtterance.pitch = this.settings.pitch;
    this.currentUtterance.volume = this.settings.volume;
    this.currentUtterance.lang = this.settings.lang;
    
    if (this.settings.voice) {
      this.currentUtterance.voice = this.settings.voice;
    }

    // 事件监听
    this.currentUtterance.onstart = () => {
      this.isPlaying = true;
    };

    this.currentUtterance.onend = () => {
      this.isPlaying = false;
      this.currentUtterance = null;
      // 继续处理队列
      setTimeout(() => this.processQueue(), 100);
    };

    this.currentUtterance.onerror = (event) => {
      console.warn('Speech synthesis error:', event);
      this.isPlaying = false;
      this.currentUtterance = null;
      // 继续处理队列
      setTimeout(() => this.processQueue(), 100);
    };

    this.synth.speak(this.currentUtterance);
  }

  /**
   * 暂停播放
   */
  pause(): void {
    if (this.synth.speaking && !this.synth.paused) {
      this.synth.pause();
    }
  }

  /**
   * 恢复播放
   */
  resume(): void {
    if (this.synth.paused) {
      this.synth.resume();
    }
  }

  /**
   * 停止播放
   */
  stop(): void {
    this.synth.cancel();
    this.isPlaying = false;
    this.currentUtterance = null;
  }

  /**
   * 清空队列
   */
  clearQueue(): void {
    this.queue = [];
  }

  /**
   * 获取播放状态
   */
  getStatus() {
    return {
      isPlaying: this.isPlaying,
      isPaused: this.synth.paused,
      isSpeaking: this.synth.speaking,
      queueLength: this.queue.length
    };
  }

  /**
   * 快速播报常用信息
   */
  speakPageTitle(): void {
    const title = document.title;
    if (title) {
      this.speak(`当前页面：${title}`, { priority: 'high' });
    }
  }

  speakElementText(element: Element): void {
    const text = this.getElementText(element);
    if (text) {
      this.speak(text);
    }
  }

  speakSuccess(message = '操作成功'): void {
    this.speak(message, { priority: 'high', interrupt: true });
  }

  speakError(message = '操作失败'): void {
    this.speak(`错误：${message}`, { priority: 'high', interrupt: true });
  }

  speakWarning(message = '警告'): void {
    this.speak(`警告：${message}`, { priority: 'high', interrupt: true });
  }

  /**
   * 获取元素的可读文本
   */
  private getElementText(element: Element): string {
    // 优先获取 aria-label
    const ariaLabel = element.getAttribute('aria-label');
    if (ariaLabel) return ariaLabel;

    // 获取 title 属性
    const title = element.getAttribute('title');
    if (title) return title;

    // 获取 alt 属性（图片）
    const alt = element.getAttribute('alt');
    if (alt) return alt;

    // 获取文本内容
    const textContent = element.textContent?.trim();
    if (textContent) return textContent;

    // 获取 placeholder（输入框）
    const placeholder = element.getAttribute('placeholder');
    if (placeholder) return `输入框：${placeholder}`;

    // 获取 value（按钮）
    const value = (element as HTMLInputElement).value;
    if (value) return value;

    return '';
  }
}

// 创建全局实例
const speechService = new SpeechService();

export default speechService;
