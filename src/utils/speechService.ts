/**
 * 全局语音播报服务
 * 基于 Web Speech API 的 SpeechSynthesisUtterance
 */

export interface SpeechSettings {
  enabled: boolean;
  rate: number; // 语速 0.1-10
  pitch: number; // 音调 0-2
  volume: number; // 音量 0-1
  voice: SpeechSynthesisVoice | null; // 选择的语音
  lang: string; // 语言
}

export interface SpeechQueueItem {
  text: string;
  priority: 'low' | 'normal' | 'high';
  interrupt?: boolean; // 是否中断当前播放
}

class SpeechService {
  private synth: SpeechSynthesis;
  private currentUtterance: SpeechSynthesisUtterance | null = null;
  private queue: SpeechQueueItem[] = [];
  private isPlaying = false;
  private settings: SpeechSettings;
  private voices: SpeechSynthesisVoice[] = [];

  constructor() {
    this.synth = window.speechSynthesis;
    
    // 默认设置
    this.settings = {
      enabled: false,
      rate: 1,
      pitch: 1,
      volume: 0.8,
      voice: null,
      lang: 'zh-CN'
    };

    this.loadSettings();
    this.initVoices();
    
    // 监听语音列表变化
    if (this.synth.onvoiceschanged !== undefined) {
      this.synth.onvoiceschanged = () => {
        this.initVoices();
      };
    }
  }

  /**
   * 初始化可用语音列表
   */
  private initVoices(): void {
    this.voices = this.synth.getVoices();
    
    // 如果没有设置语音，尝试选择中文语音
    if (!this.settings.voice && this.voices.length > 0) {
      const chineseVoice = this.voices.find(voice => 
        voice.lang.includes('zh') || voice.lang.includes('CN')
      );
      this.settings.voice = chineseVoice || this.voices[0];
    }
  }

  /**
   * 从 localStorage 加载设置
   */
  private loadSettings(): void {
    try {
      const saved = localStorage.getItem('speech-settings');
      if (saved) {
        const savedSettings = JSON.parse(saved);
        this.settings = { ...this.settings, ...savedSettings };

        // 恢复语音对象
        if (savedSettings.voice && savedSettings.voice.voiceURI) {
          // 延迟恢复语音，等待语音列表加载
          setTimeout(() => {
            const voice = this.voices.find(v => v.voiceURI === savedSettings.voice.voiceURI);
            if (voice) {
              this.settings.voice = voice;
            }
          }, 100);
        }
      }
    } catch (error) {
      console.warn('Failed to load speech settings:', error);
    }
  }

  /**
   * 保存设置到 localStorage
   */
  private saveSettings(): void {
    try {
      // 保存时排除 voice 对象，只保存 voice 的基本信息
      const settingsToSave = {
        ...this.settings,
        voice: this.settings.voice ? {
          name: this.settings.voice.name,
          lang: this.settings.voice.lang,
          voiceURI: this.settings.voice.voiceURI
        } : null
      };
      localStorage.setItem('speech-settings', JSON.stringify(settingsToSave));
    } catch (error) {
      console.warn('Failed to save speech settings:', error);
    }
  }

  /**
   * 获取当前设置
   */
  getSettings(): SpeechSettings {
    return { ...this.settings };
  }

  /**
   * 更新设置
   */
  updateSettings(newSettings: Partial<SpeechSettings>): void {
    this.settings = { ...this.settings, ...newSettings };
    this.saveSettings();
  }

  /**
   * 获取可用语音列表
   */
  getVoices(): SpeechSynthesisVoice[] {
    return this.voices;
  }

  /**
   * 启用/禁用语音播报
   */
  setEnabled(enabled: boolean): void {
    this.settings.enabled = enabled;
    if (!enabled) {
      this.stop();
    }
    this.saveSettings();
  }

  /**
   * 检查是否支持语音合成
   */
  isSupported(): boolean {
    return 'speechSynthesis' in window;
  }

  /**
   * 播放文本
   */
  speak(text: string, options: Partial<SpeechQueueItem> = {}): void {
    if (!this.settings.enabled || !this.isSupported() || !text.trim()) {
      return;
    }

    const queueItem: SpeechQueueItem = {
      text: text.trim(),
      priority: 'normal',
      interrupt: false,
      ...options
    };

    // 高优先级或需要中断时，清空队列
    if (queueItem.priority === 'high' || queueItem.interrupt) {
      this.stop();
      this.queue = [];
    }

    this.queue.push(queueItem);
    this.processQueue();
  }

  /**
   * 处理播放队列
   */
  private processQueue(): void {
    if (this.isPlaying || this.queue.length === 0) {
      return;
    }

    const item = this.queue.shift()!;
    this.playText(item.text);
  }

  /**
   * 播放单个文本
   */
  private playText(text: string): void {
    if (!text.trim()) {
      this.processQueue();
      return;
    }

    this.currentUtterance = new SpeechSynthesisUtterance(text);

    // 应用设置
    this.currentUtterance.rate = this.settings.rate;
    this.currentUtterance.pitch = this.settings.pitch;
    this.currentUtterance.volume = this.settings.volume;
    this.currentUtterance.lang = this.settings.lang;

    if (this.settings.voice) {
      this.currentUtterance.voice = this.settings.voice;
    }

    // 事件监听
    this.currentUtterance.onstart = () => {
      this.isPlaying = true;
    };

    this.currentUtterance.onend = () => {
      this.isPlaying = false;
      this.currentUtterance = null;
      // 继续处理队列
      setTimeout(() => this.processQueue(), 100);
    };

    this.currentUtterance.onerror = (event) => {
      console.warn('Speech synthesis error:', event);
      this.isPlaying = false;
      this.currentUtterance = null;
      // 继续处理队列
      setTimeout(() => this.processQueue(), 100);
    };

    this.synth.speak(this.currentUtterance);
  }

  /**
   * 暂停播放
   */
  pause(): void {
    if (this.synth.speaking && !this.synth.paused) {
      this.synth.pause();
    }
  }

  /**
   * 恢复播放
   */
  resume(): void {
    if (this.synth.paused) {
      this.synth.resume();
    }
  }

  /**
   * 停止播放
   */
  stop(): void {
    this.synth.cancel();
    this.isPlaying = false;
    this.currentUtterance = null;
  }

  /**
   * 清空队列
   */
  clearQueue(): void {
    this.queue = [];
  }

  /**
   * 获取播放状态
   */
  getStatus() {
    return {
      isPlaying: this.isPlaying,
      isPaused: this.synth.paused,
      isSpeaking: this.synth.speaking,
      queueLength: this.queue.length
    };
  }

  /**
   * 快速播报常用信息
   */
  speakPageTitle(): void {
    const title = document.title;
    if (title) {
      this.speak(`当前页面：${title}`, { priority: 'high' });
    }
  }

  speakElementText(element: Element): void {
    const text = this.getElementText(element);
    if (text) {
      this.speak(text);
    }
  }

  speakSuccess(message = '操作成功'): void {
    this.speak(message, { priority: 'high', interrupt: true });
  }

  speakError(message = '操作失败'): void {
    this.speak(`错误：${message}`, { priority: 'high', interrupt: true });
  }

  speakWarning(message = '警告'): void {
    this.speak(`警告：${message}`, { priority: 'high', interrupt: true });
  }

  /**
   * 播报页面主要内容
   */
  speakPageContent(): void {
    const content = this.extractPageContent();
    if (content.length > 0) {
      this.speak(`页面内容：${content.join('，')}`, { priority: 'high' });
    }
  }

  /**
   * 提取页面主要内容
   */
  private extractPageContent(): string[] {
    const content: string[] = [];

    // 获取主要标题
    const h1 = document.querySelector('h1');
    if (h1 && this.isValidTextContent(h1)) {
      const text = this.extractCleanText(h1);
      if (text) {
        content.push(`主标题：${text}`);
      }
    }

    // 获取副标题
    const h2Elements = document.querySelectorAll('h2');
    h2Elements.forEach((h2, index) => {
      if (index < 3 && this.isValidTextContent(h2)) {
        const text = this.extractCleanText(h2);
        if (text) {
          content.push(`副标题：${text}`);
        }
      }
    });

    // 获取主要段落内容
    const paragraphs = document.querySelectorAll('p');
    paragraphs.forEach((p, index) => {
      if (index < 2 && this.isValidTextContent(p)) {
        const text = this.extractCleanText(p);
        if (text && text.length > 20 && text.length < 200) {
          content.push(text);
        }
      }
    });

    // 获取导航菜单
    const navItems = document.querySelectorAll('nav a, .ant-menu-item');
    const navTexts: string[] = [];
    navItems.forEach((item, index) => {
      if (index < 5 && this.isValidTextContent(item)) {
        const text = this.extractCleanText(item);
        if (text) {
          navTexts.push(text);
        }
      }
    });
    if (navTexts.length > 0) {
      content.push(`导航菜单包含：${navTexts.join('、')}`);
    }

    return content;
  }

  /**
   * 检查元素是否包含有效的文本内容
   */
  private isValidTextContent(element: Element): boolean {
    // 跳过隐藏元素
    if (element instanceof HTMLElement) {
      const style = window.getComputedStyle(element);
      if (style.display === 'none' || style.visibility === 'hidden') {
        return false;
      }
    }

    // 跳过图片、视频、音频等媒体元素
    const mediaSelectors = ['img', 'video', 'audio', 'canvas', 'svg', 'iframe'];
    if (mediaSelectors.includes(element.tagName.toLowerCase())) {
      return false;
    }

    // 跳过包含媒体元素但没有文本的容器
    const hasMediaOnly = element.querySelectorAll('img, video, audio, canvas, svg, iframe').length > 0 &&
                         !this.extractCleanText(element);

    if (hasMediaOnly) {
      return false;
    }

    // 检查是否有有效文本内容
    const text = this.extractCleanText(element);
    return !!text && text.length > 0;
  }

  /**
   * 提取元素的纯文本内容，过滤掉非文字内容
   */
  private extractCleanText(element: Element): string {
    // 克隆元素以避免修改原始DOM
    const clone = element.cloneNode(true) as Element;

    // 移除所有媒体元素
    const mediaElements = clone.querySelectorAll('img, video, audio, canvas, svg, iframe, object, embed');
    mediaElements.forEach(media => media.remove());

    // 移除附件链接和下载链接
    const attachmentLinks = clone.querySelectorAll('a[href*=".pdf"], a[href*=".doc"], a[href*=".xls"], a[href*=".zip"], a[href*=".rar"], a[download]');
    attachmentLinks.forEach(link => link.remove());

    // 移除图标元素
    const iconElements = clone.querySelectorAll('.icon, .fa, .anticon, [class*="icon-"], [class*="fa-"]');
    iconElements.forEach(icon => icon.remove());

    // 获取纯文本内容
    const text = clone.textContent || '';

    // 清理文本：移除多余空白、换行符等
    return text
      .replace(/\s+/g, ' ') // 将多个空白字符替换为单个空格
      .replace(/[\r\n\t]/g, ' ') // 移除换行符和制表符
      .trim();
  }

  /**
   * 播报当前焦点元素
   */
  speakFocusedElement(): void {
    const focused = document.activeElement;
    if (focused && focused !== document.body) {
      const text = this.getElementText(focused);
      if (text) {
        this.speak(`当前焦点：${text}`, { priority: 'high' });
      }
    }
  }

  /**
   * 播报页面结构
   */
  speakPageStructure(): void {
    const structure: string[] = [];

    // 统计各种元素
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6').length;
    const links = document.querySelectorAll('a').length;
    const buttons = document.querySelectorAll('button').length;
    const forms = document.querySelectorAll('form').length;
    const images = document.querySelectorAll('img').length;

    if (headings > 0) structure.push(`${headings}个标题`);
    if (links > 0) structure.push(`${links}个链接`);
    if (buttons > 0) structure.push(`${buttons}个按钮`);
    if (forms > 0) structure.push(`${forms}个表单`);
    if (images > 0) structure.push(`${images}张图片`);

    if (structure.length > 0) {
      this.speak(`页面包含：${structure.join('、')}`, { priority: 'high' });
    }
  }

  /**
   * 获取元素的可读文本
   */
  private getElementText(element: Element): string {
    // 检查是否为有效的文本内容
    if (!this.isValidTextContent(element)) {
      // 对于图片等媒体元素，尝试获取描述性属性
      if (['img', 'video', 'audio'].includes(element.tagName.toLowerCase())) {
        const alt = element.getAttribute('alt');
        const title = element.getAttribute('title');
        if (alt) return `图片：${alt}`;
        if (title) return `媒体：${title}`;
        return ''; // 跳过没有描述的媒体元素
      }
      return '';
    }

    // 优先获取 aria-label
    const ariaLabel = element.getAttribute('aria-label');
    if (ariaLabel) return ariaLabel;

    // 获取 title 属性
    const title = element.getAttribute('title');
    if (title) return title;

    // 获取 placeholder（输入框）
    const placeholder = element.getAttribute('placeholder');
    if (placeholder) return `输入框：${placeholder}`;

    // 获取 value（按钮）
    if (element instanceof HTMLInputElement && element.value) {
      return element.value;
    }

    // 获取清理后的文本内容
    const cleanText = this.extractCleanText(element);
    if (cleanText) return cleanText;

    return '';
  }
}

// 创建全局实例
const speechService = new SpeechService();

export default speechService;
