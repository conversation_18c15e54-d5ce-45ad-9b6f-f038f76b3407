/**
 * 语音播报混入
 * 为Vue组件提供语音播报功能
 */

import Vue from 'vue';
import speechService from './speechService';

export const SpeechMixin = Vue.extend({
  methods: {
    /**
     * 播报文本
     */
    $speak(text: string, options?: any) {
      speechService.speak(text, options);
    },

    /**
     * 播报成功信息
     */
    $speakSuccess(message = '操作成功') {
      speechService.speakSuccess(message);
    },

    /**
     * 播报错误信息
     */
    $speakError(message = '操作失败') {
      speechService.speakError(message);
    },

    /**
     * 播报警告信息
     */
    $speakWarning(message = '警告') {
      speechService.speakWarning(message);
    },

    /**
     * 播报页面标题
     */
    $speakPageTitle() {
      speechService.speakPageTitle();
    },

    /**
     * 播报表单验证错误
     */
    $speakFormErrors(errors: any) {
      if (!errors || typeof errors !== 'object') {
        return;
      }

      const errorMessages: string[] = [];
      
      // 处理不同格式的错误对象
      if (Array.isArray(errors)) {
        // 数组格式的错误
        errors.forEach(error => {
          if (typeof error === 'string') {
            errorMessages.push(error);
          } else if (error.message) {
            errorMessages.push(error.message);
          }
        });
      } else {
        // 对象格式的错误
        Object.keys(errors).forEach(field => {
          const fieldErrors = errors[field];
          if (Array.isArray(fieldErrors)) {
            fieldErrors.forEach(error => {
              errorMessages.push(`${field}: ${error}`);
            });
          } else if (typeof fieldErrors === 'string') {
            errorMessages.push(`${field}: ${fieldErrors}`);
          }
        });
      }

      if (errorMessages.length > 0) {
        const message = `表单验证失败：${errorMessages.join('，')}`;
        this.$speakError(message);
      }
    },

    /**
     * 播报加载状态
     */
    $speakLoading(isLoading: boolean, message?: string) {
      if (isLoading) {
        this.$speak(message || '正在加载中，请稍候', { priority: 'normal' });
      } else {
        this.$speak('加载完成', { priority: 'normal' });
      }
    },

    /**
     * 播报路由变化
     */
    $speakRouteChange(to: any, from: any) {
      if (to.meta && to.meta.title) {
        this.$speak(`进入页面：${to.meta.title}`, { priority: 'high' });
      }
    },

    /**
     * 播报数据变化
     */
    $speakDataChange(type: 'add' | 'update' | 'delete', itemName = '数据') {
      const messages = {
        add: `已添加${itemName}`,
        update: `已更新${itemName}`,
        delete: `已删除${itemName}`
      };
      this.$speakSuccess(messages[type]);
    },

    /**
     * 播报分页信息
     */
    $speakPagination(current: number, total: number, pageSize: number) {
      const totalPages = Math.ceil(total / pageSize);
      this.$speak(`第${current}页，共${totalPages}页，${total}条记录`);
    },

    /**
     * 播报搜索结果
     */
    $speakSearchResult(count: number, keyword?: string) {
      const keywordText = keyword ? `关键词"${keyword}"` : '';
      this.$speak(`${keywordText}搜索结果：找到${count}条记录`);
    },

    /**
     * 播报选择状态
     */
    $speakSelection(selectedCount: number, totalCount: number) {
      if (selectedCount === 0) {
        this.$speak('未选择任何项目');
      } else if (selectedCount === totalCount) {
        this.$speak(`已选择全部${totalCount}个项目`);
      } else {
        this.$speak(`已选择${selectedCount}个项目，共${totalCount}个`);
      }
    },

    /**
     * 播报文件上传状态
     */
    $speakFileUpload(status: 'start' | 'progress' | 'success' | 'error', fileName?: string, progress?: number) {
      const fileText = fileName ? `文件"${fileName}"` : '文件';
      
      switch (status) {
        case 'start':
          this.$speak(`开始上传${fileText}`);
          break;
        case 'progress':
          if (progress !== undefined) {
            this.$speak(`${fileText}上传进度：${Math.round(progress)}%`);
          }
          break;
        case 'success':
          this.$speakSuccess(`${fileText}上传成功`);
          break;
        case 'error':
          this.$speakError(`${fileText}上传失败`);
          break;
      }
    },

    /**
     * 播报模态框状态
     */
    $speakModal(action: 'open' | 'close', title?: string) {
      const titleText = title ? `"${title}"` : '';
      if (action === 'open') {
        this.$speak(`打开${titleText}对话框`, { priority: 'high' });
      } else {
        this.$speak(`关闭${titleText}对话框`);
      }
    },

    /**
     * 播报菜单导航
     */
    $speakMenuNavigation(menuName: string, isOpen: boolean) {
      if (isOpen) {
        this.$speak(`展开${menuName}菜单`);
      } else {
        this.$speak(`收起${menuName}菜单`);
      }
    },

    /**
     * 播报表格操作
     */
    $speakTableAction(action: string, rowData?: any) {
      let message = action;
      if (rowData && rowData.name) {
        message += `：${rowData.name}`;
      }
      this.$speak(message);
    },

    /**
     * 播报时间信息
     */
    $speakTime(date?: Date) {
      const targetDate = date || new Date();
      const timeStr = targetDate.toLocaleString('zh-CN');
      this.$speak(`当前时间：${timeStr}`, { priority: 'high' });
    },

    /**
     * 播报快捷键提示
     */
    $speakShortcut(key: string, description: string) {
      this.$speak(`快捷键${key}：${description}`, { priority: 'normal' });
    }
  },

  mounted() {
    // 页面加载完成后播报页面标题
    this.$nextTick(() => {
      if (speechService.getSettings().enabled) {
        // 延迟播报，避免与其他语音冲突
        setTimeout(() => {
          this.$speakPageTitle();
        }, 1000);
      }
    });
  }
});

// 全局混入（可选）
export function installSpeechMixin() {
  Vue.mixin(SpeechMixin);
}

export default SpeechMixin;
