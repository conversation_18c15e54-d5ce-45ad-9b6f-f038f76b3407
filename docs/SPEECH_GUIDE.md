# 语音播报功能使用指南

## 概述

本项目集成了基于 Web Speech API 的语音播报功能，为用户提供无障碍的语音交互体验。该功能特别适用于适老化模式和视觉障碍用户。

## 功能特性

- 🎵 **文本转语音**：支持中文语音合成
- 🎛️ **语音控制**：可调节语速、音量、音调
- 🔄 **播放队列**：支持语音队列管理和优先级
- 🎯 **智能触发**：支持鼠标悬停、焦点、点击等事件触发
- 🔧 **自定义配置**：可选择不同的语音引擎
- 💾 **状态持久化**：设置自动保存到本地存储

## 快速开始

### 1. 启用语音播报

在页面右上角找到语音播报控制按钮（🔊图标），点击开启语音功能。

### 2. 适老化模式集成

开启适老化模式时，语音播报功能会自动启用，并使用适合老年人的语音参数：
- 语速：0.8（较慢）
- 音量：0.9（较大）
- 音调：1.0（标准）

## 开发者使用指南

### 基础API

```typescript
// 直接使用语音服务
import speechService from '@/utils/speechService';

// 播报文本
speechService.speak('要播报的文本');

// 播报成功消息
speechService.speakSuccess('操作成功');

// 播报错误消息
speechService.speakError('操作失败');

// 播报警告消息
speechService.speakWarning('警告信息');
```

### Vue组件中使用

```typescript
// 在Vue组件中使用
export default class MyComponent extends Vue {
  handleClick() {
    // 使用全局语音服务
    this.$speech.speak('按钮被点击了');
    
    // 或使用便捷方法
    this.$speech.speakSuccess('操作成功');
  }
}
```

### 语音指令

使用 `v-speech` 指令为元素添加语音播报：

```vue
<template>
  <!-- 鼠标悬停时播报 -->
  <button v-speech:hover="'这是一个按钮'">点击我</button>
  
  <!-- 获得焦点时播报 -->
  <input v-speech:focus="'用户名输入框'" placeholder="用户名" />
  
  <!-- 点击时播报 -->
  <a v-speech:click="'跳转到首页'" href="/">首页</a>
  
  <!-- 自动播报（元素出现时） -->
  <div v-speech:auto="'重要通知'">重要内容</div>
  
  <!-- 默认行为（根据元素类型自动选择事件） -->
  <button v-speech="'提交表单'">提交</button>
</template>
```

### 语音设置

```typescript
// 获取当前设置
const settings = speechService.getSettings();

// 更新设置
speechService.updateSettings({
  rate: 1.2,      // 语速 (0.1-10)
  pitch: 1.0,     // 音调 (0-2)
  volume: 0.8,    // 音量 (0-1)
  lang: 'zh-CN'   // 语言
});

// 启用/禁用语音播报
speechService.setEnabled(true);
```

### 高级功能

#### 播放队列和优先级

```typescript
// 普通优先级
speechService.speak('普通消息', { priority: 'normal' });

// 高优先级（会中断当前播放）
speechService.speak('重要消息', { priority: 'high', interrupt: true });

// 低优先级
speechService.speak('次要消息', { priority: 'low' });
```

#### 语音控制

```typescript
// 暂停播放
speechService.pause();

// 恢复播放
speechService.resume();

// 停止播放
speechService.stop();

// 清空队列
speechService.clearQueue();

// 获取播放状态
const status = speechService.getStatus();
console.log(status.isPlaying, status.isPaused, status.queueLength);
```

#### 常用场景

```typescript
// 表单验证错误
speechService.speakError('请填写必填字段');

// 数据加载
speechService.speak('正在加载数据，请稍候');

// 操作成功
speechService.speakSuccess('数据保存成功');

// 页面导航
speechService.speak('进入用户管理页面');

// 文件上传
speechService.speak('文件上传进度：50%');
```

## 浏览器兼容性

| 浏览器 | 版本要求 | 支持状态 |
|--------|----------|----------|
| Chrome | 33+ | ✅ 完全支持 |
| Firefox | 49+ | ✅ 完全支持 |
| Safari | 7+ | ✅ 完全支持 |
| Edge | 14+ | ✅ 完全支持 |
| IE | - | ❌ 不支持 |

## 🎯 智能内容过滤

语音播报系统具备智能内容过滤功能，确保只播报有意义的文本内容：

### 自动跳过的内容

1. **媒体元素**
   - 图片（`<img>`）
   - 视频（`<video>`）
   - 音频（`<audio>`）
   - 画布（`<canvas>`）
   - SVG图形（`<svg>`）
   - 内嵌框架（`<iframe>`）

2. **附件和下载链接**
   - PDF文件链接
   - Office文档（DOC、XLS等）
   - 压缩文件（ZIP、RAR等）
   - 带有`download`属性的链接

3. **装饰性元素**
   - 图标元素（`.icon`、`.fa`、`.anticon`等）
   - 隐藏元素（`display: none`、`visibility: hidden`）
   - 空白或无意义的文本

### 文本清理功能

- 移除多余的空白字符
- 清理换行符和制表符
- 过滤过短或过长的内容
- 限制文本长度（避免播报过长内容）

### 智能描述

对于媒体元素，系统会尝试获取描述性属性：
- 图片的`alt`属性
- 元素的`title`属性
- ARIA标签（`aria-label`）

## 最佳实践

### 1. 语音文本编写

- 使用简洁明了的语言
- 避免过长的文本（建议100字以内）
- 为专业术语提供解释
- 使用标点符号控制语音节奏

### 2. 用户体验

- 提供语音开关控制
- 避免频繁的语音播报
- 重要信息使用高优先级
- 为语音播报提供视觉反馈

### 3. 性能优化

- 合理使用播放队列
- 避免同时播报多个长文本
- 在组件销毁时清理语音资源

### 4. 无障碍设计

- 为所有交互元素添加语音描述
- 提供键盘导航支持
- 使用语义化的HTML标签
- 添加适当的ARIA标签

## 故障排除

### 常见问题

1. **语音不播放**
   - 检查浏览器是否支持Web Speech API
   - 确认语音播报功能已启用
   - 检查浏览器音量设置

2. **语音质量差**
   - 尝试选择不同的语音引擎
   - 调整语速和音调设置
   - 检查网络连接状态

3. **语音中断**
   - 检查是否有高优先级语音插队
   - 确认页面没有其他语音冲突
   - 重启语音服务

4. **内容过滤问题**
   - 系统会自动跳过图片、视频、音频等媒体元素
   - 附件链接（PDF、DOC、ZIP等）会被过滤
   - 图标元素不会被播报
   - 隐藏元素会被自动跳过
   - 只播报有意义的文本内容

### 调试方法

```typescript
// 检查语音支持
console.log('语音支持:', speechService.isSupported());

// 查看可用语音
console.log('可用语音:', speechService.getVoices());

// 监听语音状态
setInterval(() => {
  console.log('语音状态:', speechService.getStatus());
}, 1000);
```

## 更新日志

### v1.0.0
- 基础语音播报功能
- 语音控制组件
- 适老化模式集成
- 语音指令支持

## 贡献指南

欢迎提交问题和改进建议！请确保：

1. 测试新功能的浏览器兼容性
2. 更新相关文档
3. 遵循项目代码规范
4. 添加适当的测试用例

## 许可证

本项目采用 MIT 许可证。
